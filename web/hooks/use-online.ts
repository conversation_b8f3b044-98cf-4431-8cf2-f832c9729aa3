'use client'

import { useEffect, useRef, useState } from 'react'
import { getSystemFeatures } from '@/service/common'

export const useOnline = () => {
  const [online, setOnline] = useState(globalThis.navigator.onLine)
  const timer = useRef<NodeJS.Timer>()

  const startCheck = () => {
    timer.current = setInterval(() => {
      getSystemFeatures().then((res) => {
        setOnline(true)
      }).catch(() => {
        setOnline(false)
      })
    }, 15000)
  }
  const finishCheck = () => {
    return clearInterval(timer.current)
  }
  useEffect(() => {
    return () => finishCheck()
  }, [])

  return {
    online,
    startCheck,
    finishCheck,
  }
}
