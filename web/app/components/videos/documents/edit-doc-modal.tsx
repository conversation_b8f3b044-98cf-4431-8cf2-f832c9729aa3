import type { FC } from 'react'
import React, { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { Form, Input, Select } from 'antd'
import { editVideoDocument } from '@/service/videos'
import type { MediaType } from '@/types/videos'

// 公共组件
import Toast from '@/app/components/base/toast'
import Modal from '@/app/components/base/modal'
import Button from '@/app/components/base/button'
import { useFormDisabled } from '@/hooks/use-form'

type Props = {
  videoId: string
  documentId: string
  type: MediaType
  name?: string
  frameInterval?: number
  onClose: () => void
  onSaved: () => void
}

const EditDocModal: FC<Props> = ({
  documentId,
  videoId,
  type,
  name,
  frameInterval,
  onClose,
  onSaved,
}) => {
  const { t } = useTranslation()
  const [form] = Form.useForm()
  const disabled = useFormDisabled(form)

  // 重命名
  const handleSave = async () => {
    await editVideoDocument(videoId, documentId, type, {
      frame_interval: form.getFieldValue('frame_interval'),
      name: form.getFieldValue('name'),
    })
    Toast.notify({ type: 'success', message: t('common.actionMsg.modifiedSuccessfully') })
    onSaved()
  }

  useEffect(() => {
    if (name) {
      form.setFieldsValue({
        name,
      })
    }
    if (frameInterval) {
      form.setFieldsValue({
        frame_interval: frameInterval,
      })
    }
  }, [form, frameInterval, name])

  return (
    <Modal
      title={t('videos.modalTitle.edit')}
      isShow
      footer={
        <>
          <Button variant='secondary-accent' className='mr-4' onClick={onClose}>{t('common.operation.cancel')}</Button>
          <Button disabled={disabled} variant='primary' onClick={handleSave}>{t('common.operation.save')}</Button>
        </>
      }
      closable
      onClose={onClose}
    >
      <Form layout='vertical' form={form}>
        {
          name && <Form.Item
            name={'name'}
            label={t('videos.doc.info.fileName')}
            validateFirst={true}
            rules={[
              {
                required: true,
                whitespace: true,
              },
            ]}>
            <Input placeholder={t('common.placeholder.input')!}></Input>
          </Form.Item>
        }
        {
          frameInterval && <Form.Item
            name={'frame_interval'}
            label={t('videos.doc.info.frameRate')}
            validateFirst={true}
            rules={[
              {
                required: true,
              },
            ]}>
            <Select
              options={[
                { value: 3, label: '3s' },
                { value: 5, label: '5s' },
                { value: 10, label: '10s' },
                { value: 30, label: '30s' },
                { value: 60, label: '60s' },
                { value: 300, label: '300s' },
              ]}
            />
          </Form.Item>
        }
      </Form>
    </Modal>
  )
}
export default React.memo(EditDocModal)
