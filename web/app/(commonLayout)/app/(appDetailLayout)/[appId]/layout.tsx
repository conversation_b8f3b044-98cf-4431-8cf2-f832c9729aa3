'use client'
import type { FC } from 'react'
import { useUnmount } from 'ahooks'
import React, { useCallback, useEffect, useState } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import {
  RiFileList3Fill,
  RiFileList3Line,
  RiTerminalWindowFill,
  RiTerminalWindowLine,
} from '@remixicon/react'
import { useTranslation } from 'react-i18next'
import { useShallow } from 'zustand/react/shallow'
import { useContextSelector } from 'use-context-selector'
import s from './style.module.css'
import cn from '@/utils/classnames'
import { useStore } from '@/app/components/app/store'
import { fetchAppDetail, fetchAppSSO } from '@/service/apps'
import { getAppPublishInfo } from '@/service/market'
import { useAppContext } from '@/context/app-context'
import Loading from '@/app/components/base/loading'
import AppInfo from '@/app/components/app-detail/app-info'
import TopNavbar from '@/app/components/layout/top-navbar'
import { APP_NAME } from '@/config'
import SystemContext from '@/context/system-context'

export type IAppDetailLayoutProps = {
  children: React.ReactNode
  params: { appId: string }
}

const AppDetailLayout: FC<IAppDetailLayoutProps> = (props) => {
  const {
    children,
    params: { appId }, // get appId in path
  } = props
  const { t } = useTranslation()
  const router = useRouter()
  const pathname = usePathname()
  const { canEditApp, isMicroApp } = useAppContext()
  const { appDetail, setAppDetail, appPublishInfo, setAppPublishInfo } = useStore(useShallow(state => ({
    appDetail: state.appDetail,
    setAppDetail: state.setAppDetail,
    appPublishInfo: state.appPublishInfo,
    setAppPublishInfo: state.setAppPublishInfo,
  })))
  const [navigation, setNavigation] = useState<Array<{
    name: string
    href: string
    icon: any
    selectedIcon: any
  }>>([])
  const systemFeatures = useContextSelector(SystemContext, state => state.systemFeatures)

  // 获取头部导航
  const getNavigations = useCallback((appId: string, canEditApp: boolean, mode: string) => {
    if (mode === 'workflow') {
      /* return [
        ...(canEditApp
          ? [{
            name: t('app.appMenus.workflowConfig'),
            href: `/app/${appId}/${(mode === 'workflow' || mode === 'advanced-chat') ? 'workflow' : 'configuration'}`,
            icon: RiTerminalWindowLine,
            selectedIcon: RiTerminalWindowFill,
          }]
          : []
        ),
      ] */
      return []
    }
    const navs = [
      {
        name: t('app.appMenus.promptEng'),
        href: `/app/${appId}/${(mode === 'workflow' || mode === 'advanced-chat') ? 'workflow' : 'configuration'}`,
        icon: RiTerminalWindowLine,
        selectedIcon: RiTerminalWindowFill,
      },
      /* {
        name: t('app.appMenus.apiAccess'),
        href: `/app/${appId}/develop`,
        icon: RiTerminalBoxLine,
        selectedIcon: RiTerminalBoxFill,
      }, */
      {
        name: t('app.appMenus.publish'),
        href: `/app/${appId}/publish`,
        icon: RiFileList3Line,
        selectedIcon: RiFileList3Fill,
      },
      {
        name: (mode === 'agent-chat' && isMicroApp)
          ? t('app.appMenus.logAndAnn')
          : t('app.appMenus.logs'),
        href: `/app/${appId}/logs`,
        icon: RiFileList3Line,
        selectedIcon: RiFileList3Fill,
      },
      {
        name: t('app.appMenus.overview'),
        href: `/app/${appId}/overview`,
        icon: RiFileList3Line,
        selectedIcon: RiFileList3Fill,
      },
    ]
    return navs
  }, [isMicroApp, t])
  // 返回时间
  const backFunc = () => {
    if (appDetail?.mode === 'agent-chat')
      router.push('/apps?category=agent-chat')

    else if (appDetail?.mode === 'advanced-chat')
      router.push('/apps?category=chat')

    else if (appDetail?.mode === 'workflow')
      router.push('/tools')

    else
      router.push('/apps')
  }

  useEffect(() => {
    if (appDetail)
      document.title = `${(appDetail.name || 'App')} - ${APP_NAME}`
  }, [appDetail])

  useEffect(() => {
    setAppDetail()
    setAppPublishInfo()
    fetchAppDetail({ url: '/apps', id: appId }).then((res) => {
      // redirection 暂时不区分权限
      const canIEditApp = canEditApp
      // if (!canIEditApp && (pathname.endsWith('workflow') || pathname.endsWith('logs'))) {
      //   router.replace(`/app/${appId}/overview`)
      //   return
      // }
      if ((res.mode === 'workflow' || res.mode === 'advanced-chat') && (pathname).endsWith('configuration')) {
        router.replace(`/app/${appId}/workflow`)
      }
      else if ((res.mode !== 'workflow' && res.mode !== 'advanced-chat') && (pathname).endsWith('workflow')) {
        router.replace(`/app/${appId}/configuration`)
      }
      else {
        setAppDetail({ ...res, enable_sso: false })
        setNavigation(getNavigations(appId, canEditApp, res.mode))
        if (systemFeatures.enable_web_sso_switch_component && canIEditApp) {
          fetchAppSSO({ appId }).then((ssoRes) => {
            setAppDetail({ ...res, enable_sso: ssoRes.enabled })
          })
        }
        getAppPublishInfo(appId).then((res) => {
          setAppPublishInfo(res)
        })
      }
    }).catch((e: any) => {
      if (e.status === 404)
        router.replace('/apps')
    })
  }, [appId, canEditApp, systemFeatures, getNavigations, pathname, router, setAppDetail, setAppPublishInfo])

  useUnmount(() => {
    setAppDetail()
  })

  if (!appDetail || !appPublishInfo) {
    return (
      <div className='flex items-center justify-center h-full bg-white'>
        <Loading />
      </div>
    )
  }

  return (
    <div className={cn(s.app, 'flex flex-col h-full overflow-hidden')}>
      {appDetail && (
        <TopNavbar
          navigation={navigation}
          className={s.appTopBar}
          leftContent={
            <AppInfo expand={true}></AppInfo>
          }
          backFunc={backFunc}
        />
      )}
      <div className="grow" style={{ height: appDetail ? 'calc(100% - 60px)' : '100%', background: '#ffffff' }}>
        {children}
      </div>
    </div>
  )
}
export default React.memo(AppDetailLayout)
