/* eslint-disable multiline-ternary */
'use client'
import React, { forwardRef, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Form, Input } from 'antd'
import type { datasetInfoType } from '..'
import type { FileItem } from '@/types/public/file'
import { DatasetFileType, DatesetFileTypeCode } from '@/models/datasets'
// 公共能力
import { useFormDisabled } from '@/hooks/use-form'
import FileUploaderInDrag from '@/app/components/base/file-uploader/file-uploader-in-drag'
import RadioCard from '@/app/components/base/radio-card'
import {
  StructuredDataset,
  UnstructuredDataset,
} from '@/app/components/base/icons/src/public/avatar'
import { useProviderContext } from '@/context/provider-context'

type IStepOneProps = {
  datasetId?: string
  files: FileItem[]
  updateFileList: (files: FileItem[]) => void
  updateFile: (fileItem: FileItem, progress: number, list: FileItem[]) => void
  updateReady: (ready: boolean) => void
  updateFormInfo: (info: datasetInfoType) => void
  updateFileType: (type: DatasetFileType) => void
}

const StepOne = forwardRef(
  (
    {
      datasetId,
      files: fileList,
      updateReady,
      updateFile,
      updateFileList,
      updateFormInfo,
      updateFileType,
    }: IStepOneProps,
    ref,
  ) => {
    const { t } = useTranslation()
    const { useXIYANRag } = useProviderContext()
    const [form] = Form.useForm()
    const values = Form.useWatch([], form)
    const disabled = useFormDisabled(form)

    // 当前文件
    const [currentFile, setCurrentFile] = useState<File | undefined>()
    // 模式
    const [dataType, setDataType] = useState<DatasetFileType>(
      DatasetFileType.Unstructured,
    )

    // 更新当前文件
    const updateCurrentFile = (file: File) => {
      setCurrentFile(file)
    }
    // 更新数据类型
    const updateDataType = (type: DatasetFileType) => {
      if (type !== dataType)
        updateFileList([])
      setDataType(type)
      updateFileType(type)
    }

    useEffect(() => {
      updateReady(
        !(disabled || !fileList.length || fileList.some(file => !file.file.id)),
      )
    }, [disabled, updateReady, fileList])
    useEffect(() => {
      updateFormInfo({
        ...values,
      })
    }, [values])

    return (
      <Form form={form} layout="vertical">
        {!datasetId && (
          <>
            {/* 知识库名称 */}
            <Form.Item
              label={t('datasetCreation.stepOne.form.name')}
              tooltip={t('datasetCreation.stepOne.form.nameDescription')}
              name={'name'}
              validateFirst={true}
              validateTrigger="onChange"
              rules={[
                {
                  required: true,
                  whitespace: true,
                  pattern: /^[a-zA-Z0-9\u4E00-\u9FA5()-_.]{1,20}$/,
                },
              ]}
            >
              <Input
                placeholder={
                  t('datasetCreation.stepOne.form.namePlaceholder') || ''
                }
              />
            </Form.Item>
            {/* 知识库描述 */}
            <Form.Item
              name={'description'}
              label={t('datasetCreation.stepOne.form.desc')}
              validateFirst={true}
              rules={[
                {
                  required: true,
                  whitespace: true,
                  max: 400,
                },
              ]}
              validateTrigger="onBlur"
            >
              <Input.TextArea
                rows={4}
                maxLength={400}
                showCount
                placeholder={t('datasetCreation.stepOne.form.descPlaceholder') || ''}
              ></Input.TextArea>
            </Form.Item>
          </>
        )}
        {/* 数据类型 */}
        {useXIYANRag && (
          <Form.Item label={t('datasetCreation.stepOne.form.dataType.title')} required>
            <div className="flex gap-4 w-full">
              <RadioCard
                isChosen={dataType === DatasetFileType.Unstructured}
                className="w-[calc(50%-8px)] !p-3"
                descClassName="!whitespace-normal mt-1"
                radioPosition="top-right"
                size="small"
                title={
                  <div className="flex flex-col gap-1">
                    <UnstructuredDataset className="w-6 h-6"></UnstructuredDataset>
                    <div className="text-gray-G1 text-S3 leading-H3">
                      {t('datasetCreation.stepOne.form.dataType.unstructured')}
                    </div>
                  </div>
                }
                description={t('datasetCreation.stepOne.form.dataType.unstructuredDesc')}
                onChosen={() => {
                  updateDataType(DatasetFileType.Unstructured)
                }}
              ></RadioCard>
              <RadioCard
                isChosen={dataType === DatasetFileType.Structured}
                className="w-[calc(50%-8px)] !p-3"
                size="small"
                descClassName="!whitespace-normal mt-1"
                radioPosition="top-right"
                title={
                  <div className="flex flex-col gap-1">
                    <StructuredDataset className="w-6 h-6"></StructuredDataset>
                    <div className="text-gray-G1 text-S3 leading-H3">
                      {t('datasetCreation.stepOne.form.dataType.structured')}
                    </div>
                  </div>
                }
                description={t('datasetCreation.stepOne.form.dataType.structuredDesc')}
                onChosen={() => {
                  updateDataType(DatasetFileType.Structured)
                }}
              ></RadioCard>
            </div>
          </Form.Item>
        )}
        <Form.Item label={t('datasetCreation.stepOne.form.uploader')} required>
          {/* 公有云状态下 */}
          {useXIYANRag ? (
            <>
              {/* 结构化数据 */}
              {dataType === DatasetFileType.Structured && (
                <FileUploaderInDrag
                  fileList={fileList}
                  max={1}
                  maxSize={15}
                  supportTypes={['xlsx', 'xls', 'csv']}
                  prepareFileList={updateFileList}
                  onFileListUpdate={updateFileList}
                  onFileUpdate={updateFile}
                  onPreview={updateCurrentFile}
                  searchParams={`?category=${DatesetFileTypeCode.Structured}`}
                  errorMsg={{
                    type: t('datasetCreation.stepOne.form.dataType.fileTypeError', {
                      supportType: 'CSV、XLSX、XLS',
                    })!,
                  }}
                />
              )}
              {/* 非结构化数据 */}
              {dataType === DatasetFileType.Unstructured && (
                <FileUploaderInDrag
                  fileList={fileList}
                  max={50}
                  maxSize={15}
                  supportTypes={[
                    'md',
                    'pdf',
                    'txt',
                    'docx',
                    'markdown',
                  ]}
                  prepareFileList={updateFileList}
                  onFileListUpdate={updateFileList}
                  onFileUpdate={updateFile}
                  searchParams={`?category=${DatesetFileTypeCode.Unstructured}`}
                  errorMsg={{
                    type: t('datasetCreation.stepOne.form.dataType.fileTypeError', {
                      supportType: 'TXT、MARKDOWN、PDF、DOCX',
                    })!,
                  }}
                ></FileUploaderInDrag>
              )}
            </>
          ) : (
            <FileUploaderInDrag
              prepareFileList={updateFileList}
              onFileListUpdate={updateFileList}
              onFileUpdate={updateFile}
              fileList={fileList}
            ></FileUploaderInDrag>
          )}
        </Form.Item>
      </Form>
    )
  },
)

StepOne.displayName = 'StepOne'

export default StepOne
