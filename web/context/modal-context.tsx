'use client'

import type { Di<PERSON>atch, SetStateAction } from 'react'
import { useCallback, useState } from 'react'
import { createContext, useContext, useContextSelector } from 'use-context-selector'

import type {
  ConfigurationMethodEnum,
  CustomConfigurationModelFixedFields,
  ModelProvider,
} from '@/app/components/account-setting/model-provider-page/declarations'
import type { ModerationConfig } from '@/models/debug'
import type {
  ApiBasedExtension,
  ExternalDataTool,
} from '@/models/common'
import type { CreateAppDialogProps } from '@/app/components/app/modal/add-or-edit'

// 全局弹窗
import ApiBasedExtensionModal from '@/app/components/account-setting/api-based-extension-page/modal'
import AccountSettingModal from '@/app/components/account-setting/account-setting-modal'
import ExternalDataToolModal from '@/app/components/app/configuration/tools/external-data-tool-modal'
import CreateAppModal from '@/app/components/app/modal/add-or-edit'
import ModelModal from '@/app/components/account-setting/model-provider-page/model-modal'
import ModerationSettingModal from '@/app/components/base/features/new-feature-panel/moderation/moderation-setting-modal'
import type { AddToolModalProps } from '@/app/components/tools/add-tool-modal'
import AddToolModal from '@/app/components/tools/add-tool-modal'
import type { ToolItem } from '@/types/tools'

export type ModalState<T> = {
  payload: T
  onCancelCallback?: () => void
  onSaveCallback?: (newPayload: T) => void
  onRemoveCallback?: (newPayload: T) => void
  onValidateBeforeSaveCallback?: (newPayload: T) => boolean
}

export type ModelModalType = {
  currentProvider: ModelProvider
  configType: ConfigurationMethodEnum
  currentCustomConfigurationModelFixedFields?: CustomConfigurationModelFixedFields
}
export type ModalContextState = {
  setShowAccountSettingModal: Dispatch<SetStateAction<ModalState<string> | null>>
  setShowApiBasedExtensionModal: Dispatch<SetStateAction<ModalState<ApiBasedExtension> | null>>
  setShowModerationSettingModal: Dispatch<SetStateAction<ModalState<ModerationConfig> | null>>
  setShowExternalDataToolModal: Dispatch<SetStateAction<ModalState<ExternalDataTool> | null>>
  setShowCreateAppModal: Dispatch<SetStateAction<CreateAppDialogProps | null>>
  setShowModelModal: Dispatch<SetStateAction<ModalState<ModelModalType> | null>>
  setShowAddToolModal: Dispatch<SetStateAction<AddToolModalProps | null>>
}
const ModalContext = createContext<ModalContextState>({
  setShowAccountSettingModal: () => { },
  setShowApiBasedExtensionModal: () => { },
  setShowModerationSettingModal: () => { },
  setShowExternalDataToolModal: () => { },
  setShowModelModal: () => { },
  setShowCreateAppModal: () => {},
  setShowAddToolModal: () => {},
})

export const useModalContext = () => useContext(ModalContext)

// Adding a dangling comma to avoid the generic parsing issue in tsx, see:
// https://github.com/microsoft/TypeScript/issues/15713
// eslint-disable-next-line @typescript-eslint/comma-dangle
export const useModalContextSelector = <T,>(selector: (state: ModalContextState) => T): T =>
  useContextSelector(ModalContext, selector)

type ModalContextProviderProps = {
  children: React.ReactNode
}
export const ModalContextProvider = ({
  children,
}: ModalContextProviderProps) => {
  // 显示账号设置modal配置信息
  const [showAccountSettingModal, setShowAccountSettingModal] = useState<ModalState<string> | null>(null)
  // 显示调整api扩展弹窗配置信息
  const [showApiBasedExtensionModal, setShowApiBasedExtensionModal] = useState<ModalState<ApiBasedExtension> | null>(null)
  const [showModerationSettingModal, setShowModerationSettingModal] = useState<ModalState<ModerationConfig> | null>(null)
  const [showExternalDataToolModal, setShowExternalDataToolModal] = useState<ModalState<ExternalDataTool> | null>(null)
  // 显示添加工具弹窗
  const [showAddToolModal, setShowAddToolModal] = useState<AddToolModalProps | null>(null)
  // 显示模型弹窗配置信息
  const [showModelModal, setShowModelModal] = useState<ModalState<ModelModalType> | null>(null)
  // 显示标注弹窗配置信息
  // const [showAnnotationFullModal, setShowAnnotationFullModal] = useState(false)
  // 显示创建应用弹窗
  const [showCreateAppModal, setShowCreateAppModal] = useState<CreateAppDialogProps | null>(null)

  // 关闭账号设置弹窗
  const handleCancelAccountSettingModal = () => {
    setShowAccountSettingModal(null)
    if (showAccountSettingModal?.onCancelCallback)
      showAccountSettingModal?.onCancelCallback()
  }
  // 关闭模型配置弹窗
  const handleCancelModelModal = useCallback(() => {
    setShowModelModal(null)
    if (showModelModal?.onCancelCallback)
      showModelModal.onCancelCallback()
  }, [showModelModal])
  // 保存模型配置弹窗
  const handleSaveModelModal = useCallback(() => {
    if (showModelModal?.onSaveCallback)
      showModelModal.onSaveCallback(showModelModal.payload)
    setShowModelModal(null)
  }, [showModelModal])
  // 关闭创建应用弹窗
  const handleCancelCreateAppModal = () => {
    setShowCreateAppModal(null)
    if (showCreateAppModal?.onClose)
      showCreateAppModal.onClose()
  }
  // 保存创建应用弹窗
  const handleSaveCreateAppModal = () => {
    setShowCreateAppModal(null)
    if (showCreateAppModal?.onSuccess)
      showCreateAppModal.onSuccess()
  }
  // 保存api设置
  const handleSaveApiBasedExtension = (newApiBasedExtension: ApiBasedExtension) => {
    if (showApiBasedExtensionModal?.onSaveCallback)
      showApiBasedExtensionModal.onSaveCallback(newApiBasedExtension)
    setShowApiBasedExtensionModal(null)
  }
  const handleCancelModerationSettingModal = () => {
    setShowModerationSettingModal(null)
    if (showModerationSettingModal?.onCancelCallback)
      showModerationSettingModal.onCancelCallback()
  }
  const handleSaveModeration = (newModerationConfig: ModerationConfig) => {
    if (showModerationSettingModal?.onSaveCallback)
      showModerationSettingModal.onSaveCallback(newModerationConfig)
    setShowModerationSettingModal(null)
  }
  const handleCancelExternalDataToolModal = () => {
    setShowExternalDataToolModal(null)
    if (showExternalDataToolModal?.onCancelCallback)
      showExternalDataToolModal.onCancelCallback()
  }
  const handleSaveExternalDataTool = (newExternalDataTool: ExternalDataTool) => {
    if (showExternalDataToolModal?.onSaveCallback)
      showExternalDataToolModal.onSaveCallback(newExternalDataTool)
    setShowExternalDataToolModal(null)
  }
  const handleValidateBeforeSaveExternalDataTool = (newExternalDataTool: ExternalDataTool) => {
    if (showExternalDataToolModal?.onValidateBeforeSaveCallback)
      return showExternalDataToolModal?.onValidateBeforeSaveCallback(newExternalDataTool)
    return true
  }
  // 取消添加工具弹窗
  const handleCancelAddToolModal = () => {
    if (showAddToolModal?.onHide)
      showAddToolModal?.onHide()
    setShowAddToolModal(null)
  }
  // 变更添加工具弹窗
  const handleChangeAddToolModal = (value: Array<ToolItem>) => {
    if (showAddToolModal?.onChange)
      showAddToolModal.onChange(value)
  }
  /* 弹窗打开关闭函数———end */

  return (
    <ModalContext.Provider value={{
      setShowAccountSettingModal,
      setShowApiBasedExtensionModal,
      setShowModerationSettingModal,
      setShowExternalDataToolModal,
      setShowModelModal,
      setShowCreateAppModal,
      setShowAddToolModal,
    }}>
      <>
        {children}
        {
          !!showAccountSettingModal && (
            <AccountSettingModal
              onClose={handleCancelAccountSettingModal}
              activeTab={showAccountSettingModal.payload}
            />
          )
        }

        {
          !!showApiBasedExtensionModal && (
            <ApiBasedExtensionModal
              data={showApiBasedExtensionModal.payload}
              onCancel={() => setShowApiBasedExtensionModal(null)}
              onSave={handleSaveApiBasedExtension}
            />
          )
        }
        {
          !!showModerationSettingModal && (
            <ModerationSettingModal
              data={showModerationSettingModal.payload}
              onCancel={handleCancelModerationSettingModal}
              onSave={handleSaveModeration}
            />
          )
        }
        {
          !!showExternalDataToolModal && (
            <ExternalDataToolModal
              data={showExternalDataToolModal.payload}
              onCancel={handleCancelExternalDataToolModal}
              onSave={handleSaveExternalDataTool}
              onValidateBeforeSave={handleValidateBeforeSaveExternalDataTool}
            />
          )
        }
        {
          !!showModelModal && (
            <ModelModal
              provider={showModelModal.payload.currentProvider}
              configType={showModelModal.payload.configType}
              currentCustomConfigurationModelFixedFields={showModelModal.payload.currentCustomConfigurationModelFixedFields}
              onCancel={handleCancelModelModal}
              onSave={handleSaveModelModal}
            />
          )
        }
        {
          showCreateAppModal && (
            <CreateAppModal
              onClose={handleCancelCreateAppModal}
              onSuccess={handleSaveCreateAppModal}
              canChangeMode={showCreateAppModal.canChangeMode}
              defaultConfig={showCreateAppModal.defaultConfig}
            ></CreateAppModal>
          )
        }
        {
          showAddToolModal && (
            <AddToolModal
              onHide={handleCancelAddToolModal}
              onChange={handleChangeAddToolModal}
              tools={showAddToolModal.tools}
              max={showAddToolModal.max}
              hasParamsDetail={showAddToolModal.hasParamsDetail}
            ></AddToolModal>
          )
        }

      </>
    </ModalContext.Provider>
  )
}

export default ModalContext
