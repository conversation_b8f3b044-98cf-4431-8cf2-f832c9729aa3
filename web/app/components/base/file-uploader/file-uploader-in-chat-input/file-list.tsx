import { useMemo } from 'react'
import { useFile } from '../hooks'
import { useStore } from '../store'
import type { FileEntity } from '../types'
import FileImageItem from './file-image-item'
import FileItem from './file-item'
import type { FileUpload } from '@/app/components/base/features/types'
import { SupportUploadFileTypes } from '@/app/components/workflow/types'
import cn from '@/utils/classnames'
import VideoPlayer from '@/app/components/base/video-player'
import AudioPlayer from '@/app/components/base/audio-player'

type FileListProps = {
  className?: string
  files: FileEntity[]
  onRemove?: (fileId: string) => void
  onReUpload?: (fileId: string) => void
  showDeleteAction?: boolean
  showDownloadAction?: boolean
  canPreview?: boolean
  panelContentWidth?: number
  from?: string
}
export const FileList = ({
  className,
  files,
  onReUpload,
  onRemove,
  showDeleteAction = true,
  showDownloadAction = false,
  canPreview,
  panelContentWidth,
  from,
}: FileListProps) => {
  const isInChatInput = useMemo(() => {
    return showDeleteAction
  }, [showDeleteAction])

  return (
    <div className={cn('flex flex-wrap gap-2', className, from === 'FileListInChatInput' && 'pl-[12px]')}>
      {
        files.map((file) => {
          // 图片资源
          if (file.supportFileType === SupportUploadFileTypes.image) {
            return (
              <FileImageItem
                key={file.id}
                file={file}
                from={from}
                showDeleteAction={showDeleteAction}
                showDownloadAction={showDownloadAction}
                onRemove={onRemove}
                onReUpload={onReUpload}
                canPreview={canPreview}
              />
            )
          }
          // 视频、音频资源
          else if (!isInChatInput && file.supportFileType === SupportUploadFileTypes.video) {
            return (
              <VideoPlayer
                key={file.id}
                src={file.url || file.dataUrl || ''}
                width={panelContentWidth}
              />
            )
          }
          else if (!isInChatInput && file.supportFileType === SupportUploadFileTypes.audio) {
            return (
              <AudioPlayer
                key={file.id}
                src={file.url || file.dataUrl || ''}
              />
            )
          }
          return (
            <FileItem
              key={file.id}
              file={file}
              from={from}
              showDeleteAction={showDeleteAction}
              showDownloadAction={showDownloadAction}
              onRemove={onRemove}
              onReUpload={onReUpload}
            />
          )
        })
      }
    </div>
  )
}

type FileListInChatInputProps = {
  fileConfig: FileUpload
}
export const FileListInChatInput = ({
  fileConfig,
}: FileListInChatInputProps) => {
  const files = useStore(s => s.files)
  const {
    handleRemoveFile,
    handleReUploadFile,
  } = useFile(fileConfig)

  return (
    <FileList
      files={files}
      onReUpload={handleReUploadFile}
      onRemove={handleRemoveFile}
      className='pr-[118px]'
      from="FileListInChatInput"
    />
  )
}
