import { warn } from "console"
import { DOMComment } from "slate-dom/dist/utils/dom"

const translation = {
  IntelligentAgent: {
    CreationType: '请选择文章的创作类型：',
    CreationTypeTemplate: '基于范文模板创作',
    CreationTypeTopic: '基于文章主题创作',
    CreationTypeOptions: [
      {
        icon: 'template',
        text: '基于范文模板创作'
      },
      {
        icon: 'topic',
        text: '基于文章主题创作',
      }
    ],
    TopicType: '请选择文章的主题类型：',
    TopicTypeOptions: [
      {
        icon: 'finance',
        text: '金融'
      },
      {
        icon: 'education',
        text: '教育'
      },
      {
        icon: 'treatment',
        text: '医疗'
      },
      {
        icon: 'other',
        text: '其他'
      }
    ],
    ReportType: '请您提供一下信息以便撰写总结汇报：',
    ReportRegional: '投资分析报告',
    ReportSupply: '产业链分析',
    ReportIndustry: '行业报告',
    ReportTypeOptions: [
      { icon: 'regionalEconomy', text: '投资分析报告' },
      { icon: 'supplyChain', text: '财务报表解读' },
      { icon: 'industryReport', text: '企业尽调报告' },
      { icon: 'reportOther', text: '金融行业其他' },
    ],
    educationOptions: [
      { icon: 'regionalEconomy', text: '教学大纲设计' },
      { icon: 'supplyChain', text: '教育研究报告' },
      { icon: 'industryReport', text: '学生评估与反馈' },
      { icon: 'reportOther', text: '教育行业其他' },
    ],
    treatmentOptions: [
      { icon: 'regionalEconomy', text: '医疗研究论文' },
      { icon: 'supplyChain', text: '患者教育资料' },
      { icon: 'industryReport', text: '疾病流行趋势报告' },
      { icon: 'reportOther', text: '医疗行业其他' },
    ],
    otherOptions: [
      { icon: 'reportOther', text: '自定义' },
    ],
    ArrayFormList: {
      Invest: [
        {
          title: '创作主题',
          placeholder: '明确分析的具体对象或领域，如“电动车行业领军企业的投资潜力分析”',
          field: 'Theme',
          type: 'input',
          required: true,
        },
        {
          title: '字数要求',
          placeholder: '明确文章字数要求，比如10000字左右',
          field: 'Number',
          type: 'number',
          required: true,
        },
        {
          title: '报告受众',
          placeholder: '目标读者，如机构投资者、基金经理、散户投资者等',
          type: 'input',
          required: true,
        },
        {
          title: '内容结构',
          placeholder: '包括摘要、背景分析、数据分析、估值模型、结论与建议、风险提示等部分',
          type: 'input',
          required: true,
        },
        {
          title: '参考依据',
          placeholder: '使用公开财报、行业研究报告等，确保数据来源可靠',
          type: 'input',
          required: true,
        },
        {
          title: '风险评估',
          placeholder: '是否要求专门的风险章节，包括市场、政策、财务等多维风险',
          type: 'input',
          required: true,
        },
        {
          title: '行业对比',
          placeholder: '是否需要与同行业标杆公司进行比较分析',
          type: 'input',
          required: true,
        },
        {
          title: '数据范围和时间',
          placeholder: '具体的数据时间范围，如使用2018年至2023年的历史财务数据，并预测2024至2026年',
          type: 'input',
          required: true,
        },
        {
          title: '语言与风格',
          placeholder: '是否需要通俗易懂的语言风格，或以严谨专业的术语表述',
          type: 'input',
          required: false,
        }],
      Finance: [
        {
          title: '创作主题',
          placeholder: '明确分析的报表类型和目标，如“2022年X公司的利润表和现金流量表解读',
          field: 'Theme',
          type: 'input',
          required: true,
        },
        {
          title: '字数要求',
          placeholder: '明确文章字数要求，比如10000字左右',
          field: 'Number',
          type: 'number',
          required: true,
        },
        {
          title: '报告受众',
          placeholder: '目标读者，如企业管理层、投资分析师、银行贷款部门等',
          type: 'input',
          required: true,
        },
        {
          title: '内容结构',
          placeholder: '包括引言、关键财务指标解读（如盈利能力、偿债能力）、优劣势分析、建议等部分',
          type: 'input',
          required: true,
        },
        {
          title: '参考依据',
          placeholder: '基于官方财务报表、行业基准数据等',
          type: 'input',
          required: true,
        },
        {
          title: '改进建议',
          placeholder: '是否要求附上企业财务管理的改进措施或战略建议',
          type: 'input',
          required: true,
        },
        {
          title: '同行对比',
          placeholder: '是否需要加入与同行标杆企业的对比分析',
          type: 'input',
          required: true,
        },
        {
          title: '分析维度',
          placeholder: '明确重点分析的维度，如流动性、负债情况、收入结构等',
          type: 'input',
          required: true,
        },
        {
          title: '语言与风格',
          placeholder: '是否以专业术语撰写，或为特定读者群体简化表述',
          type: 'input',
          required: false,
        }],
      Adjustment: [
        {
          title: '创作主题',
          placeholder: '明确尽调对象和领域，如“X公司并购交易的尽职调查报告',
          field: 'Theme',
          type: 'input',
          required: true,
        },
        {
          title: '字数要求',
          placeholder: '明确文章字数要求，比如10000字左右',
          field: 'Number',
          type: 'number',
          required: true,
        },
        {
          title: '报告受众',
          placeholder: '报告目标群体，如私募股权投资者、并购方管理层、审计团队等',
          type: 'input',
          required: true,
        },
        {
          title: '内容结构',
          placeholder: '包括引言、财务尽调、法务尽调、市场尽调、人力资源尽调等模块',
          type: 'input',
          required: true,
        },
        {
          title: '参考依据',
          placeholder: '基于企业提供的内部数据、公开信息及访谈结果',
          type: 'input',
          required: true,
        },
        {
          title: '风险提示',
          placeholder: '是否需专门列出潜在风险点（如法律纠纷、资产估值偏差等）',
          type: 'input',
          required: true,
        },
        {
          title: '预测与评估',
          placeholder: '是否需要对未来的企业价值和运营情况提供预测和评估',
          type: 'input',
          required: true,
        },
        {
          title: '案例引用',
          placeholder: '是否加入对标企业的尽调案例或行业经验',
          type: 'input',
          required: true,
        },
        {
          title: '尽调范围',
          placeholder: '明确覆盖的领域，如仅限财务尽调，还是全方位综合尽调',
          type: 'input',
          required: true,
        },
        {
          title: '语言与风格',
          placeholder: '是否偏学术风格，或以决策支持为核心的实用性风格',
          type: 'input',
          required: false,
        }],
      FinanceOther: [
        {
          title: '创作主题',
          placeholder: '明确文章创作主题',
          field: 'Theme',
          type: 'input',
          required: true,
        },
        {
          title: '字数要求',
          placeholder: '明确文章字数要求，比如10000字左右',
          field: 'Number',
          type: 'number',
          required: true,
        },
        {
          title: '报告受众',
          placeholder: '明确目标受众',
          type: 'input',
          required: true,
        },
        {
          title: '撰写要求',
          placeholder: '明确文章内容结构和语言风格等撰写要求',
          type: 'input',
          required: true,
        }],
      SyllabusDesign: [
        {
          title: '创作主题',
          placeholder: '明确教学内容或课程的主题（如“高中数学微积分基础”）',
          field: 'Theme',
          type: 'input',
          required: true,
        },
        {
          title: '字数要求',
          placeholder: '明确文章字数要求，比如10000字左右',
          field: 'Number',
          type: 'number',
          required: true,
        },
        {
          title: '目标受众',
          placeholder: '目标学生群体的特征（如年龄、学段、已有知识水平）',
          type: 'input',
          required: true,
        },
        {
          title: '课程目标',
          placeholder: '明确课程期望达到的教学成果或能力发展方向。示例：“掌握微积分的基本概念，能独立解决简单问题',
          type: 'input',
          required: true,
        },
        {
          title: '结构要求',
          placeholder: '课程安排的基本框架（如课时分配、单元设计、评估方式）。示例：“包括10个单元，每单元含2个理论课时和1个实践课时',
          type: 'input',
          required: true,
        },
        {
          title: '教学资源',
          placeholder: '是否需要推荐教材、工具或辅助材料。示例：“建议使用教材《微积分基础》',
          type: 'input',
          required: false,
        }],
      EducationalResearch: [
        {
          title: '研究主题',
          placeholder: '教育问题的具体研究方向或探讨问题。示例：“探讨翻转课堂模式对中学生数学成绩的影响”',
          field: 'Theme',
          type: 'input',
          required: true,
        },
        {
          title: '字数要求',
          placeholder: '明确文章字数要求，比如10000字左右：通常为10000-30000字，根据期刊或会议要求调整',
          field: 'Number',
          type: 'number',
          required: true,
        },
        {
          title: '目标受众',
          placeholder: '教育研究者、教育政策制定者或学校管理者。示例：“面向教育研究学者”',
          type: 'input',
          required: true,
        },
        {
          title: '研究方法',
          placeholder: '使用的研究方式（如问卷调查、案例分析）。示例：“基于问卷调查和课堂观察数据的混合研究”',
          type: 'input',
          required: true,
        },
        {
          title: '撰写要求',
          placeholder: '包括摘要、研究背景、方法、结果与讨论等结构。示例：“需符合APA格式，包含数据分析图表”',
          type: 'input',
          required: true,
        },
        {
          title: '数据来源与时间',
          placeholder: '明确研究的样本数据和时间范围。示例：“2022年秋季学期某中学的在线课程数据”',
          type: 'input',
          required: false,
        }],
      StudentAssessment: [
        {
          title: '评估主题',
          placeholder: '针对某一学科或能力的评估（如数学能力、团队协作能力）。示例：“三年级学生的数学逻辑能力评估”',
          field: 'Theme',
          type: 'input',
          required: true,
        },
        {
          title: '字数要求',
          placeholder: '明确文章字数要求，比如10000字左右：通常为500-1500字，重点突出关键数据与建议。',
          field: 'Number',
          type: 'number',
          required: true,
        },
        {
          title: '评估对象',
          placeholder: '明确评估的群体特征（如年级、班级）。示例：“某小学五年级一班全体学生”',
          type: 'input',
          required: true,
        },
        {
          title: '评估标准',
          placeholder: '列出具体的评价指标（如成绩分布、行为表现）。示例：“基于考试成绩和课堂参与度两项指标”',
          type: 'input',
          required: true,
        },
        {
          title: '报告结构',
          placeholder: '明确需包含的内容（如成绩总结、改进建议）。示例：“需包括成绩分布表和个体表现反馈”',
          type: 'input',
          required: true,
        },
        {
          title: '数据呈现',
          placeholder: '是否需附带图表或分层分析。示例：“附带柱状图显示分数分布”',
          type: 'input',
          required: false,
        }],
      EducationalOther: [
        {
          title: '主题与目标',
          placeholder: '明确报告的核心议题及希望解决的教育问题。示例：“高中生职业生涯规划指导方案”',
          field: 'Theme',
          type: 'input',
          required: true,
        },
        {
          title: '字数要求',
          placeholder: '明确文章字数要求，比如10000字左右：通常为1500-5000字，视深度需求调整。',
          field: 'Number',
          type: 'number',
          required: true,
        },
        {
          title: '目标受众',
          placeholder: '指定阅读者，如教育局官员、学校管理者、家长。示例：“面向学校校长和教学教研组长”',
          type: 'input',
          required: true,
        },
        {
          title: '内容要求',
          placeholder: '需明确报告的基本框架及关键内容。示例“包含现状分析、具体措施和可实施建议”',
          type: 'input',
          required: true,
        },
        {
          title: '数据需求',
          placeholder: '是否需要提供数据支持或案例分析。示例：“基于某地区学生抽样调查数据”',
          type: 'input',
          required: false,
        }],
      MedicalResearchPaper: [
        {
          title: '创作主题',
          placeholder: '明确研究的具体领域和问题，如“糖尿病并发症的最新治疗方法研究”。',
          field: 'Theme',
          type: 'input',
          required: true,
        },
        {
          title: '字数要求',
          placeholder: '明确文章字数要求，比如10000字左右：通常在3000-5000字之间，具体视期刊要求而定。',
          field: 'Number',
          type: 'number',
          required: true,
        },
        {
          title: '报告受众',
          placeholder: '主要针对医学研究人员、临床医生或学术期刊读者。',
          type: 'input',
          required: true,
        },
        {
          title: '撰写结构',
          placeholder: '包括摘要、引言、方法、结果、讨论、结论等部分。',
          type: 'input',
          required: true,
        },
        {
          title: '撰写格式',
          placeholder: '遵循特定期刊的格式要求，如APA、MLA或医学期刊常用的格式。',
          type: 'input',
          required: true,
        },
        {
          title: '参考文献',
          placeholder: '引用最新和相关的学术文献，确保数据和结论的科学性。',
          type: 'input',
          required: true,
        },
        {
          title: '语言与风格',
          placeholder: '学术风格、专业术语是否严格，需不需要简化语言。',
          type: 'input',
          required: false,
        },
        {
          title: '图表需求',
          placeholder: '是否需要数据可视化（如表格、图形），以及格式要求。',
          type: 'input',
          required: false,
        },
        {
          title: '研究数据',
          placeholder: '是否附带已知实验数据或需要假设数据。',
          type: 'input',
          required: false,
        }],
      PatientEducationMaterials: [
        {
          title: '创作主题',
          placeholder: '针对特定疾病或健康主题，如“高血压的管理与预防”。',
          field: 'Theme',
          type: 'input',
          required: true,
        },
        {
          title: '字数要求',
          placeholder: '明确文章字数要求，比如10000字左右：一般控制在1000-2000字，适合患者阅读。',
          field: 'Number',
          type: 'number',
          required: true,
        },
        {
          title: '报告受众',
          placeholder: '患者及其家属，需使用通俗易懂的语言。',
          type: 'input',
          required: true,
        },
        {
          title: '语言风格',
          placeholder: '简洁、易懂，避免专业术语或对其进行解释。',
          type: 'input',
          required: true,
        },
        {
          title: '内容结构',
          placeholder: '包括疾病简介、症状、预防措施、治疗方法、日常护理建议等。',
          type: 'input',
          required: true,
        },
        {
          title: '图示',
          placeholder: '使用图片、图表辅助说明，增强理解。',
          type: 'input',
          required: true,
        },
        {
          title: '互动元素',
          placeholder: '如常见问题解答（FAQ）、小贴士等。',
          type: 'input',
          required: false,
        },
        {
          title: '资源链接',
          placeholder: '提供进一步阅读或支持的资源，如官方网站、支持团体联系方式。',
          type: 'input',
          required: false,
        },
        {
          title: '多语言版本',
          placeholder: '针对不同语言背景的患者，提供翻译版本。',
          type: 'input',
          required: false,
        }],
      DiseaseEpidemicTrendReport: [
        {
          title: '创作主题',
          placeholder: '具体疾病的流行趋势分析，如“2024年中国流感疫情趋势预测”。',
          field: 'Theme',
          type: 'input',
          required: true,
        },
        {
          title: '字数要求',
          placeholder: '明确文章字数要求，比如10000字左右：通常在20000-40000字之间，视报告的详细程度而定。',
          field: 'Number',
          type: 'number',
          required: true,
        },
        {
          title: '报告受众',
          placeholder: '公共卫生官员、政策制定者、医疗机构管理者等。',
          type: 'input',
          required: true,
        },
        {
          title: '数据来源',
          placeholder: '引用权威的数据来源，如国家卫生部门、世界卫生组织等。',
          type: 'input',
          required: true,
        },
        {
          title: '分析方法',
          placeholder: '描述所使用的统计或预测模型。',
          type: 'input',
          required: true,
        },
        {
          title: '趋势解读',
          placeholder: '对数据进行深入分析，解释可能的原因和影响。',
          type: 'input',
          required: true,
        },
        {
          title: '建议与对策',
          placeholder: '基于分析结果提出预防和控制的建议。',
          type: 'input',
          required: true,
        },
        {
          title: '图表展示',
          placeholder: '如折线图、饼图、热力图等，直观呈现数据趋势。',
          type: 'input',
          required: false,
        },
        {
          title: '历史数据对比',
          placeholder: '与过去几年或相似疾病的流行趋势进行对比分析。',
          type: 'input',
          required: false,
        },
        {
          title: '区域细分分析',
          placeholder: '按地区、年龄、性别等维度细分流行趋势。',
          type: 'input',
          required: false,
        }],
      TreatmentOther: [
        {
          title: '创作主题',
          placeholder: '根据具体需求定义，如“医院内部流程优化报告”。',
          field: 'Theme',
          type: 'input',
          required: true,
        },
        {
          title: '字数要求',
          placeholder: '明确文章字数要求，比如10000字左右：视报告的详细程度而定。',
          field: 'Number',
          type: 'number',
          required: true,
        },
        {
          title: '报告受众',
          placeholder: '明确目标读者群体，如医院管理层、特定科室人员等。',
          type: 'input',
          required: true,
        },
        {
          title: '内容结构',
          placeholder: '根据报告目的自定义，如背景介绍、现状分析、问题识别、解决方案等。',
          type: 'input',
          required: true,
        },
        {
          title: '语言风格',
          placeholder: '根据受众调整，如正式、技术性或简明扼要。',
          type: 'input',
          required: true,
        }],
      Other: [
        {
          title: '创作主题',
          placeholder: '明确文章创作主题',
          field: 'Theme',
          type: 'input',
          required: true,
        },
        {
          title: '字数限制',
          placeholder: '明确文章字数要求，比如3000字左右',
          field: 'Number',
          type: 'number',
          required: true,
        },
        {
          title: '报告受众',
          placeholder: '明确目标受众',
          type: 'input',
          required: true,
        },
        {
          title: '撰写要求',
          placeholder: '：明确文章内容结构和语言风格等撰写要求',
          type: 'input',
          required: true,
        },
      ]
    },
  },
  FormDataEvent: {
    SampleTemplate: '请选择范文模板：',
    FileRequired: '请先选择模板或上传文件模板！',
    SampleTemplateOptions: [
      { icon: 'study', text: '研究报告' },
      { icon: 'regionalEconomy', text: '事务公文' },
      { icon: 'reportPdf', text: '工作报告' },
      { icon: 'plan', text: '工作计划' },
      
    ],
    TemplateType: '请选择模板类型：',
    TemplateTypeOptions: [
      { icon: 'sound', text: '通知' },
      { icon: 'fileAdd', text: '邀请函' },
      { icon: 'fileText', text: '会议纪要' },
      { icon: 'filePdf', text: '讲话稿' },
    ],
    WorkReportOptions: [
      { icon: 'regionalEconomy', text: '部门工作报告' },
      { icon: 'industryReport', text: '个人工作报告' },
      { icon: 'supplyChain', text: '项目汇报' },
      { icon: 'MessageOutlined', text: '心得体会' },
    ],
    WorkPlanOptions: [
      { icon: 'regionalEconomy', text: '年度计划' },
      { icon: 'reportPdf', text: '专项规划' },
      { icon: 'industryReport', text: '行动方案' },
      { icon: 'regionalEconomy', text: '工作预案' },
    ],
    ResearchReportOptions: [
      { icon: 'regionalEconomy', text: '地区调研报告' },
      { icon: 'reportPdf', text: '企业数字化转型诊断报告' },
      { icon: 'plan', text: '行业分析报告' },
      { icon: 'study', text: '竞争对手分析报告' },
    ], 
    ArrayFormList: [
      {
        title: '创作主题',
        placeholder: '明确文章创作主题',
        field: 'Theme',
        type: 'input',
        required: true,
      },
      {
        title: '字数要求',
        placeholder: '明确文章字数要求，比如3000字左右',
        field: 'Number',
        type: 'number',
        required: true,
      },
    ]
  },
  warning: {
    queryRequired: '请先选择您要创作的主题及文字要求',
    topicRequired: '主题字段不能为空',
    NumRequired: '字数字段不能为空',
    WritingRequirementsRequired: '撰写要求字段不能为空',
    FormRequired: '表单内容为必填项！'
  },
  Markdown: {
    Title: '标题：',
    MarkdownTitle: '请输入您的标题',
    MarkdownFirstTitle: '请输入您的一级标题',
    MarkdownSecondTitle: '请输入您的二级标题',
    MarkdownThirdTitle: '请输入您的三级标题',
    MarkdownContent: '请输入该段内容的标题',
    AddChapter: '添加章节'
  },
  Generate: {
    GenerateOutline: '生成大纲',
    GenerateLongText: '生成长文',
    LongTextEdit: '长文改写',
    ImageChart: '图像图表',
    GenerateDocument: '基于大纲生成文档'
  },
  interface: {
    chart: '图表',
    picture: '图片',
    rewrite: '改写',
    KnowledgeBaseRetrieval: '知识库检索',
  },
  BasicModal: {
    ContentTitle: '参考资料',
    InputPlaceholder: '搜索关键词联网搜索更多参考',
    DeleteConfirm: '您确定要删除吗?',
    DeleteConfirmDescription: '删除后将无法恢复',
    LoadingDescription: '正在生成请稍后...',
    GenerateDocument: '基于大纲生成文档',
    GenerateDocumentDescription: '好的，小翼正在为您生成初稿，请稍作等待',
    GenerateDocumentDescription2: '已为您生成初稿，小翼打造的精心之作，请您点击查看',
    notitle: '暂无标题',
    basicNum:"一、,二、,三',四、,五、,六、,七、,八、,九、,十、,十一、,十二、,十三、,十四、,十五、,十六、,十七、,十八、,十九、,二十、,二十一、,二十二、,二十三、二十四、,二十五、,二十六、,二十七、,二十八、,二十九、,三十、,三十一、,三十二、,三十三、,三十四、,三十五、,三十六、,三十七、,三十八、,三十九、,四十、,四十一、,四十二、,四十三、,四十四、,四十五、,四十六、,四十七、,四十八、,四十九、,五十、",
    deltitle: '确认删除',
    delcontent: '确定要删除这条记录吗？',
    generatingOutline: '正在生成大纲...',
    generatingLongtxt: '正在生成长文...',
    modificationFailed: '修改失败',
    noEempty: '标题不能为空！',
    aiCreation:"AI正在聚精会神创作中",
    regenerate:"重新生成",
    regeneratedoc:"重新生成文档:",
    regenerateoutline:"重新生成大纲:",
    regenerateOther:"对结果不满意,请点击",
    tip1:"参考资料作为可靠的信息来源，能够帮助作者核",
    tip2:"实各种细节，如事件发生的时间、地点、人物姓",
    tip3:"名、具体数据等。",
  },
  longText: {
    modelType: '长文档类型表单配置',
    customCatalog: '自定义目录',
    firstCatalog: '一级目录配置',
    secondCatalog: '二级目录配置',
    PromptCatalog: 'Prompt结构配置',
    UploadedTemplate: '已上传范文模板',
    SearchPlaceholder: '请输入新目录名称',
    PromptPlaceholder: 'Prompt字段名称',
    PromptPlaceholder2: 'Prompt字段的文字解释',
    PromptPlaceholder3: '必填字段',
    PromptPlaceholder4: '添加字段',
    PromptPlaceholder5: '请先输入Prompt字段名称和文字解释',
    PromptPlaceholder6: '请先选择目录后进行上传',
    PromptPlaceholder7: '上传成功',
    PromptPlaceholder8: '暂未检索到相关参考资料',
    PromptPlaceholder9: '删除成功',
    PromptPlaceholder10: '添加成功',
    PromptPlaceholder11: '请先选择目录配置',
    PromptPlaceholder12: '目录中存在相同名称，请重新输入。',
    Placeholder1: '请输入图表名称',
    Placeholder2: '请选择图表类型',
    FileCannotBeEmpty: '文件不能为空',
    DOMCommentPlaceholderWarning: '该资料已存在于参考数据中',
    DOMCommentPlaceholderSuccess: '添加成功',
    DocumentExported:"文档导出",
    inserted:"插入",
    remark1:"标签1",
    remark2:"标签2",
    insertedpic:"插入图片",
    changepic:"修改图片",
    placeholder:"点击输入内容...",
    rewrite:"改写润色",
    rewritewhole:"请帮我改写润色一下这段话",
    expandConten:"扩写内容",
    expandContenwhole:"请帮我扩写一下这段话",
    streamlining:"精简内容",
    streamliningwhole:"请帮我精简一下这段话",
    numberName:"字符数",
    back:"返回",
    pdfout:"PDF导出",
    wordout:"Word导出",
    isEempty: '不能为空！',
    remark:"标签",
    getvalue:"取值",
    action:"操作",
    delline:"确定要删除当前行吗？",
    getfail:"获取聊天列表失败:",
    creative:"创作助手",
    editorial:"编辑优化",
    source:"参考来源",
    chatType:"图表类型",
    type1:"柱状图",
    type2:"饼图",
    type3:"折线图",
    chartParameters:"图表参数",
    addline:"添加行",
    generated:"生成",
    emptied:"清空",
    loadingmsg:"载中，请勿重复点击",
    loadingerror:"下载文件时出错:",

    article1:"帮我写一篇关于",
    article2:"[主题]",
    article3:"的",
    article4:"字数在",
    article5:"[输入数字]",
    article6:"字左右",
    editemsg:"是一个面向 AI 的开源富文本编辑器。",
    look:"查看",
    deltxt:"删除",
    uploadmsg:"上传自定义模板",
    longtitle:"长文档创作智能体",

    

    savetime:"保存时间",
    reduction:"还原",
    the:"第",
    version:"版本",
    history:"历史记录",
    current:"当前版本",
    chineseNumbers:"零,一,二,三,四,五,六,七,八,九",
    units:"十,百,千,万,十,百,千,亿",

    editmsg1:"请润色以下内容",
    editmsg2:"请扩写以下内容",
    editmsg3:"请精简以下内容",
    editmsg4:"请根据以下内容生成表格",
    editlong11:"请帮我检查一下这段内容，是否有拼写错误或者语法上的错误。",
    editlong12:"注意：你应该先判断一下这句话是中文还是英文，如果是中文，请给我返回中文的内容，如果是英文，请给我返回英文内容，只需要返回内容即可，不需要告知我是中文还是英文。你需要检查的内容是：",
    editlong21:"请帮我翻译以下内容，在翻译之前，想先判断一下这个内容是不是中文，如果是中文，则翻译问英文，如果是其他语言，则需要翻译为中文，注意，你只需要返回翻译的结果，不需要对此进行任何解释，不需要除了翻译结果以外的其他任何内容。你需要翻译的内容是：",
    editlong31:"请帮我总结以下内容，并直接返回总结的结果",
    editlong32:"注意：你应该先判断一下这句话是中文还是英文，如果是中文，请给我返回中文的内容，如果是英文，请给我返回英文内容，只需要返回内容即可，不需要告知我是中文还是英文。你需要总结的内容是：",
    editRemark:"插入表格",
    longtxt: '正在生成长文',

    fullfullrewrite:"全文润色",
    fullfullrewritelong:"请帮我对文档进行润色:",
    fullexpandConten:"全文扩写",
    fullexpandContenlong:"请帮我对文档进行扩写:",
    fullstreamlining:"全文缩写",
    fullstreamlininglong:"请帮我对文档进行缩写:",

    workflow:"工作流编排",
    longtitle1:"专业长文创作智能体",
    longtitle2:"高效精准的长文写作专家",

    step1:"选择文章创作类型",
    step2:"选择内容分类",
    step3:"细化创作内容",

    warn1:"请先选择文章类型",
    warn2:"请选择范文模板",
    warn3:"请选择文章类型",
    warn4:"请输入主题!",

    titleRecommend:"标题建议",
    uploadfile:"上传文件",
    localmaterials:"本地参考资料",

    flow1:"获取文章标题",
    flow2:"标题输出",
    flow3:"分配各章节字数",
    flow32:"生成章节及分配章节字数",
    flow4:"整合字数要求",
    flow42:"整合长文字数",
    flow5:"存储文章内容",
    flow52:"长文生成",

  }
}

export default translation
