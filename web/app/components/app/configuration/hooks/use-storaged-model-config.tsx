import { useCallback } from 'react'

import { useProviderContext } from '@/context/provider-context'
import { useAppContext } from '@/context/app-context'

type Props = {
  appId: string
}
const useStoragedModelConfig = ({
  appId,
}: Props) => {
  const { addModelConfig, getModelConfig, deleteModelConfig } = useProviderContext()
  const { userProfile } = useAppContext()
  const updateStoragedModelConfig = useCallback((provider: string, modelId: string, data: any) => {
    addModelConfig({
      userId: userProfile.id,
      appId,
      provider,
      modelId,
      ...data,
    })
  }, [addModelConfig])
  // 根据key保存或更新数据库中的模型某个配置
  const updateStoragedModelConfigByKey = useCallback((provider: string, modelId: string, key: string, value: any) => {
    addModelConfig({
      userId: userProfile.id,
      appId,
      provider,
      modelId,
      [key]: value,
    })
  }, [addModelConfig])

  // 获取db数据库中模型参数配置
  const getModelConfigStoraged = useCallback(async (provider: string, modelId: string) => {
    return getModelConfig({
      userId: userProfile.id,
      appId,
      provider,
      modelId,
    })
  }, [getModelConfig])

  const deleteModelConfigStoraged = useCallback(async (provider: string, modelId: string) => {
    return deleteModelConfig({
      userId: userProfile.id,
      appId,
      provider,
      modelId,
    })
  }, [deleteModelConfig])

  return {
    updateStoragedModelConfig,
    updateStoragedModelConfigByKey,
    getModelConfigStoraged,
    deleteModelConfigStoraged,
  }
}

export default useStoragedModelConfig
