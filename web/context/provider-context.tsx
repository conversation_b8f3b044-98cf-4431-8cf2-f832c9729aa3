'use client'

import { createContext, useContext, useContextSelector } from 'use-context-selector'
import useS<PERSON> from 'swr'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import {
  fetchModelList,
  fetchModelProviders,
  fetchSupportRetrievalMethods,
} from '@/service/common'
import {
  ModelStatusEnum,
  ModelTypeEnum,
} from '@/app/components/account-setting/model-provider-page/declarations'
import type { Model, ModelProvider } from '@/app/components/account-setting/model-provider-page/declarations'
import type { RETRIEVE_METHOD } from '@/types/datasets'
import { Plan } from '@/types/public/billing'
import type { CurrentPlanInfoBackend, UsagePlanInfo } from '@/types/public/billing'

import { fetchCurrentPlanInfo } from '@/service/billing'
import type { ModelConfigStorage } from '@/types/model'
import Toast from '@/app/components/base/toast'
import Loading from '@/app/components/base/loading'

// 工具、模型等添加工具最大值
export const NUM_INFINITE = ********
// 默认计划
const DEFAULT_PLAN = {
  type: Plan.sandbox,
  usage: {
    vectorSpace: 1,
    buildApps: 1,
    buildTools: 1,
    buildDatasets: 1,
    buildVideos: 1,
    teamMembers: 1,
    annotatedResponse: 1,
    documentsUploadQuota: 1,
  },
  total: {
    vectorSpace: 10,
    buildApps: 10,
    buildTools: 50,
    buildDatasets: 50,
    buildVideos: 50,
    teamMembers: 1,
    annotatedResponse: 10,
    documentsUploadQuota: 50,
  },
}

type ProviderContextState = {
  modelProviders: ModelProvider[]
  updateModelProviders: () => void
  textGenerationModelList: Model[]
  mutateTextGenerationModelList: () => void
  isTextGenerationModelListLoading: boolean
  supportRetrievalMethods: RETRIEVE_METHOD[]
  isAPIKeySet: boolean
  plan: {
    type: Plan
    usage: UsagePlanInfo
    total: UsagePlanInfo
  }
  isFetchedPlan: boolean
  enableBilling: boolean
  useXIYANRag: boolean
  onPlanInfoChanged: () => void
  enableReplaceWebAppLogo: boolean
  modelLoadBalancingEnabled: boolean
  datasetOperatorEnabled: boolean
  enableCreateLongtextApp: boolean
  addModelConfig: (value: ModelConfigStorage) => void
  deleteModelConfig: (model: ModelConfigStorage) => void
  clearModelConfig: () => void
  getModelConfig: (model: ModelConfigStorage) => Promise<ModelConfigStorage>
}
const ProviderContext = createContext<ProviderContextState>({
  // 模型提供商
  modelProviders: [],
  // 文本生成模型列表
  textGenerationModelList: [],
  mutateTextGenerationModelList: () => {},
  isTextGenerationModelListLoading: false,
  // 支持检索方法
  supportRetrievalMethods: [],
  // API密钥集？
  isAPIKeySet: true,
  plan: {
    type: Plan.sandbox,
    usage: {
      vectorSpace: 32,
      buildApps: 12,
      buildTools: 0,
      buildDatasets: 0,
      teamMembers: 0,
      buildVideos: 1,
      annotatedResponse: 1,
      documentsUploadQuota: 50,
    },
    total: {
      vectorSpace: 200,
      buildApps: 50,
      buildTools: 50,
      buildDatasets: 50,
      buildVideos: 50,
      teamMembers: 1,
      annotatedResponse: 10,
      documentsUploadQuota: 500,
    },
  },
  isFetchedPlan: false,
  enableBilling: false,
  onPlanInfoChanged: () => { },
  // 是否能够替换webApp的logo
  enableReplaceWebAppLogo: false,
  // 是否使用xiyan rag能力
  useXIYANRag: false,
  // 是否开启模型负载均衡
  modelLoadBalancingEnabled: false,
  // 知识库操作是否开启？
  datasetOperatorEnabled: false,
  // 是否可以创建长文档app
  enableCreateLongtextApp: false,
  // 添加模型配置
  addModelConfig: () => {},
  // 删除模型配置
  deleteModelConfig: () => {},
  // 查询模型配置
  getModelConfig: (model: ModelConfigStorage): any => {},
  // 清空模型配置
  clearModelConfig: () => {},
  // 更新模型列表
  updateModelProviders: () => {},
})

export const useProviderContext = () => useContext(ProviderContext)

// eslint-disable-next-line @typescript-eslint/comma-dangle
export const useProviderContextSelector = <T,>(selector: (state: ProviderContextState) => T): T =>
  useContextSelector(ProviderContext, selector)

type ProviderContextProviderProps = {
  children: React.ReactNode
}
export const ProviderContextProvider = ({
  children,
}: ProviderContextProviderProps) => {
  const { t } = useTranslation()
  // 模型提供商数据
  const { data: providersData, mutate: updateModelProviders } = useSWR('/workspaces/current/model-providers', fetchModelProviders)
  const fetchModelListUrlPrefix = '/workspaces/current/models/model-types/'
  // 文本生成模型列表
  const { data: textGenerationModelList, mutate: mutateTextGenerationModelList, isValidating: isTextGenerationModelListLoading } = useSWR(`${fetchModelListUrlPrefix}${ModelTypeEnum.textGeneration}`, fetchModelList)
  // 支持检索方法
  const { data: supportRetrievalMethods } = useSWR('/datasets/retrieval-setting', fetchSupportRetrievalMethods, { revalidateOnFocus: false })

  const [db, setDb] = useState<IDBDatabase>()
  // 默认的付费计划配置
  const [plan, setPlan] = useState(DEFAULT_PLAN)
  // 是否已经获取到付费计划
  const [isFetchedPlan, setIsFetchedPlan] = useState(false)
  // 是否开启付费
  const [enableBilling, setEnableBilling] = useState(true)
  // 是否可以替换webApp的logo
  const [enableReplaceWebAppLogo, setEnableReplaceWebAppLogo] = useState(false)
  // 是否可以创建长文档app
  const [enableCreateLongtextApp, setEnableCreateLongtextApp] = useState(false)
  // 模型负载均衡是否开启
  const [modelLoadBalancingEnabled, setModelLoadBalancingEnabled] = useState(false)
  // 是否可以配置知识库操作者权限
  const [datasetOperatorEnabled, setDatasetOperatorEnabled] = useState(false)
  // 是否拥有xiyan的rag能力
  const [enableXiyanRag, setEnableXIYANRag] = useState(false)

  // 解析付费计划限制
  const parseLimit = (limit: number) => {
    if (limit === 0)
      return NUM_INFINITE
    return limit
  }
  // 解析项目计划
  const parseCurrentPlan = (data: CurrentPlanInfoBackend) => {
    return {
      type: data.billing.subscription.plan,
      usage: {
        vectorSpace: data.vector_space.size,
        buildApps: data.apps?.size || 0,
        teamMembers: data.members.size,
        annotatedResponse: data.annotation_quota_limit.size,
        documentsUploadQuota: data.documents_upload_quota.size,
        buildDatasets: data.datasets?.size,
        buildTools: data.tools?.size,
        buildVideos: data.videos?.size,
      },
      total: {
        vectorSpace: parseLimit(data.vector_space.limit),
        buildApps: parseLimit(data.apps?.limit) || 0,
        buildTools: parseLimit(data.tools?.limit) || 0,
        buildDatasets: parseLimit(data.datasets?.limit) || 0,
        buildVideos: parseLimit(data.videos?.limit) || 0,
        teamMembers: parseLimit(data.members.limit),
        annotatedResponse: parseLimit(data.annotation_quota_limit.limit),
        documentsUploadQuota: parseLimit(data.documents_upload_quota.limit),
      },
    }
  }
  // 获取项目计划
  const fetchPlan = async () => {
    const data = await fetchCurrentPlanInfo()
    const enabled = data.billing.enabled
    setEnableBilling(enabled)
    setEnableReplaceWebAppLogo(data.can_replace_logo)
    setEnableXIYANRag(data.use_xiyan_server)
    setEnableCreateLongtextApp(data.can_create_longtext_app)
    setPlan(parseCurrentPlan(data))
    setIsFetchedPlan(true)
    if (data.model_load_balancing_enabled)
      setModelLoadBalancingEnabled(true)
    if (data.dataset_operator_enabled)
      setDatasetOperatorEnabled(true)
  }

  // 添加模型配置
  const addModelConfig = (modelConfig: ModelConfigStorage) => {
    const transaction = db?.transaction(['modelConfigs'], 'readwrite')
    if (transaction) {
      const objectStore = transaction.objectStore('modelConfigs')
      // 创建自定义key
      modelConfig.id = `${modelConfig.userId}_${modelConfig.appId}_${modelConfig.provider}_${modelConfig.modelId}`
      const getRequest = objectStore.get(modelConfig.id)
      getRequest.onsuccess = (event: Event) => {
        // @ts-expect-error 类型存在该属性
        const result = event.target.result
        // 数据存在
        if (result) {
          const updateRequest = objectStore.put({
            ...result,
            ...modelConfig,
          })
          updateRequest.onerror = () => {
            Toast.notify({
              type: 'error',
              message: t('common.db.updateModelConfigFail') as string,
            })
          }
        }
        else {
          const addRequest = objectStore.add(modelConfig)
          addRequest.onerror = () => {
            Toast.notify({
              type: 'error',
              message: t('common.db.addModelConfigFail') as string,
            })
          }
        }
      }
      getRequest.onerror = () => {
        Toast.notify({
          type: 'error',
          message: t('common.db.connectFail'),
        })
      }
    }
  }
  // 删除模型配置
  const deleteModelConfig = (modelConfig: ModelConfigStorage) => {
    const { userId, appId, provider, modelId } = modelConfig
    const transaction = db?.transaction(['modelConfigs'], 'readwrite')
    if (transaction) {
      const objectStore = transaction.objectStore('modelConfigs')
      // 确定索引和查询范围
      let indexName = 'userId'
      const query = [userId]
      if (appId) {
        indexName += '_appId'
        query.push(appId)
      }
      if (provider) {
        indexName += '_provider'
        query.push(provider)
      }
      if (modelId) {
        indexName += '_modelId'
        query.push(modelId)
      }
      // 索引对象
      const searchRequest = objectStore.openCursor(IDBKeyRange.only(query.join('_')))
      searchRequest.onsuccess = (event: any) => {
        const cursor = event.target.result
        if (cursor) {
          // 删除当前记录
          const deleteRequest = cursor.delete()
          deleteRequest.onerror = function () {
            Toast.notify({
              type: 'error',
              message: t('common.db.deleteModelConfigFail'),
            })
          }
          // 继续移动游标到下一个匹配的记录
          cursor.continue()
        }
      }
    }
  }
  // 清空模型配置
  const clearModelConfig = () => {
    const transaction = db?.transaction(['modelConfigs'], 'readwrite')
    if (transaction) {
      const objectStore = transaction.objectStore('modelConfigs')
      const clearRequest = objectStore.clear()
      clearRequest.onerror = () => {
        Toast.notify({
          type: 'error',
          message: t('common.db.connectFail'),
        })
      }
    }
  }
  // 查询模型配置
  const getModelConfig = async (model: ModelConfigStorage): Promise<ModelConfigStorage> => {
    const { userId, appId, provider, modelId } = model
    return new Promise((resolve, reject) => {
      const transaction = db?.transaction(['modelConfigs'], 'readwrite')
      if (transaction) {
        const objectStore = transaction.objectStore('modelConfigs')
        const key = `${userId}_${appId}_${provider}_${modelId}`
        const getRequest = objectStore.get(key)
        getRequest.onsuccess = (event: Event) => {
          // @ts-expect-error 类型存在该属性
          const result = event.target.result
          resolve(result)
        }
        getRequest.onerror = () => {
          Toast.notify({
            type: 'error',
            message: t('common.db.connectFail'),
          })
          // eslint-disable-next-line prefer-promise-reject-errors
          reject(t('common.db.connectFail') as string)
        }
      }
    })
  }

  useEffect(() => {
    fetchPlan()
    // 数据库请求
    const request = indexedDB && indexedDB.open('ModelConfigDB', 1)

    request.onupgradeneeded = function (event) {
      // @ts-expect-error 类型存在该属性
      const db = event.target.result
      setDb(db)
      // 创建一个对象存储（类似于表）
      // @ts-expect-error 类型存在该属性
      const objectStore = event.target.result!.createObjectStore('modelConfigs', { keyPath: 'id', autoIncrement: true })
      // 创建复合索引
      objectStore.createIndex('userId_appId_provider_modelId', ['userId', 'appId', 'provider', 'modelId'], { unique: true })
      objectStore.createIndex('userId_appId', ['userId', 'appId'], { unique: false })
      objectStore.createIndex('userId_provider_modelId', ['userId', 'provider', 'modelId'], { unique: false })
      objectStore.createIndex('userId', 'userId', { unique: false })
    }
    request.onerror = () => {
      Toast.notify({
        type: 'error',
        message: t('common.db.connectFail'),
      })
    }
    request.onsuccess = (event) => {
      // @ts-expect-error 类型存在该属性
      setDb(event.target.result)
    }
  }, [])

  if (!supportRetrievalMethods)
    return <Loading type='app' />

  return (
    <ProviderContext.Provider value={{
      modelProviders: providersData?.data || [],
      textGenerationModelList: textGenerationModelList?.data || [],
      mutateTextGenerationModelList,
      isTextGenerationModelListLoading,
      isAPIKeySet: !!textGenerationModelList?.data.some(model => model.status === ModelStatusEnum.active),
      supportRetrievalMethods: supportRetrievalMethods?.retrieval_method || [],
      plan,
      isFetchedPlan,
      enableBilling,
      onPlanInfoChanged: fetchPlan,
      enableReplaceWebAppLogo,
      modelLoadBalancingEnabled,
      datasetOperatorEnabled,
      enableCreateLongtextApp,
      useXIYANRag: enableXiyanRag,
      addModelConfig,
      deleteModelConfig,
      getModelConfig,
      clearModelConfig,
      updateModelProviders,
    }}>
      {children}
    </ProviderContext.Provider>
  )
}

export default ProviderContext
