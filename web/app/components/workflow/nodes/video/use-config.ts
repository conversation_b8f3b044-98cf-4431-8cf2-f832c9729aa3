import {
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react'
import produce from 'immer'
import type { ValueSelector, Var } from '../../types'
import { BlockEnum, VarType } from '../../types'
import {
  useNodesReadOnly,
  useWorkflow,
} from '../../hooks'
import type { VideoNodeType } from './types'

import useNodeCrud from '@/app/components/workflow/nodes/_base/hooks/use-node-crud'
import useOneStepRun from '@/app/components/workflow/nodes/_base/hooks/use-one-step-run'
// 账户设置公共能力
import type { Video } from '@/types/videos'
import { fetchVideoList } from '@/service/videos'

const useConfig = (id: string, payload: VideoNodeType) => {
  const filterVar = useCallback((varPayload: Var) => {
    return varPayload.type === VarType.string
  }, [])
  const filterNumberVar = useCallback((varPayload: Var) => {
    return varPayload.type === VarType.number
  }, [])
  const { nodesReadOnly: readOnly } = useNodesReadOnly()
  const { getBeforeNodesInSameBranch } = useWorkflow()

  const { inputs, setInputs } = useNodeCrud<VideoNodeType>(id, payload)
  const inputRef = useRef(inputs)

  // single run
  const {
    isShowSingleRun,
    hideSingleRun,
    runningStatus,
    handleRun,
    handleStop,
    runInputData,
    setRunInputData,
    runResult,
  } = useOneStepRun<VideoNodeType>({
    id,
    data: inputs,
    defaultRunInputData: {
    },
  })
  const setQuery = useCallback((newQuery: string | number, type: 'start_time' | 'end_time' | 'event_name' | 'location' | 'device') => {

    setRunInputData({
      ...runInputData,
      [type]: newQuery,
    })
  }, [runInputData, setRunInputData])

  // 选中的知识库信息
  const [selectedVideos, setSelectedVideo] = useState<Video[]>([])
  // 私密的知识库信息
  const [forkVideos, setForkVideos] = useState<Video[]>([])
  const startNode = getBeforeNodesInSameBranch(id).find(node => node.data.type === BlockEnum.Start)
  const startNodeId = startNode?.id

  // 处理开始时间变化
  const handleStartTimeChange = useCallback((newVar: ValueSelector | string) => {
    const newInputs = produce(inputs, (draft) => {
      draft.start_time_variable_selector = newVar as ValueSelector
    })
    setInputs(newInputs)
  }, [inputs, setInputs])
  // 处理结束时间变化
  const handleEndTimeChange = useCallback((newVar: ValueSelector | string) => {
    const newInputs = produce(inputs, (draft) => {
      draft.end_time_variable_selector = newVar as ValueSelector
    })
    setInputs(newInputs)
  }, [inputs, setInputs])
  // 处理事件变化
  const handleEventChange = useCallback((newVar: ValueSelector | string) => {
    const newInputs = produce(inputs, (draft) => {
      draft.event_name_variable_selector = newVar as ValueSelector
    })
    setInputs(newInputs)
  }, [inputs, setInputs])
  // 处理位置变化
  const handleLocationChange = useCallback((newVar: ValueSelector | string) => {
    const newInputs = produce(inputs, (draft) => {
      draft.location_variable_selector = newVar as ValueSelector
    })
    setInputs(newInputs)
  }, [inputs, setInputs])
  // 处理设备变化
  const handleDeviceChange = useCallback((newVar: ValueSelector | string) => {
    const newInputs = produce(inputs, (draft) => {
      draft.device_variable_selector = newVar as ValueSelector
    })
    setInputs(newInputs)
  }, [inputs, setInputs])

  // 视频库添加/删除/编辑操作
  const handleOnVideosChange = useCallback((data: Video[]) => {
    let newVideos = data
    if (newVideos.find(item => !item.name)) { // has not loaded selected dataset
      const newSelected = produce(newVideos, (draft: any) => {
        newVideos.forEach((item, index) => {
          if (!item.name) { // not fetched database
            const newItem = selectedVideos.find(i => i.id === item.id)
            if (newItem)
              draft[index] = newItem
          }
        })
      })
      setSelectedVideo(newSelected)
      newVideos = newSelected
    }
    else {
      setSelectedVideo(newVideos)
    }

    const newInputs = produce(inputs, (draft) => {
      draft.fork = false
      draft.library_ids = newVideos.map(d => d.id)
    })
    setInputs(newInputs)
  }, [inputs, setInputs, selectedVideos])

  // 不更新的话inputRef.current拿到的是旧值，会覆盖掉最新值
  useEffect(() => {
    inputRef.current = inputs
  }, [inputs])

  // 获取视频库信息
  useEffect(() => {
    (async () => {
      const inputs = inputRef.current
      const videoIds = inputs.library_ids
      if (videoIds?.length > 0) {
        const { data: videosWithDetail } = await fetchVideoList({ page: 1, ids: videoIds })
        if (inputs.fork)
          setForkVideos(videosWithDetail.info)

        else
          setSelectedVideo(videosWithDetail.info)
      }
      const newInputs = produce(inputs, (draft) => {
        draft.library_ids = inputs?.fork ? [] : videoIds
      })
      setInputs(newInputs)
    })()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  // 处理变量
  useEffect(() => {
    const inputs = inputRef.current
    let start_time: ValueSelector = inputs.start_time_variable_selector
    let end_time: ValueSelector = inputs.end_time_variable_selector
    let event: ValueSelector = inputs.event_name_variable_selector
    let location: ValueSelector = inputs.location_variable_selector
    let device: ValueSelector = inputs.device_variable_selector
    if (inputs.start_time_variable_selector && inputs.start_time_variable_selector.length === 0 && startNodeId)
      start_time = [startNodeId, 'sys.start_time']
    if (inputs.end_time_variable_selector && inputs.end_time_variable_selector.length === 0 && startNodeId)
      end_time = [startNodeId, 'sys.end_time']
    if (inputs.event_name_variable_selector && inputs.event_name_variable_selector.length === 0 && startNodeId)
      event = [startNodeId, 'sys.event']
    if (inputs.location_variable_selector && inputs.location_variable_selector.length === 0 && startNodeId)
      location = [startNodeId, 'sys.location']
    if (inputs.device_variable_selector && inputs.device_variable_selector.length === 0 && startNodeId)
      device = [startNodeId, 'sys.device']

    setInputs(produce(inputs, (draft) => {
      draft.start_time_variable_selector = start_time
      draft.end_time_variable_selector = end_time
      draft.event_name_variable_selector = event
      draft.location_variable_selector = location
      draft.device_variable_selector = device
    }))
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return {
    readOnly,
    inputs,
    filterVar,
    filterNumberVar,
    selectedVideos: selectedVideos.filter(d => d.name),
    forkVideos,
    isShowSingleRun,
    runningStatus,
    runInputData,
    runResult,
    setForkVideos,
    setQuery,
    hideSingleRun,
    handleRun,
    handleStop,
    handleStartTimeChange,
    handleEndTimeChange,
    handleEventChange,
    handleLocationChange,
    handleDeviceChange,
    handleOnVideosChange,
  }
}

export default useConfig
