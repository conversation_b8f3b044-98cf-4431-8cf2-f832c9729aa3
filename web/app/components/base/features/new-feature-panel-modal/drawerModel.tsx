import {
  AntCloudOutlined,
  AppstoreOutlined,
  DeleteOutlined,
  DownOutlined,
  FolderOpenOutlined,
  FormOutlined,
  InboxOutlined,
  PlusOutlined,
  SettingOutlined
} from '@ant-design/icons'
import { useTranslation } from 'react-i18next'
import './styles/styles_drawerModel.scss'
import { Dropdown, Modal, Input, Upload, message, Empty, Checkbox, Button, Tooltip, Popconfirm } from 'antd'
import { useEffect, useMemo, useRef, useState } from 'react'
import Search from 'antd/es/input/Search'
import type { GetProps } from 'antd'
import { TEMPLATE_URL } from '@/config'
import { useWorkflowStore } from '@/app/components/workflow/store'
import { GetTemplateList, SetTemplateList } from '@/service/share'
import TemplateDrawer from '../../chat/chat-with-history/config-panel/comments/templateDrawer'
import { upload,uploadThumb } from '@/service/base'
import type { GetProp, UploadFile, UploadProps } from 'antd'

type SearchProps = GetProps<typeof Input.Search>
type FileType = Parameters<GetProp<UploadProps, 'beforeUpload'>>[0]

interface Level3Item {
  title: string
  placeholder: string
  required: boolean
  type: string
}

const DrawerModel = () => {
  const workflowStore = useWorkflowStore()
  const { t } = useTranslation()
  const { appId } = workflowStore.getState()
  const [open, setOpen] = useState(false)
  const [drawerData, setDrawerData] = useState<any>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const setIsChecked = useRef(false)
  const [level1, setlevel1] = useState<
    { text: string; children: { text: string; children: { text: string }[] }[] }[]
  >([])
  const [level2, setlevel2] = useState<{ text: string; children: { text: string }[] }[]>([])
  const [level3, setlevel3] = useState<any[]>([])
  const [templateList, setTemplateList] = useState<any[]>([])
  const [fileList, setFileList] = useState<any[]>([])
  const [ModalTitle, setModalTitle] = useState('')
  const level1Text = useRef('')
  const level2Text = useRef('')
  const [searchValue, setSearchValue] = useState('')
  const [searchValue1, setSearchValue1] = useState('')
  const [prompt, setPrompt] = useState('')
  const [prompt2, setPrompt2] = useState('')

  const { Dragger } = Upload

  useEffect(() => {
    if (level1.length > 0 && !level1Text.current && !level2Text.current) {
      // level1Text.current = level1[0].text
      // if (level1[0].children && level1[0].children.length > 0) {
      //   setlevel2(level1[0].children || [])
      //   level2Text.current = level1[0].children[0].text
      //   if (level1[0].children[0].children && level1[0].children[0].children.length > 0) {
      //     if (ModalTitle === t('sampleTemplate.IntelligentAgent.CreationTypeTemplate')) {
      //       setTemplateList(level1[0].children[0].children || [])
      //     } else {
      //       setlevel3(level1[0].children[0].children || [])
      //     }
      //   }
      // }
      // setTimeout(() => {
      setIsModalOpen(true)
      // }, 10)
    } else {
      if (level1.length > 0) {
        SetTemplateList({
          app_id: appId,
          tt_config: level1,
          tt_type: ModalTitle
        }).then((res: any) => {
        })
      }
    }
  }, [level1])

  const addObjectToLevel3 = (newObject: any, level1Text?: string, level2Text?: string) => {
    if (!level1Text && !level2Text) {
      setlevel1(prevLevel1 => [...prevLevel1, newObject])
    } else {
      setlevel1(prevLevel1 => {
        return prevLevel1.map(level1Item => {
          if (level1Item.text === level1Text) {
            if (level2Text) {
              return {
                ...level1Item,
                children: level1Item.children.map(level2Item => {
                  if (level2Item.text === level2Text) {
                    // setlevel2((prevLevel2) => {
                    //   return prevLevel2.map((l2Item) => {
                    //     if (l2Item.text === level1Text) {
                    //       return {
                    //         ...l2Item,
                    //         children: [...l2Item.children, newObject],
                    //       };
                    //     }
                    //     return l2Item;
                    //   });
                    // });

                    return {
                      ...level2Item,
                      children: [...level2Item.children, newObject]
                    }
                  }
                  return level2Item
                })
              }
            } else {
              return {
                ...level1Item,
                children: [...level1Item.children, newObject]
              }
            }
          }
          return level1Item
        })
      })
    }
  }

  const removeObjectFromLevel3 = (newObject: any, level1Text?: string, level2Text?: string) => {
    if (!level1Text && !level2Text) {
      setlevel1(prevLevel1 => prevLevel1.filter(item => item.text !== newObject.text))
    } else {
      setlevel1(prevLevel1 => {
        return prevLevel1.map(level1Item => {
          if (level1Item.text === level1Text) {
            if (level2Text) {
              return {
                ...level1Item,
                children: level1Item.children.map(level2Item => {
                  if (level2Item.text === level2Text) {
                    return {
                      ...level2Item,
                      children: level2Item.children.filter(
                        (child: any) =>
                          (child.title !== newObject.title &&
                            child.placeholder !== newObject.placeholder) ||
                          child.file_id !== newObject.file_id
                      )
                    }
                  }
                  return level2Item
                })
              }
            } else {
              return {
                ...level1Item,
                children: level1Item.children.filter(child => child.text !== newObject.text)
              }
            }
          }
          return level1Item
        })
      })
    }
  }

  const fileTypeToAvatarMap: Record<string, string> = {
    '.docx': '/assets/image/word.png',
    '.doc': '/assets/image/word.png',
    '.pdf': '/assets/image/pdf.png',
    '.txt': '/assets/image/txt.png',
    '.xls': '/assets/image/excel.png',
    '.xlsx': '/assets/image/excel.png'
  }

  const getAvatarForFileType = (fileName: string): string => {
    if (fileName) {
      const extension = fileName.slice(fileName.lastIndexOf('.')).toLowerCase()
      return fileTypeToAvatarMap[extension] // 默认图标
    } else {
      return ''
    }
  }

  const getBase64 = (file: FileType): Promise<string> =>
    new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = error => reject(error)
    })

  const GetList = async () => {
    const data = {
      app_id: appId,
      level1: ModalTitle,
      level2: level1Text.current,
      level3: level2Text.current
    }
    const res = await GetTemplateList(data, true)
    return res
  }

  const onProgress = (e: ProgressEvent, file: UploadFile) => {
    if (e.lengthComputable) {
      const percent = Math.floor((e.loaded / e.total) * 100)
      setFileList(prevFileList =>
        prevFileList.map(f => (f.uid === file.uid ? { ...f, percent } : f))
      )
    }
  }

  const customRequest = async (options: any) => {
    const file = options.file;
    if (file.size === 0) {
      message.error(t('sampleTemplate.longText.FileCannotBeEmpty')!);
      return;
    }

    if (level1Text.current && level2Text.current) {
      const formData = new FormData()
      formData.append('file', options.file)
      formData.append('source', 'web')

      setFileList(prevFileList =>
        prevFileList.map(f =>
          f.uid === options.file.uid ? { ...f, status: 'uploading', percent: 0 } : f
        )
      )

      upload(
        {
          xhr: new XMLHttpRequest(),
          data: formData,
          onprogress: (e: ProgressEvent) => onProgress(e, options.file)
        },
        false,
        ''
      )
        .then((res: { id: string; name: string; url: string }) => {
          if (res) {
            const files:any= {
              file_name: res.name,
              file_id: res.id,
              file_path: `${TEMPLATE_URL}files/${res.id}/file-preview-look`
            }
            setTemplateList(prevTemplateList => [...prevTemplateList, files])

            uploadThumb(
              {
                xhr: new XMLHttpRequest(),
                data: formData,
                onprogress: (e: ProgressEvent) => onProgress(e, options.file)
              },
              false,
              ''
            )
              .then((res:any) => {
                 if(res){
                  let url = res.url;
                  files.file_pic=url;
                  addObjectToLevel3(files, level1Text.current, level2Text.current)
                 }else{
                  addObjectToLevel3(files, level1Text.current, level2Text.current)
                 }
              })
              .catch(error => {
                //console.log(error, 'error')
                addObjectToLevel3(files, level1Text.current, level2Text.current)
              })

            //addObjectToLevel3(files, level1Text.current, level2Text.current)
            message.success(t('sampleTemplate.longText.PromptPlaceholder7')!)
          }
          setFileList(prevFileList =>
            prevFileList.map(f => (f.uid === options.file.uid ? { ...f, status: 'done' } : f))
          )
        })
        .catch(error => {
          setFileList(prevFileList =>
            prevFileList.map(f => (f.uid === options.file.uid ? { ...f, status: 'error' } : f))
          )
        })
    } else {
      message.error(t('sampleTemplate.longText.PromptPlaceholder6')!)
    }
  }

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as FileType)
    }
  }

  const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    if (level1Text.current && level2Text.current) {
      setFileList(
        newFileList.map(file => ({
          ...file,
          percent: file.percent || 0
        }))
      )
    } else {
      message.error(t('sampleTemplate.longText.PromptPlaceholder6')!)
    }
  }

  const handleRemove = (file: UploadFile) => {

  }

  const props: UploadProps = {
    name: 'file',
    multiple: false,
    fileList: fileList,
    customRequest: customRequest,
    onChange: handleChange,
    onRemove: handleRemove,
    onPreview: handlePreview,
    showUploadList: false,
    accept: '.pdf,.docx,.txt',
    maxCount: 1,
    disabled: !(level1Text.current && level2Text.current)
  }

  const handleOk = () => {
    setIsModalOpen(false)
  }

  const handleCancel = () => {
    level1Text.current = ''
    level2Text.current = ''
    setlevel1([])
    setlevel2([])
    setlevel3([])
    setTemplateList([])
    setFileList([])
    setSearchValue('')
    setSearchValue1('')
    setIsModalOpen(false)
  }

  const handleDrawer = (item: any) => {
    setDrawerData(item)
    setOpen(true)
  }

  const onClose = () => {
    setOpen(false)
  }

  const onSearch1: SearchProps['onSearch'] = (value, _e: any, info) => {
    if (value.trim() !== '') {
      const exists = level1.some((item: any) => {
        if (item.text === value) {
          message.error(t('sampleTemplate.longText.PromptPlaceholder12')!)
          return true
        }
        return false
      })

      if (!exists) {
        const level1Data = {
          text: value,
          children: []
        }
        addObjectToLevel3(level1Data)
        setSearchValue('')
        message.success(t('sampleTemplate.longText.PromptPlaceholder10')!)
      }
    } else {
      message.error(t('sampleTemplate.longText.SearchPlaceholder')!)
    }
  }
  const onSearch2: SearchProps['onSearch'] = (value, _e, info) => {
    if (value.trim() !== '') {
      const exists = level2.some((item: any) => {
        if (item.text === value) {
          message.error(t('sampleTemplate.longText.PromptPlaceholder12')!)
          return true
        }
        return false
      })
      if (!exists) {
        if (ModalTitle === t('sampleTemplate.IntelligentAgent.CreationTypeTopic')) {
          const formData = t('sampleTemplate.FormDataEvent.ArrayFormList', {
            returnObjects: true
          }) as any[]
          const level2Data = {
            text: value,
            children: formData
          }
          setlevel2(prevLevel2 => [...prevLevel2, level2Data])
          addObjectToLevel3(level2Data, level1Text.current)
          message.success(t('sampleTemplate.longText.PromptPlaceholder10')!)
        } else {
          const level2Data = {
            text: value,
            children: []
          }
          setlevel2(prevLevel2 => [...prevLevel2, level2Data])
          addObjectToLevel3(level2Data, level1Text.current)
        }
      }
    } else {
      message.error(t('sampleTemplate.longText.SearchPlaceholder')!)
    }
    setSearchValue1('')
  }

  const CheckboxOnChange: GetProp<typeof Checkbox.Group, 'onChange'> = checkedValues => {
    if (checkedValues.length > 0) {
      setIsChecked.current = true
    } else {
      setIsChecked.current = false
    }
  }

  const InputonChange = (type: string, value: string) => {
    if (type === 'title') {
      setPrompt(value)
    } else {
      setPrompt2(value)
    }
  }

  const handleAdd = () => {
    if (prompt && prompt2 && level1Text.current && level2Text.current) {
      const level3Data = {
        title: prompt,
        placeholder: prompt2,
        required: setIsChecked.current,
        type: 'input'
      }
      addObjectToLevel3(level3Data, level1Text.current, level2Text.current)
      setlevel3(prevLevel3 => [...prevLevel3, level3Data])
      setPrompt('')
      setPrompt2('')
      message.success(t('sampleTemplate.longText.PromptPlaceholder10')!)
    } else {
      if (prompt && prompt2) {
        message.error(t('sampleTemplate.longText.PromptPlaceholder11')!)
      } else {
        message.error(t('sampleTemplate.longText.PromptPlaceholder5')!)
      }
    }
  }

  const handleItemClick = async (item: any) => {
    setTemplateList([])
    setlevel3([])
    const res = await GetList()
    if (ModalTitle === t('sampleTemplate.IntelligentAgent.CreationTypeTemplate')) {
      setTemplateList(res as any)
    } else {
      setlevel3(res as any)
    }
  }

  const renderMarkdownContent = useMemo(() => {
    return (
      <>
        <div className="Model_Content_Top">
          <div className="Model_Content_Top_Item">
            <div className="Model_Content_Top_Item_Title">
              {t('sampleTemplate.longText.firstCatalog')}
            </div>
            <div className="Model_Content_Top_Item_Input">
              <Search
                value={searchValue}
                onChange={e => {
                  setSearchValue(e.target.value)
                }}
                placeholder={t('sampleTemplate.longText.SearchPlaceholder')!}
                allowClear
                enterButton="添加"
                size="middle"
                onSearch={onSearch1}
              />
            </div>
            <div className="Model_Content_Top_Item_List">
              {level1.length > 0 ? (
                level1.map((item: any, index: number) => (
                  <div
                    key={index}
                    className={`Model_Content_Top_Item_List_Item ${item.text === level1Text.current ? 'Active' : ''
                      }`}
                    onClick={() => {
                      if (level1Text.current !== item.text) {
                        level2Text.current = ''
                        level1Text.current = item.text
                        setlevel2(item.children)
                        setTemplateList([])
                        setlevel3([])
                      }
                    }}
                  >
                    <div className="Model_Content_Top_Item_List_Item_Text">{item.text}</div>
                    {/* <FormOutlined /> */}
                    <div className="Model_Content_Top_Item_List_Item_Icon">
                      <DeleteOutlined
                        onClick={e => {
                          e.stopPropagation()
                          removeObjectFromLevel3(item)
                          message.success(t('sampleTemplate.longText.PromptPlaceholder9')!)
                        }}
                      />
                    </div>
                  </div>
                ))
              ) : (
                <div className="flex justify-center items-center w-full">
                  <Empty />
                </div>
              )}
            </div>
          </div>
          <div className="Model_Content_Top_Item">
            <div className="Model_Content_Top_Item_Title">
              {t('sampleTemplate.longText.secondCatalog')}
            </div>
            <div className="Model_Content_Top_Item_Input">
              <Search
                value={searchValue1}
                onChange={e => {
                  setSearchValue1(e.target.value)
                }}
                placeholder={t('sampleTemplate.longText.SearchPlaceholder')!}
                allowClear
                enterButton="添加"
                size="middle"
                onSearch={onSearch2}
              />
            </div>
            <div className="Model_Content_Top_Item_List">
              {level2.length > 0 ? (
                level2.map((item: any, index: number) => (
                  <div
                    key={index}
                    className={`Model_Content_Top_Item_List_Item ${item.text === level2Text.current ? 'Active' : ''
                      }`}
                    onClick={() => {
                      if (level2Text.current !== item.text) {
                        level2Text.current = item.text
                        handleItemClick(item)
                      }
                    }}
                  >
                    <div className="Model_Content_Top_Item_List_Item_Text">{item.text}</div>
                    {/* <FormOutlined /> */}
                    <div className="Model_Content_Top_Item_List_Item_Icon">
                      <DeleteOutlined
                        onClick={e => {
                          e.stopPropagation()
                          setlevel2(level2.filter((i: any) => i.text !== item.text))
                          removeObjectFromLevel3(item, level1Text.current)
                          message.success(t('sampleTemplate.longText.PromptPlaceholder9')!)
                        }}
                      />
                    </div>
                  </div>
                ))
              ) : (
                <div className="flex justify-center items-center w-full">
                  <Empty />
                </div>
              )}
            </div>
          </div>
          <div className="Model_Content_Top_Item">
            <div className="Model_Content_Top_Item_Title">
              {t('sampleTemplate.longText.PromptCatalog')}
            </div>
            {ModalTitle === t('sampleTemplate.IntelligentAgent.CreationTypeTemplate') && (
              <>
                <div className="mb-2">
                  <Dragger {...props}>
                    <p className="ant-upload-drag-icon">
                      <InboxOutlined />
                    </p>
                    <p className="ant-upload-text">Click or drag file to this area to upload</p>
                    <p className="ant-upload-hint">
                      Support for a single or bulk upload. Strictly prohibited from uploading
                      company data or other banned files.
                    </p>
                  </Dragger>
                </div>
              </>
            )}
            {ModalTitle === t('sampleTemplate.IntelligentAgent.CreationTypeTopic') && (
              <div className="Input_Content">
                <Input
                  value={prompt}
                  onChange={e => {
                    InputonChange('title', e.target.value)
                  }}
                  placeholder={t('sampleTemplate.longText.PromptPlaceholder')!}
                  className="mb-2"
                />
                <Input
                  value={prompt2}
                  onChange={e => {
                    InputonChange('placeholder', e.target.value)
                  }}
                  placeholder={t('sampleTemplate.longText.PromptPlaceholder2')!}
                  className="mb-2"
                />
                <div className="flex items-center">
                  <Checkbox.Group style={{ width: '100%' }} onChange={CheckboxOnChange}>
                    <Checkbox value="必填字段">
                      {t('sampleTemplate.longText.PromptPlaceholder3')}
                    </Checkbox>
                  </Checkbox.Group>
                  <Button type="primary" size="small" className="text-xs" onClick={handleAdd}>
                    {t('sampleTemplate.longText.PromptPlaceholder4')}
                  </Button>
                </div>
              </div>
            )}
            {ModalTitle === t('sampleTemplate.IntelligentAgent.CreationTypeTemplate') ? (
              <div className="mt-2 Template_List_Content">
                {(
                  t('sampleTemplate.FormDataEvent.ArrayFormList', { returnObjects: true }) as any[]
                ).map((item: any, index: number) => (
                  <div className="Theme_List_Content_Item" key={index}>
                    <div className="Theme_List_Content_Item_Left">
                      <div
                        className={`Theme_List_Content_Item_Title ${item.required ? 'FieldBefore' : ''
                          }`}
                      >
                        {item.title}
                      </div>
                      <div className="Theme_List_Content_Item_Placeholder">{item.placeholder}</div>
                    </div>
                    {!item.field && (
                      <DeleteOutlined
                        className="Theme_List_Content_Item_Icon"
                        onClick={e => {
                          e.stopPropagation()
                          removeObjectFromLevel3(item, level1Text.current, level2Text.current)
                          setlevel3(level3.filter((i: any) => i.title !== item.title))
                          message.success(t('sampleTemplate.longText.PromptPlaceholder9')!)
                        }}
                      />
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="mt-2 Theme_List_Content">
                {level3.length > 0 ? (
                  level3.map((item: any, index: number) => (
                    <div className="Theme_List_Content_Item" key={index}>
                      <div className="Theme_List_Content_Item_Left">
                        <div
                          className={`Theme_List_Content_Item_Title ${item.required ? 'FieldBefore' : ''
                            }`}
                        >
                          {item.title}
                        </div>
                        <div className="Theme_List_Content_Item_Placeholder">
                          {item.placeholder}
                        </div>
                      </div>
                      {!item.field && (
                        <DeleteOutlined
                          className="Theme_List_Content_Item_Icon"
                          onClick={e => {
                            e.stopPropagation()
                            removeObjectFromLevel3(item, level1Text.current, level2Text.current)
                            setlevel3(
                              level3.filter(
                                (i: any) =>
                                  i.title !== item.title && i.placeholder !== item.placeholder
                              )
                            )
                            message.success(t('sampleTemplate.longText.PromptPlaceholder9')!)
                          }}
                        />
                      )}
                    </div>
                  ))
                ) : (
                  <div className="flex justify-center items-center w-full">
                    <Empty />
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
        {ModalTitle === t('sampleTemplate.IntelligentAgent.CreationTypeTemplate') && (
          <div className="Model_Content_Bottom">
            <div className="Model_Content_Bottom_Item">
              <div className="Model_Content_Bottom_Item_Title">
                {t('sampleTemplate.longText.UploadedTemplate')}
              </div>
              <div className="Model_Content_Bottom_Item_List">
                {templateList.length > 0 ? (
                  templateList.map((item: any, index: number) => (
                    <Tooltip title={item.file_name}>
                      <div
                        key={index}
                        className="Model_Content_Bottom_Item_List_Item"
                        onClick={() => {
                          handleDrawer(item)
                        }}
                      >
                        <img src={getAvatarForFileType(item.file_name)} alt="" />
                        <div className="Model_Content_Bottom_Item_List_Item_Text">
                          <div className="Model_Content_Bottom_Item_List_Item_Text_Name">
                            {item.file_name}
                          </div>
                          <Popconfirm
                            placement="topLeft"
                            title={t('sampleTemplate.BasicModal.DeleteConfirm')}
                            overlayClassName="custom-popconfirm"
                            okText="Yes"
                            cancelText="No"
                            onConfirm={(e: any) => {
                              e.stopPropagation()
                              setTemplateList(prevTemplateList =>
                                prevTemplateList.filter((i: any) => i.file_id !== item.file_id)
                              )
                              removeObjectFromLevel3(item, level1Text.current, level2Text.current)
                              message.success(t('sampleTemplate.longText.PromptPlaceholder9')!)
                            }}
                            onCancel={(e: any) => {
                              e.stopPropagation()
                            }}
                          >
                            <DeleteOutlined
                              onClick={(e) => e.stopPropagation()}
                              className="delete-icon"
                            />
                          </Popconfirm>

                        </div>
                      </div>
                    </Tooltip>
                  ))
                ) : (
                  <div className="flex justify-center items-center w-full">
                    <Empty />
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </>
    )
  }, [
    prompt,
    prompt2,
    searchValue,
    searchValue1,
    level1,
    level2,
    level3,
    templateList,
    fileList,
    level1Text.current,
    level2Text.current
  ])

  return (
    <>
      <TemplateDrawer
        open={open}
        drawerData={drawerData}
        onClose={onClose}
        placement="left"
      ></TemplateDrawer>
      <Modal
        title={ModalTitle}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width="60%"
        footer={null}
        destroyOnClose={true}
        centered={true}
        keyboard={false}
        maskClosable={false}
      >
        <div className="Model_Content socDiv">{renderMarkdownContent}</div>
      </Modal>

      <div className="flex items-center justify-between text-sm border border-gray-300 p-4 DrawerModel">
        <div>{t('sampleTemplate.longText.modelType')}</div>
        <div
          className="flex items-center text-xs text-gray-500 cursor-pointer"
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        >
          <Dropdown
            open={isDropdownOpen}
            placement="topCenter"
            menu={{
              onClick: async (e: any) => {
                const res = await GetTemplateList(
                  {
                    app_id: appId,
                    level1: e.key,
                    get_type: 'all'
                  },
                  true
                )
                if (res) {
                  setlevel1(res as any)
                  setModalTitle(e.key)
                  setIsDropdownOpen(false)
                }
              },
              items: [
                {
                  label: t('sampleTemplate.IntelligentAgent.CreationTypeTemplate'),
                  key: t('sampleTemplate.IntelligentAgent.CreationTypeTemplate')!,
                  icon: <FolderOpenOutlined />
                },
                {
                  label: t('sampleTemplate.IntelligentAgent.CreationTypeTopic'),
                  key: t('sampleTemplate.IntelligentAgent.CreationTypeTopic')!,
                  icon: <AppstoreOutlined />
                }
              ]
            }}
          >
            <SettingOutlined className="mr-1" />
          </Dropdown>
          {t('sampleTemplate.longText.customCatalog')}
        </div>
      </div>
    </>
  )
}

export default DrawerModel
