import type { FC } from 'react'
import { memo } from 'react'
import { useTranslation } from 'react-i18next'
import Modal from '@/app/components/base/modal'
import FieldInfo from '@/app/components/datasets/common/field-info'

type DetailModalProps = {
  detail: {
    originFileName: string
    originFileSize: string
    uploadTime: string
    lastUpdateTime: string
  }
  onClose: () => void
}

const DetailModal: FC<DetailModalProps> = ({ onClose, detail }) => {
  const { t } = useTranslation()
  return (
    <Modal
      isShow
      closable
      onClose={onClose}
      title={t('datasetDocuments.list.dialog.detail')}
    >
      <FieldInfo
        labelClassName='w-[98px] truncate align-end'
        label={t('datasetDocuments.list.info.originFileName')}
        displayedValue={detail.originFileName}
      ></FieldInfo>
      <FieldInfo
        labelClassName='w-[98px] truncate align-end'
        label={t('datasetDocuments.list.info.originFileSize')}
        displayedValue={detail.originFileSize}
      ></FieldInfo>
      <FieldInfo
        labelClassName='w-[98px] truncate align-end'
        label={t('datasetDocuments.list.info.uploadTime')}
        displayedValue={detail.uploadTime}
      ></FieldInfo>
      <FieldInfo
        labelClassName='w-[98px] truncate align-end'
        label={t('datasetDocuments.list.info.lastUpdateTime')}
        displayedValue={detail.lastUpdateTime}
      ></FieldInfo>
    </Modal>
  )
}

export default memo(DetailModal)
