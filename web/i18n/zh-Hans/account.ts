const translation = {
  firecrawl: {
    configFirecrawl: '配置 🔥Firecrawl',
    apiKeyPlaceholder: '从 firecrawl.dev 获取 API Key',
    getApiKeyLinkText: '从 firecrawl.dev 获取您的 API Key',
  },
  modelProvider: {
    modalList: '模型列表',
    addLocalModal: '添加本地模型',
    addCloudModal: '添加公有云模型',
    deleteModalTitle: '确认删除模型？',
    deleteModalTip: '删除后不可恢复，包含LLM、TEXT EMBEDDING、RERANK等已经引入的模型将丢失。',
    localModelTip: '支持LocaIAI、Ollama部署的本地模型服务',
    configType: {
      'predefined-model': '预定义',
      'customizable-model': '可定制',
    },
    form: {
      providerSelect: '选择公有云',
      configTypeSelect: '选择配置方式',
    },
    systemReasoningModel: {
      title: '系统推理模型',
      tip: '设置创建应用使用的默认推理模型，以及对话名称生成、下一步问题建议等功能也会使用该默认推理模型。',
    },
    embeddingModel: {
      title: 'Embedding 模型',
      tip: '设置知识库文档嵌入处理的默认模型，检索和导入知识库均使用该Embedding模型进行向量化处理，切换后将导致已导入的知识库与问题之间的向量维度不一致，从而导致检索失败。为避免检索失败，请勿随意切换该模型。',
      required: '请选择 Embedding 模型',
    },
    speechToTextModel: {
      title: '语音转文本模型',
      tip: '设置对话中语音转文字输入的默认使用模型。',
    },
    ttsModel: {
      title: '文本转语音模型',
      tip: '设置对话中文字转语音输出的默认使用模型。',
    },
    rerankModel: {
      title: 'Rerank 模型',
      tip: '针对初步检索或推荐的结果进行再次排序，用来优化最终呈现给用户的结果顺序',
    },
    modelConfig: '模型配置',
    modelSelect: '模型选择',
    resultRearrangement: '结果重排',
    resultRearrangementTip: '开启重排后，通过模型分析用户问题，调整切片顺序，使得最相关的内容排在前面',
    encrypted: {
      front: '您的密钥将使用',
      back: '技术进行加密和存储。',
    },
    addModel: '添加模型',
    modelsNum: '{{num}} 个模型',
    showModels: '显示模型',
    config: '配置',
    model: '模型',
    featureSupported: '支持 {{feature}} 功能',
    configureModel: '设置模型',
    deprecated: '已弃用',
    loadPresets: '加载预设',
    parameters: '参数',
    modelHasBeenDeprecated: '该模型已废弃',
    paramMode: {
      normal: '常规模式',
      precise: '精准模式',
      creative: '创意模式',
      custom: '自定义模式',
    },
    modelType: {
      officalModel: '内置模型',
      thirdPartyModel: '第三方模型',
      localModel: '本地模型',
      cloudModel: '公有云模型',
    },
    info: {
      paramConfig: '高级参数配置',
      paramMode: '参数模式',
      defaultParam: '默认参数',
      customParam: '自定义参数',
    },
    action: {
      selectModel: '请选择您的模型',
    },
  },
  model: {
    params: {
      stop_sequencesPlaceholder: '输入序列并按 Tab 键',
    },
    tone: {
      Creative: '创意',
      Balanced: '平衡',
      Precise: '精确',
      Custom: '自定义',
    },
    addMoreModel: '添加更多模型',
  },
  plansCommon: {
    unlimited: '无限制',
    member: '成员',
    memberAfter: '个成员',
  },
  dataSource: {
    add: '添加数据源',
    notion: {
      title: 'Notion',
      description: '使用 Notion 作为知识库的数据源。',
      connectedWorkspace: '已绑定工作空间',
      addWorkspace: '添加工作空间',
      changeAuthorizedPages: '更改授权页面',
      pagesAuthorized: '已授权页面',
    },
    website: {
      title: '网站',
      description: '使用网络爬虫从网站导入内容。',
      configuredCrawlers: '已配置的爬虫',
    },
  },
  apiBasedExtension: {
    title: 'API 扩展提供了一个集中式的 API 管理，在此统一添加 API 配置后，方便在 智能体平台 上的各类应用中直接使用。',
    link: '了解如何开发您自己的 API 扩展。',
    linkUrl: 'https://docs.ai/features/extension/api_based_extension',
    add: '新增 API 扩展',
    modal: {
      title: '新增 API 扩展',
      editTitle: '编辑 API 扩展',
      name: {
        title: '名称',
        placeholder: '请输入名称',
      },
      apiEndpoint: {
        title: 'API Endpoint',
        placeholder: '请输入 API endpoint',
      },
      apiKey: {
        title: 'API-key',
        placeholder: '请输入 API-key',
        lengthError: 'API-key 不能少于 5 位',
      },
    },
    selector: {
      title: 'API 扩展',
      placeholder: '请选择 API 扩展',
      manage: '管理 API 扩展',
    },
    type: '类型',
  },
  info: {
    invitationLink: '邀请链接',
    failedInvitationEmails: '邀请以下邮箱失败',
    pending: '待定...',
    you: '（你）',
    account: '账户',
    role: '角色',
    myAccount: '我的账户',
    lastActive: '上次活动时间',
    avatar: '头像',
    name: '用户名',
    email: '邮箱',
    passwordTitle: '密码',
    currentPassword: '原密码',
    newPassword: '新密码',
    confirmPassword: '确认密码',
    workspace: '工作空间',
    provider: '模型来源',
    language: '语言',
  },
  action: {
    invite: '添加',
    setPassword: '设置密码',
    resetPassword: '重置密码',
    editName: '编辑名字',
    inviteTeamMember: '添加团队成员',
    sendInvite: '发送邀请',
    removeFromTeam: '移出团队',
    helpCenter: '文档',
    logout: '退出登录',
  },
  notify: {
    passwordTip: '如果您不想使用验证码登录，可以设置永久密码',
    inviteSuccful: '邀请团队成员成功',
    inviteTeamMemberTip: '对方在登录后可以访问你的团队数据。',
    invitationSent: '邀请已发送',
    invitationSentTip: '邀请已发送，对方登录 智能体平台 后即可访问你的团队数据。',
    removeFromTeamTip: '将取消团队访问',
  },
  role: {
    owner: '所有者',
    ownerTip: '当前工作空间拥有者',
    ownerTipPublicCloud: '能够创建应用、知识库、工具，可以创建团队，管理团队成员',
    admin: '管理员',
    adminTip: '能够建立应用程序和管理团队设置',
    adminTipPublicCloud: '能够创建应用、知识库、工具，可以管理团队成员',
    normal: '成员',
    normalTip: '只能使用应用程序，不能建立应用程序',
    normalTipPublicCloud: '能够创建应用、知识库、工具',
    editor: '编辑',
    editorTip: '能够建立并编辑应用程序，不能管理团队设置',
    datasetOperator: '知识库管理员',
    datasetOperatorTip: '只能管理知识库',
  },
  settingMenu: {
    accountGroup: '通用',
    workplaceGroup: '工作空间',
    account: '我的账户',
    members: '成员',
    billing: '账单',
    integrations: '集成',
    language: '语言',
    provider: '模型来源',
    dataSource: '数据来源',
    plugin: '插件',
    apiBasedExtension: 'API 扩展',
    operationLog: '操作日志',
  },
  placeholder: {
    confirmPwdPlaceholder: '请再次输入密码',
  },
  team: {
    title: '团队',
    hostTeam: '创建的团队',
    sonTeam: '加入的团队',
    createTeamTitle: '创建的团队',
    createTeam: '创建团队',
    teamName: '团队名称',
    createTime: '创建时间',
    timeCreatePeople: '团队创建人',
    joinTime: '加入时间',
    timeRole: '团队角色',
    operate: '操作',
    view: '查看',
    disband: '解散',
    exit: '退出',
    createTeamDesc: '创建失败。',
    createTeamTooltip: '只支持创建100个团队。',
    table: {
      addMembers: '添加成员',
      userID: '用户ID',
      userName: '用户名称',
      email: '邮箱',
      roleChange: '角色变更',
      delete: '移除',
      selectRolePlaceholder: '请选择角色',
      createMembersTooltip: '团队支持添加2000人。',
      selectMembersPlaceholder: '请选择成员',
      inputKeywordPlaceholder: '请输入关键词',
    },
    disbandInfo: {
      disbandPrompt: '解散提示',
      disbandPromptDesc: '团队空间包含知识库、工具、应用相关内容，不支持解散团队。需删除团队空间内容，才可解散。',
      noDisbandButton: '我知道了',
      disbandWarn: '解散警告',
      disbandWarnDesc: '您确定要解散{{key}}的团队吗？',
      confirm: '确认',
      cancel: '取消',
      exitWarn: '退出警告',
      exitWarnDesc: '您确定要退出团队吗？退出团队后，您在团队空间创建的内容包含知识库、工具、应用相关内容及数据不会被删除。之后想加入团队，需要管理员或团队创建人邀请。',
      removeWarn: '移除警告',
      removeWarnDesc: '您确定要移除{{key}}的团队吗？移除后可支持手动添加成员，该成员在团队创建的知识库、应用、工具不会被删除。'

    },
    teamNameInfo: {
      membersInfo: '成员信息',
      membersRole: '成员角色',
    },
  },
  dateTimeFormat: 'YYYY-MM-DD HH:mm',
  operationLog: {
    table: {
      header: {
        index: '#',
        userId: '用户ID',
        userName: '用户昵称',
        account: '用户账号',
        time: '操作时间',
        workspace: '团队空间',
        operation: '操作动作',
        content: '具体细节',
      },
    },
    operation: {
      add: '添加',
      modify: '修改',
      delete: '删除',
      copy: '复制',
      publish: '发布',
      roleChange: '角色变更',
      memberRemove: '成员移除',
    },
    source: {
      app: '应用',
      dataset: '知识库',
      member: '成员',
      tool: '工具',
    },
    contentLabel: '{{target}}{{typeLabel}}的{{operationLabel}}'
  },
}

export default translation
