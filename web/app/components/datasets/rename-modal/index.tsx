'use client'

import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react'
import { useEffect } from 'react'
import { useContext } from 'use-context-selector'
import { useTranslation } from 'react-i18next'
import { Form, Input } from 'antd'
import Button from '@/app/components/base/button'
import Modal from '@/app/components/base/modal'
import { ToastContext } from '@/app/components/base/toast'
import type { DataSet } from '@/models/datasets'
import { updateDatasetSetting } from '@/service/datasets'
import { useFormDisabled } from '@/hooks/use-form'

type RenameDatasetModalProps = {
  show: boolean
  dataset: DataSet
  onSuccess?: () => void
  onClose: () => void
}

const RenameDatasetModal = ({ show, dataset, onSuccess, onClose }: RenameDatasetModalProps) => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  const [form] = Form.useForm()
  const disabled = useFormDisabled(form)

  const onConfirm: MouseEventHandler = async () => {
    try {
      await updateDatasetSetting({
        datasetId: dataset.id,
        body: {
          ...form.getFieldsValue(),
        },
      })
      notify({ type: 'success', message: t('common.actionMsg.modifiedSuccessfully') })
      if (onSuccess)
        onSuccess()
      onClose()
    }
    catch (e) {
      notify({ type: 'error', message: t('common.actionMsg.modifiedUnsuccessfully') })
    }
  }
  useEffect(() => {
    form.setFieldsValue({
      name: dataset.name,
      description: dataset.description,
    })
  }, [dataset.description, dataset.name, form])

  return (
    <Modal
      className='max-w-[520px] w-[520px]'
      isShow={show}
      title={t('dataset.modalTitle.datasetSetting')}
      footer={
        <>
          <Button variant='secondary-accent' className='mr-4' onClick={onClose}>{t('common.operation.cancel')}</Button>
          <Button disabled={disabled} variant="primary" onClick={onConfirm}>{t('common.operation.save')}</Button>
        </>
      }
      closable
      onClose={onClose}
    >
      <Form form={form} layout='vertical'>
        <Form.Item
          required
          name={'name'}
          label={t('datasetCreation.stepOne.form.name')}
          tooltip={t('datasetCreation.stepOne.form.nameDescription')}
          validateTrigger="onChange"
          validateFirst={true}
          rules={[
            {
              required: true,
              whitespace: true,
              pattern: /^[a-zA-Z0-9\u4E00-\u9FA5()-_.]{1,20}$/,
            },
          ]}
        >
          <Input
            placeholder={t('datasetCreation.stepOne.form.namePlaceholder') || ''}
          ></Input>
        </Form.Item>
        <Form.Item
          name={'description'}
          label={t('datasetCreation.stepOne.form.desc')}
          validateFirst={true}
          rules={[
            {
              required: true,
              whitespace: true,
            },
          ]}
          validateTrigger='onBlur'
        >
          <Input.TextArea
            maxLength={400}
            placeholder={t('datasetCreation.stepOne.form.descPlaceholder') || ''}
          >
          </Input.TextArea>
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default RenameDatasetModal
