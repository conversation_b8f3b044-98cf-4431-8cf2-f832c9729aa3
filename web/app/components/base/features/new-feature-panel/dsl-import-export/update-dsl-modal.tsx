'use client'

import {
  memo,
  useCallback,
  useState,
} from 'react'
import { useContext } from 'use-context-selector'
import { useTranslation } from 'react-i18next'
import { Form } from 'antd'
import Uploader from './upload'
import { WORKFLOW_DATA_UPDATE } from '@/app/components/workflow/constants'
import {
  SupportUploadFileTypes,
} from '@/app/components/workflow/types'
import {
  initialEdges,
  initialNodes,
} from '@/app/components/workflow/utils'
import { fetchWorkflowDraft, importDSL } from '@/service/workflow'
import { useStore as useAppStore } from '@/app/components/app/store'
import { DSLImportMode, DSLImportStatus } from '@/types/api/dsl'
import { fetchAppChatBgConfig } from '@/service/apps'
// 公共组件
import { useEventEmitterContextContext } from '@/context/event-emitter'
import { FILE_EXTS } from '@/app/components/base/prompt-editor/constants'
import Button from '@/app/components/base/button'
import Modal from '@/app/components/base/modal'
import { ToastContext } from '@/app/components/base/toast'
import Confirm from '@/app/components/base/confirm'
import { useWorkflowStore } from '@/app/components/workflow/store'

type UpdateDSLModalProps = {
  onCancel: () => void
  onImport: () => void
}

const UpdateDSLModal = ({
  onCancel,
  onImport,
}: UpdateDSLModalProps) => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  const appDetail = useAppStore(s => s.appDetail)
  const workflowStore = useWorkflowStore()
  const { eventEmitter } = useEventEmitterContextContext()

  // dsl文件
  const [currentFile, setDSLFile] = useState<File>()
  // 文件内容
  const [fileContent, setFileContent] = useState<string>()
  // 是否显示确认弹窗
  const [showConfirm, setShowConfirm] = useState(false)

  // 读取文件内容
  const readFile = (file: File) => {
    const reader = new FileReader()
    reader.onload = function (event) {
      const content = event.target?.result
      setFileContent(content as string)
    }
    reader.readAsText(file)
  }
  // 预处理上传文件
  const handleFile = (file?: File) => {
    setDSLFile(file)
    if (file)
      readFile(file)
    if (!file)
      setFileContent('')
  }
  // 更新工作流信息
  const handleWorkflowUpdate = useCallback(async (appId: string) => {
    const {
      graph,
      features,
      hash,
      environment_variables,
      conversation_variables,
    } = await fetchWorkflowDraft(`/apps/${appId}/workflows/draft`)
    const { data } = await fetchAppChatBgConfig({ appId })
    const { nodes, edges, viewport } = graph
    const newFeatures = {
      file: {
        image: {
          enabled: !!features.file_upload?.image?.enabled,
          number_limits: features.file_upload?.image?.number_limits || 3,
          transfer_methods: features.file_upload?.image?.transfer_methods || ['local_file', 'remote_url'],
        },
        enabled: !!(features.file_upload?.enabled || features.file_upload?.image?.enabled),
        allowed_file_types: features.file_upload?.allowed_file_types || [SupportUploadFileTypes.image],
        allowed_file_extensions: features.file_upload?.allowed_file_extensions || FILE_EXTS[SupportUploadFileTypes.image].map(ext => `.${ext}`),
        allowed_file_upload_methods: features.file_upload?.allowed_file_upload_methods || features.file_upload?.image?.transfer_methods || ['local_file', 'remote_url'],
        number_limits: features.file_upload?.number_limits || features.file_upload?.image?.number_limits || 3,
      },
      opening: {
        enabled: !!features.opening_statement,
        opening_statement: features.opening_statement,
        suggested_questions: features.suggested_questions,
      },
      suggested: features.suggested_questions_after_answer || { enabled: false },
      speech2text: features.speech_to_text || { enabled: false },
      text2speech: features.text_to_speech || { enabled: false },
      citation: features.retriever_resource || { enabled: false },
      moderation: features.sensitive_word_avoidance || { enabled: false },
      chatBackground: data || { enabled: false },
    }
    // 更新workflow数据
    eventEmitter?.emit({
      type: WORKFLOW_DATA_UPDATE,
      payload: {
        nodes: initialNodes(nodes, edges),
        edges: initialEdges(edges, nodes),
        viewport,
        features: newFeatures,
        hash,
      },
    } as any)
    workflowStore.setState({
      // 环境变量加密的和没有加密的
      envSecrets: (environment_variables || []).filter(env => env.value_type === 'secret').reduce((acc, env) => {
        acc[env.id] = env.value
        return acc
      }, {} as Record<string, string>),
      environmentVariables: environment_variables?.map(env => env.value_type === 'secret' ? { ...env, value: '[__HIDDEN__]' } : env) || [],
      // #TODO chatVar sync#
      conversationVariables: conversation_variables || [],
    })
    notify({ type: 'success', message: t('workflow.common.importSuccess') })
    onCancel()
  }, [eventEmitter, workflowStore, notify, t, onCancel])
  // 导入dsl文件
  const handleImport = useCallback(async () => {
    try {
      if (appDetail && fileContent) {
        const response = await importDSL({ mode: DSLImportMode.YAML_CONTENT, yaml_content: fileContent, app_id: appDetail.id })
        const { id, status, app_id, imported_dsl_version, current_dsl_version } = response
        // 如果导入成功，则更新工作流信息
        if (status === DSLImportStatus.COMPLETED || status === DSLImportStatus.COMPLETED_WITH_WARNINGS) {
          if (!app_id) {
            notify({ type: 'error', message: t('workflow.dsl.importFailure') })
            return
          }
          handleWorkflowUpdate(app_id)
          if (onImport)
            onImport()
          notify({
            type: status === DSLImportStatus.COMPLETED ? 'success' : 'warning',
            message: t(status === DSLImportStatus.COMPLETED ? 'workflow.dsl.importSuccess' : 'workflow.dsl.importWarning'),
            children: status === DSLImportStatus.COMPLETED_WITH_WARNINGS && t('workflow.dsl.importWarningDetails'),
          })
          onCancel()
        }
        else if (status === DSLImportStatus.PENDING) {
          notify({
            type: 'warning',
            message: '联系工作人员',
          })
        }
        else {
          notify({ type: 'error', message: t('workflow.dsl.importFailure') })
        }
      }
    }
    catch (e) {
      notify({ type: 'error', message: t('workflow.dsl.importFailure') })
    }
    setShowConfirm(false)
  }, [appDetail, fileContent, handleWorkflowUpdate, onImport, notify, t, onCancel])

  return (
    <Modal
      isShow={true}
      onClose={onCancel}
      closable
      title={t('workflow.dsl.importDSL')}
      footer={
        <>
          <Button variant='secondary-accent' className='mr-4' onClick={onCancel}>{t('common.operation.cancel')}</Button>
          <Button
            variant='primary'
            disabled={!currentFile || !appDetail || !fileContent}
            onClick={() => setShowConfirm(true)}
          >
            {t('workflow.dsl.overwriteAndImport')}
          </Button>
        </>
      }
    >
      <Form layout='vertical'>
        <Form.Item className='!mb-0'>
          <Uploader
            file={currentFile}
            updateFile={handleFile}
          />
        </Form.Item>
      </Form>

      {
        showConfirm
          && <Confirm
            isShow={showConfirm}
            type='warning2'
            onCancel={() => setShowConfirm(false)}
            title={t('workflow.dsl.importDSLTip')}
            content={t('workflow.dsl.importDSLTipContent')}
            onConfirm={() => handleImport()}
          />
      }
    </Modal>
  )
}

export default memo(UpdateDSLModal)
