import {
  memo,
  useCallback,
  useState,
} from 'react'
import {
  useStoreApi,
} from 'reactflow'
import { useTranslation } from 'react-i18next'
import EnvItem from './env-item'
import VariableModal from './variable-modal'

import { useStore } from '@/app/components/workflow/store'

import type {
  EnvironmentVariable,
} from '@/app/components/workflow/types'
import { findUsedVarNodes, updateNodeVars } from '@/app/components/workflow/nodes/_base/components/variable/utils'
import { useNodesSyncDraft } from '@/app/components/workflow/hooks/use-nodes-sync-draft'
import { useNodesReadOnly } from '@/app/components/workflow/hooks'
// 公共组件
import Modal from '@/app/components/base/modal'
import TextButton from '@/app/components/base/button/text-button'
import Confirm from '@/app/components/base/confirm'
import FeatureCollapse from '@/app/components/base/features/feature-collapse'
import { PlusCircle } from '@/app/components/base/icons/src/vender/line/general'

const EnvPanel = () => {
  const { t } = useTranslation()
  const store = useStoreApi()
  const envList = useStore(s => s.environmentVariables) as EnvironmentVariable[]
  const envSecrets = useStore(s => s.envSecrets)
  const updateEnvList = useStore(s => s.setEnvironmentVariables)
  const setEnvSecrets = useStore(s => s.setEnvSecrets)
  const { doSyncWorkflowDraft } = useNodesSyncDraft()
  const { nodesReadOnly } = useNodesReadOnly()

  // 当前环境变量
  const [currentVar, setCurrentVar] = useState<EnvironmentVariable>()
  // 显示编辑变量
  const [showEditModal, setShowEditModal] = useState(false)
  // 显示删除确认
  const [showRemoveVarConfirm, setShowRemoveConfirm] = useState(false)
  // 缓存删除的变量，用于确认删除时，再次删除
  const [cacheForDelete, setCacheForDelete] = useState<EnvironmentVariable>()

  // 私密化变量
  const formatSecret = (s: string) => {
    return s.length > 8 ? `${s.slice(0, 6)}************${s.slice(-2)}` : '********************'
  }

  // 获取影响的节点
  const getEffectedNodes = useCallback((env: EnvironmentVariable) => {
    const { getNodes } = store.getState()
    const allNodes = getNodes()
    return findUsedVarNodes(
      ['env', env.name],
      allNodes,
    )
  }, [store])
  // 删除环境变量时，删除节点中使用到的变量
  const removeUsedVarInNodes = useCallback((env: EnvironmentVariable) => {
    const { getNodes, setNodes } = store.getState()
    const effectedNodes = getEffectedNodes(env)
    const newNodes = getNodes().map((node) => {
      if (effectedNodes.find(n => n.id === node.id))
        return updateNodeVars(node, ['env', env.name], [])

      return node
    })
    setNodes(newNodes)
  }, [getEffectedNodes, store])
  // 编辑环境变量
  const handleEdit = (env: EnvironmentVariable) => {
    setCurrentVar(env)
    setShowEditModal(true)
  }
  // 删除环境变量
  const handleDelete = useCallback((env: EnvironmentVariable) => {
    removeUsedVarInNodes(env)
    updateEnvList(envList.filter(e => e.id !== env.id))
    setCacheForDelete(undefined)
    setShowRemoveConfirm(false)
    doSyncWorkflowDraft()
    if (env.value_type === 'secret') {
      const newMap = { ...envSecrets }
      delete newMap[env.id]
      setEnvSecrets(newMap)
    }
  }, [doSyncWorkflowDraft, envList, envSecrets, removeUsedVarInNodes, setEnvSecrets, updateEnvList])
  // 删除确认
  const deleteCheck = useCallback((env: EnvironmentVariable) => {
    const effectedNodes = getEffectedNodes(env)
    if (effectedNodes.length > 0) {
      setCacheForDelete(env)
      setShowRemoveConfirm(true)
    }
    else {
      handleDelete(env)
    }
  }, [getEffectedNodes, handleDelete])
  // 保存环境变量
  const handleSave = useCallback(async (env: EnvironmentVariable) => {
    // add env
    let newEnv = env
    if (!currentVar) {
      if (env.value_type === 'secret') {
        setEnvSecrets({
          ...envSecrets,
          [env.id]: formatSecret(env.value),
        })
      }
      const newList = [env, ...envList]
      updateEnvList(newList)
      await doSyncWorkflowDraft()
      // 更新私密环境变量
      updateEnvList(newList.map(e => (e.id === env.id && env.value_type === 'secret') ? { ...e, value: '[__HIDDEN__]' } : e))
      return
    }
    else if (currentVar.value_type === 'secret') {
      if (env.value_type === 'secret') {
        if (envSecrets[currentVar.id] !== env.value) {
          newEnv = env
          setEnvSecrets({
            ...envSecrets,
            [env.id]: formatSecret(env.value),
          })
        }
        else {
          newEnv = { ...env, value: '[__HIDDEN__]' }
        }
      }
    }
    else {
      if (env.value_type === 'secret') {
        newEnv = env
        setEnvSecrets({
          ...envSecrets,
          [env.id]: formatSecret(env.value),
        })
      }
    }
    const newList = envList.map(e => e.id === currentVar.id ? newEnv : e)
    updateEnvList(newList)
    // side effects of rename env
    if (currentVar.name !== env.name) {
      const { getNodes, setNodes } = store.getState()
      const effectedNodes = getEffectedNodes(currentVar)
      const newNodes = getNodes().map((node) => {
        if (effectedNodes.find(n => n.id === node.id))
          return updateNodeVars(node, ['env', currentVar.name], ['env', env.name])

        return node
      })
      setNodes(newNodes)
    }
    await doSyncWorkflowDraft()
    updateEnvList(newList.map(e => (e.id === env.id && env.value_type === 'secret') ? { ...e, value: '[__HIDDEN__]' } : e))
  }, [currentVar, doSyncWorkflowDraft, envList, envSecrets, getEffectedNodes, setEnvSecrets, store, updateEnvList])

  return (
    <>
      <FeatureCollapse
        inWorkflow
        title={t('workflow.env.envPanelTitle')}
        useSwitch={false}
        headerRight={(
          <TextButton variant={'hover'} disabled={nodesReadOnly} onClick={() => setShowEditModal(true)} >
            <PlusCircle className='w-4 h-4' />
            <div>{t('workflow.common.addVariable')}</div>
          </TextButton>
        )}
      >
        <>
          <div className={'text-S1 leading-H1 text-gray-G2 mb-2'}>{t('workflow.env.envDescription')}</div>
          {envList.map(env => (
            <EnvItem
              key={env.id}
              env={env}
              onEdit={handleEdit}
              onDelete={deleteCheck}
            />
          ))}
        </>
      </FeatureCollapse>
      {/* 变量添加/编辑弹框 */}
      {showEditModal && <Modal
        isShow={showEditModal}
        closable
        onClose={() => {
          setShowEditModal(false)
          setCurrentVar(undefined)
        }}
        title={(!currentVar ? t('workflow.env.modal.title') : t('workflow.env.modal.editTitle')) || ''}
      >
        <VariableModal
          env={currentVar}
          onSave={handleSave}
          onClose={() => {
            setShowEditModal(false)
            setCurrentVar(undefined)
          }}
        />
      </Modal>}

      <Confirm
        title={t('workflow.common.effectVarConfirm.title')}
        content={t('workflow.common.effectVarConfirm.content')}
        isShow={showRemoveVarConfirm}
        onCancel={() => setShowRemoveConfirm(false)}
        onConfirm={() => cacheForDelete && handleDelete(cacheForDelete)}
      />
    </>
  )
}

export default memo(EnvPanel)
