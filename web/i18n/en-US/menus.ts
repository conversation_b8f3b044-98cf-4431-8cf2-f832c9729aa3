const translation = {
  csghub: {
    datasets: 'data set',
    publickDatasets: 'Public datasets',
    model: 'model',
    myModels: 'My model',
    inferences: 'Inference services',
    myDatasets: 'My datasets',
    algorithmManage: 'Algorithm management',
    tasks: 'Model fine-tuning',
    datasetLabel: 'Online annotation',
    batch: 'Batch forecasting',
    modelReason: 'Model inference',
    modelCustom: 'Model customization',
    models: 'Model Library',
    algorithmAuth: 'Algorithmic authorization',
    resource: 'resource management',
  },
  explore: 'Explore',
  tools: 'Tool',
  plugins: 'Plug-ins',
  modelDevelop: 'Model development',
  apps: 'Apply',
  newDataset: 'Create a knowledge base',
  appMarket: 'Application Square',
  datasets: 'Knowledge base',
  pluginsTips: 'Integrate third-party plugins or create ChatGPT-compatible AI plugins.',
  systemTool: 'System tools',
  newApp: 'Create an app',
  return: 'return',
  appDevelop: 'Application development',
  videos: 'Video Library',
  dataStatistics: 'Data statistics',
}

export default translation
