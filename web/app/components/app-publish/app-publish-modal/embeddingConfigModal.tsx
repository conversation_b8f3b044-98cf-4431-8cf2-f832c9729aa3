'use client'
import { useCallback, useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { useMount } from 'ahooks'
import { debounce } from 'lodash-es'
import { Form, Input, Radio, Select, Checkbox } from 'antd'

import Modal from '../../base/modal'
import Button from '../../base/button'
import EmbeddedOption from '@/app/components/app/embedded/embedded-option'

import style from './styles/style.module.css'
import cn from '@/utils/classnames'

type EmbeddingConfigModalProps = {
  appPublishConfig: any
  onCancel: () => void
  onSave: (params: object) => void
}

const EmbeddingConfigModal = ({
  appPublishConfig,
  onCancel,
  onSave
}: EmbeddingConfigModalProps) => {
  const { t } = useTranslation()
  const [form] = Form.useForm()
  const values = Form.useWatch([], form)

  // 应用类别列表
  const [appCategoriesList, setAppCategoriesList] = useState<Array<any>>([])
  // 是否可提交
  const [disabled, setDisabled] = useState(false)

  // 表单初始化
  useEffect(() => {
    form.setFieldsValue({
      embedding_method: appPublishConfig?.embedding_method || 'iframe'
    })
  }, [])
  
  const handleSave = () => {
    const params = values
    if(onSave)
      onSave(params)
  }

  useEffect(debounce(() => {
    form.validateFields({ validateOnly: true }).then(() => {
      setDisabled(false)
    },
    ).catch(() => {
      setDisabled(true)
    })
  }, 100), [form, values])

  return (
    <Modal
      isShow
      closable
      onClose={onCancel}
      title={t('app.publish.methodOption.embed')}
      footer={
        <>
          <Button onClick={onCancel} className="mr-4" variant={'secondary-accent'}>{ t('common.operation.cancel') }</Button>
          <Button disabled={disabled} onClick={handleSave} variant={'primary'}>{ t('common.operation.save') }</Button>
        </>
      }
    >
        <Form form={form} layout='vertical' initialValues={{
          embedding_method: 'iframe'
        }}>
          <Form.Item
            name="embedding_method"
            label={t('app.publish.embed.embeddingMethod')}
            validateFirst={true}
            rules={[{ required: true }]}
            validateTrigger='onChange'
          >
            <div className='flex flex-wrap items-center justify-between gap-3'>
              <EmbeddedOption
                value={values?.embedding_method || 'iframe'}
                onSelect={(option) => {
                  form.setFieldValue('embedding_method', option)
                }}
                showRadio
              />
            </div>
          </Form.Item>
        </Form>
    </Modal>
  )
}

export default EmbeddingConfigModal