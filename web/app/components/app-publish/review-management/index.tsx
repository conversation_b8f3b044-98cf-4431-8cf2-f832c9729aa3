'use client'
import React, { useCallback, useEffect, useState } from 'react'
import useSWR from 'swr'
import { useMount } from 'ahooks'
import { Input, Pagination, Table } from 'antd'
import { useTranslation } from 'react-i18next'
import { useContext } from 'use-context-selector'
import type { TableColumnsType } from 'antd'
import useTimestamp from '@/hooks/use-timestamp'

import { ModifyPublishAppStatus, fetchMarketCategories, getPublishAppList, unPublishAppspublic } from '@/service/market'

import { ToastContext } from '@/app/components/base/toast'
import Indicator from '@/app/components/base/indicator'
import TextButton from '@/app/components/base/button/text-button'
import Confirm from '@/app/components/base/confirm'
import type { statusEnum } from '@/app/components/app/app-publish-config/type'
import { statusMap } from '@/app/components/app/app-publish-config/type'
import { APP_PAGE_LIMIT } from '@/config'
import { clickExperience } from '@/app/components/app-square/utils'

const i18nPrefix = 'app.info'

const AppPublishReviewManagement = () => {
  const { t } = useTranslation()
  // const { push } = useRouter()
  const { notify } = useContext(ToastContext)
  const { formatTime } = useTimestamp()
  // 当前页
  const [currPage, setCurrPage] = useState<number>(1)
  // 应用类别列表
  const [appCategoriesList, setAppCategoriesList] = useState<Array<any>>([])
  const [showConfirmUnpublish, setShowConfirmUnpublish] = useState(false)
  const [currentApp, setCurrentApp] = useState<any>({})
  // 拒绝通过/下架原因
  const [failedMessage, setFailedMessage] = useState<string>('')

  useEffect(() => {
    setFailedMessage('')
  }, [showConfirmUnpublish])

  const defaultValue = '-'

  const query = {
    page: currPage,
    limit: APP_PAGE_LIMIT,
  }

  // 获取应用市场应用列表
  const { data: dataList, mutate: mutateDataList, isLoading } = useSWR(() => ({
    url: '/market/appsrelease',
    params: query,
  }), getPublishAppList)

  // 获取应用种类列表
  const getAppCategoriesList = async () => {
    await fetchMarketCategories().then((res) => {
      setAppCategoriesList(
        res.map(item => ({
          value: item.id,
          label: item.name,
        })),
      )
    })
  }

  const total = dataList?.total

  // 表格列
  const columns: TableColumnsType<any> = [
    // 应用名称
    {
      title: t(`${i18nPrefix}.name`),
      key: 'name',
      render: (_: any, data) => {
        return data.create_data.name || defaultValue
      },
      width: 100,
      ellipsis: true,
    },
    // 应用描述
    {
      title: t(`${i18nPrefix}.description`),
      key: 'description',
      render: (_: any, data) => {
        return data.create_data.description || defaultValue
      },
      width: 150,
      ellipsis: true,
    },
    // 标签
    {
      title: t(`${i18nPrefix}.category`),
      key: 'category',
      render: (_: any, data) => {
        return data.create_data.category_id ? 
        data.create_data.category_id.split(',')
          .map((id: string) => appCategoriesList.find(category => category.value === id)?.label)
          .filter((label: string) => label !== undefined)
          .join()
        : '-'
      },
      width: 150,
      ellipsis: true,
    },
    // 创建时间
    {
      title: t(`${i18nPrefix}.createAt`),
      key: 'time',
      render: (_: any, data) => {
        return formatTime(data.created_at, t('common.dateFormat.dateTime') as string)
      },
      width: 190,
    },
    // 状态
    {
      title: t(`${i18nPrefix}.status`),
      key: 'status',
      render: (_: any, data) => {
        return (
          <Indicator
            color={statusMap[data.market_status as statusEnum]?.color}
            textClassName='text-S3 leading-H3'
          >
            <div className='flex items-center'>
              {t(statusMap[data.market_status as statusEnum]?.text) || data.market_status}
            </div>
          </Indicator>
        )
      },
      width: 100,
      ellipsis: true,
    },
    // 操作
    {
      title: t(`${i18nPrefix}.action`),
      render: (_: any, data) => (
        <div className='flex gap-6'>
          {/* 体验应用 */}
          <TextButton size='middle'
            onClick={() => clickExperience({
              id: data.pubilc_id,
              site_code: data.site_code,
            })}
            disabled={checkDisabled(data, 'experience')}
          >
            {t('app.action.experienceApp')}
          </TextButton>
          {/* 应用详情 */}
          {/* <TextButton size='middle' onClick={()=>clickDetail(data)} disabled={checkDisabled(data, 'detail')}>
            {t(`${i18nPrefix}.action.appDetail`)}
          </TextButton> */}
          {/* 上线应用 */}
          <TextButton size='middle' onClick={() => clickPublish(data)} disabled={checkDisabled(data, 'publish')}>
            {t('app.action.publishApp')}
          </TextButton>
          {/* 下线应用/拒绝通过 */}
          <TextButton size='middle' onClick={() => ClickUnpublish(data)} disabled={checkDisabled(data, 'unPublish')}>
            {data.market_status === 'review' ? t('app.action.reject') : t('app.action.unPublish')}
          </TextButton>
          {/* <TextButton size='middle'>设为精选</TextButton> */}
        </div>
      ),
      key: 'operation',
      align: 'left',
      width: 300,
    },
  ]

  // // 应用详情
  // const clickDetail = (app: any) => {
  //   let path = 'configuration'
  //   if(app.mode === 'workflow' || app.mode === 'advanced-chat')
  //     path = 'workflow'
  //   window.open(`/app/${app.app_id}/${path}`, '_blank')
  // }

  // 审核通过
  const clickPublish = async (app: any) => {
    try {
      await ModifyPublishAppStatus({
        id: app.id,
        market_status: 'online',
        failed_message: '',
      })
      notify({
        type: 'success',
        message: t('app.notify.successPublishApp'),
      })
      mutateDataList()
    }
    catch {

    }
  }

  // 下线应用/拒绝通过
  const ClickUnpublish = useCallback((app: any) => {
    setShowConfirmUnpublish(true)
    setCurrentApp(app)
  }, [])

  // 申请上线：不通过; 已上线：下架应用
  const handleUnPublish = async () => {
    // 申请上线：不通过
    if (currentApp.market_status === 'review') {
      try {
        await ModifyPublishAppStatus({
          id: currentApp.id,
          market_status: 'failed',
          failed_message: failedMessage,
        })
        notify({
          type: 'success',
          message: t('app.notify.unPublishSuccess'),
        })
        mutateDataList()
      }
      catch {
      }
    }
    else {
      // 已上线：下架应用
      try {
        await unPublishAppspublic(currentApp.app_id, {
          failed_message: failedMessage,
        })
        notify({
          type: 'success',
          message: t('app.notify.unPublishSuccess'),
        })
        mutateDataList()
      }
      catch {
      }
    }
    setShowConfirmUnpublish(false)
  }

  // 判断按钮是否禁用
  const checkDisabled = useCallback((app: any, action: string) => {
    if (action === 'experience') {
      // 体验应用
      return app.market_status !== 'online'
    }
    else if (action === 'publish') {
      // 上线应用
      return app.market_status !== 'review'
    }
    else if (action === 'unPublish') {
      // 下线应用
      return ['pending', 'offline', 'failed'].includes(app.market_status)
    }
    else if (action === 'detail') {
      // 应用详情
      return false
    }
    return true
  }, [])

  useMount(() => {
    getAppCategoriesList()
  })

  return (
    <div className='p-8'>
      <Table
        size='middle'
        loading={isLoading}
        columns={columns}
        pagination={false}
        scroll={{x: '100%'}}
        rowKey='id'
        dataSource={dataList?.data || []}
      />
      {/* 分页组件 */}
      <Pagination
        className='mt-3'
        align='end'
        current={currPage}
        hideOnSinglePage
        onChange={setCurrPage}
        total={total}
        pageSize={APP_PAGE_LIMIT}
        showQuickJumper={false}
        showSizeChanger={false}
      />
      <Confirm
        title={currentApp.market_status === 'review' ? t('app.action.reject') : t('app.action.unPublish')}
        content={(
          <div>
            {
              currentApp.market_status === 'online'
              && <div className='text-gray-G2'>{t('app.publish.tip.unPublishTip')}</div>
            }
            <Input
              placeholder={t('app.placeholder.failMessagePlaceholder') as string}
              maxLength={100}
              onChange={e => setFailedMessage(e.target.value)}
              className='mt-2'
            ></Input>
          </div>
        )}
        isDisabled={!failedMessage}
        isShow={showConfirmUnpublish}
        onCancel={() => setShowConfirmUnpublish(false)}
        onConfirm={handleUnPublish}
      ></Confirm>
    </div>
  )
}

export default AppPublishReviewManagement
