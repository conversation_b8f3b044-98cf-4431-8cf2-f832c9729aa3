import type { LLMNodeType } from './types'

// 工作流公共能力
import { ALL_CHAT_AVAILABLE_BLOCKS, ALL_COMPLETION_AVAILABLE_BLOCKS } from '@/app/components/workflow/constants/index'
import type { NodeDefault, PromptItem } from '@/app/components/workflow/types'
import { BlockEnum, EditionType, PromptRole, TextVarFormat } from '@/app/components/workflow/types'
import { DATASET_DEFAULT } from '@/config'
import { RETRIEVE_TYPE, RerankingModeEnum } from '@/types/datasets'
import { DEFAULT_WEIGHTED_SCORE } from '@/config/dataset'

const i18nPrefix = 'workflow.errorMsg'

const nodeDefault: NodeDefault<LLMNodeType> = {
  defaultValue: {
    // 模型信息
    model: {
      provider: '',
      name: '',
      mode: 'chat',
      completion_params: {
        temperature: 0.7,
      },
      outputs: {},
      response_format: TextVarFormat.Markdown,
    },
    // 提示词内容
    prompt_template: [{
      role: PromptRole.system,
      text: '',
      new_level: true,
    }, {
      role: PromptRole.user,
      new_level: true,
      text: '',
    }],
    // 上下文配置
    context: {
      enabled: false,
      variable_selector: [],
    },
    // 视觉开关
    vision: {
      enabled: false,
    },
    // 工具选择
    tools: [],
    // 知识库配置
    dataset_configs: {
      retrieval_model: RETRIEVE_TYPE.multiWay,
      reranking_model: {
        reranking_provider_name: '',
        reranking_model_name: '',
      },
      reranking_mode: RerankingModeEnum.RerankingModel,
      reranking_enable: true,
      weights: {
        vector_setting: {
          vector_weight: DEFAULT_WEIGHTED_SCORE.other.semantic,
          embedding_provider_name: '',
          embedding_model_name: '',
        },
        keyword_setting: {
          keyword_weight: DEFAULT_WEIGHTED_SCORE.other.keyword,
        },
      },
      top_k: DATASET_DEFAULT.top_k,
      score_threshold_enabled: false,
      score_threshold: DATASET_DEFAULT.score_threshold,
      datasets: {
        datasets: [],
      },
    },
    // 工具缺失
    tool_lack: false,
  },
  // 获取有效的上一个节点
  getAvailablePrevNodes(isChatMode: boolean) {
    const nodes = isChatMode
      ? ALL_CHAT_AVAILABLE_BLOCKS
      : ALL_COMPLETION_AVAILABLE_BLOCKS.filter(type => type !== BlockEnum.End)
    return nodes
  },
  // 获取有效的下一个节点
  getAvailableNextNodes(isChatMode: boolean) {
    const nodes = isChatMode ? ALL_CHAT_AVAILABLE_BLOCKS : ALL_COMPLETION_AVAILABLE_BLOCKS
    return nodes
  },
  // 确认节点配置是否有效
  checkValid(payload: LLMNodeType, t: any) {
    let errorMessages = ''
    // 未选择模型
    if (!errorMessages && !payload.model.provider)
      errorMessages = t(`${i18nPrefix}.fieldRequired`, { field: t(`${i18nPrefix}.fields.model`) })
    // 知识库缺失
    if (!errorMessages && payload.dataset_configs?.lack)
      errorMessages = t(`${i18nPrefix}.lackDataset`)
    // 工具缺失
    if (!errorMessages && payload.tool_lack)
      errorMessages = t(`${i18nPrefix}.lackTool`)
    // 没有打开记忆，且所有提示词都是空的
    if (!errorMessages && !payload.memory) {
      const isChatModel = payload.model.mode === 'chat'
      const isPromptEmpty = isChatModel
        ? !(payload.prompt_template as PromptItem[]).some((t) => {
          if (t.edition_type === EditionType.jinja2)
            return t.jinja2_text !== ''

          return t.text !== ''
        })
        : ((payload.prompt_template as PromptItem).edition_type === EditionType.jinja2 ? (payload.prompt_template as PromptItem).jinja2_text === '' : (payload.prompt_template as PromptItem).text === '')
      if (isPromptEmpty)
        errorMessages = t(`${i18nPrefix}.fieldRequired`, { field: t('workflow.nodes.llm.prompt') })
    }
    // 打开记忆，且记忆提示词内容中，不包含sys.query
    if (!errorMessages && !!payload.memory) {
      const isChatModel = payload.model.mode === 'chat'
      // payload.memory.query_prompt_template not pass is default: {{#sys.query#}}
      if (isChatModel && !!payload.memory.query_prompt_template && !payload.memory.query_prompt_template.includes('{{#sys.query#}}'))
        errorMessages = t('workflow.nodes.llm.sysQueryInUser')
    }
    // 检查提示词jinja的变量列表
    if (!errorMessages) {
      const isChatModel = payload.model.mode === 'chat'
      const isShowVars = (() => {
        if (isChatModel)
          return (payload.prompt_template as PromptItem[]).some(item => item.edition_type === EditionType.jinja2)
        return (payload.prompt_template as PromptItem).edition_type === EditionType.jinja2
      })()
      // 检查jinjia的变量列表
      if (isShowVars && payload.prompt_config?.jinja2_variables) {
        payload.prompt_config?.jinja2_variables.forEach((i) => {
          if (!errorMessages && !i.variable)
            errorMessages = t(`${i18nPrefix}.fieldRequired`, { field: t(`${i18nPrefix}.fields.variable`) })
          if (!errorMessages && !i.value_selector.length)
            errorMessages = t(`${i18nPrefix}.fieldRequired`, { field: t(`${i18nPrefix}.fields.variableValue`) })
        })
      }
    }
    // 视觉开关打开，但是没有选择视觉变量
    if (!errorMessages && payload.vision?.enabled && !payload.vision.configs?.variable_selector?.length)
      errorMessages = t(`${i18nPrefix}.fieldRequired`, { field: t(`${i18nPrefix}.fields.visionVariable`) })
    return {
      isValid: !errorMessages,
      errorMessage: errorMessages,
    }
  },
}

export default nodeDefault
