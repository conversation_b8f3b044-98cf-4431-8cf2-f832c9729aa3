import { getFullWsUrl } from '../utils'
export class ASRService {
  wsUrl: string
  authData: object
  req_id: string
  rec_status: number
  option: {
    sample_rate: number
    enable_punctuation: boolean
    enable_inverse_text_normalization: boolean
  }
  total_text: string
  socket: WebSocket | null

  constructor(wsUrl: string, authData: object, req_id: string) {
    this.wsUrl = wsUrl
    this.authData = authData
    this.req_id = req_id // 请求全局唯一 ID
    this.rec_status = 0 // 0：开始识别；1：发送语音流；2：结束语音流
    this.option = {
      sample_rate: 16000,
      enable_punctuation: true,
      enable_inverse_text_normalization: true,
    }
    this.total_text = '' // 存储最终识别结果
    this.socket = null
  }

  async init(): Promise<void> {
    try {
      this.socket = new WebSocket(this.wsUrl)
      // this.socket.onmessage = this.onMessage.bind(this)
      // this.socket.onclose = this.onClose.bind(this)
      // this.socket.onerror = this.onError.bind(this)
    } catch (error) {
      this.socket = new WebSocket(getFullWsUrl(this.wsUrl))
    }
    this.socket.onopen = this.onOpen.bind(this)
  }

  onOpen(): void {
    this.sendStartRecognitionMessage()
  }

  // onMessage(event: MessageEvent): void {
  //   const data = event.data
  //   console.log('====================================')

  //   console.log('收到消息:', data)
  //   const response = JSON.parse(data)
  //   console.log('解析后的响应:', response)

  //   if ('res_status' in response) {
  //     if (response.res_status === 3) {
  //       console.log('识别结果:', response.data.results[0].text)
  //       this.total_text += response.data.results[0].text // 累加识别结果
  //     } else if (response.res_status === 1) {
  //       console.log(
  //         '识别到有效语音:',
  //         response.res_status,
  //         response.data.results[0].text
  //       )
  //     } else if (response.res_status === 2) {
  //       console.log(
  //         '处理中状态:',
  //         response.res_status,
  //         response.data.results[0].text
  //       )
  //     } else if (response.res_status === 4) {
  //       console.log('识别结束:', response.data.results[0].text)
  //       this.total_text += response.data.results[0].text // 累加识别结果
  //       console.log('最终识别结果:', this.total_text) // 输出最终识别结果
  //       this.stopRecognition() // 在识别结束后停止识别
  //     } else if (response.res_status === 0) {
  //       console.log('开始识别:', response.res_status)
  //     } else {
  //       console.log('非识别结果状态:', response.res_status)
  //     }
  //   }

  //   console.log('====================================')
  // }

  // onClose(): void {
  //   console.log('WebSocket连接已关闭')
  // }

  // onError(error: Event): void {
  //   console.error('WebSocket错误:', error)
  // }

  sendStartRecognitionMessage(): void {
    const message = {
      req_id: this.req_id,
      rec_status: this.rec_status,
      option: this.option,
      ...this.authData
    }
    this.socket?.send(JSON.stringify(message))
    console.log('已发送开始识别请求:', message)
  }

  async sendAudioStream(base64Audio: string): Promise<void> {
    if (this.socket?.readyState !== WebSocket.OPEN) return
    this.rec_status = 1 // 发送语音流
    const message = {
      req_id: this.req_id,
      rec_status: this.rec_status,
      audio_stream: base64Audio,
    }
    this.socket?.send(JSON.stringify(message))
    console.log('已发送语音流:', message)
  }
  // 停止语音识别
  stopRecognition(): void {
    if (!this.socket) {
      console.error('WebSocket 未初始化')
      return
    }

    switch (this.socket.readyState) {
      case WebSocket.CONNECTING:
        console.log('WebSocket 正在连接，等待连接建立后再发送结束语音流请求')
        const openHandler = () => {
          this.sendEndRecognitionMessage()
          this.socket?.removeEventListener('open', openHandler)
        }
        this.socket.addEventListener('open', openHandler, { once: true })
        break
      case WebSocket.OPEN:
        console.log('WebSocket 已连接，发送结束语音流请求')
        this.sendEndRecognitionMessage()
        break
      case WebSocket.CLOSING:
        console.log('WebSocket 正在关闭，无需发送结束语音流请求')
        break
      case WebSocket.CLOSED:
        console.log('WebSocket 已关闭，无需发送结束语音流请求')
        break
      default:
        console.error('未知的 WebSocket 状态')
    }
  }
  // 发送结束语音流请求
  sendEndRecognitionMessage(): void {
    this.rec_status = 2 // 结束语音流
    const endMessage = {
      req_id: this.req_id,
      rec_status: this.rec_status,
    }
    this.socket?.send(JSON.stringify(endMessage))
    console.log('已发送结束语音流请求:', endMessage)
    this.socket?.close()
    console.log('已停止识别')
  }
}
