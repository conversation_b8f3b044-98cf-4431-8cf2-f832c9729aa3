import { createContext, useContext } from 'use-context-selector'
import { Datasets, UsageData } from './type'

type DataStatisticsType = {
  resourcePoints: UsageData
  datasetUsage: Datasets
  appUsage: UsageData
  toolUsage: UsageData
}

const DataStatisticsContext = createContext<DataStatisticsType>({
  resourcePoints: {},
  datasetUsage: {
    knowledge_creation_limit: {},
    single_knowledge_file_upload_limit: {},
    total_knowledge_storage_space: {}
  },
  appUsage: {},
  toolUsage: {}
})

export const useDataStatisticsContext = () => useContext(DataStatisticsContext)

export default DataStatisticsContext
