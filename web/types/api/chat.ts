import type { FileResponse } from '../public/file'
import type { Metadata } from '@/app/components/base/chat/chat/type'

export type WorkflowStartedResponse = {
  task_id: string
  workflow_run_id: string
  event: string
  data: {
    id: string
    workflow_id: string
    sequence_number: number
    created_at: number
  }
}
export type WorkflowFinishedResponse = {
  task_id: string
  workflow_run_id: string
  event: string
  data: {
    id: string
    workflow_id: string
    status: string
    outputs: any
    error: string
    elapsed_time: number
    total_tokens: number
    total_steps: number
    created_at: number
    created_by: {
      id: string
      name: string
      email: string
    }
    finished_at: number
    files?: FileResponse[]
  }
}
export type IterationStartedResponse = {
  task_id: string
  workflow_run_id: string
  event: string
  data: {
    id: string
    node_id: string
    metadata: {
      iterator_length: number
      iteration_id: string
      iteration_index: number
    }
    created_at: number
    extras?: any
  }
}
export type IterationFinishedResponse = {
  task_id: string
  workflow_run_id: string
  event: string
  data: {
    id: string
    node_id: string
    outputs: any
    extras?: any
    status: string
    created_at: number
    error: string
    execution_metadata: {
      parallel_id?: string
    }
  }
}
export type IterationNextResponse = {
  task_id: string
  workflow_run_id: string
  event: string
  data: {
    id: string
    node_id: string
    index: number
    output: any
    extras?: any
    created_at: number
    execution_metadata: {
      parallel_id?: string
    }
  }
}
export type NodeFinishedResponse = {
  task_id: string
  workflow_run_id: string
  event: string
  data: {
    id: string
    node_id: string
    iteration_id?: string
    node_type: string
    index: number
    predecessor_node_id?: string
    inputs: any
    process_data: any
    outputs: any
    status: string
    error: string
    elapsed_time: number
    execution_metadata: {
      total_tokens: number
      total_price: number
      currency: string
      parallel_id?: string
      parallel_start_node_id?: string
      iteration_index?: number
      iteration_id?: string
    }
    created_at: number
    files?: FileResponse[]
  }
}
export type NodeStartedResponse = {
  task_id: string
  workflow_run_id: string
  event: string
  data: {
    id: string
    node_id: string
    iteration_id?: string
    node_type: string
    index: number
    predecessor_node_id?: string
    inputs: any
    created_at: number
    extras?: any
  }
}
export type ParallelBranchStartedResponse = {
  task_id: string
  workflow_run_id: string
  event: string
  data: {
    parallel_id: string
    parallel_start_node_id: string
    parent_parallel_id: string
    parent_parallel_start_node_id: string
    iteration_id?: string
    created_at: number
  }
}
export type ParallelBranchFinishedResponse = {
  task_id: string
  workflow_run_id: string
  event: string
  data: {
    parallel_id: string
    parallel_start_node_id: string
    parent_parallel_id: string
    parent_parallel_start_node_id: string
    iteration_id?: string
    status: string
    created_at: number
    error: string
  }
}
export type TextChunkResponse = {
  task_id: string
  workflow_run_id: string
  event: string
  data: {
    text: string
  }
}

export type TextReplaceResponse = {
  task_id: string
  workflow_run_id: string
  event: string
  data: {
    text: string
  }
}
export type VoiceChannelBuild = {
  app_id: string
  channel_id: string
  server_user_id: string
  token: string
}

export type VoiceQuery = {
  channel_id: string
  created_at: number
  event: string
  message_id: string
  query: string
  server_user_id: string
}

export type VoiceTimeout = {
  event: string
  status: string
}

export type MessageEnd = {
  id: string
  metadata: Metadata
  files?: FileResponse[]
}

export type MessageReplace = {
  id: string
  task_id: string
  answer: string
  conversation_id: string
}

export type AnnotationReply = {
  id: string
  task_id: string
  answer: string
  conversation_id: string
  annotation_id: string
  annotation_author_name: string
}
export type VoiceAnswerSafetyFence = {
  channel_id: string
  created_at: number
  event: string
  message_id: string
  query: string
  safety_pass: boolean
}
export type VoiceQuerySafetyFence = {
  message: string
}
