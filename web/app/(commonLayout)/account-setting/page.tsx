'use client'
import React, { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import AccountSetting from '@/app/components/account-setting'
import { useAppContext } from '@/context/app-context'

export default function AccountSettingPage() {
  const router = useRouter()
  const { canAccountSetting } = useAppContext()

  useEffect(() => {
    if (!(canAccountSetting))
      return router.replace('/square')
  }, [canAccountSetting, router])

  return (
    <AccountSetting></AccountSetting>
  )
}
