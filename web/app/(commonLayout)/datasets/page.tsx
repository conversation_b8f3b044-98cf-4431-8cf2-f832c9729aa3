'use client'

// Libraries
import { useMemo, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import useS<PERSON> from 'swr'
import { useContext } from 'use-context-selector'

// Components
import { ConfigProvider } from 'antd'
import useAppsQueryState from '../apps/hooks/useAppsQueryState'
import Datasets from './Datasets'
import ApiServer from './ApiServer'
import Doc from './Doc'
import s from './styles/index.module.css'

// Services
import { fetchDatasetApiBaseUrl } from '@/service/datasets'

// Hooks
import { useTabSearchParams } from '@/hooks/use-tab-searchparams'
import { useAppContext } from '@/context/app-context'
import { GRAY } from '@/themes/var-define'
import SearchInput from '@/app/components/base/input/search-input'
import Button from '@/app/components/base/button'
import { useProviderContext } from '@/context/provider-context'
import { ToastContext } from '@/app/components/base/toast'
import { APP_NAME } from '@/config'

const Container = () => {
  const { t } = useTranslation()
  const router = useRouter()
  const { notify } = useContext(ToastContext)
  const { plan } = useProviderContext()
  const { canViewApp, canDataset } = useAppContext()
  const [activeTab, setActiveTab] = useTabSearchParams({
    defaultTab: 'dataset',
  })
  const isDatasetFull = plan.usage.buildDatasets >= plan.total.buildDatasets

  const { data } = useSWR(activeTab === 'dataset' ? null : '/datasets/api-base-info', fetchDatasetApiBaseUrl)
  // 路由query参数
  const { query: { tagIDs = [], keywords = '' }, setQuery } = useAppsQueryState()
  // 页签列表
  const tabList = useMemo(() => {
    return [{
      key: 'dataset',
      label: `${t('dataset.title')} - ${APP_NAME}`,
    }, ...(canViewApp ? [{ key: 'api', label: t('dataset.titleApi') }] : [])]
  }, [canViewApp, t])
  // 数据集ref
  const datasetRef = useRef({
    resetDatasetCard: (params?: { currentTag: string[]; currentKeyword: string }) => {},
  })

  // 变更关键字
  const changeKeyWord = (value: string) => {
    setQuery(prev => ({ ...prev, keywords: value }))
    datasetRef.current.resetDatasetCard({ currentKeyword: value, currentTag: tagIDs })
  }
  // 创建知识库
  const createDataset = () => {
    if (isDatasetFull)
      notify({ type: 'error', message: t('dataset.notify.datasetFull', { num: plan.total.buildDatasets }) })
    else router.push('/datasets/create')
  }

  return (
    <div className='relative flex flex-col h-full overflow-y-hidden grow'>
      {/* 头部信息 */}
      <div className={s['app-list-header']}>
        <div className={s['header-title']}>{t('dataset.title')}</div>
        { canDataset && <Button onClick={createDataset} variant={'primary'}>{t('dataset.action.createDataset')}</Button> }
      </div>
      {/* 赛选条件 */}
      <div className={s['app-list-filter']}>
        {/* <Tabs activeKey={activeTab} items={tabList} onChange={setActiveTab}></Tabs> */}
        <div className={s['search-criteria']}>
          {activeTab === 'dataset' && (
            <ConfigProvider
              theme={{
                components: {
                  Input: {
                    colorBgContainer: 'transparent',
                    colorBorder: GRAY['G3-5'],
                  },
                },
              }}
            >
              <SearchInput value={keywords} className='w-[220px]' onChange={changeKeyWord} />
            </ConfigProvider>
          )}
          {activeTab === 'api' && data && <ApiServer apiBaseUrl={data.api_base_url || ''} />}
        </div>
      </div>
      {/* 卡片列表 */}
      {activeTab === 'dataset' && (
        <Datasets ref={datasetRef} tags={tagIDs} keywords={keywords} />
      )}
      {/* 知识库文档 */}
      {activeTab === 'api' && data && <Doc apiBaseUrl={data.api_base_url || ''} />}
    </div>

  )
}

export default Container
