'use client'

import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { useAppContext } from '@/context/app-context'

export default function PublishReviewLayout({ children }: { children: React.ReactNode }) {
  const { canAdmin, isSuperUser } = useAppContext()
  const router = useRouter()

  useEffect(() => {
    if (!canAdmin && !isSuperUser)
      return router.replace('/square')
  }, [canAdmin])

  return (
    <>
      {children}
    </>
  )
}
