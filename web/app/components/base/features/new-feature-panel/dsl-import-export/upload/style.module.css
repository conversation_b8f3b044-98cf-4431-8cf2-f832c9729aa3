.uploader {
  @apply relative flex flex-col gap-[2px] justify-center items-center;
  @apply p-3 w-full min-h-[80px];
  @apply border border-gray-G5 bg-gray-G7 rounded text-S3 leading-H3;
}
.uploadIcon {
  @apply w-6 h-6 text-gray-G3;
}
.uploadTitle {
  @apply flex justify-center items-center text-gray-G2 text-S3 leading-H4 font-semibold;
}
.uploadTip {
  @apply text-S1 leading-H3 text-gray-G3;
}
.uploader .browse {
  @apply pl-1 cursor-pointer text-primary-P1;
}
.uploader.dragging {
  background: #F5F8FF;
  border: 1px dashed #B2CCFF;
}
.uploader .draggingCover {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}