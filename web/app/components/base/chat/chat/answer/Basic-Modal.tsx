'use client'

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { Checkbox, Input, List, message, Modal, Popconfirm, Skeleton, InputRef } from 'antd'
import './styles/style.modal.scss'
import { SearchOutlined, DeleteOutlined, PaperClipOutlined,DownloadOutlined} from '@ant-design/icons'
import { fetchUserProfile } from '@/service/common'
import { t } from 'i18next'
import useSWR from 'swr'
import { fetchWorkflowsRun, GetreferencefileId } from '@/service/share'
import { debounce, throttle } from 'lodash'
import TemplateDrawer from '../../chat-with-history/config-panel/comments/templateDrawer'
import { TEMPLATE_URL } from '@/config'

interface BasicModalProps {
  isOpen: boolean
  onOk: (updatedData: any[]) => void
  onCancel: () => void
  modalData: {
    item: any
    index: number
    fetchDataList: any
  }
}

const BasicModal: React.FC<BasicModalProps> = ({ isOpen, onOk, onCancel, modalData }) => {
  const [loading, setLoading] = useState(false)
  const [referenceData, setReferenceData] = useState<any[]>([])
  const [searchData, setSearchData] = useState<any[]>([])
  const [selectedItems, setSelectedItems] = useState<Set<number>>(new Set())
  const [inputValue, setInputValue] = useState('')
  const [showResults, setShowResults] = useState(false)
  const [open, setOpen] = useState(false)
  const [drawerData, setDrawerData] = useState<any>({})
  const CompositionFlag = useRef(false)
  const inputRef = useRef<InputRef | null>(null)
  // 直接在组件中获取用户数据
  // const { data: userProfileResponse } = useSWR(
  //   { url: '/account/profile', params: {} },
  //   fetchUserProfile
  // )
  // const [userProfile, setUserProfile] = useState<any>(null)

  // useEffect(() => {
  //   const getUserProfile = async () => {
  //     if (userProfileResponse && !userProfileResponse.bodyUsed) {
  //       const result = await userProfileResponse.json()
  //       setUserProfile(result)
  //     }
  //   }
  //   getUserProfile()
  // }, [userProfileResponse])

  // // 在数据加载完成后打印验证
  // useEffect(() => {
  //   if (userProfile) {
  //     console.log('用户数据已加载:', userProfile)
  //   }
  // }, [userProfile])

  useEffect(() => {
    setReferenceData([])
    if (modalData.fetchDataList && modalData.fetchDataList[modalData.index]) {
      const data = modalData.fetchDataList[modalData.index].reference_content || []
      setReferenceData(data)
      setSelectedItems(new Set(data.map((_: any, index: number) => index)))
    }
  }, [modalData])

  const handleDelete = (index: number) => {
    const newData = [...referenceData]
    newData.splice(index, 1)
    setReferenceData(newData)

    const newSelected = new Set(selectedItems)
    newSelected.delete(index)
    const adjustedSelected = new Set<number>()
    newSelected.forEach(item => {
      if (item < index) adjustedSelected.add(item)
      else if (item > index) adjustedSelected.add(item - 1)
    })
    setSelectedItems(adjustedSelected)
  }

  const handleCheckboxChange = (index: number, checked: boolean) => {
    const newSelected = new Set(selectedItems)
    if (checked) {
      newSelected.add(index)
    } else {
      newSelected.delete(index)
    }
    setSelectedItems(newSelected)
  }

  const handleCheckboxItem = (index: number, checked: boolean, item: any) => {
    if (checked) {
      setReferenceData([item, ...referenceData]);
      message.success(t('sampleTemplate.longText.DOMCommentPlaceholderSuccess')!);
    } else {
      setReferenceData(referenceData.filter((_, i) => i !== index))
    }
  }

  const fetchData = throttle(async (value: string) => {
    if (!value.trim()) return

    const dat = {
      request_type: t('sampleTemplate.interface.KnowledgeBaseRetrieval')
    }
    const data = {
      conversation_id: '',
      inputs: {
        sample: JSON.stringify(dat)
      },
      parent_message_id: null,
      query: value,
      response_mode: 'streaming'
    }
    const res = (await fetchWorkflowsRun(data)) as string
    const sanitizedRes = res.replace(/[\x00-\x1F\x7F]/g, '')
    try {
      const parsedRes = JSON.parse(sanitizedRes)

      if (parsedRes.result.length > 0) {
        setSearchData(parsedRes.result)
      } else {
        message.error(t('sampleTemplate.longText.PromptPlaceholder8')!)
      }
    } catch (error) {
      console.error('Error parsing JSON:', error)
    }
  }, 3000)

  const Composition = (e: React.CompositionEvent<HTMLInputElement>) => {
    switch (e.type) {
      case 'compositionstart':
        CompositionFlag.current = true
        break
      case 'compositionend':
        CompositionFlag.current = false
        debounceSearch((e.target as HTMLInputElement).value)
        break
    }
  }

  const debounceSearch = useCallback(
    debounce((e: string) => {
      fetchData(e)
    }, 800),
    []
  )

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value)
    if (!CompositionFlag.current) {
      debounceSearch(e.target.value)
    }
  }

  const handleFocus = () => {
    setShowResults(true)
  }

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    if (!e.relatedTarget?.closest('.result-list')) {
      setShowResults(false)
    }
  }

  const handleMouseDownOnList = (e: React.MouseEvent) => {
    e.preventDefault()
  }

  const handleOk = () => {
    let updatedDataList = []
    updatedDataList = [...modalData.fetchDataList]
    updatedDataList[modalData.index] = {
      ...updatedDataList[modalData.index],
      reference_content: referenceData
    }
    onOk(updatedDataList)
  }

  const LookDocumentItem = async (item: any) => {
    const res = await GetreferencefileId(item.doc_id)
    if (res) {
      const data = {
        file_name: item.title,
        file_path: `${TEMPLATE_URL}files/${res}/file-preview-look`,
        file_id: res
      }
      setDrawerData(data)
      setOpen(true)
    }
  }

  const LookDocumentItemDown = async (item: any) => {
    const res = await GetreferencefileId(item.doc_id)
    if (res) {
      let type="docx";
      let ar = item.title.split('.');
      let title = item.title;
      if(ar.length>1){
        type=ar[1];
        title=ar[0];
      }
      let pathurl = `${TEMPLATE_URL}files/${res}/file-preview-look`
      const a = document.createElement('a')
      a.href = pathurl
      a.download = title+ '.' + type // 使用提取的文件名
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
    }
  }

  const onClose = () => {
    setOpen(false)
  }

  return (
    <div className="modal-relative">
      <TemplateDrawer open={open} drawerData={drawerData} onClose={onClose} placement="left"></TemplateDrawer>
      <Modal
        title={t('sampleTemplate.BasicModal.ContentTitle') || ''}
        open={isOpen}
        onOk={handleOk}
        onCancel={onCancel}
        width={820}
        loading={loading}
      >
        <div className="px-6">
          <Input
            size="large"
            allowClear
            ref={inputRef}
            value={inputValue}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onChange={handleInputChange}
            onCompositionStart={Composition}
            onCompositionEnd={Composition}
            placeholder={t('sampleTemplate.BasicModal.InputPlaceholder') || ''}
            prefix={<SearchOutlined style={{ fontSize: '20px', marginRight: '10px' }} />}
          />
          {showResults && (
            <div
              className="result-list"
              onMouseDown={handleMouseDownOnList} // 捕获点击，阻止失焦
              style={{
                position: 'absolute',
                top: '20%',
                left: '50%',
                transform: 'translateX(-50%)',
                width: '95%',
                height: '280px',
                background: 'white',
                boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
                zIndex: 10,
                padding: '20px',
                borderRadius: '4px',
                overflowY: 'auto'
              }}
            >
              {searchData.length > 0 ? (
                <div className="">
                  {searchData.map((item: any, index: number) => (
                    <div
                      key={index}
                      className="p-4 border hover:bg-gray-50 cursor-pointer mb-2 rounded-xl group"
                    >
                      <div className="flex justify-between items-center">
                        <div className="font-medium text-lg text-gray-900 truncate">{item.title}</div>
                        <div className="flex items-center gap-2">
                          <Checkbox
                            style={{
                              width: '16px',
                              height: '16px',
                              verticalAlign: 'middle'
                            }}
                            checked={referenceData.some((data: any) => data.title === item.title)}
                            onClick={e => e.stopPropagation()}
                            onChange={e => {
                              const dat = {
                                doc_id: item.metadata.document_id,
                                title: item.title,
                                content: item.content
                              }
                              handleCheckboxItem(index, e.target.checked, dat)
                            }}
                          />
                        </div>
                      </div>
                      <div className="text-sm text-gray-400 mt-1 line-clamp-4 h-[80px]">
                        {item.content}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <Skeleton
                  active
                  paragraph={{
                    rows: 6 // 设置行数
                  }}
                />
              )}
            </div>
          )}
          <div className="mt-4 h-[350px] overflow-y-auto">
            {referenceData.map((item: any, index: number) => (
              <div
                key={index}
                className="p-4 border hover:bg-gray-50 cursor-pointer mb-2 rounded-xl group"
              >
                <div className="flex justify-between items-center">
                  <div className="font-medium text-lg text-gray-900 truncate">{item.title}</div>
                  <div className="flex items-center gap-2">

                  <DownloadOutlined
                      onClick={() => {
                        LookDocumentItemDown(item)
                      }}
                      style={{
                        fontSize: '16px',
                        fontWeight: 'bold',
                        lineHeight: '1'
                      }}
                      className=" hidden group-hover:inline-block mr-2"
                    />

                    <PaperClipOutlined
                      onClick={() => {
                        LookDocumentItem(item)
                      }}
                      style={{
                        fontSize: '16px',
                        fontWeight: 'bold',
                        lineHeight: '1'
                      }}
                      className=" hidden group-hover:inline-block mr-2"
                    />

                    <Popconfirm
                      placement="left"
                      description={t('sampleTemplate.BasicModal.DeleteConfirmDescription')}
                      title={t('sampleTemplate.BasicModal.DeleteConfirm')}
                      okText="Yes"
                      cancelText="No"
                      overlayClassName="custom-popconfirm"
                      onConfirm={() => {
                        handleDelete(index)
                      }}
                    >
                      <DeleteOutlined
                        className="text-gray-400 hidden group-hover:inline-block group-hover:text-red-600"
                        style={{
                          fontSize: '16px',
                          lineHeight: '1'
                        }}
                      />
                    </Popconfirm>
                  </div>
                </div>
                <div className="text-sm text-gray-400 mt-1 line-clamp-4 h-[80px]">{item.content}</div>
              </div>
            ))}
          </div>
        </div>
      </Modal>
    </div>
  )
}

export default BasicModal
