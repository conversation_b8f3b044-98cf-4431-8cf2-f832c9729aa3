'use client'
import type { FC } from 'react'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
// import { Resizable, ResizableBox, ResizeCallbackData } from 'react-resizable'
// import 'react-resizable/css/styles.css'
import { useTranslation } from 'react-i18next'
import { useContext } from 'use-context-selector'
import { usePathname } from 'next/navigation'
import produce from 'immer'
import { useBoolean, useGetState } from 'ahooks'
import { clone, debounce, isEqual } from 'lodash-es'
import { useShallow } from 'zustand/react/shallow'
import { Divider } from 'antd'
import cn from 'classnames'
import ResizableBox from '../../base/resizable-box'
import Button from '../../base/button'
import Tooltip from '../../base/tooltip'
import { useAppPublishInit } from '../../app-publish/hooks/use-app-publish'
import AddDatasetModal from '../../datasets/common/add-dataset-modal'
import AppSaveBtn from './operation/save'
import useAdvancedPromptConfig from '@/app/components/app/configuration/hooks/use-advanced-prompt-config'
import useStoragedModelConfig from '@/app/components/app/configuration/hooks/use-storaged-model-config'
import EditHistoryModal from '@/app/components/app/configuration/config-prompt/conversation-history/edit-modal'
import AppPublishModal from '@/app/components/app-publish/app-publish-modal'
import {
  useDebugWithSingleOrMultipleModel,
  useFormattingChangedDispatcher,
} from '@/app/components/app/configuration/debug/hooks'
import type { ModelAndParameter } from '@/app/components/app/configuration/debug/types'
import Loading from '@/app/components/base/loading'
import type {
  DatasetConfigs,
  Inputs,
  ModelConfig,
  PromptConfig,
  PromptVariable,
} from '@/models/debug'
import type { ExternalDataTool } from '@/models/common'
import type { DataSet } from '@/models/datasets'
import { RETRIEVE_TYPE } from '@/types/datasets'
import type { ModelConfig as BackendModelConfig } from '@/types/model'
import ConfigContext from '@/context/debug-configuration'
import AppConfig from '@/app/components/app/configuration/app-config'
import FeatureList from '@/app/components/app/configuration/feature-list'
import Debug from '@/app/components/app/configuration/debug'
import { ModelFeatureEnum } from '@/app/components/account-setting/model-provider-page/declarations'
import { ToastContext } from '@/app/components/base/toast'
import { fetchAppChatBgConfig, getYjyTtsVoices, saveAppChatBgConfig, updateAppModelConfig } from '@/service/apps'
import { userInputsFormToPromptVariables } from '@/utils/model-config'
import { fetchDatasets } from '@/service/datasets'
import { publishApp } from '@/service/market'
import { useProviderContext } from '@/context/provider-context'
import { AppType } from '@/types/app'
import { AdvancedParamModeEnum, AgentStrategy, BasicParamModeEnum, ModelModeType, Resolution } from '@/types/model'

import { PromptMode } from '@/models/debug'
import { DATASET_DEFAULT, DEFAULT_AGENT_SETTING, DEFAULT_CHAT_PROMPT_CONFIG, DEFAULT_COMPLETION_PROMPT_CONFIG } from '@/config'
import { useModalContext } from '@/context/modal-context'
import ModelParameterModal from '@/app/components/account-setting/model-provider-page/model-parameter-modal'
import type { FormValue } from '@/app/components/account-setting/model-provider-page/declarations'
import { CollectionType } from '@/app/components/tools/types'
import { useTextGenerationCurrentProviderAndModelAndModelList } from '@/app/components/account-setting/model-provider-page/hooks'
import { fetchCollectionList } from '@/service/tools'
import { type Collection } from '@/app/components/tools/types'
import { useStore as useAppStore } from '@/app/components/app/store'
import {
  getMultipleRetrievalConfig,
} from '@/app/components/workflow/nodes/knowledge-retrieval/utils'
import style from '@/app/components/app/configuration/styles/style.module.scss'
import { FeaturesProvider } from '@/app/components/base/features'
import { type AppChatBgConfig, FeatureEnum, type Features as FeaturesData, type FileUpload } from '@/app/components/base/features/types'
import { FILE_EXTS } from '@/app/components/base/prompt-editor/constants'
import { SupportUploadFileTypes } from '@/app/components/workflow/types'

import type { NewLanguageType, NewVoiceConfigType } from '@/models/app'
import { useSystemContext } from '@/context/system-context'

type PublishConfig = {
  modelConfig: ModelConfig
  completionParams: FormValue
  basicParamMode?: BasicParamModeEnum
}

const Configuration: FC = () => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  const { isPrivate } = useSystemContext()
  const { appDetail, setAppSiderbarExpand } = useAppStore(useShallow(state => ({
    appDetail: state.appDetail,
    setAppSiderbarExpand: state.setAppSiderbarExpand,
  })))
  const formattingChangedDispatcher = useFormattingChangedDispatcher()
  const { setShowAccountSettingModal } = useModalContext()
  // 最后更新时间
  const { isAPIKeySet } = useProviderContext()

  const pathname = usePathname()
  const matched = pathname.match(/\/app\/([^/]+)/)
  // 应用id
  const appId = (matched?.length && matched[1]) ? matched[1] : ''
  // 缓存的模型配置信息
  const {
    updateStoragedModelConfigByKey,
    getModelConfigStoraged,
  } = useStoragedModelConfig({
    appId,
  })
  // 应用发布信息
  const {
    getAppPublishInfoData,
    appPublishInfo,
  } = useAppPublishInit()
  const {
    bestModel,
    debugWithMultipleModel,
    multipleModelConfigs,
    handleMultipleModelConfigsChange,
  } = useDebugWithSingleOrMultipleModel(appId)

  // 应用配置是否变更
  const [formattingChanged, setFormattingChanged] = useState(false)
  // 是否已经获取到详情
  const [hasFetchedDetail, setHasFetchedDetail] = useState(false)
  // 应用模式
  const [mode, setMode] = useState('')
  // 应用配置
  const [prevPromptConfig, setPrevPromptConfig] = useState<PromptConfig>({
    prompt_template: '',
    prompt_variables: [],
  })
  const [externalDataToolsConfig, setExternalDataToolsConfig] = useState<ExternalDataTool[]>([])
  // 预览时用户输入
  const [inputs, setInputs] = useState<Inputs>({})
  const [_, setTempStop, getTempStop] = useGetState<string[]>([])
  // 模型参数
  const [completionParams, doSetCompletionParams] = useState<FormValue>({})
  // 模型模式
  const [basicParamMode, setBasicParamMode] = useState<BasicParamModeEnum>(BasicParamModeEnum.normal)
  // 模型配置
  const [modelConfig, doSetModelConfig] = useState<ModelConfig>({
    provider: '',
    model_id: '',
    mode: ModelModeType.unset,
    configs: {
      prompt_template: '',
      prompt_variables: [] as PromptVariable[],
    },
    more_like_this: null,
    opening_statement: '',
    suggested_questions: [],
    sensitive_word_avoidance: null,
    speech_to_text: null,
    text_to_speech: null,
    file_upload: null,
    suggested_questions_after_answer: null,
    retriever_resource: null,
    annotation_reply: null,
    dataSets: [],
    agentConfig: DEFAULT_AGENT_SETTING,
  })
  // 知识库配置
  const [datasetConfigs, setDatasetConfigs] = useState<DatasetConfigs>({
    retrieval_model: RETRIEVE_TYPE.multiWay,
    reranking_model: {
      reranking_provider_name: '',
      reranking_model_name: '',
    },
    top_k: DATASET_DEFAULT.top_k,
    score_threshold_enabled: false,
    score_threshold: DATASET_DEFAULT.score_threshold,
    datasets: {
      datasets: [],
    },
  })
  // 背景图配置
  const [chatBgConfig, setChatBgConfig] = useState<AppChatBgConfig>()
  // 语言列表
  const [languageList, setLanguageList] = useState<NewLanguageType[]>([])
  // 音色配置
  const [voicesConfigData, setVoicesConfigData] = useState<NewVoiceConfigType[]>([])
  // 默认语言
  const [languageDefaulVal, setLanguageDefaulValue] = useState<string>('')
  // 默认音色
  const [timbreDefaulValue, setTimbreDefaulValue] = useState<string>('')
  // 用于提示创作同款时原应用的知识库和工具数据无法被复制
  const [forkDatasets, setForkDatasets] = useState<DataSet[]>([])
  // 私有化工具
  const [forkTools, setForkTools] = useState<any[]>([])
  // 是否已发布过
  const [isPublished, setIsPublished] = useState<boolean>(false)
  // 工具集列表
  const [collectionList, setCollectionList] = useState<Collection[]>([])
  // 模型配置中——选中的知识库列表
  const [dataSets, setDataSets] = useState<DataSet[]>([])
  // 是否显示应用发布弹窗
  const [showAppPublishModal, setShowAppPublishModal] = useState(false)
  // 是否显示选择知识库弹窗
  const [isShowSelectDataSet, { setTrue: showSelectDataSet, setFalse: hideSelectDataSet }] = useBoolean(false)
  // 是否显示历史记录弹窗
  const [isShowHistoryModal, { setTrue: showHistoryModal, setFalse: hideHistoryModal }] = useBoolean(false)
  // 提示词模式
  const [promptMode, doSetPromptMode] = useState(PromptMode.simple)
  const [canReturnToSimpleMode, setCanReturnToSimpleMode] = useState(true)
  // 是否正在语音通话
  const [isVoiceCalling, setIsVoiceCalling] = useState(false)
  // 拖拽调整大小的控制方法
  const [featureListWidth, setFeatureListWidth] = useState(0)
  // debug面板宽度
  const [debugWidth, setDebugWidth] = useState(0)

  // 加载中
  const isLoading = !hasFetchedDetail
  // 是否为智能体应用
  const isAgent = mode === 'agent-chat'
  // 当前当前provider的文本生成模型列表
  const {
    currentModel: currModel,
    textGenerationModelList,
  } = useTextGenerationCurrentProviderAndModelAndModelList(
    {
      provider: modelConfig.provider,
      model: modelConfig.model_id,
    },
  )
  // 模型模式
  const modelModeType = modelConfig.mode
  const contextVar = modelConfig.configs.prompt_variables.find((item: any) => item.is_context_var)?.key
  const hasSetContextVar = !!contextVar
  const isAdvancedMode = promptMode === PromptMode.advanced
  // 应用初始化feature数据
  const featuresData: FeaturesData = useMemo(() => {
    return {
      moreLikeThis: modelConfig.more_like_this || { enabled: false },
      opening: {
        enabled: !!modelConfig.opening_statement,
        opening_statement: modelConfig.opening_statement || '',
        suggested_questions: modelConfig.suggested_questions || [],
      },
      moderation: modelConfig.sensitive_word_avoidance || { enabled: false },
      speech2text: modelConfig.speech_to_text || { enabled: false },
      // text2speech: modelConfig.text_to_speech || { enabled: false },
      text2speech: {
        ...modelConfig.text_to_speech,
        enabled: modelConfig?.text_to_speech?.enabled || false,
        voice_input: {
          language: languageDefaulVal, //  || 1,
          timbre: timbreDefaulValue, //  || 'yixiaobin',
          auto_play: false,
          ...modelConfig?.text_to_speech?.voice_input,
          enabled: (!isPrivate && modelConfig.text_to_speech?.voice_input?.enabled) || false,
        },
        voice_conversation: {
          language: languageDefaulVal,
          timbre: timbreDefaulValue,
          ...modelConfig?.text_to_speech?.voice_conversation,
          enabled: (!isPrivate && modelConfig.text_to_speech?.voice_conversation?.enabled) || false,
        },
      },
      file: {
        image: {
          detail: modelConfig.file_upload?.image?.detail || Resolution.high,
          enabled: !!modelConfig.file_upload?.image?.enabled,
          number_limits: modelConfig.file_upload?.image?.number_limits || 3,
          transfer_methods: modelConfig.file_upload?.image?.transfer_methods || ['local_file', 'remote_url'],
        },
        enabled: !!(modelConfig.file_upload?.enabled || modelConfig.file_upload?.image?.enabled),
        allowed_file_types: modelConfig.file_upload?.allowed_file_types || [SupportUploadFileTypes.image],
        allowed_file_extensions: modelConfig.file_upload?.allowed_file_extensions || FILE_EXTS[SupportUploadFileTypes.image].map(ext => `.${ext}`),
        allowed_file_upload_methods: modelConfig.file_upload?.allowed_file_upload_methods || modelConfig.file_upload?.image?.transfer_methods || ['local_file', 'remote_url'],
        number_limits: modelConfig.file_upload?.number_limits || modelConfig.file_upload?.image?.number_limits || 3,
      } as FileUpload,
      suggested: modelConfig.suggested_questions_after_answer || { enabled: false },
      citation: modelConfig.retriever_resource || { enabled: false },
      annotationReply: modelConfig.annotation_reply || { enabled: false },
      chatBackground: chatBgConfig,
    }
  }, [modelConfig, chatBgConfig, timbreDefaulValue, languageDefaulVal, isPrivate])
  const isFunctionCall = (() => {
    const features = currModel?.features
    if (!features)
      return false
    return features.includes(ModelFeatureEnum.toolCall) || features.includes(ModelFeatureEnum.multiToolCall)
  })()
  // 处理单模型情况下 模型参数变更
  const setCompletionParams = async (value: FormValue, useStore?: boolean) => {
    const params = { ...value }

    if ((!params.stop || params.stop.length === 0) && (modelModeType === ModelModeType.completion)) {
      params.stop = getTempStop()
      setTempStop([])
    }
    doSetCompletionParams(params)
    if (useStore) {
      const storaged = await getModelConfigStoraged(modelConfig.provider, modelConfig.model_id)
      updateStoragedModelConfigByKey(modelConfig.provider, modelConfig.model_id, 'params', {
        ...storaged?.params,
        ...params,
      })
    }
  }
  // 处理单模型情况下 模型参数模式变更
  const handleModelParamsModeChange = (key: string, value: any) => {
    updateStoragedModelConfigByKey(modelConfig.provider, modelConfig.model_id, key, value)
    if (key === 'basicParamMode')
      setBasicParamMode(value as BasicParamModeEnum)
  }
  // 变更模型配置
  const setModelConfig = (newModelConfig: ModelConfig) => {
    doSetModelConfig(newModelConfig)
  }
  // 清除知识库和工具的fork提示状态
  const clearForkStatus = () => {
    setDatasetConfigs(
      {
        ...datasetConfigs,
        fork: false,
      },
    )
    const ModelConfig = produce(modelConfig, (draft) => {
      draft.agentConfig.fork = false
    })
    setModelConfig(ModelConfig)
  }

  // 获取背景图配置
  const getChatBgConfig = async () => {
    const result = await fetchAppChatBgConfig({
      appId,
    })
    setChatBgConfig(result.data as AppChatBgConfig)
  }
  // 设置提示词模式
  const setPromptMode = async (mode: PromptMode) => {
    if (mode === PromptMode.advanced) {
      // eslint-disable-next-line @typescript-eslint/no-use-before-define
      await migrateToDefaultPrompt()
      setCanReturnToSimpleMode(true)
    }

    doSetPromptMode(mode)
  }
  // 恢复上次发布的配置
  const syncToPublishedConfig = async (_publishedConfig: PublishConfig) => {
    getChatBgConfig()
    const modelConfig = _publishedConfig.modelConfig
    setModelConfig(modelConfig)
    setDataSets(modelConfig.dataSets || [])

    const modelConfigStoraged = await getModelConfigStoraged(modelConfig.provider, modelConfig.model_id)
    if (modelConfigStoraged) {
      // 数据库存在缓存的模型配置 就调用缓存
      setCompletionParams(modelConfigStoraged.params || {}, true)
      setBasicParamMode(modelConfigStoraged.basicParamMode || BasicParamModeEnum.normal)
    }
    else {
      // 如果不存在缓存，则缓存
      setCompletionParams(_publishedConfig.completionParams, true)
      handleModelParamsModeChange('basicParamMode', _publishedConfig.basicParamMode ? _publishedConfig.basicParamMode : BasicParamModeEnum.normal)
    }
    // 如果存在其他参数配置，则缓存中配置为自定义模式
    const customAdvancedParamMode = Object.keys(_publishedConfig.completionParams).filter(item => item !== 'temperature' && item !== 'stop').length > 0
    if (customAdvancedParamMode) {
      handleModelParamsModeChange('openAdvancedParamMode', true)
      handleModelParamsModeChange('advancedParamMode', AdvancedParamModeEnum.custom)
    }
  }
  const {
    chatPromptConfig,
    setChatPromptConfig,
    completionPromptConfig,
    setCompletionPromptConfig,
    currentAdvancedPrompt,
    setCurrentAdvancedPrompt,
    hasSetBlockStatus,
    setConversationHistoriesRole,
    migrateToDefaultPrompt,
  } = useAdvancedPromptConfig({
    appMode: mode,
    modelName: modelConfig.model_id,
    promptMode,
    modelModeType,
    prePrompt: modelConfig.configs.prompt_template,
    hasSetDataSet: dataSets.length > 0,
    onUserChangedPrompt: () => {
      setCanReturnToSimpleMode(false)
    },
    completionParams,
    setCompletionParams,
    setStop: setTempStop,
  })
  const promptEmpty = (() => {
    if (mode !== AppType.completion)
      return false

    if (isAdvancedMode) {
      if (modelModeType === ModelModeType.chat)
        return chatPromptConfig.prompt.every(({ text }: any) => !text)

      else
        return !completionPromptConfig.prompt?.text
    }

    else { return !modelConfig.configs.prompt_template }
  })()
  const contextVarEmpty = mode === AppType.completion && dataSets.length > 0 && !hasSetContextVar
  // 变更模型
  const setModel = async ({
    modelId,
    provider,
    mode: modeMode,
  }: { modelId: string; provider: string; mode: string; features: string[] }) => {
    if (isAdvancedMode) {
      const appMode = mode

      if (modeMode === ModelModeType.completion) {
        if (appMode !== AppType.completion) {
          if (!completionPromptConfig.prompt?.text || !completionPromptConfig.conversation_histories_role.assistant_prefix || !completionPromptConfig.conversation_histories_role.user_prefix)
            await migrateToDefaultPrompt(true, ModelModeType.completion)
        }
        else {
          if (!completionPromptConfig.prompt?.text)
            await migrateToDefaultPrompt(true, ModelModeType.completion)
        }
      }
      if (modeMode === ModelModeType.chat) {
        if (chatPromptConfig.prompt.length === 0)
          await migrateToDefaultPrompt(true, ModelModeType.chat)
      }
    }
    const newModelConfig = produce(modelConfig, (draft: ModelConfig) => {
      draft.provider = provider
      draft.model_id = modelId
      draft.mode = modeMode as ModelModeType
    })

    setModelConfig(newModelConfig)

    const modelConfigStoraged = await getModelConfigStoraged(provider, modelId)
    // 有缓存参数配置则取缓存
    setCompletionParams(modelConfigStoraged?.params ? modelConfigStoraged.params : {})
    setBasicParamMode(modelConfigStoraged?.basicParamMode || BasicParamModeEnum.normal)
  }
  // 变更特性
  const handleFeaturesChange = useCallback((flag: any) => {
    if (flag)
      formattingChangedDispatcher()
  }, [formattingChangedDispatcher])
  // 变更添加提示词变量
  const handleAddPromptVariable = useCallback((variable: PromptVariable[]) => {
    const newModelConfig = produce(modelConfig, (draft: ModelConfig) => {
      draft.configs.prompt_variables = variable
    })
    setModelConfig(newModelConfig)
  }, [modelConfig])
  // 获取语音能力配置
  const getData = async () => {
    try {
      const data = await getYjyTtsVoices()
      const { voices_config, default_language = '', default_voice = '' } = data
      const languages = (voices_config || []).map(({ language_name = '', language_value = '' }) => ({
        label: language_name || '',
        value: language_value || '',
      }))
      const timbres = (voices_config || []).map(({ language_name = '', language_value = '', voices = [] }) => ({
        label: language_name,
        value: language_value,
        children: (voices || []).map(({ voice_name = '', voice_value = '' }) => ({
          label: voice_name,
          value: voice_value,
        })),
      }))
      setLanguageDefaulValue(default_language)
      setTimbreDefaulValue(default_voice !== 'yixiaoling' ? 'yixiaoling' : default_voice)
      setLanguageList(languages || [])
      setVoicesConfigData(timbres || [])
    }
    catch (error) {
      console.error('Failed to fetch voice data:', error)
    }
  }
  // 点击右上角发布或者更新
  const onPublish = async (showNotify = true, modelAndParameter?: ModelAndParameter, features?: FeaturesData) => {
    const modelId = modelAndParameter?.model || modelConfig.model_id
    const promptTemplate = modelConfig.configs.prompt_template
    // const promptVariables = modelConfig.configs.prompt_variables

    if (promptEmpty) {
      notify({ type: 'error', message: t('appDebug.otherError.promptNoBeEmpty') })
      return
    }
    if (isAdvancedMode && mode !== AppType.completion) {
      if (modelModeType === ModelModeType.completion) {
        if (!hasSetBlockStatus.history) {
          notify({ type: 'error', message: t('appDebug.otherError.historyNoBeEmpty') })
          return
        }
        if (!hasSetBlockStatus.query) {
          notify({ type: 'error', message: t('appDebug.otherError.queryNoBeEmpty') })
          return
        }
      }
    }
    if (contextVarEmpty) {
      notify({ type: 'error', message: t('appDebug.feature.dataSet.queryVariable.contextVarNotEmpty') })
      return
    }
    const postDatasets = dataSets.map(({ id }) => ({
      dataset: {
        enabled: true,
        id,
      },
    }))

    // new model config data struct
    const voice_input_data = features?.text2speech?.voice_input || { enabled: false }
    const voice_conversation_data = features?.text2speech?.voice_conversation || { enabled: false }
    const data: BackendModelConfig = {
      // Simple Mode prompt
      pre_prompt: !isAdvancedMode ? promptTemplate : '',
      prompt_type: promptMode,
      chat_prompt_config: {} as any,
      completion_prompt_config: {} as any,
      // user_input_form: promptVariablesToUserInputsForm(promptVariables),
      user_input_form: [],
      dataset_query_variable: contextVar || '',
      //  features
      more_like_this: features?.moreLikeThis || { enabled: false },
      opening_statement: features?.opening?.enabled ? (features.opening?.opening_statement || '') : '',
      suggested_questions: features?.opening?.enabled ? (features.opening?.suggested_questions || []) : [],
      sensitive_word_avoidance: features?.moderation as any,
      speech_to_text: features?.speech2text as any,
      text_to_speech: {
        ...features?.text2speech,
        voice_input: voice_input_data,
        voice_conversation: voice_conversation_data,
      } as any,
      file_upload: features?.file as any,
      suggested_questions_after_answer: features?.suggested as any || { enabled: false },
      retriever_resource: features?.citation as any,
      agent_mode: {
        ...modelConfig.agentConfig,
        strategy: isFunctionCall ? AgentStrategy.functionCall : AgentStrategy.react,
        fork: false,
      },
      model: {
        provider: modelAndParameter?.provider || modelConfig.provider,
        name: modelId,
        mode: modelConfig.mode,
        completion_params: modelAndParameter?.parameters || completionParams as any,
        basic_param_mode: modelAndParameter?.basicParamMode || basicParamMode || BasicParamModeEnum.normal,
      },
      dataset_configs: {
        ...datasetConfigs,
        datasets: {
          datasets: [...postDatasets],
        } as any,
        fork: false,
      },
      external_data_tools: [],
    }

    if (isAdvancedMode) {
      data.chat_prompt_config = chatPromptConfig
      data.completion_prompt_config = completionPromptConfig
    }
    await updateAppModelConfig({ url: `/apps/${appId}/model-config`, body: data })
    // 更新背景图配置
    await saveAppChatBgConfig({ appId, body: { configs: features ? features[FeatureEnum.chatBg] : undefined } })
    // 清除fork提示状态
    clearForkStatus()

    if (showNotify)
      notify({ type: 'success', message: t('common.actionMsg.saveSuccessfully') })

    setCanReturnToSimpleMode(false)
    return true
  }
  // 保存
  const onSave = useCallback(async (appPublishConfig: any) => {
    try {
      await publishApp(appId, appPublishConfig)
      notify({
        type: 'success',
        message: t('common.actionMsg.saveSuccessfully'),
      })
      setShowAppPublishModal(false)
      getAppPublishInfoData()
      setIsPublished(true)
    }
    catch {
    }
  }, [appId, notify, t, getAppPublishInfoData])
  // 发布
  const clickPublish = useCallback(async () => {
    setShowAppPublishModal(true)
  }, [])

  /* 知识库部分 ———— start */
  // 知识库选择弹框保存回调
  const handleDatasetSelect = (data: DataSet[]) => {
    if (isEqual(data.map(item => item.id), dataSets.map(item => item.id)))
      return

    formattingChangedDispatcher()
    let newDatasets = data
    if (data.find(item => !item.name)) { // has not loaded selected dataset
      const newSelected = produce(data, (draft: any) => {
        data.forEach((item, index) => {
          if (!item.name) { // not fetched database
            const newItem = dataSets.find(i => i.id === item.id)
            if (newItem)
              draft[index] = newItem
          }
        })
      })
      setDataSets(newSelected)
      newDatasets = newSelected
    }
    else {
      setDataSets(data)
    }

    const { datasets, retrieval_model, score_threshold_enabled, ...restConfigs } = datasetConfigs

    const retrievalConfig = getMultipleRetrievalConfig({
      top_k: restConfigs.top_k,
      score_threshold: restConfigs.score_threshold,
      reranking_model: restConfigs.reranking_model && {
        provider: restConfigs.reranking_model.reranking_provider_name,
        model: restConfigs.reranking_model.reranking_model_name,
      },
      reranking_mode: restConfigs.reranking_mode,
      weights: restConfigs.weights,
      reranking_enable: restConfigs.reranking_enable,
    }, newDatasets)

    setDatasetConfigs({
      ...retrievalConfig,
      reranking_model: restConfigs.reranking_model && {
        reranking_provider_name: restConfigs.reranking_model.reranking_provider_name,
        reranking_model_name: restConfigs.reranking_model.reranking_model_name,
      },
      retrieval_model,
      score_threshold_enabled,
      datasets: {
        datasets: newDatasets.map(item => ({
          enabled: true,
          id: item.id,
        })),
      },
      fork: false, // 清空fork状态
    })
  }
  /* 知识库部分 ———— end */
  /* 尺寸变更 ———— start */
  const resizeFeatureList = useCallback((width: number) => {
    setFeatureListWidth(width)
  }, [setFeatureListWidth])
  const resizeDebug = useCallback(debounce((width: number) => {
    setDebugWidth(width)
  }, 500), [setDebugWidth])
  /* 尺寸变更 ———— end */

  // 判断应用是否发布过
  useEffect(() => {
    if (isPublished)
      return
    const getIsPublished = appPublishInfo?.status || appDetail?.web_site?.access_token || appDetail?.embed_site?.access_token || appDetail?.api_site?.access_token
    setIsPublished(!!getIsPublished)
  }, [isPublished, appPublishInfo, appDetail])
  // Fill old app data missing model mode.
  useEffect(() => {
    if (hasFetchedDetail && !modelModeType) {
      const mode = currModel?.model_properties.mode as (ModelModeType | undefined)
      if (mode) {
        const newModelConfig = produce(modelConfig, (draft: ModelConfig) => {
          draft.mode = mode
        })
        setModelConfig(newModelConfig)
      }
    }
  }, [textGenerationModelList, hasFetchedDetail, modelModeType, currModel, modelConfig])
  useEffect(() => {
    (async () => {
      // 获取背景图
      getChatBgConfig()
      // 获取音频能力
      getData()
      // 获取模型列表
      const collectionList = await fetchCollectionList()
      setCollectionList(collectionList)
      // 查询应用详情
      setMode(appDetail!.mode)
      const modelConfig = appDetail!.model_config
      const promptMode = modelConfig.prompt_type === PromptMode.advanced ? PromptMode.advanced : PromptMode.simple
      doSetPromptMode(promptMode)
      if (promptMode === PromptMode.advanced) {
        if (modelConfig.chat_prompt_config && modelConfig.chat_prompt_config.prompt.length > 0)
          setChatPromptConfig(modelConfig.chat_prompt_config)
        else
          setChatPromptConfig(clone(DEFAULT_CHAT_PROMPT_CONFIG) as any)
        setCompletionPromptConfig(modelConfig.completion_prompt_config || clone(DEFAULT_COMPLETION_PROMPT_CONFIG) as any)
        setCanReturnToSimpleMode(false)
      }
      const model = appDetail!.model_config.model
      // 处理选中的知识库数据
      let datasets: any = null
      if (modelConfig.agent_mode?.tools?.find(({ dataset }: any) => dataset?.enabled))
        datasets = modelConfig.agent_mode?.tools.filter(({ dataset }: any) => dataset?.enabled)
      else if (modelConfig.dataset_configs.datasets?.datasets?.length)
        datasets = modelConfig.dataset_configs?.datasets?.datasets
      if (dataSets && datasets?.length && datasets?.length > 0) {
        const { data: dataSetsWithDetail } = await fetchDatasets({ url: '/datasets', params: { page: 1, ids: datasets.map(({ dataset }: any) => dataset.id) } })
        // fork情况下 不实际存入知识库资源
        if (modelConfig.dataset_configs?.fork) {
          setForkDatasets(dataSetsWithDetail)
          datasets = []
          setDataSets([])
        }
        else {
          datasets = dataSetsWithDetail
          setDataSets(datasets)
        }
      }
      if (modelConfig.dataset_configs?.fork) {
        setDatasetConfigs({
          // @ts-expect-error 可接受错误
          retrieval_model: RETRIEVE_TYPE.multiWay,
          ...modelConfig.dataset_configs,
          datasets: {
            datasets: [],
          },
        })
      }
      else {
        setDatasetConfigs({
          // @ts-expect-error 可接受错误
          retrieval_model: RETRIEVE_TYPE.multiWay,
          ...modelConfig.dataset_configs,
          datasets: {
            datasets: modelConfig.dataset_configs.datasets?.datasets.map(item => ({
              // @ts-expect-error 可接受错误
              enabled: item.dataset.enabled,
              // @ts-expect-error 可接受错误
              id: item.dataset.id,
            })) || [],
          },
        })
      }

      if (modelConfig.external_data_tools)
        setExternalDataToolsConfig(modelConfig.external_data_tools)

      const filterTools = modelConfig.agent_mode?.tools.filter((tool: any) => {
        return !tool.dataset
      }).map((tool: any) => {
        return {
          ...tool,
          isDeleted: appDetail!.deleted_tools?.includes(tool.tool_name),
          notAuthor: collectionList.find(c => tool.provider_id === c.id)?.is_team_authorization === false,
        }
      })
      // 内置工具
      const builtinTools = filterTools.filter((tool: any) => {
        return tool.provider_type === CollectionType.builtIn
      })
      // 除了内置工具以外都是私有工具
      const privateTools = filterTools.filter((tool: any) => {
        return tool.provider_type !== CollectionType.builtIn
      })
      // fork情况下 不实际存入私有工具资源
      if (modelConfig.agent_mode.fork)
        setForkTools(privateTools)

      const config = {
        modelConfig: {
          provider: model.provider,
          model_id: model.name,
          mode: model.mode,
          configs: {
            prompt_template: modelConfig.pre_prompt || '',
            prompt_variables: userInputsFormToPromptVariables(
              [
                // ...modelConfig.user_input_form,
                ...(
                  modelConfig.external_data_tools?.length
                    ? modelConfig.external_data_tools.map((item: any) => {
                      return {
                        external_data_tool: {
                          variable: item.variable as string,
                          label: item.label as string,
                          enabled: item.enabled,
                          type: item.type as string,
                          config: item.config,
                          required: true,
                          icon: item.icon,
                          // icon_background: item.icon_background,
                        },
                      }
                    })
                    : []
                ),
              ] as any,
              modelConfig.dataset_query_variable,
            ),
          },
          more_like_this: modelConfig.more_like_this,
          opening_statement: modelConfig.opening_statement,
          suggested_questions: modelConfig.suggested_questions,
          sensitive_word_avoidance: modelConfig.sensitive_word_avoidance,
          speech_to_text: modelConfig.speech_to_text,
          text_to_speech: modelConfig.text_to_speech,
          file_upload: modelConfig.file_upload,
          suggested_questions_after_answer: modelConfig.suggested_questions_after_answer,
          retriever_resource: modelConfig.retriever_resource,
          annotation_reply: modelConfig.annotation_reply,
          external_data_tools: modelConfig.external_data_tools,
          dataSets: datasets || [],
          // eslint-disable-next-line multiline-ternary
          agentConfig: appDetail!.mode === 'agent-chat' ? {
            max_iteration: DEFAULT_AGENT_SETTING.max_iteration,
            ...modelConfig.agent_mode,
            // remove dataset
            enabled: true, // modelConfig.agent_mode?.enabled is not correct. old app: the value of app with dataset's is always true
            tools: modelConfig.agent_mode.fork ? builtinTools : filterTools, // fork情况下 不实际存入私有工具资源
          } : DEFAULT_AGENT_SETTING,
        },
        completionParams: model.completion_params,
        basicParamMode: model.basic_param_mode ? model.basic_param_mode : BasicParamModeEnum.normal,
      }

      syncToPublishedConfig(config as PublishConfig)
      setHasFetchedDetail(true)
    })()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [appId])

  // 开启多模型调试模式
  const handleDebugWithMultipleModelChange = () => {
    const id = Date.now().toString()
    handleMultipleModelConfigsChange(
      true,
      [
        { id: `${id}`, model: modelConfig.model_id, provider: modelConfig.provider, parameters: completionParams },
        { id: `${id}-no-repeat`, model: '', provider: '', parameters: {} },
      ],
      id,
    )
    setAppSiderbarExpand('collapse')
  }

  if (isLoading) {
    return <div className='flex items-center justify-center h-full'>
      <Loading type='area' />
    </div>
  }

  return (
    <ConfigContext.Provider value={{
      appId,
      mode,
      modelModeType,
      promptMode,
      isAdvancedMode,
      isAgent,
      isFunctionCall,
      collectionList,
      voicesConfigData,
      setPromptMode,
      chatPromptConfig,
      completionPromptConfig,
      currentAdvancedPrompt,
      setCurrentAdvancedPrompt,
      conversationHistoriesRole: completionPromptConfig.conversation_histories_role,
      showHistoryModal,
      setConversationHistoriesRole,
      hasSetBlockStatus,
      prevPromptConfig,
      setPrevPromptConfig,
      externalDataToolsConfig,
      setExternalDataToolsConfig,
      formattingChanged,
      setFormattingChanged,
      inputs,
      setInputs,
      completionParams,
      setCompletionParams,
      modelConfig,
      setModelConfig,
      showSelectDataSet,
      dataSets,
      setDataSets,
      datasetConfigs,
      setDatasetConfigs,
      forkDatasets,
      setForkDatasets,
      forkTools,
      setForkTools,
      hasSetContextVar,
      isVoiceCalling,
      setIsVoiceCalling,
    }}
    >
      <FeaturesProvider features={featuresData}>
        <>
          <div className="flex flex-col h-full">
            <div className='relative flex h-full grow'>
              {/* 右上角顶部按钮 */}
              <div className='absolute top-[-60px] right-4 w-1/3 h-[60px]'>
                <div className='flex items-center justify-end h-full gap-3'>
                  {/* Agent 设置 */}
                  {/* {isAgent && (
                    <AgentSettingButton
                      isChatModel={modelConfig.mode === ModelModeType.chat}
                      agentConfig={modelConfig.agentConfig}

                      isFunctionCall={isFunctionCall}
                      onAgentSettingChange={(config) => {
                        const nextConfig = produce(modelConfig, (draft: ModelConfig) => {
                          draft.agentConfig = config
                        })
                        setModelConfig(nextConfig)
                      }}
                    />
                  )} */}
                  {/* 保存按钮 */}
                  <AppSaveBtn onSave={onPublish} />
                  {/* 发布按钮 */}
                  <Tooltip popupContent={t('app.notify.multipleModelPublishTip')} disabled={!debugWithMultipleModel}>
                    <Button variant={'primary'} onClick={clickPublish} disabled={debugWithMultipleModel}>
                      { isPublished ? t('app.action.updatePublish') : t('app.action.publish') }
                    </Button>
                  </Tooltip>
                </div>
              </div>
              <div className={cn(style.appConfigCardBg, 'flex w-full')}>
                {/* 左侧配置 */}
                <div className={cn(
                  'grow w-0 flex flex-col h-full min-w-[300px] md:min-w-[360px]',
                  debugWidth ? '' : debugWithMultipleModel ? 'w-1/2' : 'w-1/2 md:w-2/3',
                )}
                style={{ background: 'linear-gradient(103deg, rgba(255, 255, 255, 0.70) 0%, #FFF 100%)' }}
                >
                  <div className={cn(style.appModuleHeader, 'title-16-24')}>
                    {t('appDebug.pageTitle.appConfig')}
                    {!debugWithMultipleModel && (
                      <>
                        <ModelParameterModal
                          isAdvancedMode={isAdvancedMode}
                          mode={mode}
                          provider={modelConfig.provider}
                          basicParamMode={basicParamMode}
                          completionParams={completionParams}
                          modelId={modelConfig.model_id}
                          setModel={setModel as any}
                          onCompletionParamsChange={(newParams: FormValue, useStore?: boolean) => {
                            setCompletionParams(newParams, useStore)
                          }}
                          onModelParamsModeChange={handleModelParamsModeChange}
                          getModelConfigStoraged={getModelConfigStoraged}
                          debugWithMultipleModel={debugWithMultipleModel}
                          onDebugWithMultipleModelChange={handleDebugWithMultipleModelChange}
                        />
                      </>
                    )}

                  </div>
                  <Divider className={style.cardDivider}></Divider>
                  <div className={cn(style.appConfigContent)} style={{ position: 'relative' }}>
                    {/* 应用信息 */}
                    <ResizableBox
                      className={'!flex-row min-w-[150px] md:min-w-[180px]'}
                      triggerNode={(
                        <div className={cn(style.resizeTrigger, '-right-1.5')}>
                          <Divider className={style.verticalDivider} type='vertical'></Divider>
                        </div>
                      )}
                      direction='horizontal'
                      triggerDirection='right'
                      minWidth={150}
                      onResize={resizeFeatureList}
                      style={{ width: featureListWidth ? `${featureListWidth}px` : '50%' }}
                    >
                      <div className='h-full overflow-y-auto grow'>
                        <AppConfig />
                      </div>
                    </ResizableBox>
                    <div className='grow w-0 h-full min-w-[150px] md:min-w-[180px]'>
                      {/* 能力拓展工具列表 */}
                      <FeatureList
                        isChatMode={mode !== 'completion'}
                        disabled={false}
                        onChange={handleFeaturesChange}
                        onAutoAddPromptVariable={handleAddPromptVariable}
                      />
                    </div>
                  </div>
                </div>
                {/* 右侧 预览与调试 */}
                <ResizableBox
                  className={cn(debugWidth ? '' : debugWithMultipleModel ? 'w-1/2' : 'w-1/2 md:w-1/3')}
                  triggerNode={(
                    <div className={cn(style.resizeTrigger, '-left-1.5')}>
                      <Divider className={style.verticalDivider} type='vertical'></Divider>
                    </div>
                  )}
                  direction='horizontal'
                  triggerDirection='left'
                  minWidth={300}
                  onResize={resizeDebug}
                >
                  <Debug
                    isAPIKeySet={isAPIKeySet}
                    onSetting={() => setShowAccountSettingModal({ payload: 'provider' })}
                    inputs={inputs}
                    modelParameterParams={{
                      setModel: setModel as any,
                      onCompletionParamsChange: setCompletionParams,
                      onModelParamsModeChange: handleModelParamsModeChange,
                      getModelConfigStoraged,
                    }}
                    bestModel={bestModel}
                    debugWithMultipleModel={debugWithMultipleModel}
                    multipleModelConfigs={multipleModelConfigs}
                    onMultipleModelConfigsChange={handleMultipleModelConfigsChange}
                  />
                </ResizableBox>
              </div>
            </div>
          </div>
          {/* 选中的知识库弹窗 */}
          {isShowSelectDataSet && (
            <AddDatasetModal
              isShow={isShowSelectDataSet}
              onClose={hideSelectDataSet}
              value={dataSets}
              onSelect={handleDatasetSelect}
            />
          )}
          {/* 历史记录弹窗 */}
          {isShowHistoryModal && (
            <EditHistoryModal
              isShow={isShowHistoryModal}
              saveLoading={false}
              onClose={hideHistoryModal}
              data={completionPromptConfig.conversation_histories_role}
              onSave={(data) => {
                setConversationHistoriesRole(data)
                hideHistoryModal()
              }}
            />
          )}
          {/* 发布弹窗 */}
          {showAppPublishModal && (
            <AppPublishModal
              app={appDetail!}
              appPublishInfo={appPublishInfo}
              isPublished={isPublished}
              onPublish={onPublish}
              onSave={onSave}
              onCancel={() => setShowAppPublishModal(false)}
            />
          )}
        </>
      </FeaturesProvider>
    </ConfigContext.Provider>
  )
}
export default React.memo(Configuration)
