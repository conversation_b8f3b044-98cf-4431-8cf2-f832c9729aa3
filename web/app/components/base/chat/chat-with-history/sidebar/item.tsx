import type { FC } from 'react'
import {
  memo,
  useRef,
} from 'react'
import { Input } from 'antd'
import { useChatWithHistoryContext } from '../context'
import ItemOperation from './item-operation'
import style from './styles/style.module.css'
import type { ConversationItem } from '@/types/share'

type ItemProps = {
  index?: number
  isPin?: boolean
  item: ConversationItem
  onOperate: (type: string, item: ConversationItem) => void
  onChangeConversation: (conversationId: string) => void
  renameItem?: ConversationItem | null
  setNewName: (name: string) => void
  currentConversationId: string
}
const Item: FC<ItemProps> = ({
  index,
  isPin,
  item,
  onOperate,
  onChangeConversation,
  renameItem,
  setNewName,
  currentConversationId,
}) => {
  const ref = useRef(null)
  const { setChatAllTypeResult } = useChatWithHistoryContext()

  return (
    <div
      ref={ref}
      key={item.id}
      className={`
        ${style['chat-item']}
        flex mb-1 last-of-type:mb-0 cursor-pointer group
        ${currentConversationId === item.id && style.active}
        ${!currentConversationId && index === 0 && style.active}
      `}
      onClick={() => {
        setChatAllTypeResult({})
        onChangeConversation(item.id)
      }}
      onTouchStart={() => {
        setChatAllTypeResult({})
        onChangeConversation(item.id)
      }}
    >
      {
        renameItem?.id === item.id && (
          <Input
            defaultValue={item.name}
            onPressEnter={(e) => { setNewName(e.currentTarget.value) }}
            onBlur={(e) => { setNewName(e.currentTarget.value) }}
            size='small'
            className='mr-2 text-S1'
          />
        )
      }
      {
        renameItem?.id !== item.id && (
          <div className='truncate grow text-S1 leading-H1' title={item.name}>{item.name || '-'}</div>
        )
      }
      {item.id !== '' && (
        <ItemOperation
          className={'hidden group-hover:flex ant-dropdown-open-show'}
          isPinned={!!isPin}
          togglePin={() => onOperate(isPin ? 'unpin' : 'pin', item)}
          isShowDelete
          isShowRenameConversation
          onRenameConversation={() => onOperate('rename', item)}
          onDelete={() => onOperate('delete', item)}
        />
      )}
    </div>
  )
}

export default memo(Item)
