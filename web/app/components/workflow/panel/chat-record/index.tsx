import {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react'
import { RiCloseLine } from '@remixicon/react'
import { Divider } from 'antd'
import {
  useStore,
  useWorkflowStore,
} from '../../store'
import { useWorkflowRun } from '../../hooks'
import UserInput from './user-input'
import Chat from '@/app/components/base/chat/chat'
import type { ChatItem } from '@/app/components/base/chat/types'
import { fetchConversationMessages } from '@/service/debug'
import { useStore as useAppStore } from '@/app/components/app/store'
import { UUID_NIL } from '@/app/components/base/chat/constants'
import { getProcessedFilesFromResponse } from '@/app/components/base/file-uploader/utils'
import CropperWrapper from '@/app/components/base/cropper/wrapper'
import { fetchAppChatBgConfig } from '@/service/apps'
import type { AppChatBgConfig } from '@/app/components/base/features/types'
import { getCropperFontCss } from '@/app/components/app/app-bg-cropper/utils'
import TextButton from '@/app/components/base/button/text-button'
import cn from '@/utils/classnames'

function appendQAToChatList(newChatList: ChatItem[], item: any) {
  const answerFiles
    = item.message_files?.filter((file: any) => file.belongs_to === 'assistant') || []
  newChatList.push({
    id: item.id,
    content: item.answer,
    feedback: item.feedback,
    isAnswer: true,
    citation: item.metadata?.retriever_resources,
    message_files: getProcessedFilesFromResponse(
      answerFiles.map((item: any) => ({ ...item, related_id: item.related_id || item.id })),
    ),
    workflow_run_id: item.workflow_run_id,
  })
  const questionFiles = item.message_files?.filter((file: any) => file.belongs_to === 'user') || []
  newChatList.push({
    id: `question-${item.id}`,
    content: item.query,
    isAnswer: false,
    message_files: getProcessedFilesFromResponse(
      questionFiles.map((item: any) => ({ ...item, related_id: item.related_id || item.id })),
    ),
  })
}

function getFormattedChatList(messages: any[]) {
  const newChatList: ChatItem[] = []
  let nextMessageId = null
  for (const item of messages) {
    if (!item.parent_message_id) {
      appendQAToChatList(newChatList, item)
      break
    }

    if (!nextMessageId) {
      appendQAToChatList(newChatList, item)
      nextMessageId = item.parent_message_id
    }
    else {
      if (item.id === nextMessageId || nextMessageId === UUID_NIL) {
        appendQAToChatList(newChatList, item)
        nextMessageId = item.parent_message_id
      }
    }
  }
  return newChatList.reverse()
}

const ChatRecord = () => {
  const [fetched, setFetched] = useState(false)
  const [chatList, setChatList] = useState<ChatItem[]>([])
  const [bgConfig, setBgConfig] = useState<AppChatBgConfig>()
  const appDetail = useAppStore(s => s.appDetail)
  const workflowStore = useWorkflowStore()
  const { handleLoadBackupDraft } = useWorkflowRun()
  const historyWorkflowData = useStore(s => s.historyWorkflowData)
  const currentConversationID = historyWorkflowData?.conversation_id
  // 第二风格字体颜色
  const secondColorCss = useMemo(() => {
    return bgConfig?.enabled ? ((bgConfig.fontColor === 'black' || bgConfig.fontColor === undefined) ? 'text-gray-G2' : 'text-gray-G5') : 'text-gray-G2'
  }, [bgConfig])

  const handleFetchConversationMessages = useCallback(async () => {
    if (appDetail && currentConversationID) {
      try {
        setFetched(false)
        const res = await fetchConversationMessages(appDetail.id, currentConversationID)
        setChatList(getFormattedChatList((res as any).data))
      }
      catch (e) {
        console.error(e)
      }
      finally {
        setFetched(true)
      }
    }
  }, [appDetail, currentConversationID])
  const getAppBgConfig = useCallback(async () => {
    if (appDetail) {
      const result = await fetchAppChatBgConfig({ appId: appDetail.id })
      setBgConfig(result.data as AppChatBgConfig)
    }
  }, [appDetail])

  useEffect(() => {
    handleFetchConversationMessages()
  }, [currentConversationID, appDetail, handleFetchConversationMessages])
  // 获取背景图配置
  useEffect(() => {
    getAppBgConfig()
  }, [getAppBgConfig])

  return (
    <CropperWrapper
      config={bgConfig?.enabled ? bgConfig?.narrow : undefined}
      className={`
        flex flex-col w-[420px] h-full bg-chatbot-bg border-l border-gray-G6
      `}
      header={
        <>
          <div className='flex items-center justify-between py-4 px-6 font-semibold'>
            <span className={getCropperFontCss(bgConfig)}>{`TEST CHAT#${historyWorkflowData?.sequence_number}`}</span>
            <TextButton
              onClick={() => {
                handleLoadBackupDraft()
                workflowStore.setState({ historyWorkflowData: undefined })
              }}
            >
              <RiCloseLine className={cn('w-4 h-4', secondColorCss)} />
            </TextButton>
          </div>
          <div className='px-6'>
            <Divider className='my-0' type='horizontal'></Divider>
          </div>
        </>
      }
    >
      <Chat
        answerIcon={appDetail?.icon_url || appDetail?.site?.icon_url || ''}
        config={{
          supportCitationHitInfo: true,
        } as any}
        chatList={chatList}
        chatContainerClassName='px-3'
        chatContainerInnerClassName={cn('w-full max-w-full mx-auto', getCropperFontCss(bgConfig))}
        chatFooterClassName='rounded-b-2xl'
        chatFooterInnerClassName='pb-4 w-full max-w-full mx-auto'
        chatNode={<UserInput />}
        noChatInput
        allToolIcons={{}}
        showPromptLog
        chatAnswerContainerInner='!pr-2'
      />

    </CropperWrapper>
  )
}

export default memo(ChatRecord)
