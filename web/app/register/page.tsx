'use client'
import React, { useEffect, useMemo, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import style from '../signin/page.module.css'
import s from './styles/index.module.css'

import RegisterForm from './register-form'
import PrivateRegisterForm from './private-register-form'
import SsoRegisterForm from './sso-code-register-form'
import NewSsoCodeRegisterForm from './new-sso-code-register-form'

// 公共组件
import cn from '@/utils/classnames'
import Loading from '@/app/components/base/loading'
import Logo from '@/app/components/base/logo'
import { languages } from '@/i18n/language'
import { useI18N } from '@/context/i18n'
import PopoverSelect from '@/app/components/base/select/popover-select'
import TextButton from '@/app/components/base/button/text-button'
import { ArrowDown } from '@/app/components/base/icons/src/vender/line/arrows'
import { Globe01 } from '@/app/components/base/icons/src/vender/line/mapsAndTravel'
import { GRAY } from '@/themes/var-define'
import { useSystemContext } from '@/context/system-context'

const Install = () => {
  const { t } = useTranslation()
  const router = useRouter()
  const { isPrivate, canI18n, isSso } = useSystemContext()
  const { locale, setLocaleOnClient } = useI18N()
  const searchParams = useSearchParams()
  // 获取code
  const search = searchParams.get('code')
  const [codeValue, setCodeValue] = useState('')
  // 是否正在加载
  const [loading, setLoading] = useState<boolean>(true)

  // 语言切换选项
  const languageOptions = useMemo(() => {
    return languages.map((item) => {
      return {
        key: item.value,
        label: item.name,
        title: item.name,
      }
    })
  }, [])

  // 跳转登录页面
  const jumpLogin = () => {
    router.push('/signin')
  }

  useEffect(() => {
    if (!isPrivate)
      router.replace('/')
    setCodeValue(search || '')
  }, [isPrivate, router, search])

  if (!isPrivate)
    return <Loading type='app'></Loading>

  return (
    <div className={style.background}>
      <div className={cn(style.wrap)}>

        {/* 头部图标 */}
        <Logo className={style.header}></Logo>
        {/* 右侧tab部分 */}
        {
          (!isSso && !codeValue) && (
            <div onClick={jumpLogin} className={s['login-tab']}>
              { t('login.action.signBtn') }
            </div>
          )
        }
        {/* 登录表单 */}
        {/* newSsoCodeRegisterForm */}
        {
          codeValue
            ? <NewSsoCodeRegisterForm></NewSsoCodeRegisterForm>
            : (
              isSso
                ? <SsoRegisterForm></SsoRegisterForm>
                : isPrivate
                  ? <PrivateRegisterForm></PrivateRegisterForm>
                  : <RegisterForm />
            )
        }
        {/* { (isSso || codeValue)
              ? <SsoRegisterForm></SsoRegisterForm>
              : isPrivate
                ? <PrivateRegisterForm></PrivateRegisterForm>
                : <RegisterForm /> } */}
      </div>
      {/* 语言切换能力 */}
      {canI18n && <PopoverSelect
        options={languageOptions}
        defaultValue={locale}
        onChange={setLocaleOnClient}
        triggerNode={(open, value) => {
          return (
            <TextButton className='flex items-center gap-1 absolute top-6 right-6' style={{ color: GRAY.G1 }} size='middle'>
              <Globe01 className='w-4 h-4'></Globe01>
              {languageOptions.find(item => item.key === value)?.label}
              <ArrowDown className={cn('w-4 h-4', open && 'rotate-180')} />
            </TextButton>
          )
        }}
      >
      </PopoverSelect>
      }
    </div>
  )
}

export default Install
