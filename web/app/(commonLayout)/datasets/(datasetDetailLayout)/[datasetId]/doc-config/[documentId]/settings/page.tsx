'use client'

import React, { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Settings from '@/app/components/datasets/documents/detail/settings'
import { useProviderContext } from '@/context/provider-context'

export type IProps = {
  params: { datasetId: string; documentId: string }
}

const DocumentSettings = ({
  params: { datasetId, documentId },
}: IProps) => {
  const { useXIYANRag } = useProviderContext()
  const router = useRouter()

  useEffect(() => {
    if (useXIYANRag)
      return router.replace('/datasets')
  }, [useXIYANRag, router])

  return (
    <Settings datasetId={datasetId} documentId={documentId} />
  )
}

export default DocumentSettings
