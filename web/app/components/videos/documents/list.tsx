'use client'
import type { FC } from 'react'
import React, { useCallback, useEffect, useState } from 'react'
import { useBoolean } from 'ahooks'
import { useTranslation } from 'react-i18next'
import type { TableColumnsType } from 'antd'
import { Table } from 'antd'
import { useContext } from 'use-context-selector'
import { useVideoCreateMode } from '../hook'
import s from './style.module.css'
import EditDocModal from './edit-doc-modal'
import DetailModal from './detail-modal'

import useTimestamp from '@/hooks/use-timestamp'
import { deleteVideoDocument, enableVideoDocument, reprocessVideoDocument } from '@/service/videos'
import { ProcessStatus } from '@/types/videos'
import type { DocItem, DocList, ImageItem, VideoItem, VideoStreamItem } from '@/types/videos'

import SiteStatusIndicator from '@/app/components/videos/common/status-indicator/site-status-indicator'
import ProceessStatusIndicator from '@/app/components/videos/common/status-indicator'
import { VideoActionSwitch, VideoActionTextBtn } from '@/app/components/videos/common/video-action-btn'

// 公共的工具和方法
import Avatar from '@/app/components/base/app-info/avatar'
import { Edit } from '@/app/components/base/icons/src/vender/line/general'
import Confirm from '@/app/components/base/confirm'
import { asyncRunSafe } from '@/utils'
import Toast, { ToastContext } from '@/app/components/base/toast'
import { useVideoDetailContext } from '@/context/video-detail'
import ImagePreview from '@/app/components/base/image-uploader/image-preview'
import VideoPreview from '@/app/components/base/video-player/video-preview'

type IDocumentListProps = {
  documents: DocList
  onUpdate: () => void
  onDelete: () => void
}

const DocumentList: FC<IDocumentListProps> = ({ documents = [], onUpdate, onDelete }) => {
  const { t } = useTranslation()
  const { formatTime } = useTimestamp()
  const { notify } = useContext(ToastContext)
  const { video } = useVideoDetailContext()
  const { id: videoId, type: videoType } = video!
  const { isImageMode, isVideoMode, isVideoStreamMode } = useVideoCreateMode(videoType)
  // 文档列表
  const [localDocs, setLocalDocs] = useState<DocList>(documents)
  // 当前选中文档
  const [currDocument, setCurrDocument] = useState<DocItem>()
  // 是否显示重命名弹窗
  const [isShowRenameModal, {
    setTrue: setShowRenameModalTrue,
    setFalse: setShowRenameModalFalse,
  }] = useBoolean(false)
  // 是否显示删除文件弹窗
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  // 是否显示文件详情弹窗
  const [showDetailModal, setShowDetailModal] = useState(false)
  // 是否显示设置弹窗
  const [showSettingModal, setShowSettingModal] = useState(false)
  // 是否显示预览图片弹窗
  const [showPreviewImg, setShowPreviewImg] = useState(false)
  // 是否显示预览视频弹窗
  const [showPreviewVideo, setShowPreviewVideo] = useState(false)

  // 显示重命名弹窗
  const showRenameModal = useCallback((doc: DocItem) => {
    setCurrDocument(doc)
    setShowRenameModalTrue()
  }, [setShowRenameModalTrue])
  // 打开删除文档弹窗
  const openDeleteModal = useCallback((doc: DocItem) => {
    setCurrDocument(doc)
    setShowDeleteModal(true)
  }, [])
  // 打开文档详情弹窗
  const openDetailModal = useCallback((doc: DocItem) => {
    setCurrDocument(doc)
    setShowDetailModal(true)
  }, [])
  // 打开设置弹窗
  const openSettingModal = useCallback((doc: DocItem) => {
    setCurrDocument(doc)
    setShowSettingModal(true)
  }, [])
  // 启用文档
  const handleEnabledDoc = useCallback(async (doc: DocItem, enabled: boolean) => {
    await enableVideoDocument(videoId, doc.id, enabled)
    Toast.notify({ type: 'success', message: enabled ? t('common.actionMsg.enableSuccessfully') : t('common.actionMsg.disableSuccessfully') })
  }, [t, videoId])
  // 重新处理
  const handleProcessDoc = useCallback(async (doc: DocItem) => {
    await reprocessVideoDocument(videoId, doc.id)
    Toast.notify({ type: 'success', message: t('common.actionMsg.reprocessSuccessfully') })
  }, [t, videoId])

  // 删除视频库
  const deleteVideoDoc = async () => {
    const [e] = await asyncRunSafe(deleteVideoDocument({ videoId, documentId: currDocument!.id }) as Promise<any>)
    if (!e) {
      setShowDeleteModal(false)
      notify({ type: 'success', message: t('common.actionMsg.deleteSuccessfully') })
      onDelete()
    }
  }
  // 获取文档重新处理disabled
  const getReprocessDisabled = (doc: DocItem) => {
    return !(doc.process_status === ProcessStatus.Processing
     || doc.process_status === ProcessStatus.Failed
     || doc.process_status === ProcessStatus.Waiting
    ) || doc.enabled
  }

  useEffect(() => {
    setLocalDocs(documents)
  }, [documents])

  const columns: TableColumnsType<DocItem> = [
    {
      title: '',
      key: 'test',
      width: 20,
    },
    {
      title: '#',
      key: 'position',
      width: 60,
      dataIndex: 'position',
    },
    // 视频模式或图片模式——文件名
    ...((isVideoMode || isImageMode)
      ? [{
        title: t('videos.doc.info.fileName'),
        key: 'name',
        render: (_: any, record: any) => {
          return (<div className='group gap-1 flex items-center'>
            <Avatar size={16} appMode={video!.type}></Avatar>
            <span className={s.tdValue} title={record.name}>{record.name}</span>
            <Edit
              className='w-4 h-4 text-gray-G3 hover:text-gray-G1 cursor-pointer shrink-0 ml-3'
              onClick={(e) => {
                e.stopPropagation()
                showRenameModal(record)
              }}
            />
          </div>)
        },
      }]
      : []),
    // 视频流模式——(url信息以及ip)
    ...(isVideoStreamMode
      ? [
        {
          title: t('videos.doc.info.deviceId'),
          key: 'deviceId',
          dataIndex: 'device_id',
          ellipsis: true,
        },
        {
          title: t('videos.doc.info.locationIp'),
          key: 'locationIp',
          dataIndex: 'location_ip',
          ellipsis: true,
        },
        {
          title: t('videos.doc.info.urlName'),
          key: 'urlName',
          ellipsis: true,
          dataIndex: 'url_name',
        },
        {
          title: t('videos.doc.info.urlSite'),
          key: 'urlSite',
          dataIndex: 'url',
          ellipsis: true,
        },
      ]
      : []),
    // 添加/上传时间
    {
      title: t(`videos.doc.info.${isVideoStreamMode ? 'addTime' : 'uploadTime'}`),
      key: 'uploadTime',
      render: (_: any, record: DocItem) => formatTime(record.created_at, t('common.dateFormat.dateTime') as string),
      ellipsis: true,
    },
    // 视频流模式——(网址状态以)
    ...(isVideoStreamMode
      ? [{
        title: t('videos.doc.info.siteStatus'),
        key: 'status',
        render: (_: any, record: any) => {
          return <SiteStatusIndicator status={record.status} errorMessage={record.msg}></SiteStatusIndicator>
        },
        width: 130,
      }, {
        title: t('videos.doc.info.processTime'),
        dataIndex: 'process_time',
        ellipsis: true,
      }]
      : []),
    // 处理状态
    {
      title: t('videos.doc.info.status'),
      key: 'process_status',
      width: 100,
      render: (_: any, record) => {
        return (
          <ProceessStatusIndicator status={record.process_status}></ProceessStatusIndicator>
        )
      },
    },
    // 操作
    {
      title: t('videos.doc.info.action'),
      render: (_: any, record: DocItem) => (
        <div className='flex items-center gap-6' onClick={e => e.stopPropagation()}>
          <VideoActionSwitch defaultValue={record.enabled} onChange={v => handleEnabledDoc(record, !!v)} size='small' />
          <VideoActionTextBtn disabled={getReprocessDisabled(record)} type='primary' onClick={() => handleProcessDoc(record)}>{t('common.operation.reProcess')}</VideoActionTextBtn>
          { (isVideoMode || isVideoStreamMode) && <VideoActionTextBtn type='primary' onClick={() => openSettingModal(record)}>{t('common.operation.settings')}</VideoActionTextBtn> }
          <VideoActionTextBtn type='primary' onClick={() => openDetailModal(record)}>{t('common.operation.detail')}</VideoActionTextBtn>
          <VideoActionTextBtn type='primary' onClick={() => openDeleteModal(record)}>{t('common.operation.delete')}</VideoActionTextBtn>
        </div>
      ),
      key: 'operation',
      align: 'left' as any,
      width: 300,
    },
  ]
  return (
    <>
      <Table
        size='large'
        columns={columns}
        pagination={{ position: ['none', 'none'], pageSize: 15, showSizeChanger: false }}
        scroll={{ y: 'calc(100vh - 285px)' }}
        dataSource={localDocs}
        className='border-gray-G5 rounded border'
        rowClassName='cursor-pointer'
        onRow={(record) => {
          return {
            onClick: () => {
              setCurrDocument(record)
              if (isVideoMode)
                setShowPreviewVideo(true)
              if (isImageMode)
                setShowPreviewImg(true)
            },
          }
        }}
      ></Table>
      {/* 重命名弹窗 */}
      {isShowRenameModal && currDocument && (
        <EditDocModal
          videoId={videoId}
          type={videoType}
          documentId={currDocument.id}
          name={(currDocument as VideoItem).name}
          onClose={setShowRenameModalFalse}
          onSaved={() => {
            onUpdate()
            setShowRenameModalFalse()
          }}
        />
      )}
      {/* 设置帧率弹窗 */}
      {
        showSettingModal && currDocument && (
          <EditDocModal
            type={videoType}
            videoId={videoId}
            documentId={currDocument.id}
            frameInterval={(currDocument as VideoStreamItem).frame_interval}
            onClose={() => setShowSettingModal(false)}
            onSaved={() => {
              onUpdate()
              setShowSettingModal(false)
            }}
          ></EditDocModal>
        )
      }
      {showDeleteModal
        && <Confirm
          isShow={showDeleteModal}
          title={t('videos.notify.deleteDocTip')}
          content={t('videos.notify.deleteDocTipDesc')}
          onConfirm={() => deleteVideoDoc()}
          onCancel={() => setShowDeleteModal(false)}
        />
      }
      {showDetailModal && currDocument
       && <DetailModal
         detail={
           {
             originFileName: (currDocument as VideoItem).file_name || '-',
             originFileSize: (currDocument as VideoItem).size || '0',
             frameRate: `${(currDocument as VideoStreamItem).frame_interval || 0}s`,
             uploadTime: formatTime(currDocument!.created_at, t('common.dateFormat.dateTime') as string),
             lastUpdateTime: formatTime(currDocument!.updated_at, t('common.dateFormat.dateTime') as string),
           }
         }
         mode={videoType}
         onClose={() => setShowDetailModal(false)}
       ></DetailModal>
      }
      {/* 图片预览 */}
      {
        showPreviewImg && currDocument?.preview_url && <ImagePreview
          url={`/${currDocument?.preview_url}`}
          onCancel={() => setShowPreviewImg(false)}
          title={(currDocument as ImageItem).file_name}
        ></ImagePreview>
      }
      {/* 视频预览 */}
      {
        showPreviewVideo && currDocument?.preview_url && <VideoPreview
          url={`/${currDocument.preview_url}`}
          title={(currDocument as VideoItem).file_name}
          onCancel={() => setShowPreviewVideo(false)}
        ></VideoPreview>
      }
    </>
  )
}

export default DocumentList
