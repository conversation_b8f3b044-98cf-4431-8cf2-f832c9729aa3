'use client'

import type { FC } from 'react'
import {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
} from 'react'
import { setAutoFreeze } from 'immer'
import {
  useEventListener,
} from 'ahooks'
import ReactFlow, {
  Background,
  ReactFlowProvider,
  SelectionMode,
  useEdgesState,
  useNodesState,
  useOnViewportChange,
  useReactFlow,
  useStoreApi,
} from 'reactflow'
import type {
  Viewport,
} from 'reactflow'
import 'reactflow/dist/style.css'

import './style.css'
import { useAppPublishInit } from '../app-publish/hooks/use-app-publish'
import type {
  Edge,
  Node,
} from './types'
import {
  ControlMode,
  SupportUploadFileTypes,
} from './types'
import { WorkflowContextProvider } from './context'
import {
  useEdgesInteractions,
  useNodesInteractions,
  useNodesReadOnly,
  useNodesSyncDraft,
  usePanelInteractions,
  useSelectionInteractions,
  useShortcuts,
  useWorkflow,
  useWorkflowInit,
  useWorkflowReadOnly,
  useWorkflowUpdate,
} from './hooks'
import Header from './header'
import CustomNode from './nodes'
import CustomNoteNode from './note-node'
import CustomIterationStartNode from './nodes/iteration-start'

import { CUSTOM_NOTE_NODE } from './note-node/constants'
import { CUSTOM_ITERATION_START_NODE } from './nodes/iteration-start/constants'
import Operator from './operator'
import CustomEdge from './custom-edge'
import CustomConnectionLine from './custom-connection-line'
import Panel from './panel'
import Features from './features'
import HelpLine from './common/help-line'
import CandidateNode from './candidate-node'
import PanelContextmenu from './panel-contextmenu'
import LimitTips from './common/limit-tips'
import {
  useStore,
  useWorkflowStore,
} from './store'
import {
  initialEdges,
  initialNodes,
} from './utils'
import {
  CUSTOM_NODE,
  ITERATION_CHILDREN_Z_INDEX,
  WORKFLOW_DATA_UPDATE,
} from './constants'
import { WorkflowHistoryProvider } from './workflow-history-store'
import SelectAgentMode from './nodes/agent/components/select-agent-mode'
import { useStore as useAppStore } from '@/app/components/app/store'

// 公共组件
import { useEventEmitterContextContext } from '@/context/event-emitter'
import Loading from '@/app/components/base/loading'
import { FeaturesProvider } from '@/app/components/base/features'
import type { Features as FeaturesData } from '@/app/components/base/features/types'
import { useFeaturesStore } from '@/app/components/base/features/hooks'
import Confirm from '@/app/components/base/confirm'
import { FILE_EXTS } from '@/app/components/base/prompt-editor/constants'
import { useSystemContext } from '@/context/system-context'

// 节点类型
const nodeTypes = {
  [CUSTOM_NODE]: CustomNode,
  [CUSTOM_NOTE_NODE]: CustomNoteNode,
  [CUSTOM_ITERATION_START_NODE]: CustomIterationStartNode,
}
// 边类型
const edgeTypes = {
  [CUSTOM_NODE]: CustomEdge,
}

type WorkflowProps = {
  nodes: Node[]
  edges: Edge[]
  viewport?: Viewport
}
const Workflow: FC<WorkflowProps> = memo(({
  nodes: originalNodes,
  edges: originalEdges,
  viewport,
}) => {
  const [nodes, setNodes] = useNodesState(originalNodes)
  const [edges, setEdges] = useEdgesState(originalEdges)
  const { eventEmitter } = useEventEmitterContextContext()
  const {
    handleNodeDragStart,
    handleNodeDrag,
    handleNodeDragStop,
    handleNodeEnter,
    handleNodeLeave,
    handleNodeClick,
    handleNodeConnect,
    handleNodeConnectStart,
    handleNodeConnectEnd,
    handleNodeContextMenu,
    handleHistoryBack,
    handleHistoryForward,
    handleNodeSelect,
  } = useNodesInteractions()
  // 刷新工作流画布
  const { handleRefreshWorkflowDraft } = useWorkflowUpdate()
  // 工作流是否只读
  const { workflowReadOnly } = useWorkflowReadOnly()
  // 节点是否只读
  const { nodesReadOnly } = useNodesReadOnly()
  // 画布保存hook
  const {
    handleSyncWorkflowDraft,
    syncWorkflowDraftWhenPageClose,
  } = useNodesSyncDraft()

  const workflowContainerRef = useRef<HTMLDivElement>(null)
  const workflowStore = useWorkflowStore()
  const reactflow = useReactFlow()
  const featuresStore = useFeaturesStore()
  const showFeaturesPanel = useStore(state => state.showFeaturesPanel)
  const controlMode = useStore(s => s.controlMode)
  const nodeAnimation = useStore(s => s.nodeAnimation)
  const showConfirm = useStore(s => s.showConfirm)
  const agentModeConfig = useStore(s => s.agentModeConfig)
  const {
    setShowConfirm,
    setControlPromptEditorRerenderKey,
    setSyncWorkflowDraftHash,
    setAgentModeConfig,
  } = workflowStore.getState()

  // 处理web页，隐藏自动保存，展示自动刷新
  const handleSyncWorkflowDraftWhenPageClose = useCallback(() => {
    if (document.visibilityState === 'hidden')
      syncWorkflowDraftWhenPageClose()
    // else if (document.visibilityState === 'visible')
    //   setTimeout(() => handleRefreshWorkflowDraft(), 500)
  }, [syncWorkflowDraftWhenPageClose, handleRefreshWorkflowDraft])

  useEffect(() => {
    setAutoFreeze(false)
    return () => {
      setAutoFreeze(true)
    }
  }, [])
  // 页面销毁自动保存
  useEffect(() => {
    return () => {
      handleSyncWorkflowDraft(true, true)
    }
  }, [])
  // 监听web页状态变化
  useEffect(() => {
    document.addEventListener('visibilitychange', handleSyncWorkflowDraftWhenPageClose)

    return () => {
      document.removeEventListener('visibilitychange', handleSyncWorkflowDraftWhenPageClose)
    }
  }, [handleSyncWorkflowDraftWhenPageClose])

  // 快捷键的防止默认操作
  useEventListener('keydown', (e) => {
    if ((e.key === 'd' || e.key === 'D') && (e.ctrlKey || e.metaKey))
      e.preventDefault()
    if ((e.key === 'z' || e.key === 'Z') && (e.ctrlKey || e.metaKey))
      e.preventDefault()
    if ((e.key === 'y' || e.key === 'Y') && (e.ctrlKey || e.metaKey))
      e.preventDefault()
    if ((e.key === 's' || e.key === 'S') && (e.ctrlKey || e.metaKey))
      e.preventDefault()
  })
  // 监听鼠标移动
  useEventListener('mousemove', (e) => {
    const containerClientRect = workflowContainerRef.current?.getBoundingClientRect()

    if (containerClientRect) {
      workflowStore.setState({
        mousePosition: {
          pageX: e.clientX,
          pageY: e.clientY,
          elementX: e.clientX - containerClientRect.left,
          elementY: e.clientY - containerClientRect.top,
        },
      })
    }
  })
  // 全局监听工作流变化，同步更新画布
  eventEmitter?.useSubscription((v: any) => {
    if (v.type === WORKFLOW_DATA_UPDATE) {
      setNodes(v.payload.nodes)
      setEdges(v.payload.edges)

      if (v.payload.viewport)
        reactflow.setViewport(v.payload.viewport)

      if (v.payload.features && featuresStore) {
        const { setFeatures } = featuresStore.getState()

        setFeatures(v.payload.features)
      }

      if (v.payload.hash)
        setSyncWorkflowDraftHash(v.payload.hash)

      setTimeout(() => setControlPromptEditorRerenderKey(Date.now()))
    }
  })

  const {
    handleEdgeEnter,
    handleEdgeLeave,
    handleEdgesChange,
  } = useEdgesInteractions()
  const {
    handleSelectionStart,
    handleSelectionChange,
    handleSelectionDrag,
  } = useSelectionInteractions()
  const {
    handlePaneContextMenu,
  } = usePanelInteractions()
  const {
    isValidConnection,
  } = useWorkflow()

  useOnViewportChange({
    onEnd: () => {
      handleSyncWorkflowDraft()
    },
  })

  useShortcuts()

  const store = useStoreApi()
  if (process.env.NODE_ENV === 'development') {
    store.getState().onError = (code, message) => {
      if (code === '002')
        return
      console.warn(message)
    }
  }

  return (
    <div
      id='workflow-container'
      className={`
        relative w-full h-full bg-[#F0F2F7]
        ${workflowReadOnly && 'workflow-panel-animation'}
        ${nodeAnimation && 'workflow-node-animation'}
      `}
      ref={workflowContainerRef}
    >
      <CandidateNode />
      <Header />
      <Panel />
      <Operator handleRedo={handleHistoryForward} handleUndo={handleHistoryBack} />
      {
        showFeaturesPanel && <Features />
      }
      <PanelContextmenu />
      <HelpLine />
      {
        !!showConfirm && (
          <Confirm
            isShow
            onCancel={() => setShowConfirm(undefined)}
            onConfirm={showConfirm.onConfirm}
            title={showConfirm.title}
            content={showConfirm.desc}
          />
        )
      }
      <LimitTips />
      <ReactFlow
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        nodes={nodes}
        edges={edges}
        onNodeDragStart={handleNodeDragStart}
        onNodeDrag={handleNodeDrag}
        onNodeDragStop={handleNodeDragStop}
        onNodeMouseEnter={handleNodeEnter}
        onNodeMouseLeave={handleNodeLeave}
        onNodeClick={handleNodeClick}
        onNodeContextMenu={handleNodeContextMenu}
        onConnect={handleNodeConnect}
        onConnectStart={handleNodeConnectStart}
        onConnectEnd={handleNodeConnectEnd}
        onEdgeMouseEnter={handleEdgeEnter}
        onEdgeMouseLeave={handleEdgeLeave}
        onEdgesChange={handleEdgesChange}
        onSelectionStart={handleSelectionStart}
        onSelectionChange={handleSelectionChange}
        onSelectionDrag={handleSelectionDrag}
        onPaneContextMenu={handlePaneContextMenu}
        onPaneClick={() => handleNodeSelect('', true)}
        connectionLineComponent={CustomConnectionLine}
        connectionLineContainerStyle={{ zIndex: ITERATION_CHILDREN_Z_INDEX }}
        defaultViewport={viewport}
        multiSelectionKeyCode={null}
        deleteKeyCode={null}
        nodesDraggable={!nodesReadOnly}
        nodesConnectable={!nodesReadOnly}
        nodesFocusable={!nodesReadOnly}
        edgesFocusable={!nodesReadOnly}
        panOnDrag={controlMode === ControlMode.Hand && !workflowReadOnly}
        zoomOnPinch={!workflowReadOnly}
        zoomOnScroll={!workflowReadOnly}
        zoomOnDoubleClick={!workflowReadOnly}
        isValidConnection={isValidConnection}
        selectionKeyCode={null}
        selectionMode={SelectionMode.Partial}
        selectionOnDrag={controlMode === ControlMode.Pointer && !workflowReadOnly}
        minZoom={0.25}
        // 连接线吸附判断距离
        connectionRadius={50}
      >
        <Background
          gap={0}
          size={2}
          color='#F4F5F9'
        />
      </ReactFlow>
      {
        agentModeConfig.show
          && <SelectAgentMode
            onHide={() => setAgentModeConfig({ show: false, callback: () => {} })}
            onFinish={(value) => {
              agentModeConfig.callback(value)
              setAgentModeConfig({
                show: false,
                callback: () => {},
              })
            }}
          ></SelectAgentMode>
      }
    </div>
  )
})
Workflow.displayName = 'Workflow'

const WorkflowWrap = memo(() => {
  const {
    data,
    isLoading,
    chatBgConfig,
  } = useWorkflowInit()
  const { isPrivate } = useSystemContext()

  const nodesData = useMemo(() => {
    if (data)
      return initialNodes(data.graph.nodes, data.graph.edges)

    return []
  }, [data])
  const edgesData = useMemo(() => {
    if (data)
      return initialEdges(data.graph.edges, data.graph.nodes)

    return []
  }, [data])

  const setAppPublishInfo = useAppStore(state => state.setAppPublishInfo)
  const {
    appPublishInfo,
  } = useAppPublishInit()
  useEffect(() => {
    setAppPublishInfo(appPublishInfo)
  }, [appPublishInfo])

  if (!data || !chatBgConfig || isLoading) {
    return (
      <div className='flex justify-center items-center relative w-full h-full bg-[#F0F2F7]'>
        <Loading />
      </div>
    )
  }
  // 工作流应用初始化feature =>查询数据
  const features = data.features || {}
  const voice_input_data = features?.text_to_speech?.voice_input || { enabled: false }
  const voice_conversation_data = features?.text_to_speech?.voice_conversation || { enabled: false }
  const initialFeatures: FeaturesData = {
    file: {
      enabled: !!(features.file_upload?.enabled || features.file_upload?.image?.enabled),
      allowed_file_types: features.file_upload?.allowed_file_types || [SupportUploadFileTypes.image],
      allowed_file_extensions: features.file_upload?.allowed_file_extensions || FILE_EXTS[SupportUploadFileTypes.image].map(ext => `.${ext}`),
      allowed_file_upload_methods: features.file_upload?.allowed_file_upload_methods || features.file_upload?.image?.transfer_methods || ['local_file', 'remote_url'],
      number_limits: features.file_upload?.number_limits || features.file_upload?.image?.number_limits || 3,
    },
    opening: {
      enabled: !!features.opening_statement,
      opening_statement: features.opening_statement,
      suggested_questions: features.suggested_questions,
    },
    suggested: features.suggested_questions_after_answer || { enabled: false },
    speech2text: features.speech_to_text || { enabled: false },
    // text2speech: features.text_to_speech || { enabled: false },
    text2speech: {
      ...features.text_to_speech,
      enabled: features.text_to_speech?.enabled || false,
      voice_input: {
        ...voice_input_data,
        enabled: !isPrivate && features.text_to_speech?.voice_input?.enabled,
      },
      voice_conversation: {
        ...voice_conversation_data,
        enabled: !isPrivate && features.text_to_speech?.voice_conversation?.enabled,
      },
    },
    citation: features.retriever_resource || { enabled: false },
    moderation: features.sensitive_word_avoidance || { enabled: false },
    chatBackground: chatBgConfig,
  }
  // 处理获取不了默认值情况
  if (!((initialFeatures?.text2speech ?? {}).voice_input?.enabled)) {
    initialFeatures!.text2speech!.voice_input = {
      ...initialFeatures!.text2speech!.voice_input,
      language: 1,
      timbre: 'yixiaoling',
    }
  }
  if ((!((initialFeatures?.text2speech ?? {}).voice_conversation?.enabled))) {
    initialFeatures!.text2speech!.voice_conversation = {
      ...initialFeatures.text2speech?.voice_conversation,
      language: 1,
      timbre: 'yixiaoling',
    }
  }

  return (
    <ReactFlowProvider>
      <WorkflowHistoryProvider
        nodes={nodesData}
        edges={edgesData} >
        <FeaturesProvider features={initialFeatures} inWorkflow={true}>
          <Workflow
            nodes={nodesData}
            edges={edgesData}
            viewport={data?.graph.viewport}
          />
        </FeaturesProvider>
      </WorkflowHistoryProvider>
    </ReactFlowProvider>
  )
})
WorkflowWrap.displayName = 'WorkflowWrap'

const WorkflowContainer = () => {
  return (
    <WorkflowContextProvider>
      <WorkflowWrap />
    </WorkflowContextProvider>
  )
}

export default memo(WorkflowContainer)
