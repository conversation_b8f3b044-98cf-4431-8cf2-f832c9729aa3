'use client'
import s from '@/app/components/data-statistics/styles/style.module.scss'
import cn from '@/utils/classnames'
import { useTranslation } from 'react-i18next'
import { useAppContext } from '@/context/app-context'
import DataStatisticsContext from './context'
import useDataStatistics from './hooks'
import { Filter } from '@/app/components/base/icons/src/vender/statisticsData'
import React, { ReactNode, useCallback, useEffect, useState } from 'react';
import { Button, Dropdown, Menu, Select } from 'antd';
import StatisticsTable from '@/app/components/data-statistics/common/statistics-table/index'
import DataSummary from '@/app/components/data-statistics/data-summary/index'
import PriceListModal from '@/app/components/data-statistics/price-list-modal/index'
import Link from 'next/link'
import MultiplSelect from '@/app/components/base/select/multiple-select'
import useFeatureAccess from '@/hooks/use-feature-access'
import TextButton from '../base/button/text-button';
import { useSystemContext } from '@/context/system-context'


// 表格配置类型
interface TableConfig {
  key: string;
  tableTitle: string;
  columns: any[];
  dataSource: any[];
  titleExtra?: ReactNode;  
  tableExtra?: ReactNode;  
  api?: () => Promise<any>; // 接口调用函数
  dataTransform?: (data: any) => any[]; // 数据转换函数
  isShow: boolean;
}

const DataStatistics = () => {
  const { t } = useTranslation()
  
  const { userProfile, currentWorkspace } = useAppContext()
  const { isPrivate } = useSystemContext()
  const { is_parent, is_vip } = userProfile // is_parent是否主账号，is_vip是否付费版
  const { plan } = currentWorkspace // plan: 'basic'个人版 |'team'团队版

  // 权限属性
  const accessAttr = {
  consumeDetail: useFeatureAccess({
    allowPaid: true,    // 仅付费版
    allowBasic: true,  // 基础版不可见
    allowAdmin: true,   // 主账号 
    allowSub: false,    // 子账号不可见
    allowTeam: false,    // 团队空间
    allowPersonal: true, // 个人空间
    allowPrivatePersonal: true, // 私有化个人空间
    allowPrivateTeam: false, // 私有化团队空间
  }),
  personalSpace: useFeatureAccess({
    allowPaid: true,    // 仅付费版
    allowBasic: false,  // 基础版不可见
    allowAdmin: true,   // 主账号  
    allowSub: true,    // 子账号不可见
    allowTeam: true,    // 团队空间 
    allowPersonal: true,
    allowPrivatePersonal: true,
    allowPrivateTeam: true,
  }),
  personalSpaceText: useFeatureAccess({
    allowPaid: true,    // 仅付费版
    allowBasic: false,  // 基础版不可见
    allowAdmin: true,   // 主账号  
    allowSub: false,    // 子账号不可见
    allowTeam: false,    // 团队空间
    allowPersonal: true,
    allowPrivatePersonal: false,
    allowPrivateTeam: false,
  }),
  paidAdminText: useFeatureAccess({
    allowPaid: true,    // 仅付费版
    allowBasic: false,  // 基础版不可见
    allowAdmin: true,   // 主账号  
    allowSub: false,    // 子账号不可见
    allowTeam: false,    // 团队空间
    allowPersonal: true,
    allowPrivatePersonal: false,
    allowPrivateTeam: false,
  }),
  appConsumeText: useFeatureAccess({
    allowPaid: true,    // 仅付费版
    allowBasic: true,  // 基础版不可见
    allowAdmin: true,   // 主账号  
    allowSub: true,    // 子账号不可见
    allowTeam: true,    // 团队空间 
    allowPersonal: true,
    allowPrivatePersonal: false,
    allowPrivateTeam: false,
  }),
  myConsume: useFeatureAccess({
    allowPaid: false,    // 仅付费版
    allowBasic: false,  // 基础版不可见
    allowAdmin: false,   // 主账号  
    allowSub: false,    // 子账号不可见
    allowTeam: false,    // 团队空间
    allowPersonal: false,
    allowPrivatePersonal: false,
    allowPrivateTeam: true,
  })
}

  const {
    resourcePoints,
    datasetUsage,
    appUsage,
    toolUsage
  } = useDataStatistics()
  
  // 跳转至详情页
  const getLinkOptions = (type: any) => {
    return {
      pathname: '/data-statistics/detail',
      query: { type },
    }
  }
  // 表格配置
  const tableConfigs: TableConfig[] = [
    {
      key: '1',
      tableTitle: t('dataStatistics.resourcePoints.title'),
      isShow: useFeatureAccess({
        allowPaid: true,    // 仅付费版
        allowBasic: false,  // 基础版
        allowAdmin: true,   // 主账号 
        allowSub: false,    // 子账号不可见
        allowTeam: false,    // 团队空间
        allowPersonal: true,
        allowPrivatePersonal: false, // 私有化个人空间
        allowPrivateTeam: false, // 私有化团队空间
      }),
      columns: [
        { title: t('dataStatistics.resourcePoints.purchaseDate'), dataIndex: 'purchaseDate' },
        { title: t('dataStatistics.resourcePoints.purchasePoints'), dataIndex: 'purchasePoints' },
        { title: t('dataStatistics.resourcePoints.availablePoints'), dataIndex: 'availablePoints' },
        { title: t('dataStatistics.resourcePoints.expirationTime'), dataIndex: 'expirationTime' },
        { title: t('dataStatistics.resourcePoints.daysRemaining'), dataIndex: 'daysRemaining' },
      ],
      dataSource: [
        {purchaseDate: '2025-03-03 10:10:10', purchasePoints: '10000', availablePoints: '10000', expirationTime: '2025-03-03 10:10:10', daysRemaining: '20' }
      ],
      tableExtra: (
        <MultiplSelect
          className="w-[300px]"
          defaultValue={[]}
          placeholder={t('dataStatistics.resourcePoints.timeSelect.placeholder')}
          options={[
            { value: '1', label: t('dataStatistics.resourcePoints.timeSelect.last1Month')},
            { value: '2', label: t('dataStatistics.resourcePoints.timeSelect.last3Months')},
            { value: '3', label: t('dataStatistics.resourcePoints.timeSelect.last5Months')},
            { value: '4', label: t('dataStatistics.resourcePoints.timeSelect.last7Months')},
            { value: '5', label: t('dataStatistics.resourcePoints.timeSelect.last9Months')},
            { value: '6', label: t('dataStatistics.resourcePoints.timeSelect.last12Months')},
          ]}
          onChange={()=>{}}
          maxTagCount={'responsive'}
          showSearch={false}
        />
      )
      // api: fetchUsers, // 假设这是封装好的API函数
      // dataTransform: (data) => data.list, //  如果接口返回数据需要处理
    },
    {
      key: '2',
      tableTitle: t('dataStatistics.modelPointsConsum.title'),
      isShow: useFeatureAccess({
        allowPaid: true,    // 仅付费版
        allowBasic: true,  // 基础版
        allowAdmin: true,   // 主账号 
        allowSub: false,    // 子账号不可见
        allowTeam: false,    // 团队空间
        allowPersonal: true,
        allowPrivatePersonal: false, // 私有化个人空间
        allowPrivateTeam: false, // 私有化团队空间
      }),
      columns: [
        { title: t('dataStatistics.modelPointsConsum.model'), dataIndex: 'internalModel' },
        { title: t('dataStatistics.modelPointsConsum.input'), dataIndex: 'inputConsumePoints' },
        { title: t('dataStatistics.modelPointsConsum.output'), dataIndex: 'outConsumePoints' },
        { title: t('dataStatistics.modelPointsConsum.all'), dataIndex: 'consumePoints' },
      ],
      dataSource: [
        {internalModel: '千问', inputConsumePoints: '10000', outConsumePoints: '10000', consumePoints: '10000' },
        {internalModel: '模型名称2', inputConsumePoints: '10000', outConsumePoints: '10000', consumePoints: '10000' },
      ],
      titleExtra: <TextButton variant="primary" onClick={() => setShowPriceListModal(true)}>{t(`dataStatistics.action.getPriceList`)}</TextButton>,
      tableExtra: <Link href={getLinkOptions('model')} className='text-btn text-btn-primary text-btn-small'>{t(`dataStatistics.action.detail`)}</Link>,
    },
    {
      key: '3',
      tableTitle: t('dataStatistics.modelTokenConsum.title'),
      isShow: useFeatureAccess({
        allowPaid: true,    // 仅付费版
        allowBasic: true,  // 基础版
        allowAdmin: true,   // 主账号 
        allowSub: false,    // 子账号不可见
        allowTeam: false,    // 团队空间
        allowPersonal: true,
        allowPrivatePersonal: true, // 私有化个人空间
        allowPrivateTeam: false, // 私有化团队空间
      }),
      columns: [ 
        { title: t('dataStatistics.modelTokenConsum.model'), dataIndex: 'internalModel' },
        { title: t('dataStatistics.modelTokenConsum.input'), dataIndex: 'inputConsumeToken' },
        { title: t('dataStatistics.modelTokenConsum.output'), dataIndex: 'outConsumeToken' },
        { title: t('dataStatistics.modelTokenConsum.all'), dataIndex: 'consumeToken' },
      ],
      dataSource: [
        {internalModel: '千问', inputConsumeToken: '10000', outConsumeToken: '10000', consumeToken: '10000' },
        {internalModel: '模型名称2', inputConsumeToken: '10000', outConsumeToken: '10000', consumeToken: '10000' },
      ],
        tableExtra:  <Link href={getLinkOptions('token')} className='text-btn text-btn-primary text-btn-small'>{t(`dataStatistics.action.detail`)}</Link>,
      // api: fetchProducts, 
    },
  ];
  // 模型及点数消耗数据表格配置
  const consumeTableConfigs: TableConfig[] = [
    {
      key: '1',
      tableTitle: t('dataStatistics.modelTokenConsum.title'),
      isShow: useFeatureAccess({
        allowPaid: true,    // 仅付费版
        allowBasic: false,  // 基础版不可见
        allowAdmin: true,   // 主账号  
        allowSub: true,    // 子账号不可见
        allowTeam: true,    // 团队空间 
        allowPersonal: true,
        allowPrivatePersonal: false,
        allowPrivateTeam: true,
      }),
      columns: [ 
        { title: t('dataStatistics.modelTokenConsum.model'), dataIndex: 'internalModel' },
        { title: t('dataStatistics.modelTokenConsum.input'), dataIndex: 'inputConsumeToken' },
        { title: t('dataStatistics.modelTokenConsum.output'), dataIndex: 'outConsumeToken' },
        { title: t('dataStatistics.modelTokenConsum.all'), dataIndex: 'consumeToken' },
      ],
      dataSource: [
        {internalModel: '千问', inputConsumeToken: '10000', outConsumeToken: '10000', consumeToken: '10000' },
        {internalModel: '模型名称2', inputConsumeToken: '10000', outConsumeToken: '10000', consumeToken: '10000' },
      ],
        tableExtra: <Link href={getLinkOptions('token')} className='text-btn text-btn-primary text-btn-small'>{t(`dataStatistics.action.detail`)}</Link>,
      // api: fetchProducts, 
    },
    {
      key: '2',
      tableTitle: t('dataStatistics.modelPointsConsum.title'),
      isShow: useFeatureAccess({
        allowPaid: true,    // 仅付费版
        allowBasic: false,  // 基础版不可见
        allowAdmin: true,   // 主账号  
        allowSub: true,    // 子账号不可见
        allowTeam: true,    // 团队空间 
        allowPersonal: true,
        allowPrivatePersonal: false,
        allowPrivateTeam: false,
      }),
      columns: [
        { title: t('dataStatistics.modelPointsConsum.model'), dataIndex: 'internalModel' },
        { title: t('dataStatistics.modelPointsConsum.input'), dataIndex: 'inputConsumePoints' },
        { title: t('dataStatistics.modelPointsConsum.output'), dataIndex: 'outConsumePoints' },
        { title: t('dataStatistics.modelPointsConsum.all'), dataIndex: 'consumePoints' },
      ],
      dataSource: [
        {internalModel: '千问', inputConsumePoints: '10000', outConsumePoints: '10000', consumePoints: '10000' },
        {internalModel: '模型名称2', inputConsumePoints: '10000', outConsumePoints: '10000', consumePoints: '10000' },
      ], 
      tableExtra:  <Link href={getLinkOptions('model')} className='text-btn text-btn-primary text-btn-small'>{t(`dataStatistics.action.detail`)}</Link>,
    },
  ];
  // 归属本人的消耗
  const myConsumeTableConfigs: TableConfig[] = [
    {
      key: '1',
      tableTitle: t('dataStatistics.modelTokenConsum.title'),
      isShow: useFeatureAccess({
        allowPaid: true,    // 仅付费版
        allowBasic: false,  // 基础版不可见
        allowAdmin: true,   // 主账号  
        allowSub: true,    // 子账号不可见
        allowTeam: true,    // 团队空间 
        allowPersonal: true,
        allowPrivatePersonal: false,
        allowPrivateTeam: true,
      }),
      columns: [ 
        { title: t('dataStatistics.modelTokenConsum.model'), dataIndex: 'internalModel' },
        { title: t('dataStatistics.modelTokenConsum.input'), dataIndex: 'inputConsumeToken' },
        { title: t('dataStatistics.modelTokenConsum.output'), dataIndex: 'outConsumeToken' },
        { title: t('dataStatistics.modelTokenConsum.all'), dataIndex: 'consumeToken' },
      ],
      dataSource: [
        {internalModel: '千问', inputConsumeToken: '10000', outConsumeToken: '10000', consumeToken: '10000' },
        {internalModel: '模型名称2', inputConsumeToken: '10000', outConsumeToken: '10000', consumeToken: '10000' },
      ],
        tableExtra: <Link href={getLinkOptions('token')} className='text-btn text-btn-primary text-btn-small'>{t(`dataStatistics.action.detail`)}</Link>,
      // api: fetchProducts, 
    },
  ];
    // 应用表格配置
    const appTableConfigs: TableConfig[] = [
    {
      key: '1',
      tableTitle: t('dataStatistics.appApiCalls.title'), 
      isShow: true,
      columns: [ 
        { title: t('dataStatistics.appApiCalls.name'), dataIndex: 'name' },
        { title: t('dataStatistics.appApiCalls.callNum'), dataIndex: 'callNum' },
      ],
      dataSource: [
        {name: '千问', callNum: '10000' },
        {name: '模型名称2', callNum: '10000' },
      ],
      tableExtra:  <Link href={getLinkOptions('app')} className='text-btn text-btn-primary text-btn-small'>{t(`dataStatistics.action.detail`)}</Link>,
      // api: fetchProducts, 
    },
  ];
  // 显示价格清单弹窗
  const [showPriceListModal, setShowPriceListModal] = useState(false)
  return (
    <DataStatisticsContext.Provider value={{
      resourcePoints,
      datasetUsage,
      appUsage,
      toolUsage
    }}>
      <div className={s['header-title']}>
        <span>{t('dataStatistics.title')}</span>
        {/* <div className={s['day-count-down']}>
          <Filter />
          <span>{t('dataStatistics.dayCount', {day: 100})}</span>
        </div> */}
      </div>
      {/* 主账号权益下数据消耗详情 */}
      {accessAttr['consumeDetail'] && 
        <div className={s['dataStatistics-consume-detail']}>
          {accessAttr['paidAdminText'] && (
            <div className={s['sub-title']}>{t('dataStatistics.primaryAccountDataSummary')}</div>
          )}
          <div className={s['table-flex']}>
            <DataSummary />
            {/* {tableConfigs && tableConfigs.map(config => (
                config.isShow && <StatisticsTable
                  tableTitle={config.tableTitle}
                  key={config.key}
                  titleExtra={config.titleExtra}
                  tableExtra={config.tableExtra}
                  columns={config.columns}
                  dataSource={config.dataSource}
                  rowKey="id"
            />))} */}
          </div>
        </div>
      }
      {/* 个人空间 / 归属我本人的消耗 */}
      {/* {accessAttr['personalSpaceText'] && (<div className={s['header-title']}>{t('dataStatistics.personalSpace')}</div>)} */}
      <div className={s['personal-space-content']}>
        {/* 模型及点数消耗数据 */}
        {/* {accessAttr['personalSpace'] && <div className={s['model-consume-detail']}> 
          <div className={s['sub-title']}>{isPrivate ? (plan === 'team' ? t('dataStatistics.teamSpaceConsume') : t('dataStatistics.personalSpaceConsume'))
          : t('dataStatistics.personalSpaceSubTitle')}</div> 
          <div className={s['table-flex']}>
            {consumeTableConfigs && consumeTableConfigs.map(config => (
              config.isShow && <StatisticsTable 
                tableTitle={config.tableTitle}
                key={config.key}
                titleExtra={config.titleExtra}
                tableExtra={config.tableExtra}
                columns={config.columns}
                dataSource={config.dataSource}
                rowKey="id"
              />
              ))}
          </div>
        </div>} */}
         {/* 应用 */}
         {/* <div className={s['application-detail']}>
          {accessAttr['appConsumeText'] && <div className={s['sub-title']}>{t('dataStatistics.app')}</div> }
          <div className={cn(s['table-flex'], 'first:mt-4')}>
            {appTableConfigs && appTableConfigs.map(config => (
              config.isShow && <StatisticsTable 
                tableTitle={config.tableTitle}
                key={config.key}
                titleExtra={config.titleExtra}
                tableExtra={config.tableExtra}
                columns={config.columns}
                dataSource={config.dataSource}
                rowKey="id"
              />
              ))}
          </div>
        </div>  */}
      </div>
      {/* 归属我本人的消耗 */}
      {/* <div className={s['personal-space-content']}>
        {accessAttr['myConsume'] && <div className={s['model-consume-detail']}> 
          <div className={s['sub-title']}>{t('dataStatistics.myConsume')}</div> 
          <div className={s['table-flex']}>
            {myConsumeTableConfigs && myConsumeTableConfigs.map(config => (
              config.isShow && <StatisticsTable 
                tableTitle={config.tableTitle}
                key={config.key}
                titleExtra={config.titleExtra}
                tableExtra={config.tableExtra}
                columns={config.columns}
                dataSource={config.dataSource}
                rowKey="id"
              />
              ))}
          </div>
        </div>}
      </div> */}
     {showPriceListModal && <PriceListModal onCancel={() => setShowPriceListModal(false)} />}
    </DataStatisticsContext.Provider>
  )
}
export default DataStatistics
  