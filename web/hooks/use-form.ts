import type { FormInstance } from 'antd'
import { Form } from 'antd'
import { debounce } from 'lodash-es'
import type { RefObject } from 'react'
import { useCallback, useEffect, useRef, useState } from 'react'

// antd表单禁用hook
export const useFormDisabled = (form: FormInstance) => {
  // 表单是否禁用
  const [disabled, setDisabled] = useState(true)
  const values = Form.useWatch([], form)

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(debounce(() => {
    form.validateFields({ validateOnly: true }).then(() => {
      setDisabled(false)
    },
    ).catch(() => {
      setDisabled(true)
    })
  }, 100), [form, values])

  return disabled
}
export const useFormDisabledRef = (form: FormInstance) => {
  // 表单是否禁用
  const disabledRef = useRef(true)
  const values = Form.useWatch([], form)

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(debounce(() => {
    form.validateFields({ validateOnly: true }).then(() => {
      disabledRef.current = false
    },
    ).catch(() => {
      disabledRef.current = true
    })
  }, 100), [form, values])

  return disabledRef.current
}
// antd popover补充hook
export const usePopoverClick = (open: boolean, ref: RefObject<HTMLDivElement>, callback: (open: boolean) => void) => {
  const isChildNode = (node: any, parentNode: any) => {
    if (node === parentNode)
      return true

    else if (node.parentNode)
      return isChildNode(node.parentNode, parentNode)

    else
      return false
  }
  const closeModal = useCallback((e: any) => {
    if (open) {
      let shouldClose = false
      if (ref.current)
        shouldClose = !isChildNode(e.target, ref.current)

      shouldClose && callback(false)
    }
  }, [callback, open, ref])
  useEffect(() => {
    document.addEventListener('click', closeModal)
    return () => document.removeEventListener('click', closeModal)
  }, [closeModal]) // eslint-disable-lin
}
