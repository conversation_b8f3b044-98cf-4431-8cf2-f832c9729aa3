import type { ToolItem, UserInputFormItem } from './tools'
import type { CompletionParams } from './app'
import type { FormValue } from '@/app/components/account-setting/model-provider-page/declarations'
import type { AnnotationReplyConfig, ChatPromptConfig, CompletionPromptConfig, DatasetConfigs, PromptMode } from '@/models/debug'
import type { UploadFileSetting } from '@/app/components/workflow/types'
import type { AppChatBgConfig } from '@/app/components/base/features/types'
import type { ExternalDataTool } from '@/models/common'

export enum BasicParamModeEnum {
  normal = 'normal',
  precise = 'precise',
  creative = 'creative',
  custom = 'custom',
}
export enum AdvancedParamModeEnum {
  normal = 'normal',
  custom = 'custom',
}
export enum TtsAutoPlay {
  enabled = 'enabled',
  disabled = 'disabled',
}
export enum Resolution {
  low = 'low',
  high = 'high',
}
export enum AgentStrategy {
  functionCall = 'function_call',
  react = 'react',
}
export enum TransferMethod {
  all = 'all',
  local_file = 'local_file',
  remote_url = 'remote_url',
}
export type VisionFile = {
  id?: string
  type: string
  transfer_method: TransferMethod
  url: string
  upload_file_id: string
  belongs_to?: string
}
export enum ModelModeType {
  'chat' = 'chat',
  'completion' = 'completion',
  'unset' = '',
}
export type ModelConfigStorage = {
  id?: string
  userId: string
  appId: string
  provider: string
  modelId: string
  params?: FormValue
  basicParamMode?: BasicParamModeEnum
  advancedParamMode?: AdvancedParamModeEnum
  openAdvancedParamMode?: boolean
}
export type voiceInputConfig = {
  enabled: boolean
  language?: string
  timbre?: string
  auto_play?: boolean
}
export type voiceConversationConfig = {
  enabled: boolean
  language?: string
  timbre?: string
}
export type VisionSettings = {
  enabled: boolean
  number_limits: number
  detail: Resolution
  transfer_methods: TransferMethod[]
  image_file_size_limit?: number | string
}

export type Model = {
  /** LLM provider, e.g., OPENAI */
  provider: string
  /** Model name, e.g, gpt-3.5.turbo */
  name: string
  mode: ModelModeType
  /** Default Completion call parameters */
  completion_params: CompletionParams
  basic_param_mode?: string
}

export type ModelConfig = {
  opening_statement: string
  suggested_questions?: string[]
  // voice_to_input: {
  //   enabled: boolean
  //   language: string
  //   timbre: string
  //   auto_play?: boolean
  // }
  // voice_to_conversation: {
  //   enabled: boolean
  //   language: string
  //   timbre: string
  // }
  // voice_speed: string
  pre_prompt: string
  prompt_type: PromptMode
  chat_prompt_config: ChatPromptConfig
  completion_prompt_config: CompletionPromptConfig | {}
  user_input_form: UserInputFormItem[]
  dataset_query_variable?: string
  more_like_this: {
    enabled?: boolean
  }
  suggested_questions_after_answer: {
    enabled: boolean
  }
  speech_to_text: {
    enabled: boolean
  }
  text_to_speech: {
    enabled: boolean
    voice?: string
    language?: string
    autoPlay?: TtsAutoPlay
    voice_input: voiceInputConfig
    voice_conversation: voiceConversationConfig
  }
  retriever_resource: {
    enabled: boolean
  }
  sensitive_word_avoidance: {
    enabled: boolean
  }
  annotation_reply?: AnnotationReplyConfig
  agent_mode: {
    enabled: boolean
    strategy?: AgentStrategy
    tools: ToolItem[]
    fork: boolean
  }
  model: Model
  dataset_configs: DatasetConfigs
  file_upload?: {
    image: VisionSettings
  } & UploadFileSetting
  files?: VisionFile[]
  created_at?: number
  updated_at?: number
  background_configs?: AppChatBgConfig
  external_data_tools: ExternalDataTool[]
}
