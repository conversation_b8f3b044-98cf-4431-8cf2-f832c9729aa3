'use client'
import type { FC } from 'react'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useContextSelector } from 'use-context-selector'
import { Input as AInput, Form, Switch } from 'antd'

import { debounce } from 'lodash-es'
import s from './style.module.css'
import type { AppDetailResponse } from '@/models/app'
import type { AppIconType, AppSSO } from '@/types/app'
import { useAppContext } from '@/context/app-context'
import type { SiteConfig } from '@/types/share'
import { MAX_APP_DESC_LENGTH, MAX_APP_NAME_LENGTH } from '@/config'

import Input from '@/app/components/base/input'
import Avatar from '@/app/components/base/avatar'
import Modal from '@/app/components/base/modal'
import Button from '@/app/components/base/button'
import SystemContext from '@/context/system-context'
const { TextArea } = AInput

export type ISettingsModalProps = {
  isChat: boolean
  appInfo: AppDetailResponse & Partial<AppSSO>
  siteInfo: SiteConfig
  isShow: boolean
  defaultValue?: string
  onClose: () => void
  onSave?: (params: ConfigParams) => Promise<void>
}

export type ConfigParams = {
  title: string
  description: string
  default_language: string
  chat_color_theme: string
  chat_color_theme_inverted: boolean
  prompt_public: boolean
  copyright: string
  privacy_policy: string
  custom_disclaimer: string
  icon_type: AppIconType
  icon: string
  icon_background?: string
  show_workflow_steps: boolean
  use_icon_as_answer_icon: boolean
  enable_sso?: boolean
}

const prefixSettings = 'appOverview.overview.appInfo.settings'

const SettingsModal: FC<ISettingsModalProps> = ({
  isChat,
  appInfo,
  siteInfo,
  isShow = false,
  onClose,
  onSave,
}) => {
  const systemFeatures = useContextSelector(SystemContext, state => state.systemFeatures)
  const { canEditApp } = useAppContext()
  const [form] = Form.useForm()
  const values = Form.useWatch([], form)



  // 是否显示更多
  const [isShowMore, setIsShowMore] = useState(false)
  // 保存按钮disabled
  const [disabled, setDisabled] = useState(false)

  const { icon_url } = appInfo.site

  const { t } = useTranslation()

  useEffect(() => {
    if (appInfo) {
      const {
        title,
        description,
        chat_color_theme,
        chat_color_theme_inverted,
        copyright,
        privacy_policy,
        custom_disclaimer,
        default_language,
        use_icon_as_answer_icon,
        show_workflow_steps,
        icon_type,
        icon,
        icon_url,
        icon_background,
      } = appInfo.site
      form.setFieldsValue({
        title,
        description,
        chat_color_theme,
        chat_color_theme_inverted,
        copyright,
        privacy_policy,
        custom_disclaimer,
        default_language,
        use_icon_as_answer_icon,
        show_workflow_steps,
        enable_sso: appInfo.enable_sso,
        icon_type,
        icon_url,
        icon_background,
        icon,
      })
    }
  }, [appInfo, form])
  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(debounce(() => {
    form.validateFields({ validateOnly: true }).then(() => {
      setDisabled(false)
    },
    ).catch(() => {
      setDisabled(true)
    })
  }, 100), [form, values])

  // 隐藏窗口
  const onHide = () => {
    onClose()
    setTimeout(() => {
      setIsShowMore(false)
    }, 200)
  }
  // 保存app设置
  const onClickSave = async () => {
    const params = {
      prompt_public: false,
      ...form.getFieldsValue(),
    }
    await onSave?.(params)
    onHide()
  }

  return (
    <>
      <Modal
        title={t(`${prefixSettings}.title`)}
        isShow={isShow}
        onClose={onHide}
        closable
        className={`${s.settingsModal}`}
        footer={<>
          <Button variant={'secondary-accent'} className='w-[92px] mr-2' onClick={onHide}>{t('common.operation.cancel')}</Button>
          <Button variant='primary' disabled={disabled} className='w-[92px]' onClick={onClickSave}>{t('common.operation.save')}</Button>
        </>}
      >
        <Form form={form} layout='vertical'>
          {/* webapp名称 */}
          <Form.Item required label={t(`${prefixSettings}.webName`)}>
            <div className='flex gap-3 items-center'>
              <Avatar avatar={icon_url}/>
              <Form.Item
                label={t(`${prefixSettings}.webName`)}
                noStyle
                name={'title'}
                validateTrigger='onBlur'
                rules={[{
                  required: true, whitespace: true, max: MAX_APP_NAME_LENGTH,
                }]}
              >
                <Input placeholder={t('app.placeholder.appName') || ''} maxLength={MAX_APP_NAME_LENGTH}/>
              </Form.Item>
            </div>
          </Form.Item>
          {/* webapp描述 */}
          <Form.Item
            label={t(`${prefixSettings}.webDesc`)}
            tooltip={t(`${prefixSettings}.webDescTip`)}
            name={'description'}
            rules={[{
              required: true,
              whitespace: true,
              max: MAX_APP_DESC_LENGTH,
            }]}
          >
            <TextArea
              autoSize={{ minRows: 3, maxRows: 3 }}
              placeholder={t(`${prefixSettings}.webDescPlaceholder`) as string}
              maxLength={MAX_APP_DESC_LENGTH}
            />
          </Form.Item>
          {/* 语言选择 */}
          {/* <Form.Item
            label={t(`${prefixSettings}.language`)}
            required
            name={'default_language'}
          >
            <Select options={languages.filter(item => item.supported).map(v => ({ value: v.value, label: v.name }))}></Select>
          </Form.Item> */}
          {/* 工作流 */}
          <Form.Item
            label={t(`${prefixSettings}.workflow.title`)}
            tooltip={t(`${prefixSettings}.workflow.showDesc`)}
            layout='horizontal'
            name={'show_workflow_steps'}
          >
            <Switch
              disabled={!(appInfo.mode === 'workflow' || appInfo.mode === 'advanced-chat')}
            />
          </Form.Item>
          {/* 聊天颜色风格 */}
          {/* {isChat
            && <Form.Item
              label={t(`${prefixSettings}.chatColorTheme`)}
              tooltip={t(`${prefixSettings}.chatColorThemeDesc`)}
              name={'chat_color_theme'}
              rules={[{
                required: true,
                whitespace: true,
                pattern: /#([A-Fa-f0-9]{6})/,
              }]}
            >
              <Input
                placeholder='E.g #A020F0'
              />
            </Form.Item>} */}
          {systemFeatures.enable_web_sso_switch_component
          && <Form.Item
            label={t(`${prefixSettings}.sso.label`)}
            tooltip={t(`${prefixSettings}.sso.tooltip`)}
            layout='horizontal'
            name={'enable_sso'}
          >
            <Switch
              disabled={!systemFeatures.sso_enforced_for_web || !canEditApp}
            ></Switch>
          </Form.Item>}
          {/* 显示更多按钮 */}
          {/* {!isShowMore && <div className='w-full cursor-pointer mt-4' onClick={() => setIsShowMore(true)}>
            <div className='flex justify-between'>
              <div className={`font-semibold ${s.settingTitle} flex-grow text-gray-900`}>{t(`${prefixSettings}.more.entry`)}</div>
              <div className='flex-shrink-0 w-4 h-4 text-gray-500'>
                <ChevronRightIcon />
              </div>
            </div>
            <p className={`mt-1 ${s.policy} text-gray-500`}>{t(`${prefixSettings}.more.copyright`)} & {t(`${prefixSettings}.more.privacyPolicy`)}</p>
          </div>} */}
          {isShowMore && <>
            <Form.Item
              label={t(`${prefixSettings}.more.copyright`)}
              name={'copyright'}
            >
              <Input
                placeholder={t(`${prefixSettings}.more.copyRightPlaceholder`) as string}
              />
            </Form.Item>
            <Form.Item
              label={t(`${prefixSettings}.more.privacyPolicy`)}
              name={'privacy_policy'}
            >
              <Input
                placeholder={t(`${prefixSettings}.more.privacyPolicyPlaceholder`) as string}
              />
            </Form.Item>
            <Form.Item
              label={t(`${prefixSettings}.more.customDisclaimer`)}
              tooltip={t(`${prefixSettings}.more.customDisclaimerTip`)}
              name={'custom_disclaimer'}
            >
              <Input
                placeholder={t(`${prefixSettings}.more.customDisclaimerPlaceholder`) as string}
              />
            </Form.Item>
          </>}
        </Form>

      </Modal >
    </>

  )
}
export default React.memo(SettingsModal)
