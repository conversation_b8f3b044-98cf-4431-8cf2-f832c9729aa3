'use client'
import React, { memo, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useRouter } from 'next/navigation'
import { Form, Input } from 'antd'
import Verify from '../components/base/verify'
import style from './page.module.css'
import classNames from '@/utils/classnames'
import { getLoginPicCode, login } from '@/service/common'
import useRefreshToken from '@/hooks/use-refresh-token'

// 公共组件
import Button from '@/app/components/base/button'
import Toast from '@/app/components/base/toast'
import Scrollbar from '@/app/components/base/scrollbar'
import { useFormDisabled } from '@/hooks/use-form'

type Props = {
  onLogin: () => void
}
const NormalForm = ({ onLogin }: Props) => {
  const { t } = useTranslation()
  const { getNewAccessToken } = useRefreshToken()
  const router = useRouter()
  const [form] = Form.useForm()
  const disabled = useFormDisabled(form)
  // 图形验证码配置
  const [picConfig, setPicConfig] = useState<{ uuid: string; captcha_image: string }>()

  // 获取图形验证码
  const getPicCode = async () => {
    try {
      setPicConfig(undefined)
      const res = await getLoginPicCode()
      setPicConfig(res)
    }
    catch {
    }
  }
  // 邮箱登录
  const handleEmailPasswordLogin = async () => {
    const { email, password, pic_code } = form.getFieldsValue()
    const res = await login({
      url: '/login',
      body: {
        email,
        password,
        remember_me: true,
        pic_code,
        uuid: picConfig?.uuid,
      },
    })
    if (res.result === 'success') {
      localStorage.setItem('console_token', res.data.access_token)
      localStorage.setItem('sso_token', res.data.access_token)
      localStorage.setItem('refresh_token', res.data.refresh_token)
      getNewAccessToken()
      router.replace('/apps')
      if (onLogin)
        onLogin()
    }
    else {
      Toast.notify({
        type: 'error',
        message: res.data,
      })
    }
  }

  useEffect(() => {
    getPicCode()
  }, [])

  return (
    <>
      <Scrollbar className={style.content}>
        <Form form={form} layout='vertical'>
          <Form.Item name={'email'} label={t('login.info.email')}>
            <Input
              className={classNames(style.noAutofillBg, 'h-[42px]')}
              type="email"
              autoComplete='off'
              placeholder={t('login.placeholder.email') || ''}
            ></Input>
          </Form.Item>
          <Form.Item name={'password'} label={t('login.info.password')}>
            <Input
              className={classNames(style.noAutofillBg, 'h-[42px]')}
              onKeyDown={(e) => {
                if (e.key === 'Enter')
                  handleEmailPasswordLogin()
              }}
              type='password'
              autoComplete="off"
              placeholder={t('login.placeholder.password') || ''}
            />
          </Form.Item>
          {/* 验证码 */}
          <Form.Item className='!mb-10' name={'pic_code'} label={t('login.info.picCode')}>
            <Input
              className={classNames(style.noAutofillBg, 'h-[42px]')}
              autoComplete="off"
              placeholder={t('login.placeholder.picCode') || ''}
              suffix={
                <Verify onRefresh={getPicCode} width={86} height={30} url={picConfig ? `data:image/png;base64,${picConfig?.captcha_image}` : ''}></Verify>
              }
            />
          </Form.Item>
        </Form>
        <Button
          disabled={disabled}
          variant='primary'
          onClick={handleEmailPasswordLogin}
          className="w-full !h-[48px] !text-S4"
        >{t('login.action.signBtn')}</Button>
      </Scrollbar>
    </>
  )
}

export default memo(NormalForm)
