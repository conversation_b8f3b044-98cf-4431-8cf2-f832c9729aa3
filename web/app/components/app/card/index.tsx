'use client'

import { useRouter } from 'next/navigation'
import React, { useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { OperationsContent } from '../operations'
import type { App } from '@/types/app'
import { getRedirection } from '@/utils/app-redirection'
import { useAppContext } from '@/context/app-context'
/* 公共组件 */
import Card from '@/app/components/base/card'
import AppAvatar from '@/app/components/app/avatar'
import AppTag from '@/app/components/app/tag'
import Indicator from '@/app/components/base/indicator'

export type AppCardProps = {
  app: App
  onRefresh?: () => void
}

const AppCard = ({ app, onRefresh }: AppCardProps) => {
  const { canPublishApp, setLongDocState, canEditApp } = useAppContext()
  const { t } = useTranslation()
  const { push } = useRouter()

  // 是否发布
  const isPublish = useMemo(() => {
    return app.is_publish
  }, [app.is_publish])

  return (
    <Card
      onClick={() => {
        if (app.is_longtxt_type)
          setLongDocState(true)
        else
          setLongDocState(false)
        getRedirection(app, push)
      }}
      generateTag={() => AppTag({ appMode: app.mode })}
      generateHead={() => {
        return <AppAvatar url={app.icon_url || app.icon} size={48} className='!rounded'></AppAvatar>
      }}
      description={app.description || ''}
      title={app.name}
      layout='line'
    >
      <div className='flex items-center justify-between w-[316px] shrink-0'>
        {canPublishApp
          ? <Indicator color={isPublish ? 'green' : 'gray'}>
            { isPublish ? t('app.status.published') : t('app.status.unPublish') }
          </Indicator>
          : <div></div>}
        {canEditApp && <OperationsContent layout='line' app={app} fallback={onRefresh}></OperationsContent>}
      </div>
    </Card>
  )
}

export default React.memo(AppCard)
