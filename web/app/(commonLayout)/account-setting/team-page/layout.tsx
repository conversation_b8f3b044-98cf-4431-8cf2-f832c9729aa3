'use client'
import type { FC } from 'react'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { useRouter, useSearchParams } from 'next/navigation'
import { useTeamContext } from '@/app/components/account-setting/team-page/team-context'
// 公共组件
// import AppBasic from '@/app/components/app-detail/app-basic'
import AppBasicVariant from '@/app/components/app-detail/app-basic-variant'
import TopNavbar from '@/app/components/layout/top-navbar'

export type IAppDetailLayoutProps = {
  children: React.ReactNode
  params: { datasetId: string }
}
const TabLayout: FC<IAppDetailLayoutProps> = (props) => {
  const {
    children,
    params: { datasetId },
  } = props
  const { t } = useTranslation()
  const router = useRouter()
  const searchParams = useSearchParams()
  const team_name = searchParams.get('team_name')
  const { teamDataObj } = useTeamContext()

  return (
    <>
      {/* 顶栏 */}
      <TopNavbar
        navigation={[]}
        leftContent={
          <AppBasicVariant
            name={team_name || '--'}
          ></AppBasicVariant>
        }
        backFunc={() => router.push('/account-setting?category=team')}
      />
      <div className='h-[calc(100%-60px)]'>
        {children}
      </div>
    </>

  )
}
export default React.memo(TabLayout)
