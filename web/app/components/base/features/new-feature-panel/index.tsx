import React from 'react'
import type { OnFeaturesChange } from '../types'
import ConfigVision from './config-vision'
import MoreLikeThis from './more-like-this'
import FileUpload from './file-upload'
import ImageUpload from './image-upload'

type Props = {
  isChatMode: boolean
  disabled: boolean
  onChange?: OnFeaturesChange
  inWorkflow?: boolean
  showFileUpload?: boolean
}

// 工具箱
const NewFeaturePanel = ({
  isChatMode,
  disabled,
  onChange,
  inWorkflow = true,
  showFileUpload = true,
}: Props) => {
  return (
    <>
      <ConfigVision disabled={disabled} onChange={onChange} />

      {/* 更多类似的 */}
      {!isChatMode && !inWorkflow && (
        <MoreLikeThis disabled={disabled} onChange={onChange} />
      )}
      {/* 测试用 全部打开 */}
      {/* <MoreLikeThis disabled={disabled} onChange={onChange} />
      <FileUpload disabled={disabled} onChange={onChange} />
      <ImageUpload disabled={disabled} onChange={onChange} />
      <AnnotationReply disabled={disabled} onChange={onChange} /> */}

      {/* 文件上传 */}
      {showFileUpload && isChatMode && <FileUpload disabled={disabled} onChange={onChange} />}
      {/* 图片上传 */}
      {showFileUpload && !isChatMode && <ImageUpload disabled={disabled} onChange={onChange} />}
      {/* {(isChatMode || !inWorkflow) && <Moderation disabled={disabled} onChange={onChange} />} */}
      {/* 标注回复 */}
      {/* {!inWorkflow && isChatMode && (
        <AnnotationReply disabled={disabled} onChange={onChange} />
      )} */}
    </>
  )
}

export default NewFeaturePanel
