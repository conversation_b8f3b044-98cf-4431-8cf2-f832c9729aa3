import type { FC } from 'react'
import React, { useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { Divider, Input, Select } from 'antd'

import ConfigPrompt from '../llm/components/config-prompt'
import useConfig from './use-config'
import AdvanceSetting from './components/advance-setting'
import type { AgentNodeType } from './types'
import { AgentCreateMode } from './types'

import Field from '@/app/components/workflow/nodes/_base/components/field'
import ModelParameterModalInWorkflow from '@/app/components/account-setting/model-provider-page/model-parameter-modal-in-workflow'

import MemoryConfig from '@/app/components/workflow/nodes/_base/components/memory-config'
import { VarItem } from '@/app/components/workflow/nodes/_base/components/variable/output-vars'
import VarList from '@/app/components/workflow/nodes/_base/components/variable/var-list'
import ConfigVision from '@/app/components/workflow/nodes/_base/components/config-vision'
import BeforeRunForm from '@/app/components/workflow/nodes/_base/components/before-run-form'
import type { Props as FormProps } from '@/app/components/workflow/nodes/_base/components/before-run-form/form'
import ResultPanel from '@/app/components/workflow/run/result-panel'
import Editor from '@/app/components/workflow/nodes/_base/components/prompt/editor'
import OutputVarList from '@/app/components/workflow/nodes/_base/components/variable/output-var-list'

// 工作流公共能力
import { InputVarType, type NodePanelProps, TextVarFormat } from '@/app/components/workflow/types'
// 公共能力
import Tooltip from '@/app/components/base/tooltip'
import { AddTextButton } from '@/app/components/base/button/add-button'
import AddToolPanel from '@/app/components/tools/add-tool-panel'
import AddDatasetPanel from '@/app/components/datasets/common/add-dataset-panel'
import { Add } from '@/app/components/base/icons/src/vender/solid/general'
import TextButton from '@/app/components/base/button/text-button'

const i18nPrefix = 'workflow.nodes.agent'

const Panel: FC<NodePanelProps<AgentNodeType>> = ({
  id,
  data,
}) => {
  const { t } = useTranslation()
  const {
    readOnly,
    inputs,
    isChatModel,
    isChatMode,
    isShowVars,
    isVisionModel,
    datasetList,
    hasSetBlockStatus,
    filterInputVar,
    availableVars,
    availableNodesWithParent,
    filterVar,
    isShowSingleRun,
    inputVarValues,
    setInputVarValues,
    contexts,
    setContexts,
    runningStatus,
    varInputs,
    runResult,
    visionFiles,
    setVisionFiles,

    handleModelChanged,
    handleCompletionParamsChange,
    handlePromptChange,
    handleAddVariable,
    handleSyeQueryChange,
    handleMemoryChange,
    handleAddEmptyVariable,
    handleVarListChange,
    handleVarNameChange,
    hideSingleRun,
    handleRun,
    handleStop,
    handleChangeQuery,
    handleVisionResolutionChange,
    handleVisionResolutionEnabledChange,
    handleChangeSceneDesc,
    handleOuputConfig,
    handleChangeTool,
    handleDeleteTool,
    handleSelectTool,
    handleSelectDataset,
    handleConfigDataset,
    handleRemoveDataset,
    handleChangeJsonOutput,
    handleAddJsonOutput,
    handleChangeAdvanceSetting,
    handleRemoveJsonOutput,
  } = useConfig(id, data)

  // 当前智能体节点模式
  const isEmptyMode = inputs.create_mode === AgentCreateMode.Empty
  // 节点对应模型
  const model = inputs.model
  // 单次运行表单
  const singleRunForms = (() => {
    const forms: FormProps[] = []

    if (varInputs.length > 0) {
      forms.push(
        {
          label: t(`${i18nPrefix}.singleRun.variable`)!,
          inputs: varInputs,
          values: inputVarValues,
          onChange: setInputVarValues,
        },
      )
    }

    if (inputs.context?.variable_selector && inputs.context?.variable_selector.length > 0) {
      forms.push(
        {
          label: t(`${i18nPrefix}.context`)!,
          inputs: [{
            label: '',
            variable: '#context#',
            type: InputVarType.contexts,
            required: false,
          }],
          values: { '#context#': contexts },
          onChange: keyValue => setContexts((keyValue as any)['#context#']),
        },
      )
    }
    // 视觉模式下
    if (isVisionModel) {
      const variableName = data.vision.configs?.variable_selector?.[1] || t(`${i18nPrefix}.files`)!
      forms.push(
        {
          label: t(`${i18nPrefix}.vision`)!,
          inputs: [{
            label: variableName!,
            variable: '#files#',
            type: InputVarType.files,
            required: false,
          }],
          values: { '#files#': visionFiles },
          onChange: keyValue => setVisionFiles((keyValue as any)['#files#']),
        },
      )
    }

    return forms
  })()
  const parameterRef = useRef<{
    getOutputFormat: () => string[]
  }>()

  return (
    <>
      {/* 空态模式下——模型选择 */}
      {isEmptyMode && <Field title={t(`${i18nPrefix}.model`)} className='mb-5'>
        <ModelParameterModalInWorkflow
          popupClassName='!w-[387px]'
          isInWorkflow
          isAdvancedMode={true}
          mode={model!.mode}
          provider={model!.provider}
          completionParams={model!.completion_params}
          modelId={model!.name}
          setModel={handleModelChanged}
          onCompletionParamsChange={handleCompletionParamsChange}
          hideDebugWithMultipleModel
          debugWithMultipleModel={false}
          readonly={readOnly}
        />
      </Field> }
      {/* 非空态模式下——智能体描述 */}
      {!isEmptyMode && <Field title={t(`${i18nPrefix}.desc`)} className='mb-5'>
        <div className='bg-gray-G7 px-2 py-3 rounded overflow-hidden text-gray-G2 text-S3 leading-H3 h-[72px] line-clamp-2'>{inputs.agent_desc}</div>
      </Field>}
      {/* 非空题模式下——使用场景说明 */}
      {<Field title={t(`${i18nPrefix}.sceneDesc`)}>
        <Input.TextArea defaultValue={inputs.scene_desc} onChange={event => handleChangeSceneDesc(event.target.value)}></Input.TextArea>
      </Field>}
      <Divider />
      <div className='flex flex-col gap-4'>
        {/* 空态模式下——能力开发 */}
        {isEmptyMode && isChatModel && <Field title={t(`${i18nPrefix}.abilityDevelopment`)} supportFold>
          <div className='flex flex-col gap-2'>
            {/* 工具选择 */}
            <AddToolPanel
              tools={inputs.tools}
              scene='workflow'
              onSelectTool={handleSelectTool}
              onChangeTool={handleChangeTool}
              onDelete={handleDeleteTool}
              className='relative flex justify-between items-center last-of-type:mb-0 h-[40px] py-2 w-full rounded'
            />
            {/* 知识库配置 */}
            <AddDatasetPanel
              config={{
                ...inputs.dataset_configs,
                datasets: {
                  datasets: inputs.dataset_configs.datasets?.datasets.map(item => ({
                    enabled: item.dataset.enabled,
                    id: item.dataset.id,
                  })) || [],
                },
              }}
              scene='workflow'
              dataSets={datasetList}
              onSelect={handleSelectDataset}
              onConfig={handleConfigDataset}
              onRemove={handleRemoveDataset}
            ></AddDatasetPanel>
            {/* 记忆配置 */}
            <MemoryConfig
              readonly={readOnly}
              config={{ data: inputs.memory }}
              onChange={handleMemoryChange}
            >
              {/* 记忆变量配置 */}
              {/* {!!inputs.memory && (
                <div>
                  <Editor
                    title={<div className='flex items-center space-x-1'>
                      <div className='text-xs font-semibold text-gray-700 uppercase'>user</div>
                      <Tooltip
                        popupContent={
                          <div className='max-w-[180px]'>{t('workflow.nodes.llm.roleDescription.user')}</div>
                        }
                        triggerClassName='w-4 h-4'
                      />
                    </div>}
                    placeholder={t('workflow.nodes.llm.rolePlaceholder.user') || ''}
                    value={inputs.memory.query_prompt_template || '{{#sys.query#}}'}
                    onChange={handleSyeQueryChange}
                    readOnly={readOnly}
                    isShowContext={false}
                    isChatApp
                    isChatModel
                    hasSetBlockStatus={hasSetBlockStatus}
                    nodesOutputVars={availableVars}
                    availableNodes={availableNodesWithParent}
                  />

                  {inputs.memory.query_prompt_template && !inputs.memory.query_prompt_template.includes('{{#sys.query#}}') && (
                    <div className='leading-[18px] text-xs font-normal text-[#DC6803]'>{t(`${i18nPrefix}.sysQueryInUser`)}</div>
                  )}
                </div>
              )} */}
            </MemoryConfig>
            {/* 视觉配置 */}
            {model.name && isVisionModel && <ConfigVision
              nodeId={id}
              readOnly={readOnly}
              isVisionModel={isVisionModel}
              enabled={inputs.vision?.enabled}
              onEnabledChange={handleVisionResolutionEnabledChange}
              config={inputs.vision?.configs}
              onConfigChange={handleVisionResolutionChange}
            />}
          </div>
        </Field>}
        {/* 上下文配置 */}
        {/* <Field
          title={t(`${i18nPrefix}.context`)}
          tooltip={t(`${i18nPrefix}.contextTooltip`)!}
        >
          <>
            <VarReferencePicker
              readonly={readOnly}
              nodeId={id}
              isShowNodeName
              value={inputs.context?.variable_selector || []}
              onChange={handleContextVarChange}
              filterVar={filterVar}
            />
            {shouldShowContextTip && (
              <div className='leading-[18px] text-xs font-normal text-[#DC6803]'>{t(`${i18nPrefix}.notSetContextInPromptTip`)}</div>
            )}
          </>
        </Field> */}
        {/* 空态模式下——系统提示词 */}
        {isEmptyMode && model!.name && (
          <ConfigPrompt
            readOnly={readOnly}
            nodeId={id}
            filterVar={filterInputVar}
            isChatModel={isChatModel}
            isChatApp={isChatMode}
            isShowContext
            payload={inputs.prompt_template}
            onChange={handlePromptChange}
            hasSetBlockStatus={hasSetBlockStatus}
            varList={inputs.prompt_config?.jinja2_variables || []}
            handleAddVariable={handleAddVariable}
            modelConfig={model!}
          />
        )}
        {/* jinja 对应的上下文 */}
        {isShowVars && (
          <Field
            title={t('workflow.nodes.templateTransform.inputVars')}
            operations={
              !readOnly ? <AddTextButton onClick={handleAddEmptyVariable} /> : undefined
            }
          >
            <VarList
              nodeId={id}
              readonly={readOnly}
              list={inputs.prompt_config?.jinja2_variables || []}
              onChange={handleVarListChange}
              onVarNameChange={handleVarNameChange}
              filterVar={filterVar}
            />
          </Field>
        )}
        {/* 输入变量   */}
        {!isEmptyMode && <Field title={t(`${i18nPrefix}.query`)} supportFold>
          <Editor
            readOnly={readOnly}
            isShowContext={false}
            isChatApp
            isChatModel
            justVar
            placeholder={t(`${i18nPrefix}.rolePlaceholder.system`) as string}
            title={<div className='flex items-center space-x-1'>
              <div className='text-xs font-semibold text-gray-700 uppercase'>user</div>
              <Tooltip
                popupContent={
                  <div className='max-w-[180px]'>{t('workflow.nodes.llm.roleDescription.user')}</div>
                }
                triggerClassName='w-4 h-4'
              />
            </div>}
            value={inputs.query}
            onChange={handleChangeQuery}
            nodesOutputVars={availableVars}
            availableNodes={availableNodesWithParent}
          />
        </Field>}
        {/* 高级设置 */}
        {isEmptyMode && <Field title={t(`${i18nPrefix}.advanceSetting`)} supportFold >
          <AdvanceSetting
            payload={inputs.advance_setting}
            onChange={handleChangeAdvanceSetting}
          ></AdvanceSetting>
        </Field>}
      </div>
      <Divider />
      {/* 输出变量 */}
      <Field
        title={t('workflow.nodes.common.outputVars')}
        tooltip={t(`${i18nPrefix}.contextTooltip`)!}
        operations={
          (isEmptyMode
            ? <div className='flex items-center'>
              <span className='mr-3 text-S3'>{t(`${i18nPrefix}.outputVars.type`)}</span>
              <Select
                value={inputs.model.response_format}
                style={inputs.model.response_format === TextVarFormat.Json ? { width: 100 } : { width: 120 }}
                options={[
                  { value: TextVarFormat.Markdown, label: 'Markdown', disabled: false },
                  { value: TextVarFormat.Json, label: 'JSON', disabled: !parameterRef.current?.getOutputFormat().includes('JSON') },
                ]}
                onChange={handleOuputConfig}
              />
              { inputs.model.response_format === TextVarFormat.Json
              && <>
                <Divider className='!mx-2' type='vertical'></Divider>
                <TextButton
                  variant={'hover'}
                  onClick={handleAddJsonOutput}
                >
                  <Add></Add>
                </TextButton>
              </>

              }
            </div>
            : <></>)
        }
      >
        <>
          {
            inputs.model.response_format === TextVarFormat.Markdown
              ? <div className='flex flex-col gap-2 px-3 py-2 bg-gray-G7 rounded'>
                <VarItem
                  name='text'
                  type='string'
                  description={t(`${i18nPrefix}.outputVars.output`)}
                />
              </div>
              : <></>
          }
          {
            inputs.model.response_format === TextVarFormat.Json
              ? <OutputVarList
                readonly={false}
                onRemove={handleRemoveJsonOutput}
                onChange={handleChangeJsonOutput}
                outputs={inputs.model.outputs}
                outputKeyOrders={Object.keys(inputs.model.outputs)}
              ></OutputVarList>
              : <></>
          }
        </>
      </Field>
      {isShowSingleRun && (
        <BeforeRunForm
          nodeName={inputs.title}
          onHide={hideSingleRun}
          forms={singleRunForms}
          runningStatus={runningStatus}
          onRun={handleRun}
          onStop={handleStop}
          result={<ResultPanel {...runResult} showSteps={false} />}
        />
      )}
    </>
  )
}

export default React.memo(Panel)
