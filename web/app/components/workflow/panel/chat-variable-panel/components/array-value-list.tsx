import type { FC } from 'react'
import React, { useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import produce from 'immer'
import Input from '@/app/components/base/input'
import DeleteButton from '@/app/components/base/button/delete-button'
import { AddButton } from '@/app/components/base/button/add-button'

type Props = {
  isString: boolean
  list: any[]
  onChange: (list: any[]) => void
}

const ArrayValueList: FC<Props> = ({
  isString = true,
  list,
  onChange,
}) => {
  const { t } = useTranslation()

  // 变更数组项
  const handleNameChange = useCallback((index: number) => {
    return (e: React.ChangeEvent<HTMLInputElement>) => {
      const newList = produce(list, (draft: any[]) => {
        draft[index] = isString ? e.target.value : Number(e.target.value)
      })
      onChange(newList)
    }
  }, [isString, list, onChange])
  // 删除数组项
  const handleItemRemove = useCallback((index: number) => {
    return () => {
      const newList = produce(list, (draft) => {
        draft.splice(index, 1)
      })
      onChange(newList)
    }
  }, [list, onChange])
  // 添加新的数组项
  const handleItemAdd = useCallback(() => {
    const newList = produce(list, (draft: any[]) => {
      draft.push(undefined)
    })
    onChange(newList)
  }, [list, onChange])

  return (
    <div className='w-full space-y-2'>
      {list.map((item, index) => (
        <div className='flex items-center space-x-1' key={index}>
          <Input
            placeholder={t('workflow.chatVariable.modal.arrayValue') || ''}
            value={list[index]}
            onChange={handleNameChange(index)}
            type={isString ? 'text' : 'number'}
          />
          <DeleteButton
            onClick={handleItemRemove(index)}
          />
        </div>
      ))}
      <AddButton variant='secondary-accent' className='w-full' onClick={handleItemAdd}>
        <span>{t('workflow.chatVariable.modal.addArrayValue')}</span>
      </AddButton>
    </div>
  )
}
export default React.memo(ArrayValueList)
