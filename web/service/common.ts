import type { Fetcher } from 'swr'
import { del, get, getPublic, patch, post, put } from './base'
import type {
  ApiBasedExtension,
  CodeBasedExtension,
  CommonResponse,
  DataSourceNotion,
  FileUploadConfigResponse,
  ICurrentWorkspace,
  IWorkspace,
  InvitationResponse,
  LangGeniusVersionResponse,
  Member,
  QueryUserInfoByCodeType,
  SetupStatusResponse,
  SsoCommonResponse,
  UserProfileOriginResponse,
} from '@/models/common'

import type {
  DefaultModelResponse,
  Model,
  ModelItem,
  ModelParameterRule,
  ModelProvider,
  ModelTypeEnum,
} from '@/app/components/account-setting/model-provider-page/declarations'
import type { RETRIEVE_METHOD } from '@/types/datasets'
import type { SystemFeatures } from '@/types/system'

type LoginSuccess = {
  result: 'success'
  data: { access_token: string; refresh_token: string }
}
type LoginFail = {
  result: 'fail'
  data: string
  code: string
  message: string
}
type LoginResponse = LoginSuccess | LoginFail

// 服务是否启动
export const fetchSetupStatus = () => {
  return get<SetupStatusResponse>('/setup')
}
// 获取语言版本
export const fetchLanggeniusVersion: Fetcher<LangGeniusVersionResponse, { url: string; params: Record<string, any> }> = ({ url, params }) => {
  return get<LangGeniusVersionResponse>(url, { params })
}
// 获取用户信息
export const fetchUserProfile: Fetcher<UserProfileOriginResponse, { url: string; params: Record<string, any> }> = ({ url, params }) => {
  return get<UserProfileOriginResponse>(url, params, { needAllResponseContent: true })
}
// 更新用户信息
export const updateUserProfile: Fetcher<CommonResponse, { url: string; body: Record<string, any> }> = ({ url, body }) => {
  return post<CommonResponse>(url, { body })
}
// 获取模型提供商信息
export const fetchModelProviders: Fetcher<{ data: ModelProvider[] }, string> = (url) => {
  return get<{ data: ModelProvider[] }>(url)
}
// 获取当前工作空间信息
export const fetchCurrentWorkspace: Fetcher<ICurrentWorkspace, { url: string; params: Record<string, any> }> = ({ url, params }) => {
  return get<ICurrentWorkspace>(url, { params })
}
// 获取系统特性
export const getSystemFeatures = () => {
  return getPublic<SystemFeatures>('/system-features')
}
// 账户同步
export const syncAccount = () => {
  return get<CommonResponse>('/account/sync')
}

// 根据code获取用户信息
export const getQueryUserInfoByCode: Fetcher<SsoCommonResponse, QueryUserInfoByCodeType> = ({ code, accountId }) => {
  return get<SsoCommonResponse>(`/upms/public/user/queryUserInfoByCode?code=${code}&accountId=${accountId}`)
}
/* 文件部分--start */
export const fetchFilePreview: Fetcher<{ content: string }, { fileID: string }> = ({ fileID }) => {
  return get<{ content: string }>(`/files/${fileID}/preview`)
}
export const fetchFileUploadConfig: Fetcher<FileUploadConfigResponse, { url: string }> = ({ url }) => {
  return get<FileUploadConfigResponse>(url)
}
export const fetchRemoteFileInfo = ({ url }: { url: string }) => {
  return post<{ file_type: string; file_length: number }>('/remote-files', { body: { url } })
}
/* 文件部分--end */

/* 成员相关接口--start */
// 获取成员列表
export const fetchMembers: Fetcher<{ accounts: Member[] | null }, { url: string; params: Record<string, any> }> = ({ url, params }) => {
  return get<{ accounts: Member[] | null }>(url, { params })
}
export const inviteMember: Fetcher<InvitationResponse, { url: string; body: Record<string, any> }> = ({ url, body }) => {
  return post<InvitationResponse>(url, { body })
}
export const updateMemberRole: Fetcher<CommonResponse, { url: string; body: Record<string, any> }> = ({ url, body }) => {
  return put<CommonResponse>(url, { body })
}
export const deleteMemberOrCancelInvitation: Fetcher<CommonResponse, { url: string }> = ({ url }) => {
  return del<CommonResponse>(url)
}
/* 成员相关接口--end */

/* 工作空间相关接口--start */
// 更新工作空间
export const updateCurrentWorkspace: Fetcher<ICurrentWorkspace, { url: string; body: Record<string, any> }> = ({ url, body }) => {
  return post<ICurrentWorkspace>(url, { body })
}
// 获取工作空间信息
export const fetchWorkspaces: Fetcher<{ workspaces: IWorkspace[] }, { url: string; params?: Record<string, any> }> = ({ url, params }) => {
  return get<{ workspaces: IWorkspace[] }>(url, { params })
}
// 切换工作空间
export const switchWorkspace: Fetcher<CommonResponse & { new_tenant: IWorkspace }, { url: string; body: Record<string, any> }> = ({ url, body }) => {
  return post<CommonResponse & { new_tenant: IWorkspace }>(url, { body })
}
/* 工作空间相关接口--end */

/* 数据源相关接口--start */
export const fetchDataSource: Fetcher<{ data: DataSourceNotion[] }, { url: string }> = ({ url }) => {
  return get<{ data: DataSourceNotion[] }>(url)
}
export const syncDataSourceNotion: Fetcher<CommonResponse, { url: string }> = ({ url }) => {
  return get<CommonResponse>(url)
}
export const updateDataSourceNotionAction: Fetcher<CommonResponse, { url: string }> = ({ url }) => {
  return patch<CommonResponse>(url)
}
export const fetchNotionConnection: Fetcher<{ data: string }, string> = (url) => {
  return get(url) as Promise<{ data: string }>
}
export const fetchDataSourceNotionBinding: Fetcher<{ result: string }, string> = (url) => {
  return get(url) as Promise<{ result: string }>
}
/* 数据源相关接口--end */

/* 模型相关接口--start */
export type ModelProviderCredentials = {
  credentials?: Record<string, string | undefined | boolean>
  load_balancing: any
}
export const fetchModelProviderCredentials: Fetcher<ModelProviderCredentials, string> = (url) => {
  return get<ModelProviderCredentials>(url)
}
export const fetchModelProviderModelList: Fetcher<{ data: ModelItem[] }, string> = (url) => {
  return get<{ data: ModelItem[] }>(url)
}
export const fetchModelList: Fetcher<{ data: Model[] }, string> = (url) => {
  return get<{ data: Model[] }>(url)
}
export const setModelProvider: Fetcher<CommonResponse, { url: string; body: any }> = ({ url, body }) => {
  return post<CommonResponse>(url, { body })
}
export const deleteModelProvider: Fetcher<CommonResponse, { url: string; body?: any }> = ({ url, body }) => {
  return del<CommonResponse>(url, { body })
}
// 获取默认模型
export const fetchDefaultModal: Fetcher<{ data: DefaultModelResponse }, string> = (url) => {
  return get<{ data: DefaultModelResponse }>(url)
}
export const updateDefaultModel: Fetcher<CommonResponse, { url: string; body: any }> = ({ url, body }) => {
  return post<CommonResponse>(url, { body })
}
export const fetchModelParameterRules: Fetcher<{ data: ModelParameterRule[] }, string> = (url) => {
  return get<{ data: ModelParameterRule[] }>(url)
}
export const enableModel = (url: string, body: { model: string; model_type: ModelTypeEnum }) =>
  patch<CommonResponse>(url, { body })
export const disableModel = (url: string, body: { model: string; model_type: ModelTypeEnum }) =>
  patch<CommonResponse>(url, { body })
/* 模型相关接口--end */

/* api-base接口--start */
export const fetchApiBasedExtensionList: Fetcher<ApiBasedExtension[], string> = (url) => {
  return get(url) as Promise<ApiBasedExtension[]>
}
export const fetchApiBasedExtensionDetail: Fetcher<ApiBasedExtension, string> = (url) => {
  return get(url) as Promise<ApiBasedExtension>
}
export const addApiBasedExtension: Fetcher<ApiBasedExtension, { url: string; body: ApiBasedExtension }> = ({ url, body }) => {
  return post(url, { body }) as Promise<ApiBasedExtension>
}
export const updateApiBasedExtension: Fetcher<ApiBasedExtension, { url: string; body: ApiBasedExtension }> = ({ url, body }) => {
  return post(url, { body }) as Promise<ApiBasedExtension>
}
export const deleteApiBasedExtension: Fetcher<{ result: string }, string> = (url) => {
  return del(url) as Promise<{ result: string }>
}
export const fetchCodeBasedExtensionList: Fetcher<CodeBasedExtension, string> = (url) => {
  return get(url) as Promise<CodeBasedExtension>
}
/* api-base接口--end */

/* 登录注册-start */
// 获取新token
export const fetchNewToken: Fetcher<CommonResponse & { data: { access_token: string; refresh_token: string } }, { body: Record<string, any> }> = ({ body }) => {
  return post('/refresh-token', { body }) as Promise<CommonResponse & { data: { access_token: string; refresh_token: string } }>
}
// 登出
export const logout: Fetcher<SsoCommonResponse, { url: string; params: Record<string, any> }> = ({ url, params }) => {
  return get<SsoCommonResponse>(url, params)
}
// 获取登录图形验证码
export const getLoginPicCode = () => post<{ uuid: string; captcha_image: string }>('/captcha/login')
// 获取登录短信验证码
export const getloginSmsCode = (body: { phone: string; pic_code: string; uuid: string }) => post('/SmsCaptcha/login', { body })
// 获取注册图形验证码
export const getRegisterPicCOde = () => post<{ uuid: string; captcha_image: string }>('/captcha/register')
// 获取注册短信验证码
export const getRegisterSmsCode = (body: { phone: string; pic_code: string; uuid: string }) => post('/SmsCaptcha/register', { body })
// 验证码登录
export const codeLogin = (body: Record<string, any>) => post<CommonResponse>('/ai_login', { body })
// 账号密码登录
export const login: Fetcher<LoginResponse, { url: string; body: Record<string, any> }> = ({ url, body }) => {
  return post(url, { body }) as Promise<LoginResponse>
}
// 省-注册
export const register = (body: Record<string, any>) => post<CommonResponse>('/register', { body })
// 省-申请账号
export const trialRegisterApi: Fetcher<CommonResponse & { data: string }, { body: Record<string, any> }> = ({ body }) => {
  return post('/oauth/trial_register', { body }) as Promise<CommonResponse & { data: string }>
}
/* 私有化注册 */
export const registerApi: Fetcher<CommonResponse & { data: string }, { body: Record<string, any> }> = ({ body }) => {
  return post('/oauth/register', { body }) as Promise<CommonResponse & { data: string }>
}
/* 登录注册-end */

type RetrievalMethodsRes = {
  'retrieval_method': RETRIEVE_METHOD[]
}
export const fetchSupportRetrievalMethods: Fetcher<RetrievalMethodsRes, string> = (url) => {
  return get<RetrievalMethodsRes>(url)
}
// 根据文件id查询文件信息
export const fetchFileInfo = (fileIds: string[]) => {
  return post<{
    metadatas: Array<{
      id: string
      metadata: Array<{
        sheet_name: string
        total_count: number
        columns: string[]
      }>
    }>
  }>('/files/metadata', {
    body: {
      file_ids: fileIds,
    },
  })
}
