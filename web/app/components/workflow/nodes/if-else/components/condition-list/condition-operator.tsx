import {
  useMemo,
} from 'react'
import { useTranslation } from 'react-i18next'
import { getOperators, isComparisonOperatorNeedTranslate } from '../../utils'
import type { ComparisonOperator } from '../../types'

import type { VarType } from '@/app/components/workflow/types'
// 公共组件
import cn from '@/utils/classnames'
import PopoverSelect from '@/app/components/base/select/popover-select'

const i18nPrefix = 'workflow.nodes.ifElse'

type ConditionOperatorProps = {
  className?: string
  disabled?: boolean
  varType: VarType
  file?: { key: string }
  value?: string
  onSelect: (value: ComparisonOperator) => void
}
const ConditionOperator = ({
  className,
  disabled,
  varType,
  value,
  file,
  onSelect,
}: ConditionOperatorProps) => {
  const { t } = useTranslation()
  // 操作选项
  const options = useMemo(() => {
    console.log(varType, file)
    return getOperators(varType, file).map((o) => {
      return {
        label: isComparisonOperatorNeedTranslate(o) ? t(`${i18nPrefix}.comparisonOperator.${o}`) : o,
        key: o,
        title: isComparisonOperatorNeedTranslate(o) ? t(`${i18nPrefix}.comparisonOperator.${o}`) : o,
      }
    })
  }, [t, varType, file])
  return (
    <PopoverSelect
      className={cn('w-[60px] !text-gray-G2 !border-none !text-S1 !leading-H1 !px-0 !h-auto')}
      options={options}
      onChange={onSelect}
      placeholder={t('common.placeholder.select')!}
      defaultValue={value as any}
      disabled={disabled}
      showCheck={false}
    >
    </PopoverSelect>
  )
}

export default ConditionOperator
