'use client'
import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import { LanguagesSupported } from '@/i18n/language'

const loadLangResources = (lang: string) => ({
  translation: {
    common: require(`./${lang}/common`).default,
    menus: require(`./${lang}/menus`).default,
    login: require(`./${lang}/login`).default,
    app: require(`./${lang}/app`).default,
    appOverview: require(`./${lang}/app-overview`).default,
    appDebug: require(`./${lang}/app-debug`).default,
    sampleTemplate: require(`./${lang}/sampleTemplate`).default,
    appApi: require(`./${lang}/app-api`).default,
    appDataReflux: require(`./${lang}/app-data-reflux`).default,
    appLog: require(`./${lang}/app-log`).default,
    appAnnotation: require(`./${lang}/app-annotation`).default,
    share: require(`./${lang}/share-app`).default,
    dataset: require(`./${lang}/dataset`).default,
    datasetDocuments: require(`./${lang}/dataset-documents`).default,
    datasetCreation: require(`./${lang}/dataset-creation`).default,
    tools: require(`./${lang}/tools`).default,
    workflow: require(`./${lang}/workflow`).default,
    runLog: require(`./${lang}/run-log`).default,
    account: require(`./${lang}/account`).default,
    videos: require(`./${lang}/videos`).default,
    dataStatistics: require(`./${lang}/dataStatistics`).default,
  },
})

// Automatically generate the resources object
const resources = LanguagesSupported.reduce((acc: any, lang: string) => {
  acc[lang] = loadLangResources(lang)
  return acc
}, {})

i18n.use(initReactI18next)
  .init({
    lng: undefined,
    fallbackLng: 'zh-Hans',
    resources,
  })

export const changeLanguage = i18n.changeLanguage
export default i18n
