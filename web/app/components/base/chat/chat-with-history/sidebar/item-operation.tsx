import type { FC } from 'react'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { Dropdown } from 'antd'
import type { MenuProps } from 'antd'
import TextButton from '@/app/components/base/button/text-button'
import { More } from '@/app/components/base/icons/src/vender/workflow'

export type IItemOperationProps = {
  className?: string
  isPinned: boolean
  isShowRenameConversation?: boolean
  onRenameConversation?: () => void
  isShowDelete: boolean
  togglePin: () => void
  onDelete: () => void
}

const ItemOperation: FC<IItemOperationProps> = ({
  className,
  isPinned,
  togglePin,
  isShowRenameConversation,
  onRenameConversation,
  isShowDelete,
  onDelete,
}) => {
  const { t } = useTranslation()
  const items: MenuProps['items'] = [
    // {
    //   key: 'pin',
    //   label: (
    //     <div onClick={togglePin}>
    //       <span>{isPinned ? t('explore.sidebar.action.unpin') : t('explore.sidebar.action.pin')}</span>
    //     </div>
    //   ),
    // },
    // {
    //   key: 'edit',
    //   label: (
    //     <div onClick={onRenameConversation} className='text-S1 leading-H1'>
    //       <span>{t('explore.sidebar.action.rename')}</span>
    //     </div>
    //   ),
    //   disabled: !isShowRenameConversation
    // },
    {
      key: 'delete',
      label: (
        <div onClick={onDelete} className='text-S1 leading-H1'>
          <span>{t('common.operation.delete')}</span>
        </div>
      ),
      disabled: !isShowDelete,
    },
  ]

  return (
    <Dropdown
      menu={{
        items,
      }}
      className={className}
    >
      <TextButton>
        <More className='w-3 h-3 text-gray-G1 hover:bg-gray-G7' />
      </TextButton>
    </Dropdown>
  )
}
export default React.memo(ItemOperation)
