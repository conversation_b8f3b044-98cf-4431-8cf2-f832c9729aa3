import type { FC } from 'react'
import {
  memo,
  useEffect,
  useMemo,
  useState,
} from 'react'
import { useTranslation } from 'react-i18next'
import { Button, Divider, Input, Popover, Radio, Space } from 'antd'
import type { RadioChangeEvent } from 'antd/es/radio'
import type { ChatItem } from '../../types'
import { useChatContext } from '../context'
import style from './styles/operation.module.css'
import EditReplyModal from '@/app/components/app/annotation/edit-annotation-modal'
// 公共组件
import cn from '@/utils/classnames'
import CopyBtn from '@/app/components/base/button/copy-button'
import Log from '@/app/components/base/chat/chat/log'
import Tooltip from '@/app/components/base/tooltip'
import { Refresh, Speaker } from '@/app/components/base/icons/src/vender/line/general'
import { ThumbsDown, ThumbsUp } from '@/app/components/base/icons/src/vender/line/alertsAndFeedback'
import { ThumbsDown as ThumbsDownSolid, ThumbsUp as ThumbsUpSolid } from '@/app/components/base/icons/src/vender/solid/alertsAndFeedback'
import Toast from '@/app/components/base/toast'

const { TextArea } = Input
const I18N_PREFIX = 'appDebug.operation'
type OperationProps = {
  item: ChatItem
  question: string
  index: number
}
type FeedbackRating = {
  rating: 'like' | 'dislike' | null
  label?: string
  content?: string
}
const Operation: FC<OperationProps> = ({
  item,
  question,
  index,
}) => {
  const { t } = useTranslation()
  const {
    config,
    isVoiceCall,
    isLongWeb,
    noChatInput,
    showPromptLog,
    onAnnotationAdded,
    onAnnotationEdited,
    onAnnotationRemoved,
    onFeedback,
    onRegenerate,
    onPlayVoice,
  } = useChatContext()
  const {
    id,
    isOpeningStatement,
    content: messageContent,
    annotation,
    feedback,
    adminFeedback,
    agent_thoughts,
  } = item
  // 支持语音播放,需要开启语音输入或者语音对话
  const voiceTtsEnabled = useMemo(() => {
    return config?.text_to_speech?.voice_input?.enabled || config?.text_to_speech?.voice_conversation?.enabled || false
  }, [config])
  // 支持管理员赞踩
  const canAdminFeedback = !!config?.supportAnnotation
  // 是否展示回复弹窗
  const [isShowReplyModal, setIsShowReplyModal] = useState(false)
  // 管理员赞踩
  const [localAdminFeedback, setLocalAdminFeedback] = useState(adminFeedback)
  // 用户赞踩
  const [localUserFeedback, setLocalUserFeedback] = useState(feedback)
  // 用户踩弹窗是否打开
  const [isOpenPopover, setIsOpenPopover] = useState(false)
  // const [dislikeRadio, setDislikeRadio] = useState<{ value: string; label: string }[]>([])
  const [isDisabledButton, setIsDisabledButton] = useState(false) // 禁用按钮
  const [radioValue, setRadioValue] = useState<string>('')
  const [textAreaValue, setTextAreaValue] = useState<string>('')
  // 踩弹窗建议
  const dislikeRadio = [
    { value: t(`${I18N_PREFIX}.dislikeRadio.wrongAnswer`), label: t(`${I18N_PREFIX}.dislikeRadio.wrongAnswer`) },
    { value: t(`${I18N_PREFIX}.dislikeRadio.unsafe`), label: t(`${I18N_PREFIX}.dislikeRadio.unsafe`) },
    { value: t(`${I18N_PREFIX}.dislikeRadio.isFake`), label: t(`${I18N_PREFIX}.dislikeRadio.isFake`) },
    { value: t(`${I18N_PREFIX}.dislikeRadio.logicProblem`), label: t(`${I18N_PREFIX}.dislikeRadio.logicProblem`) },
    { value: t(`${I18N_PREFIX}.dislikeRadio.formatProblem`), label: t(`${I18N_PREFIX}.dislikeRadio.formatProblem`) },
    { value: t(`${I18N_PREFIX}.dislikeRadio.noHelp`), label: t(`${I18N_PREFIX}.dislikeRadio.noHelp`) },
  ]

  // 回答的文本
  const content = useMemo(() => {
    if (agent_thoughts?.length)
      return agent_thoughts.reduce((acc, cur) => acc + cur.thought, '')

    return messageContent
  }, [agent_thoughts?.length, agent_thoughts?.[agent_thoughts?.length - 1]?.thought, messageContent])

  const hasAnnotation = !!annotation?.id

  // 关闭踩弹窗建议
  const onClose = (type?: string) => {
    // 关闭后清空
    setRadioValue('')
    setTextAreaValue('')
    setIsOpenPopover(false)
  }
  // 变更回答赞踩
  const handleFeedback = async (rating: 'like' | 'dislike' | null) => {
    const currentRating = canAdminFeedback ? localAdminFeedback?.rating : localUserFeedback?.rating
    const isCancelRating = currentRating === rating
    const value = isCancelRating ? null : rating
    if (!config?.supportFeedback || !onFeedback)
      return
    const body: FeedbackRating = { rating: value }
    if (value === 'dislike') {
      body.label = radioValue
      body.content = textAreaValue
    }
    await onFeedback?.(id, body)
    if (canAdminFeedback)
      setLocalAdminFeedback({ rating: value })
    else
      setLocalUserFeedback({ rating: value })
  }
  // 弹窗状态变更
  const handleOpenChange = (newOpenPopover: boolean) => {
    setIsOpenPopover(newOpenPopover)
    if (newOpenPopover) {
      if (radioValue)
        setIsDisabledButton(false)
      else
        setIsDisabledButton(true)
    }
    else {
      onClose()
    }
  }
  const onRadioChange = (e: RadioChangeEvent) => {
    setIsDisabledButton(false)
    setRadioValue(e?.target?.value)
  }
  const onTextAreaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (e?.target?.value)
      setIsDisabledButton(false)
    setTextAreaValue(e?.target?.value)
  }
  // 提交反馈
  const onConfirm = async () => {
    if (!radioValue && !textAreaValue) {
      Toast.notify({
        type: 'warning',
        message: t('appDebug.operation.disagreeContent.reason'),
      })
      return
    }
    // 调用提交反馈接口
    handleFeedback('dislike')
    onClose()
  }
  useEffect(() => {
    setLocalUserFeedback(feedback)
    setLocalAdminFeedback(adminFeedback)
  }, [feedback, adminFeedback])

  // 生成单选按钮
  const generateRadioButtons = (items: { value: string; label: string }[]) => {
    return items.map(item => (
      <Radio key={item.value} value={item.value} className=' text-center'>
        {item.label}
      </Radio>
    ))
  }
  const DislikeReasonSurvey = () => {
    if (!Array.isArray(dislikeRadio) || dislikeRadio.length === 0)
      return <div>{t(`${I18N_PREFIX}.loading`)}</div>

    return (
      <div className='p-[20px] max-w-[390px]'>
        <div
          className='mb-[20px] text-[#181818] leading-[20px] text-[18px] font-[600]'
        >
          { t('appDebug.operation.disagreeContent.feedback') }
        </div>
        <div className='flex flex-col items-center justify-center'>
          <div>
            <Radio.Group optionType="button" onChange={ onRadioChange } value={ radioValue }>
              <Space wrap={ true }>
                {generateRadioButtons(dislikeRadio)}
              </Space>
            </Radio.Group>
          </div>
          <div className='mt-[10px]' style={{ width: '100%' }}>
            <TextArea
              rows={4}
              placeholder={t('appDebug.operation.disagreeContent.textAreaPlaceholder') || ''}
              onChange={onTextAreaChange}
              value={textAreaValue}
            />
          </div>
          <div className='mt-[20px] flex items-end justify-end ' style={{ width: '100%' }}>
            <Space>
              <Button disabled={isDisabledButton} onClick={() => onClose()}>{t('common.operation.cancel')}</Button>
              <Button disabled={isDisabledButton} type="primary" onClick={onConfirm}>{t('common.operation.submit')}</Button>
            </Space>
          </div>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className='flex items-center justify-between mt-3'>
        {/* 用户赞踩 */}
        {
          (canAdminFeedback && localUserFeedback?.rating)
            ? (
              <Tooltip
                popupContent={localUserFeedback?.rating === 'like' ? t('appDebug.operation.agree') : t('appDebug.operation.disagree')}
              >
                <div className={style.operationItem}>
                  {localUserFeedback?.rating === 'like' ? <ThumbsUp className='w-[14px] h-[14px] text-primary-P1'></ThumbsUp> : <ThumbsDown className='w-4 h-4 text-primary-P1'></ThumbsDown>}
                </div>
              </Tooltip>
            )
            : <div></div>
        }
        <div className={style.operationListWrap}>
          {/* 语音播放 */}
          {voiceTtsEnabled && !isLongWeb && !isVoiceCall
            && <Tooltip
              popupContent={t('common.voiceInput.voicePlay') as string}
            >
              <div
                className={style.operationItem}
                onClick={() => onPlayVoice?.(content, id)}
              >
                <Speaker className={cn('w-4 h-4')}/>
              </div>
            </Tooltip>
          }
          {/* 复制 */}
          {!isOpeningStatement && !isLongWeb && (
            <>
              {voiceTtsEnabled && <Divider className='mx-2 h-[14px]' type='vertical'></Divider>}
              <CopyBtn wrapClassName={style.operationItem} value={content} />
            </>
          )}
          {/* 日志 */}
          {!isOpeningStatement && showPromptLog && (
            <>
              <Divider className='mx-2 h-[14px]' type='vertical'></Divider>
              <Log logItem={item} className={style.operationItem} />
            </>
          )}
          {/* 刷新 */}
          {!isVoiceCall && !isOpeningStatement && !noChatInput
            && <>
              <Divider className='mx-2 h-[14px]' type='vertical'></Divider>
              <Tooltip
                popupContent={t('appApi.regenerate') as string}
              >
                <div
                  className={style.operationItem}
                  onClick={() => onRegenerate?.(item)}
                >
                  <Refresh className={cn('w-4 h-4')} />
                </div>
              </Tooltip>
            </>
          }
          {/* {(!isOpeningStatement && config?.supportAnnotation && config.annotation_reply?.enabled) && (
          <AnnotationCtrlBtn
            appId={config?.appId || ''}
            messageId={id}
            annotationId={annotation?.id || ''}
            className='hidden group-hover:flex ml-1 shrink-0'
            cached={hasAnnotation}
            query={question}
            answer={content}
            onAdded={(id, authorName) => onAnnotationAdded?.(id, authorName, question, content, index)}
            onEdit={() => setIsShowReplyModal(true)}
            onRemoved={() => onAnnotationRemoved?.(index)}
          />
        )}
        {
          annotation?.id && (
            <div
              className='relative box-border flex items-center justify-center h-7 w-7 p-0.5 rounded-lg bg-white cursor-pointer text-[#444CE7] shadow-md group-hover:hidden'
            >
              <div className='p-1 rounded-lg bg-[#EEF4FF] '>
                <MessageFast className='w-4 h-4' />
              </div>
            </div>
          )
        } */}
          {/* 支持回调，且没有赞踩的情况下 */}
          {
            config?.supportFeedback && onFeedback && !isOpeningStatement && (
              <>
                <Divider className='mx-2 h-[14px]' type='vertical'></Divider>
                {/* 点赞  */}
                <Tooltip popupContent={t('common.operation.agree')}>
                  <div className={style.operationItem} onClick={() => handleFeedback('like')}>
                    {canAdminFeedback
                      ? (
                        localAdminFeedback?.rating === 'like'
                          ? <ThumbsUpSolid className={'text-primary-P1 w-4 h-4'} />
                          : <ThumbsUp className={'w-[14px] h-[14px]'} />
                      )
                      : <ThumbsUp
                        className={cn(
                          localUserFeedback?.rating === 'like' && 'text-primary-P1',
                          'w-[14px] h-[14px]',
                        )}
                      />
                    }
                  </div>
                </Tooltip>
                <Divider className='mx-2 h-[14px]' type='vertical'></Divider>
                {/* 点踩 */}
                <Tooltip popupContent={t('common.operation.disagree')}>
                  <Popover
                    className="p-0"
                    arrow={false}
                    content={DislikeReasonSurvey}
                    trigger='click'
                    open={isOpenPopover}
                    onOpenChange={handleOpenChange}
                  >
                    <div className={style.operationItem}>
                      <div onClick={(event) => {
                        if (canAdminFeedback ? localAdminFeedback?.rating === 'dislike' : localUserFeedback?.rating === 'dislike') {
                          event.stopPropagation()
                          handleFeedback('dislike')
                        }
                      }}>
                        {canAdminFeedback
                          ? (
                            localAdminFeedback?.rating === 'dislike'
                              ? <ThumbsDownSolid className={'text-primary-P1 w-4 h-4'} />
                              : <ThumbsDown className={'w-[14px] h-[14px]'} />
                          )
                          : <ThumbsDown
                            className={cn(
                              localUserFeedback?.rating === 'dislike' && 'text-primary-P1',
                              'w-[14px] h-[14px] cursor-pointer',
                            )}
                          />
                        }
                      </div>
                    </div>
                  </Popover>
                </Tooltip>
              </>
            )
          }
        </div>
      </div>
      <EditReplyModal
        isShow={isShowReplyModal}
        onHide={() => setIsShowReplyModal(false)}
        query={question}
        answer={content}
        onEdited={(editedQuery, editedAnswer) => onAnnotationEdited?.(editedQuery, editedAnswer, index)}
        onAdded={(annotationId, authorName, editedQuery, editedAnswer) => onAnnotationAdded?.(annotationId, authorName, editedQuery, editedAnswer, index)}
        appId={config?.appId || ''}
        messageId={id}
        annotationId={annotation?.id || ''}
        createdAt={annotation?.created_at}
        onRemove={() => onAnnotationRemoved?.(index)}
      />
    </>
  )
}

export default memo(Operation)
