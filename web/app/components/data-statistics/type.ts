
export type UsageData = {
  total?: number;
  consumed?: number;
  remaining?: number;
}
export type Datasets = {
  knowledge_creation_limit: UsageData
  single_knowledge_file_upload_limit: UsageData
  total_knowledge_storage_space: UsageData
}
// 知识库使用情况
export type DatasetUsage = {
  account_id: string;
  datasets: [
    Datasets
  ]
}
// 应用使用情况
export type AppUsage = {
  account_id: string;
  apps: UsageData
}
// 工具使用情况
export type ToolUsage = {
  account_id: string;
  tools: UsageData
}
// 使用情况接口返回内容
export type UsageResponse = {
  code: number;
  message: string;
  data: any
}