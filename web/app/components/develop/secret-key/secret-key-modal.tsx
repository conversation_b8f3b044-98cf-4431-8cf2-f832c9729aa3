'use client'
import {
  useEffect,
  useState,
} from 'react'
import { useTranslation } from 'react-i18next'
import { PlusIcon } from '@heroicons/react/20/solid'
import useSWR, { useSWRConfig } from 'swr'
import copy from 'copy-to-clipboard'
import type { TableColumnsType } from 'antd'
import { Table, Tooltip } from 'antd'

import SecretKeyGenerateModal from './secret-key-generate'
import s from './styles/style.module.css'
import {
  createApikey as createAppApikey,
  delApikey as delAppApikey,
  fetchApiKeysList as fetchAppApiKeysList,
} from '@/service/apps'
import {
  createApikey as createDatasetApikey,
  delApikey as delDatasetApikey,
  fetchApiKeysList as fetchDatasetApiKeysList,
} from '@/service/datasets'
import type { ApiKeyItemResponse, CreateApiKeyResponse } from '@/models/app'
import useTimestamp from '@/hooks/use-timestamp'
import { useAppContext } from '@/context/app-context'
/* 公共组件 */
import Modal from '@/app/components/base/modal'
import Button from '@/app/components/base/button'
import Confirm from '@/app/components/base/confirm'

type ISecretKeyModalProps = {
  isShow: boolean
  appId?: string
  onClose: () => void
}

const SecretKeyModal = ({
  isShow = false,
  appId,
  onClose,
}: ISecretKeyModalProps) => {
  const { t } = useTranslation()
  const { formatTime } = useTimestamp()
  const { currentWorkspace, canAdmin, canEditApp } = useAppContext()
  const { mutate } = useSWRConfig()
  const commonParams = appId
    ? { url: `/apps/${appId}/api-keys`, params: {} }
    : { url: '/datasets/api-keys', params: {} }
  // 显示确认modal
  const [showConfirmDelete, setShowConfirmDelete] = useState(false)
  // 是否展示生成api密钥弹窗
  const [isVisible, setVisible] = useState(false)
  // 新的api密钥
  const [newKey, setNewKey] = useState<CreateApiKeyResponse | undefined>(undefined)
  // 获取api密钥列表
  const fetchApiKeysList = appId ? fetchAppApiKeysList : fetchDatasetApiKeysList
  // api密钥列表
  const { data: apiKeysList } = useSWR(commonParams, fetchApiKeysList)
  // 正在删除的id
  const [delKeyID, setDelKeyId] = useState('')
  // 当前复制的值
  const [copyValue, setCopyValue] = useState('')

  // 删除api密钥
  const onDel = async () => {
    setShowConfirmDelete(false)
    if (!delKeyID)
      return

    const delApikey = appId ? delAppApikey : delDatasetApikey
    const params = appId
      ? { url: `/apps/${appId}/api-keys/${delKeyID}`, params: {} }
      : { url: `/datasets/api-keys/${delKeyID}`, params: {} }
    await delApikey(params)
    mutate(commonParams)
  }
  // 创建api密钥
  const onCreate = async () => {
    const params = appId
      ? { url: `/apps/${appId}/api-keys`, body: {} }
      : { url: '/datasets/api-keys', body: {} }
    const createApikey = appId ? createAppApikey : createDatasetApikey
    const res = await createApikey(params)
    setVisible(true)
    setNewKey(res)
    mutate(commonParams)
  }
  // 生成可展示token
  const generateToken = (token: string) => {
    return `${token.slice(0, 3)}...${token.slice(-20)}`
  }

  // 表格列
  const columns: TableColumnsType<ApiKeyItemResponse> = [
    {
      title: t('appApi.apiKeyModal.secretKey'),
      key: 'token',
      render: (_: any, record) => {
        return generateToken(record.token)
      },
    },
    {
      title: t('appApi.apiKeyModal.created'),
      key: 'created_at',
      render: (_: any, record) => {
        return formatTime(Number(record.created_at), t('appLog.dateTimeFormat') as string)
      },
    },
    {
      title: t('appApi.apiKeyModal.lastUsed'),
      key: 'last_used_at',
      render: (_: any, record) => {
        return record.last_used_at ? formatTime(Number(record.last_used_at), t('appLog.dateTimeFormat') as string) : t('appApi.never')
      },
    },
    {
      title: t('appApi.apiKeyModal.operation'),
      key: 'operation',
      render: (_, record) => (
        <div className='flex'>
          <Tooltip
            title={copyValue === record.token ? `${t('appApi.copied')}` : `${t('appApi.copy')}`}
          >
            <div className={`flex items-center justify-center flex-shrink-0 w-6 h-6 mr-1 rounded-lg cursor-pointer hover:bg-gray-100 ${s.copyIcon} ${copyValue === record.token ? s.copied : ''}`} onClick={() => {
              // setIsCopied(true)
              copy(record.token)
              setCopyValue(record.token)
            }}></div>
          </Tooltip>
          {canAdmin
                      && <div className={`flex items-center justify-center flex-shrink-0 w-6 h-6 rounded-lg cursor-pointer ${s.trashIcon}`} onClick={() => {
                        setDelKeyId(record.id)
                        setShowConfirmDelete(true)
                      }}>
                      </div>
          }
        </div>
      ),
    },
  ]

  useEffect(() => {
    if (copyValue) {
      const timeout = setTimeout(() => {
        setCopyValue('')
      }, 1000)

      return () => {
        clearTimeout(timeout)
      }
    }
  }, [copyValue])

  return (
    <Modal
      isShow={isShow}
      onClose={onClose}
      closable
      title={`${t('appApi.apiKeyModal.apiSecretKey')}`}
      description={t('appApi.apiKeyModal.apiSecretKeyTips')}
      className='!w-[800px]'
      footer={
        <Button variant={'secondary'} className={`${s.autoWidth}`} onClick={onCreate} disabled={!currentWorkspace || !canEditApp}>
          <PlusIcon className='w-4 h-4' />
          <div>{t('appApi.apiKeyModal.createNewSecretKey')}</div>
        </Button>
      }
    >
      {/* api密钥列表 */}
      <Table
        columns={columns}
        dataSource={(apiKeysList?.data || []) as unknown as Array<ApiKeyItemResponse>}
        pagination={false}
        size='middle'
      ></Table>
      {/* 创建密钥弹窗 */}
      <SecretKeyGenerateModal isShow={isVisible} onClose={() => setVisible(false)} newKey={newKey} />
      {/* 确认删除部分 */}
      {showConfirmDelete && (
        <Confirm
          title={`${t('appApi.actionMsg.deleteConfirmTitle')}`}
          content={`${t('appApi.actionMsg.deleteConfirmTips')}`}
          isShow={showConfirmDelete}
          onConfirm={onDel}
          onCancel={() => {
            setDelKeyId('')
            setShowConfirmDelete(false)
          }}
        />
      )}
    </Modal >
  )
}

export default SecretKeyModal
