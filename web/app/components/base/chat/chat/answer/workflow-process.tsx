import {
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react'
import {
  RiArrowRightSLine,
  RiErrorWarningFill,
  RiLoader2Line,
} from '@remixicon/react'
import { useTranslation } from 'react-i18next'
import type { ChatItem, WorkflowProcess } from '../../types'
import TracingPanel from '@/app/components/workflow/run/tracing-panel'
import cn from '@/utils/classnames'
import { CheckCircle } from '@/app/components/base/icons/src/vender/solid/general'
import { WorkflowRunningStatus } from '@/app/components/workflow/types'
import { useStore as useAppStore } from '@/app/components/app/store'
import style from './styles/index.module.css'

type WorkflowProcessProps = {
  data: WorkflowProcess
  item?: ChatItem
  expand?: boolean
  hideInfo?: boolean
  hideProcessDetail?: boolean
}
const WorkflowProcessItem = ({
  data,
  item,
  expand = false,
  hideInfo = false,
  hideProcessDetail = false,
}: WorkflowProcessProps) => {
  const { t } = useTranslation()
  // 是否折叠
  const [collapse, setCollapse] = useState(!expand)
  // 工作流运行状态
  const running = data.status === WorkflowRunningStatus.Running
  const succeeded = data.status === WorkflowRunningStatus.Succeeded
  const failed = data.status === WorkflowRunningStatus.Failed || data.status === WorkflowRunningStatus.Stopped
  // 工作流运行状态对应背景色
  const background = useMemo(() => {
    if (collapse)
      return 'linear-gradient(90deg, rgba(200, 206, 218, 0.20) 0%, rgba(200, 206, 218, 0.04) 100%)'
    if (running && !collapse)
      return 'linear-gradient(180deg, #E1E4EA 0%, #EAECF0 100%)'

    if (succeeded && !collapse)
      return 'linear-gradient(180deg, #ECFDF3 0%, #F6FEF9 100%)'

    if (failed && !collapse)
      return 'linear-gradient(180deg, #FEE4E2 0%, #FEF3F2 100%)'
  }, [running, succeeded, failed, collapse])

  useEffect(() => {
    setCollapse(!expand)
  }, [expand])

  const setCurrentLogItem = useAppStore(s => s.setCurrentLogItem)
  const setShowMessageLogModal = useAppStore(s => s.setShowMessageLogModal)
  const setCurrentLogModalActiveTab = useAppStore(s => s.setCurrentLogModalActiveTab)
  const showIterationDetail = useCallback(() => {
    setCurrentLogItem(item)
    setCurrentLogModalActiveTab('TRACING')
    setShowMessageLogModal(true)
  }, [item, setCurrentLogItem, setCurrentLogModalActiveTab, setShowMessageLogModal])

  return (
    <div
      className={cn(
        '-mx-1 px-2.5 rounded-xl ',
        collapse ? 'pt-[7px] px-1 pb-1 border-components-panel-border-subtle' : 'pt-[7px] px-1 pb-1 border-components-panel-border-subtle',
      )}
      style={{
        borderRadius: '4px',
      }}
    >
      <div  className={`${style.lcpressDiv} xq-border-box`}>
      <div
        className={cn('flex items-center cursor-pointer')}
        onClick={() => setCollapse(!collapse)}
      >
        {
          running && (
            <RiLoader2Line className='shrink-0 mr-1 w-3.5 h-3.5 text-text-tertiary' />
          )
        }
        {
          succeeded && (
            <CheckCircle className='shrink-0 mr-1 w-3.5 h-3.5 text-text-success' />
          )
        }
        {
          failed && (
            <RiErrorWarningFill className='shrink-0 mr-1 w-3.5 h-3.5 text-text-destructive' />
          )
        }
        <div className={cn('system-xs-medium text-text-secondary', !collapse && '')}>
          <span className={`${style.proname} xq-border-box`}>{t('workflow.common.workflowProcess')}</span>
        </div>
        <RiArrowRightSLine className={`'ml-1 w-4 h-4 text-text-tertiary' ${collapse ? '' : 'rotate-90'}`} />
      </div>
      {
        !collapse && (
          <div  className={`${style.prodiv} xq-border-box`}>
            <div className='mt-1.5'>
              {
                <TracingPanel
                  list={data.tracing}
                  onShowIterationDetail={showIterationDetail}
                  hideNodeInfo={hideInfo}
                  hideNodeProcessDetail={hideProcessDetail}
                />
              }
            </div>
          </div>
        )
      }
    </div>
   
    </div>
  )
}

export default WorkflowProcessItem
