/* eslint-disable import/no-mutable-exports */
import { InputVarType } from '@/app/components/workflow/types'
import { AgentStrategy } from '@/types/model'
import { PromptRole } from '@/models/debug'

export let apiPrefix = ''
export let publicApiPrefix = ''
export let template_url = ''

// NEXT_PUBLIC_API_PREFIX=/console/api NEXT_PUBLIC_PUBLIC_API_PREFIX=/api npm run start
if (process.env.NEXT_PUBLIC_API_PREFIX && process.env.NEXT_PUBLIC_PUBLIC_API_PREFIX && process.env.NEXT_PUBLIC_DOCUMENTATION_URL) {
  apiPrefix = process.env.NEXT_PUBLIC_API_PREFIX
  publicApiPrefix = process.env.NEXT_PUBLIC_PUBLIC_API_PREFIX
  template_url = process.env.NEXT_PUBLIC_DOCUMENTATION_URL
}
else if (
  globalThis.document?.body?.getAttribute('data-api-prefix')
  && globalThis.document?.body?.getAttribute('data-public-api-prefix')
  && globalThis.document?.body?.getAttribute('data-public-document-api-prefix')
) {
  apiPrefix = globalThis.document.body.getAttribute('data-api-prefix') as string
  publicApiPrefix = globalThis.document.body.getAttribute('data-public-api-prefix') as string
  template_url = globalThis.document.body.getAttribute('data-public-document-api-prefix') as string
}
else {
  // const domainParts = globalThis.location?.host?.split('.');
  // in production env, the host is agent.app . In other env, the host is [dev].agent.app
  // const env = domainParts.length === 2 ? 'ai' : domainParts?.[0];
  apiPrefix = '/console/api'
  publicApiPrefix = '/api' // avoid browser private mode api cross origin
  template_url = '/'
}
// 接口前缀
export const API_PREFIX: string = apiPrefix
export const PUBLIC_API_PREFIX: string = publicApiPrefix
export const TEMPLATE_URL: string = template_url
// 应用名称
export const APP_NAME: string = process.env.NEXT_PUBLIC_APP_NAME || globalThis.document?.body?.getAttribute('data-public-app-name') || '星辰智能体平台'
// 应用描述
export const APP_DESCRIPTION: string = process.env.NEXT_PUBLIC_APP_DESC || globalThis.document?.body?.getAttribute('data-public-app-desc') || '星辰智能体平台是中国电信自主研发的智能体应用开发平台，提供自主规划和工作流编排应用创建方式，以及高效的知识库管理工具；内置丰富的开发模板和工具，可实现智能体应用快速创建和发布，加速大模型技术的产业化落地。'
// 应用key
export const APP_KEY: string = process.env.NEXT_PUBLIC_APP_KEY || globalThis.document?.body?.getAttribute('data-public-app-key') || '星辰智能体平台,自主规划,工作流,知识库,智能体,应用开发'

const EDITION = process.env.NEXT_PUBLIC_EDITION || globalThis.document?.body?.getAttribute('data-public-edition') || 'SELF_HOSTED'
export const IS_CE_EDITION = EDITION === 'SELF_HOSTED'

export const TONE_LIST = [
  {
    id: 1,
    name: 'Creative',
    config: {
      temperature: 0.8,
      top_p: 0.9,
      presence_penalty: 0.1,
      frequency_penalty: 0.1,
    },
  },
  {
    id: 2,
    name: 'Balanced',
    config: {
      temperature: 0.5,
      top_p: 0.85,
      presence_penalty: 0.2,
      frequency_penalty: 0.3,
    },
  },
  {
    id: 3,
    name: 'Precise',
    config: {
      temperature: 0.2,
      top_p: 0.75,
      presence_penalty: 0.5,
      frequency_penalty: 0.5,
    },
  },
  {
    id: 4,
    name: 'Custom',
  },
]

export const DEFAULT_CHAT_PROMPT_CONFIG = {
  prompt: [
    {
      role: PromptRole.system,
      text: '',
    },
  ],
}

export const DEFAULT_COMPLETION_PROMPT_CONFIG = {
  prompt: {
    text: '',
  },
  conversation_histories_role: {
    user_prefix: '',
    assistant_prefix: '',
  },
}

export const LOCALE_COOKIE_NAME = 'locale'

export const DEFAULT_VALUE_MAX_LEN = 48
export const DEFAULT_PARAGRAPH_VALUE_MAX_LEN = 1000

export const zhRegex = /^[\u4E00-\u9FA5]$/m
export const emailRegex = /^[\w.!#$%&'*+\-/=?^{|}~]+@([\w-]+\.)+[\w-]{2,}$/m
const MAX_ZN_VAR_NAME_LENGTH = 8
const MAX_EN_VAR_VALUE_LENGTH = 30
export const getMaxVarNameLength = (value: string) => {
  if (zhRegex.test(value))
    return MAX_ZN_VAR_NAME_LENGTH

  return MAX_EN_VAR_VALUE_LENGTH
}

export const MAX_VAR_KEY_LENGTH = 30

export const MAX_PROMPT_MESSAGE_LENGTH = 10

export const VAR_ITEM_TEMPLATE = {
  key: '',
  name: '',
  type: 'string',
  max_length: DEFAULT_VALUE_MAX_LEN,
  required: true,
}

export const VAR_ITEM_TEMPLATE_IN_WORKFLOW = {
  variable: '',
  label: '',
  type: InputVarType.textInput,
  max_length: DEFAULT_VALUE_MAX_LEN,
  required: true,
  options: [],
}

export const DATASET_DEFAULT = {
  top_k: 4,
  score_threshold: 0.8,
}

export const APP_PAGE_LIMIT = 10

export const ANNOTATION_DEFAULT = {
  score_threshold: 0.9,
}

export const MAX_TOOLS_NUM = 10

export const DEFAULT_AGENT_SETTING = {
  enabled: false,
  max_iteration: 5,
  strategy: AgentStrategy.functionCall,
  tools: [],
}

export const DEFAULT_AGENT_PROMPT = {
  chat: `Respond to the human as helpfully and accurately as possible.

  {{instruction}}

  You have access to the following tools:

  {{tools}}

  Use a json blob to specify a tool by providing an {{TOOL_NAME_KEY}} key (tool name) and an {{ACTION_INPUT_KEY}} key (tool input).
  Valid "{{TOOL_NAME_KEY}}" values: "Final Answer" or {{tool_names}}

  Provide only ONE action per $JSON_BLOB, as shown:

  \`\`\`
  {
    "{{TOOL_NAME_KEY}}": $TOOL_NAME,
    "{{ACTION_INPUT_KEY}}": $ACTION_INPUT
  }
  \`\`\`

  Follow this format:

  Question: input question to answer
  Thought: consider previous and subsequent steps
  Action:
  \`\`\`
  $JSON_BLOB
  \`\`\`
  Observation: action result
  ... (repeat Thought/Action/Observation N times)
  Thought: I know what to respond
  Action:
  \`\`\`
  {
    "{{TOOL_NAME_KEY}}": "Final Answer",
    "{{ACTION_INPUT_KEY}}": "Final response to human"
  }
  \`\`\`

  Begin! Reminder to ALWAYS respond with a valid json blob of a single action. Use tools if necessary. Respond directly if appropriate. Format is Action:\`\`\`$JSON_BLOB\`\`\`then Observation:.`,
  completion: `
  Respond to the human as helpfully and accurately as possible.

{{instruction}}

You have access to the following tools:

{{tools}}

Use a json blob to specify a tool by providing an {{TOOL_NAME_KEY}} key (tool name) and an {{ACTION_INPUT_KEY}} key (tool input).
Valid "{{TOOL_NAME_KEY}}" values: "Final Answer" or {{tool_names}}

Provide only ONE action per $JSON_BLOB, as shown:

\`\`\`
{{{{
  "{{TOOL_NAME_KEY}}": $TOOL_NAME,
  "{{ACTION_INPUT_KEY}}": $ACTION_INPUT
}}}}
\`\`\`

Follow this format:

Question: input question to answer
Thought: consider previous and subsequent steps
Action:
\`\`\`
$JSON_BLOB
\`\`\`
Observation: action result
... (repeat Thought/Action/Observation N times)
Thought: I know what to respond
Action:
\`\`\`
{{{{
  "{{TOOL_NAME_KEY}}": "Final Answer",
  "{{ACTION_INPUT_KEY}}": "Final response to human"
}}}}
\`\`\`

Begin! Reminder to ALWAYS respond with a valid json blob of a single action. Use tools if necessary. Respond directly if appropriate. Format is Action:\`\`\`$JSON_BLOB\`\`\`then Observation:.
Question: {{query}}
Thought: {{agent_scratchpad}}
  `,
}

export const VAR_REGEX = /\{\{(#[a-zA-Z0-9_-]{1,50}(\.[a-zA-Z_][a-zA-Z0-9_]{0,29}){1,10}#)\}\}/gi

export const resetReg = () => VAR_REGEX.lastIndex = 0

export let textGenerationTimeoutMs = 60000

if (process.env.NEXT_PUBLIC_TEXT_GENERATION_TIMEOUT_MS && process.env.NEXT_PUBLIC_TEXT_GENERATION_TIMEOUT_MS !== '')
  textGenerationTimeoutMs = parseInt(process.env.NEXT_PUBLIC_TEXT_GENERATION_TIMEOUT_MS)
else if (globalThis.document?.body?.getAttribute('data-public-text-generation-timeout-ms') && globalThis.document.body.getAttribute('data-public-text-generation-timeout-ms') !== '')
  textGenerationTimeoutMs = parseInt(globalThis.document.body.getAttribute('data-public-text-generation-timeout-ms') as string)

export const TEXT_GENERATION_TIMEOUT_MS = textGenerationTimeoutMs

// rsa公钥
export const PUBLIC_RSA = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0u6OYtAf11I5pLtVLP0JEmfIP8mo5Octx5cM95Yv/SeYljk7vgr+YJbKuRxQh+osFgGE9cnESCuR69rVtbgAxlDJypNwg+TR+CXIFCxCMs3SXnYAYf5bGaSFn4KojZG9ExA+adXUr+kQnC/OPKovhwxJfDsKVgPnPZoGcb4GyyVtXvUbxOJ9ZxOiu6uR8Sj2+efFWjXlAtD6V1heNkOpFdAktH/S3ex4uXfDzmf0UAoYhrjmYxOJm2cQYIW/LC3+xNiF6mh5nhMO63rFMuyg4yEe/464GOIgUlehoV9eON4DySPrky+GdyTeCJYoj0E5jkgtl6rSnpydidBHEKHfVQIDAQAB'
// rsa私钥
export const PRIVATE_RSA = 'MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDS7o5i0B/XUjmku1Us/QkSZ8g/yajk5y3Hlwz3li/9J5iWOTu+Cv5glsq5HFCH6iwWAYT1ycRIK5Hr2tW1uADGUMnKk3CD5NH4JcgULEIyzdJedgBh/lsZpIWfgqiNkb0TED5p1dSv6RCcL848qi+HDEl8OwpWA+c9mgZxvgbLJW1e9RvE4n1nE6K7q5HxKPb558VaNeUC0PpXWF42Q6kV0CS0f9Ld7Hi5d8POZ/RQChiGuOZjE4mbZxBghb8sLf7E2IXqaHmeEw7resUy7KDjIR7/jrgY4iBSV6GhX1443gPJI+uTL4Z3JN4IliiPQTmOSC2XqtKenJ2J0EcQod9VAgMBAAECggEACn+xOp3dV9xR3i8s2CZoEzBzOSmSbIJNNwf/rzscfF9CKwgCPz1Q2AHpaTlNkNZJB3df5NP8HxN8Gd80j+zurPBMevbHvOjgwpcu1EI3sUupUuZQso/0fNxBgmUAertsS8ydLqtkALV7DCsc+swFvZUQ+0v9AbcEBX7rHzI0KFxHZtv813VH+K4VWKUvslo/kahKBX7lecK0J1U0gkWUM7BL4JRmkpFL+hL7EjeVJB7wvv6rGdSVimS2gcOWyIgr6tHeg5l3X3eOXotgZ9dsYC5PR3HMK4dUMqmpvXktBqbD2jBg2cJp4xfq8O5qw0wbz0GqqT1pvet9IJtpGD80AQKBgQDvRYDjCmHP5AM8pX3MNC/GH2AZHU7GYdiBaEKk2tWW7Y3dQfqzsEq7oCaqw+sOjD4aro4D7qkt0Iq4jp+2nqwh1xqoiHWTh+v5zD7WkT2RKzxMHDPQwRUV4Ql1JOz52i0540ZXll9CaQ1aiB+3QJn3P8GfrPX6RSpRW8ApmYOzgQKBgQDhrdP9cQlG3Sd4PJeJMC+BOVgjs1WzD3SiNhSjRFmhDB/IPeq9oXpMm9TaejD60hLedSwh++VOh/nwCFsU8WpIroEEWaGsuSj6lor3AwMEcsczb+fiDysUpUUJ3bCCNKRZcMGOzA6bNLgUdxhyvgaaaRe/cdec6hDmnredGYwF1QKBgQDqr5h4mOFRgIrwPD5EQdeuE241k/qFOMmcPEBJ5F7ld8HTCHk3MC5NFAt5KUtv7HypvRaMotWLm2XFGhdrjvy1AIAkWBxDBWmejQgBe1oQ/oBZAryMACaHXD/jh8FsW+RKSDLhjBuJTvMLHkNFgfbCfeWTkZl+zcJxJHY6F1oHgQKBgQCV3NjpdCa06CdNsO7wKw9gwLPHAU/nmFvbXA5YXQsn5uU0E+nOA20zckfmzT25Ucc18plb9+pOO/29Z88UGTx7B2alfN/f0xVjUpT1cwqN6apeffF+LJRvIVC3PH/59vssabQfH2KTrm6PKIcTqxy3bRJYD0Ee8lBG1R8ZM93IAQKBgCPEnMJevWeLWOUwUB63TLL5olgVH4R0cCU4NlTPVmE4M/+PT8zvMPxDPYG5OGqcGAk5+gTPiPOoiKs95YHM7ZknVFmEXAzRNSZhKDAmM8CIsgZFqHCiNiS1PJsrh4nbyYvlA1Ynp004gPbjW0YkLIznvWQ+FcV+tkBLpBfMYomx'

export const MAX_APP_NAME_LENGTH = 30
// 最大应用名称长度
export const MAX_APP_DESC_LENGTH = 500
