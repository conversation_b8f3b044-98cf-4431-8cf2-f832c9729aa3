import { useTranslation } from 'react-i18next'
import { type FC, useEffect, useRef } from 'react'
import React from 'react'
import { isEqual } from 'lodash-es'
import type { KnowledgeRetrievalNodeType } from './types'
import { useMiniConfig } from './use-config'
import type { DataSet } from '@/models/datasets'

// 公共能力
import s from '@/app/components/workflow/nodes/styles/style.module.css'
import type { NodeProps } from '@/app/components/workflow/types'
import ForkTip from '@/app/components/base/forkTip'
import DatasetItem from '@/app/components/datasets/common/dataset-item'

const Node: FC<NodeProps<KnowledgeRetrievalNodeType>> = ({
  id,
  data,
}) => {
  const { t } = useTranslation()
  const { forkDatasets, allDatasets, mutate } = useMiniConfig(id, data)
  const init = useRef(false)
  const initIds = useRef<string[]>([])

  useEffect(() => {
    console.log(data.dataset_ids)
    if (!init.current || isEqual(initIds.current, data.dataset_ids))
      init.current = true
    else
      mutate(data.dataset_ids)
  }, [data.dataset_ids])
  if (!allDatasets.length && !forkDatasets.length)
    return null
  return (
    <div className={s.nodeContent}>
      {forkDatasets.length > 0 && data.fork && (
        <ForkTip
          className='forkTip mb-2'
          message={t('common.fork.datasetTip')}
        >
          <div>
            {forkDatasets.map(({ id, name }) => (
              <div key={id} className=''>
                <div className=''>
                  · {name}
                </div>
              </div>
            ))}
          </div>
        </ForkTip>
      )}
      {allDatasets.length > 0 && (
        <div className='space-y-2'>
          {(allDatasets as DataSet[]).map((item, index) => (
            <DatasetItem
              key={index}
              name={item.name}
              id={item.id}
              border={false}
              lack={item.lack}
              className='!h-[26px] !px-2 !py-1 !bg-[#f8f9fa]'
              textClassName='!text-S1 !leading-H1'
            ></DatasetItem>
          ))}
        </div>
      )}
    </div>
  )
}

export default React.memo(Node)
