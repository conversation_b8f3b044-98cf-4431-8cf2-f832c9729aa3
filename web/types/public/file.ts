import { TransferMethod } from "../model"

export type ImageFile = {
    type: TransferMethod
    _id: string
    fileId: string
    file?: File
    progress: number
    url: string
    base64Url?: string
    deleted?: boolean
  }

  export type FileResponse = {
    related_id: string
    extension: string
    filename: string
    size: number
    mime_type: string
    transfer_method: TransferMethod
    type: string
    url: string
  }
  export type CustomFile = File & {
    id?: string
    extension?: string
    mime_type?: string
    created_by?: string
    created_at?: number
  }
  export type FileItem = {
    fileID: string
    file: CustomFile
    progress: number
  }