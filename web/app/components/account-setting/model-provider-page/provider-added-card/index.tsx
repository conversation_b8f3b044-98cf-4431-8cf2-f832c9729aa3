import type { FC } from 'react'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { LoadingOutlined } from '@ant-design/icons'
import type {
  ModelItem,
  ModelProvider,
} from '../declarations'
import {
  ConfigurationMethodEnum,
  EventEmitterType,
} from '../declarations'

import {
  modelTypeFormat,
} from '../utils'
import s from '../styles/index.module.css'
import ProviderIcon from '../provider-icon'
import CredentialPanel from './credential-panel'
import ModelListItem from './model-list-item'
import { fetchModelProviderModelList } from '@/service/common'
import { useEventEmitterContextContext } from '@/context/event-emitter'
import { useAppContext } from '@/context/app-context'
import cn from '@/utils/classnames'
// 公共组件
import Badge from '@/app/components/base/badge'
import TextButton from '@/app/components/base/button/text-button'
import { ArrowDown } from '@/app/components/base/icons/src/vender/line/arrows'
import { Add } from '@/app/components/base/icons/src/vender/solid/general'

export const UPDATE_MODEL_PROVIDER_CUSTOM_MODEL_LIST = 'UPDATE_MODEL_PROVIDER_CUSTOM_MODEL_LIST'
type ProviderAddedCardProps = {
  provider: ModelProvider
  showAdd?: Boolean
  showTool?: Boolean
  showItemOperation?: Boolean
}
const ProviderAddedCard: FC<ProviderAddedCardProps> = ({
  provider,
  showAdd = true,
  showTool = true,
  showItemOperation = true,
}) => {
  const { t } = useTranslation()
  const { eventEmitter } = useEventEmitterContextContext()
  const { canAdmin } = useAppContext()
  // 模型数据是否获取
  const [fetched, setFetched] = useState(false)
  // 加载
  const [loading, setLoading] = useState(false)
  // 模型列表是否折叠
  const [collapsed, setCollapsed] = useState(true)
  // 模型列表
  const [modelList, setModelList] = useState<ModelItem[]>([])
  // 配置方法
  const hasModelList = fetched && !!modelList.length

  // 通过provide获取模型列表
  const getModelList = async (providerName: string) => {
    if (loading)
      return
    try {
      setLoading(true)
      const modelsData = await fetchModelProviderModelList(`/workspaces/current/model-providers/${providerName}/models`)
      setModelList(modelsData.data)
      setCollapsed(false)
      // setFetched(true)
    }
    finally {
      setLoading(false)
    }
  }
  // 控制模型列表展开
  const handleOpenModelList = () => {
    if (fetched) {
      setCollapsed(false)
      return
    }

    getModelList(provider.provider)
  }
  // 新增模型
  const addModel = () => {
    eventEmitter?.emit({
      type: EventEmitterType.addModel,
      provider,
    } as any)
  }

  // 事件总线更新模型列表
  eventEmitter?.useSubscription((v: any) => {
    if (v?.type === EventEmitterType.updateModelList && v.provider === provider.provider)
      getModelList(v.provider)
  })

  return (
    <div className={s['provider-item']}>
      <div className={s['provider-item-header']}>
        <div className='mb-4 w-full flex justify-between items-center'>
          {/* 供应商信息面板 */}
          <ProviderIcon provider={provider}/>
          {/* 工具箱 */}
          {showTool && <CredentialPanel provider={provider}/>}
        </div>
        {/* 供应商标签信息 */}
        <div className='flex gap-2'>
          {
            provider.supported_model_types.map(modelType => (
              <Badge key={modelType}>
                {modelTypeFormat(modelType)}
              </Badge>
            ))
          }
        </div>
      </div>
      <div className={s['provider-item-content']}>
        <div className={cn(s['provider-item-collapse'], 'group') }>
          {/* 展开按钮 */}
          <div
            className='flex items-center cursor-pointer'
            onClick={ collapsed ? handleOpenModelList : () => setCollapsed(true)}
          >
            {
              hasModelList
                ? t('account.modelProvider.modelsNum', { num: modelList.length })
                : t('account.modelProvider.showModels')
            }
            <ArrowDown className={cn('hidden group-hover:block ml-1', collapsed || 'rotate-180')}></ArrowDown>
            {
              loading && (
                <LoadingOutlined className='ml-0.5 w-3 h-3' />
              )
            }
          </div>
          {/* 新增按钮 */}
          {
            showAdd && canAdmin && (
              <TextButton
                onClick={() => addModel()}
                className='hidden group-hover:flex !gap-0.5 !text-S1 !leading-H1'
              >
                <Add></Add>
                <span>{t('account.modelProvider.addModel')}</span>
              </TextButton>
            )
          }
        </div>
        {
          !collapsed && (

            modelList.map(model => (
              <ModelListItem
                key={model.model}
                {...{
                  model,
                  provider,
                  isConfigurable: model.fetch_from === ConfigurationMethodEnum.customizableModel,
                  showOperation: showItemOperation,
                }}
              />
            ))
          )
        }
      </div>
    </div>
  )
}

export default ProviderAddedCard
