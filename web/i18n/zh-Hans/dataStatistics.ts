const translation = {
  title: '统计数据',
  dayCount: '{{day}}天',
  purchase: '购买资源包',
  primaryAccountDataSummary: '主账号权益下数据消耗详情',
  personalSpace: '个人空间',
  teamSpaceConsume: '团队整体消耗数据',
  personalSpaceConsume: '个人空间消耗数据',
  myConsume: '归属我本人的消耗',
  personalSpaceSubTitle: '模型及点数消耗数据',
  app: '应用',
  action: {
    detail: '详情',
    getPriceList: '价格清单'
  },
  dataSummary: {
    title: '数据概要总览',
    resourcePackPoints: '资源包点数',
    appNum: '应用个数',
    datasetNum: '知识库个数',
    datasetCapacity: '知识库容量',
    toolNum: '工具个数'
  },
  resourcePoints: {
    title: '资源点数据详情',
    purchaseDate: '购买时间',
    purchasePoints: '购买点数',
    availablePoints: '可用点数',
    expirationTime: '到期时间',
    daysRemaining: '剩余天数',
    timeSelect: {
      placeholder: '到期时间',
      last1Month: '1个月以内',
      last3Months: '1-3个月',
      last5Months: '3-5个月',
      last7Months: '5-7个月',
      last9Months: '7-9个月',
      last12Months: '9-12个月'
    }
  },
  modelPointsConsum: {
    title: '内置模型点数消耗详情',
    model: '内置模型',
    input: '输入消耗点数',
    output: '输出消耗点数',
    all: '总消耗点数'
  },
  modelTokenConsum: {
    title: '内置模型token数据消耗详情',
    model: '内置模型',
    input: '输入消耗token',
    output: '输出消耗token',
    all: '消耗总token'
  },
  appApiCalls: {
    title: '已发布应用API调用次数',
    name: '名称',
    callNum: '支持调用次数'
  },
  modelPricing: {
    title: '内置模型价格清单',
    name: '名称',
    inputConsume: '输入消耗(点/千token）',
    outputConsume: '输出消耗(点/千token）',
  }
}

export default translation
