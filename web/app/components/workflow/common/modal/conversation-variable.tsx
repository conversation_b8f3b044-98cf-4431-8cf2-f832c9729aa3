'use client'
import React, { memo, useCallback, useEffect, useState } from 'react'
import { useMount } from 'ahooks'
import { useTranslation } from 'react-i18next'
import { capitalize } from 'lodash-es'
import { Table } from 'antd'

import { useStore } from '@/app/components/workflow/store'
import type { ConversationVariable } from '@/app/components/workflow/types'
import { fetchCurrentValueOfConversationVariable } from '@/service/workflow'

// 公共组件
import Modal from '@/app/components/base/modal'
import useTimestamp from '@/hooks/use-timestamp'

export type Props = {
  conversationID: string
  onHide: () => void
}

const ConversationVariableModal = ({
  conversationID,
  onHide,
}: Props) => {
  const { t } = useTranslation()
  const { formatTime } = useTimestamp()
  const varList = useStore(s => s.conversationVariables) as ConversationVariable[]
  const appID = useStore(s => s.appId)

  // 对话变量id和值的映射
  const [latestValueMap, setLatestValueMap] = useState<Record<string, string>>({})
  // 对话变量id和时间戳的映射，用于判断是否更新
  const [latestValueTimestampMap, setLatestValueTimestampMap] = useState<Record<string, number>>({})
  // 对话变量列表
  const [data, setData] = useState<{
    key: string
    variableName: string
    variableType: string
    storedContent: string
    updatedAt: string
  }[]>([])

  // 获取对话变量
  const getChatVarLatestValues = useCallback(async () => {
    if (conversationID && varList.length > 0) {
      const res = await fetchCurrentValueOfConversationVariable({
        url: `/apps/${appID}/conversation-variables`,
        params: { conversation_id: conversationID },
      })
      if (res.data.length > 0) {
        const valueMap = res.data.reduce((acc: any, cur) => {
          acc[cur.id] = cur.value
          return acc
        }, {})
        setLatestValueMap(valueMap)
        const timestampMap = res.data.reduce((acc: any, cur) => {
          acc[cur.id] = cur.updated_at
          return acc
        }, {})
        setLatestValueTimestampMap(timestampMap)
      }
    }
  }, [appID, conversationID, varList.length])

  useMount(() => {
    getChatVarLatestValues()
  })
  useEffect(() => {
    const newData = varList.map(chatVar => ({
      key: chatVar.id,
      variableName: chatVar.name,
      variableType: capitalize(chatVar.value_type),
      storedContent: latestValueMap[chatVar.id] || '',
      updatedAt: latestValueTimestampMap[chatVar.id]
        ? formatTime(latestValueTimestampMap[chatVar.id], 'YYYY-MM-DD HH:mm:ss')
        : '',
    }))
    setData(newData)
  }, [formatTime, latestValueMap, latestValueTimestampMap, varList])

  const columns = [
    {
      title: t('workflow.chatVariable.variableName'),
      dataIndex: 'variableName',
      key: 'variableName',
    },
    {
      title: t('workflow.chatVariable.variableType'),
      dataIndex: 'variableType',
      key: 'variableType',
    },
    {
      title: t('workflow.chatVariable.storedContent').toLocaleUpperCase(),
      dataIndex: 'storedContent',
      key: 'storedContent',
    },
    {
      title: t('workflow.chatVariable.updatedAt'),
      dataIndex: 'updatedAt',
      key: 'updatedAt',
    },
  ]

  return (
    <Modal
      isShow
      title={t('workflow.chatVariable.panelTitle')}
      onClose={onHide}
      className='w-[920px] max-w-[920px]'
      closable
    >
      <Table
        className='border-gray-G5 rounded border'
        columns={columns}
        dataSource={data}
        pagination={false}
      />
    </Modal>
  )
}

export default memo(ConversationVariableModal)
