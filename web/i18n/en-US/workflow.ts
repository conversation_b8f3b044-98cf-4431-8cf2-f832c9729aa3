const translation = {
  common: {
    undo: 'Undo',
    redo: 'Redo',
    toolConfig: 'Tool Configuration',
    updateSubmit: 'Update Submit',
    autoSaved: 'Auto Saved',
    unpublished: 'Unpublished',
    published: 'Published',
    publish: 'Publish',
    update: 'Update',
    run: 'Run',
    running: 'Running',
    preview: 'Preview',
    viewRunHistory: 'View Run History',
    runHistory: 'Run History',
    goBackToEdit: 'Go Back to Edit Mode',
    features: 'Features',
    addVariable: 'Add Variable',
    ImageUploadLegacyTip: 'You can now create file type variables in the start form. In the future, we will no longer support image upload functionality.',
    fileUploadTip: 'Image upload functionality has been extended to file upload.',
    featuresDocLink: 'Learn More',
    debugAndPreview: 'Preview',
    restart: 'Restart',
    currentDraft: 'Current Draft',
    currentDraftUnpublished: 'Current Draft Unpublished',
    latestPublished: 'Latest Published',
    publishedAt: 'Published At',
    restore: 'Restore',
    runApp: 'Run Web App',
    batchRunApp: 'Batch Run App',
    runDisabled: {
      notPublished: 'The current app is not published',
      notPublishedAfterCopied: 'Copied apps/created similar apps need to be published before they can run',
    },
    appUseType: 'App Usage Type',
    embedIntoSite: 'Embed into Site',
    addTitle: 'Add Title...',
    addDescription: 'Add Description...',
    noVar: 'No Variables',
    variableNamePlaceholder: 'Variable Name',
    searchVar: 'Search Variable',
    setVarValuePlaceholder: 'Set Variable Value',
    needConnectTip: 'This node is not connected to other nodes',
    maxTreeDepth: 'Each branch is limited to {{depth}} nodes',
    needEndNode: 'An end node must be added',
    needAnswerNode: 'A direct reply node must be added',
    workflowProcess: 'Workflow',
    notRunning: 'Not Running',
    previewPlaceholder: 'Enter content in the box below to start debugging the chatbot',
    effectVarConfirm: {
      title: 'Remove Variable',
      content: 'This variable is used in other nodes. Do you still want to delete it?',
    },
    insertVarTip: 'Press \'/\' to quickly insert',
    processData: 'Process Data',
    input: 'Input',
    output: 'Output',
    jinjaEditorPlaceholder: 'Enter “/” or “{” to insert variables',
    viewOnly: 'View Only',
    showRunHistory: 'Show Run History',
    enableJinja: 'Enable Jinja Template Support',
    learnMore: 'Learn More',
    copy: 'Copy',
    duplicate: 'Duplicate',
    addBlock: 'Add Node',
    pasteHere: 'Paste Here',
    pointerMode: 'Pointer Mode',
    handMode: 'Hand Mode',
    model: 'Model',
    workflowAsTool: 'Tool Configuration',
    configureRequired: 'Configuration Required',
    configure: 'Configure',
    manageInTools: 'Access Tools Page',
    workflowAsToolTip: 'After updating the workflow, tool parameters need to be reconfigured',
    asToolInitTip: 'After creating the workflow, it needs to be published first before configuring tool parameters',
    viewDetailInTracingPanel: 'View Details',
    syncingData: 'Syncing data, just a few seconds.',
    backupCurrentDraft: 'Backup Current Draft',
    importFailure: 'Import Failed',
    importSuccess: 'Import Successful',
    parallelRun: 'Parallel Run',
    parallelTip: {
      click: {
        title: 'Click ',
        desc: 'to add a node,',
      },
      drag: {
        title: 'Drag ',
        desc: 'to connect nodes',
      },
      limit: 'Parallel branches are limited to {{num}}',
      depthLimit: 'Parallel nesting depth is limited to {{num}} layers',
    },
    disconnect: 'Disconnect',
    jumpToNode: 'Jump to Node',
    addParallelNode: 'Add Parallel Node',
    parallel: 'Parallel',
    branch: 'Branch',
  },
  env: {
    envPanelTitle: 'Environment Variables',
    envDescription: 'Environment variables are a way to store sensitive information such as API keys, database passwords, etc. They are stored in the workflow instead of the code, so they can be shared across different environments.',
    envPanelButton: 'Add Environment Variable',
    modal: {
      title: 'Add Environment Variable',
      editTitle: 'Edit Environment Variable',
      type: 'Type',
      name: 'Name',
      namePlaceholder: 'Variable Name',
      value: 'Value',
      valuePlaceholder: 'Variable Value',
      secretTip: 'Store keys or other sensitive information to prevent plaintext storage from leaking information.',
      nameExist: 'Name already exists',
      valueEmpty: 'Variable value cannot be empty',
    },
    type: {
      string: 'String',
      number: 'Number',
      secret: 'Secret',
    },
  },
  chatVariable: {
    panelTitle: 'Conversation Variables',
    panelDescription: 'Conversation variables are used to store context information needed by LLM, such as user preferences, conversation history, etc. They are readable and writable.',
    docLink: 'See documentation for more.',
    button: 'Add Variable',
    modal: {
      title: 'Add Conversation Variable',
      editTitle: 'Edit Conversation Variable',
      name: 'Name',
      namePlaceholder: 'Variable Name',
      type: 'Type',
      value: 'Default Value',
      valuePlaceholder: 'Default value, can be empty',
      description: 'Description',
      descriptionPlaceholder: 'Description of the variable',
      editInJSON: 'Edit in JSON',
      oneByOne: 'Add one by one',
      editInForm: 'Edit in Form',
      arrayValue: 'Value',
      addArrayValue: 'Add Value',
      objectKey: 'Property',
      objectType: 'Type',
      objectValue: 'Default Value',
      nameExist: 'Name already exists',
      objectKeyEmpty: 'Object property cannot be empty',
    },
    storedContent: 'Stored Content',
    updatedAt: 'Updated At ',
    variableName: 'Variable name',
    variableType: 'Variable type',
  },
  changeHistory: {
    title: 'Change History',
    placeholder: 'No changes made yet',
    clearHistory: 'Clear History',
    hint: 'Hint',
    hintText: 'Your editing actions will be tracked and stored on your device until you leave the editor. This history will be cleared when you leave the editor.',
    stepBackward_one: '{{count}} step backward',
    stepBackward_other: '{{count}} steps backward',
    stepForward_one: '{{count}} step forward',
    stepForward_other: '{{count}} steps forward',
    sessionStart: 'Session Start',
    currentState: 'Current State',
    nodeTitleChange: 'Node title changed',
    nodeDescriptionChange: 'Node description changed',
    nodeDragStop: 'Node moved',
    nodeChange: 'Node changed',
    nodeConnect: 'Node connected',
    nodePaste: 'Node pasted',
    nodeDelete: 'Node deleted',
    nodeAdd: 'Node added',
    nodeResize: 'Node resized',
    noteAdd: 'Note added',
    noteChange: 'Note changed',
    noteDelete: 'Note deleted',
    edgeDelete: 'Node disconnected',
  },
  errorMsg: {
    fieldRequired: '{{field}} is required',
    rerankModelRequired: 'Before enabling the Rerank model, please ensure the model is successfully configured in the settings.',
    authRequired: 'Please authorize first',
    invalidJson: '{{field}} is invalid JSON',
    fields: {
      variable: 'Variable Name',
      variableValue: 'Variable Value',
      code: 'Code',
      model: 'Model',
      rerankModel: 'Rerank Model',
      visionVariable: 'Vision Variable',
      outputVar: 'Input variables',
      sceneDesc: 'Description of the usage scenario',
    },
    invalidVariable: 'Invalid Variable',
    inputVarRequired: 'Please fill in at least one input variable',
    lackDataset: 'Part of the knowledge base has been removed',
    toolNotExist: 'The tool doesn\'t exist',
    lackTool: 'Some of the tools have been removed',
  },
  singleRun: {
    testRun: 'Test Run ',
    startRun: 'Start Run',
    running: 'Running',
    testRunIteration: 'Test Run Iteration',
    back: 'Back',
    iteration: 'Iteration',
  },
  tabs: {
    searchBlock: 'Search Node',
    blocks: 'Nodes',
    searchTool: 'Search Tool',
    tools: 'Tools',
    allTool: 'All',
    builtInTool: 'Built-in',
    customTool: 'Custom',
    workflowTool: 'Workflow',
    default: 'Basic Capabilities',
    logic: 'Logic Processing',
    development: 'Advanced Development',
    noResult: 'No matching items found',
    'information-processing': 'information processing',
    'knowledge-and-memory': 'Knowledge and memory',
  },
  blocks: {
    start: 'Start',
    end: 'End',
    answer: 'Reply',
    llm: 'Large Model',
    code: 'Code',
    assigner: 'Variable Assignment',
    iteration: 'Iteration',
    'list-operator': 'List operations',
    'http-request': 'HTTP',
    'document-extractor': 'Document extraction',
    'question-classifier': 'Classification of issues',
    'if-else': 'condition',
    'template-transform': 'Template conversion',
    'iteration-start': 'The iteration begins',
    'parameter-extractor': 'Parameter extraction',
    'variable-assigner': 'Variable aggregation',
    'variable-aggregator': 'Variable aggregation',
    'knowledge-retrieval': 'knowledge base',
    agent: 'Agents',
    'video-library': 'Video Library',
  },
  blocksAbout: {
    start: 'Define the initial parameters for starting a workflow process',
    end: 'End of the process',
    answer: 'Set the response content in the chat conversation',
    llm: 'Call a large language model to answer questions or process natural language',
    code: 'Write code to implement custom logic',
    assigner: 'Assign values to variables',
    iteration: 'Continuously apply steps to list objects until all results are displayed',
    'question-classifier': 'Define rules for categorizing user questions, and the large model determines the flow of conversations based on these rules',
    'knowledge-retrieval': 'From the selected knowledge base, the best matching information is extracted based on the input variables',
    'if-else': 'Use if/else to split the workflow into two branches',
    'list-operator': 'Used to filter or sort the contents of an array',
    'variable-aggregator': 'Merge variables from different branches into a single variable so that downstream nodes can be configured uniformly',
    'document-extractor': 'Used to parse user-uploaded documents into text content that is easy to understand by LLMs',
    'variable-assigner': 'Merge variables from different branches into a single variable so that downstream nodes can be configured uniformly',
    'template-transform': 'Use a Jinja template to convert data into strings',
    'parameter-extractor': 'Use the large model to extract structured parameters from the text for subsequent tool calls or HTTP requests',
    'http-request': 'Allows HTTP requests to be sent to external services',
    agent: 'Select an existing agent application or create a new agent application to use in the canvas',
    'video-library': 'Introducing videos, video streams, and images is suitable for image segmentation scenarios.',
  },
  operator: {
    zoomIn: 'Zoom In',
    zoomOut: 'Zoom Out',
    zoomTo50: 'Zoom to 50%',
    zoomTo100: 'Zoom to 100%',
    zoomToFit: 'Fit',
  },
  panel: {
    userInputField: 'User Input Field',
    changeBlock: 'Change Node',
    helpLink: 'Help Link',
    about: 'About',
    createdBy: 'Created By',
    nextStep: 'Next Step',
    addNextStep: 'Add the next node in this workflow',
    selectNextStep: 'Select the next node',
    runThisStep: 'Run this step',
    checklist: 'Pre-publish Checklist',
    checklistTip: 'Ensure all issues are resolved before publishing',
    checklistResolved: 'All issues resolved',
    organizeBlocks: 'Organize Nodes',
    change: 'Change',
    optional: '(Optional)',
  },
  nodes: {
    common: {
      outputVars: 'Output Variables',
      insertVarTip: 'Insert Variable',
      memory: {
        memory: 'Memory',
        memoryTip: 'Chat Memory Settings',
        windowSize: 'Memory Window',
        conversationRoleName: 'Conversation Role Name',
        user: 'User Prefix',
        assistant: 'Assistant Prefix',
        type: {
          custom: 'Customize',
          auto: 'Dynamic equilibrium',
        },
        memoryType: 'Ways to memorize',
      },
      tool: {
        tool: 'tool',
      },
      memories: {
        title: 'Memories',
        tip: 'Chat Memories',
        builtIn: 'Built-in',
      },
      errorRetry: {
        maxRetry: 'Maximum number of retries',
        retryInterval: 'Maximum Retry Interval (ms)',
        errorRetry: 'Error retry',
      },
      dataset: {
        knowledge: 'knowledge base',
      },
    },
    start: {
      required: 'Required',
      inputField: 'Input Field',
      builtInVar: 'Built-in Variables',
      outputVars: {
        query: 'User Input',
        memories: {
          des: 'Conversation History',
          type: 'Message Type',
          content: 'Message Content',
        },
        files: 'File List',
      },
      noVarTip: 'The set input can be used in the workflow',
      systemField: 'System input variables',
    },
    end: {
      outputs: 'Outputs',
      output: {
        type: 'Output Type',
        variable: 'Output Variable',
      },
      type: {
        none: 'None',
        structured: 'Structured',
        'plain-text': 'Plain text',
      },
    },
    answer: {
      answer: 'Reply',
      outputVars: 'Output Variables',
      placeholder: 'Please fill in the reply message content, enter/insert variables',
      output: {
        type: 'Output type',
        variable: 'Output variables',
      },
      outputAnswer: 'Output a reply',
      answerContent: 'Reply to the content',
    },
    llm: {
      model: 'Model',
      variables: 'Variables',
      context: 'Context',
      contextTooltip: 'After adding a knowledge base, the large model will provide more accurate answers based on the uploaded documents',
      notSetContextInPromptTip: 'To enable the context feature, fill in the context variable in the prompt.',
      prompt: 'Prompt',
      addMessage: 'Add Message',
      rolePlaceholder: {
        system: 'Provide system-level guidance for the agent, such as setting personas and response logic and limitations.',
        user: 'Provide instructions, queries, or any text-based input for the agent',
        assistant: 'Model response based on user messages',
      },
      roleDescription: {
        system: 'Provide system-level guidance for the agent, such as setting personas and response logic and limitations.',
        user: 'Provide instructions, queries, or any text-based input for the agent',
        assistant: 'Model response based on user messages',
      },
      vision: 'Vision',
      files: 'Files',
      resolution: {
        name: 'Resolution',
        high: 'High',
        low: 'Low',
      },
      outputVars: {
        output: 'Generated Content',
        usage: 'Model Usage Information',
        type: 'Output Type',
        outputPlaceholderName : 'Output Placeholder Name',
        outputPlaceholderValue : 'Output Placeholder Value'
      },
      singleRun: {
        variable: 'Variable',
      },
      sysQueryInUser: 'The user message must contain sys.query',
      userPrompt: 'User prompt words',
      expansion: 'Capacity development',
      systemPrompt: 'System prompt words',
      abilityDevelopment: 'Capacity development',
    },
    knowledgeRetrieval: {
      queryVariable: 'Query Variable',
      knowledge: 'Knowledge Base',
      outputVars: {
        output: 'Retrieved Segments',
        content: 'Segment Content',
        title: 'Segment Title',
        icon: 'Segment Icon',
        url: 'Segment URL',
        metadata: 'Other Metadata',
      },
    },
    http: {
      inputVars: 'Input Variables',
      api: 'API',
      apiPlaceholder: 'Enter URL, type ‘/’ to insert variables',
      notStartWithHttp: 'API should start with http:// or https://',
      key: 'Key',
      type: 'Type',
      value: 'Value',
      bulkEdit: 'Bulk Edit',
      keyValueEdit: 'Key-Value Edit',
      headers: 'Headers',
      params: 'Params',
      body: 'Body',
      binaryFileVariable: 'Binary File Variable',
      outputVars: {
        body: 'Response Body',
        statusCode: 'Response Status Code',
        headers: 'Response Headers JSON',
        files: 'File List',
      },
      authorization: {
        authorization: 'Authorization',
        authorizationType: 'Authorization Type',
        basic: 'Basic',
        bearer: 'Bearer',
        custom: 'Custom',
        header: 'Header',
        'api-key': 'API-Key',
        'no-auth': 'not',
        'api-key-title': 'API Key',
        'auth-type': 'API authentication type',
      },
      insertVarPlaceholder: 'Type \'/\' to quickly insert variables',
      timeout: {
        title: 'Timeout Settings',
        connectLabel: 'Connect Timeout',
        connectPlaceholder: 'Enter connect timeout (in seconds)',
        readLabel: 'Read Timeout',
        readPlaceholder: 'Enter read timeout (in seconds)',
        writeLabel: 'Write Timeout',
        writePlaceholder: 'Enter write timeout (in seconds)',
      },
      advancedSetting: 'Advanced settings',
    },
    code: {
      inputVars: 'Input Variables',
      outputVars: 'Output Variables',
      advancedDependencies: 'Advanced Dependencies',
      advancedDependenciesTip: 'Add some preloaded dependencies that take a lot of time or are not built-in by default',
      searchDependencies: 'Search Dependencies',
      advancedSetting: 'Advanced settings',
    },
    templateTransform: {
      inputVars: 'Input Variables',
      code: 'Code',
      codeSupportTip: 'Only supports Jinja2',
      outputVars: {
        output: 'Transformed Content',
      },
    },
    ifElse: {
      if: 'If',
      else: 'Else',
      elseDescription: 'Defines the logic to execute when the if condition is not met.',
      and: 'and',
      or: 'or',
      operator: 'Operator',
      notSetVariable: 'Please set the variable first',
      comparisonOperator: {
        contains: 'contains',
        is: 'is',
        empty: 'is empty',
        null: 'is null',
        in: 'is in',
        exists: 'exists',
        'is not': 'No',
        'not empty': 'Not empty',
        'end with': 'The end is',
        'not in': 'No',
        'all of': 'All of them',
        'not null': 'Not empty',
        'not exists': 'does not exist',
        'not contains': 'Not included',
        'start with': 'The beginning is',
      },
      optionName: {
        image: 'Image',
        doc: 'Document',
        audio: 'Audio',
        video: 'Video',
        localUpload: 'Local Upload',
        url: 'URL',
      },
      enterValue: 'Enter Value',
      addCondition: 'Add Condition',
      conditionNotSetup: 'Condition not set',
      selectVariable: 'Select Variable',
      addSubVariable: 'Add Sub-variable',
      select: 'Select',
      conditionalBranch: 'Conditional branching',
      addBranch: 'Create a new branch',
    },
    variableAssigner: {
      title: 'Variable Assignment',
      outputType: 'Output Type',
      varNotSet: 'Variable not set',
      noVarTip: 'Add the variables to be assigned',
      type: {
        string: 'String',
        number: 'Number',
        object: 'Object',
        array: 'Array',
      },
      aggregationGroup: 'Aggregation Group',
      aggregationGroupTip: 'When this feature is enabled, multiple groups of variables can be aggregated simultaneously within the variable aggregator',
      addGroup: 'Add Group',
      outputVars: {
        varDescribe: 'Output variables of {{groupName}}',
      },
      setAssignVariable: 'Set Assigned Variable',
    },
    assigner: {
      assignedVariable: 'Assigned Variable',
      writeMode: 'Write Mode',
      writeModeTip: 'When using append mode, the assigned variable must be of array type.',
      append: 'Append',
      plus: 'Plus',
      clear: 'Clear',
      setVariable: 'Set Variable',
      variable: 'Variable',
      'over-write': 'cover',
    },
    tool: {
      toAuthorize: 'Authorize',
      inputVars: 'Input Variables',
      outputVars: {
        text: 'Content generated by the tool',
        files: {
          title: 'Files generated by the tool',
          type: 'Supported types. Currently only images are supported',
          transfer_method: 'Transfer method. The value is remote_url or local_file',
          url: 'Image URL',
          upload_file_id: 'Upload File ID',
        },
        json: 'JSON generated by the tool',
      },
    },
    questionClassifiers: {
      model: 'Model',
      inputVars: 'Input Variables',
      outputVars: {
        className: 'Classification Name',
      },
      class: 'Classification',
      classNamePlaceholder: 'Enter your classification name',
      advancedSetting: 'Advanced Settings',
      topicName: 'Topic Content',
      topicPlaceholder: 'Enter your topic content here',
      addClass: 'Add Classification',
      instruction: 'Instruction',
      instructionTip: 'You can enter additional instructions to help the question classifier better understand how to classify',
      instructionPlaceholder: 'Enter your instructions here',
    },
    parameterExtractor: {
      inputVar: 'Input Variable',
      extractParameters: 'Extract Parameters',
      importFromTool: 'Import from Tool',
      addExtractParameter: 'Add Extract Parameter',
      addExtractParameterContent: {
        name: 'Name',
        namePlaceholder: 'Extract Parameter Name',
        type: 'Type',
        typePlaceholder: 'Extract Parameter Type',
        description: 'Description',
        descriptionPlaceholder: 'Extract Parameter Description',
        required: 'Required',
        requiredContent: 'Required is only a reference for model inference and is not used for mandatory validation of parameter output.',
      },
      extractParametersNotSet: 'Extract parameters not set',
      instruction: 'Instruction',
      instructionTip: 'You can enter additional instructions to help the parameter extractor understand how to extract parameters',
      advancedSetting: 'Advanced Settings',
      reasoningMode: 'Reasoning Mode',
      reasoningModeTip: 'You can choose the appropriate reasoning mode based on the model\'s response capability to Function calling or Prompt instructions',
      isSuccess: 'Whether successful. The value is 1 when successful, and 0 when failed.',
      errorReason: 'Error Reason',
    },
    iteration: {
      deleteTitle: 'Delete Iteration Node?',
      deleteDesc: 'Deleting the iteration node will delete all sub-nodes',
      input: 'Input',
      output: 'Output Variables',
      iteration_one: '{{count}} iteration',
      iteration_other: '{{count}} iterations',
      currentIteration: 'Current Iteration',
    },
    note: {
      addNote: 'Add Note',
      editor: {
        placeholder: 'Enter note...',
        small: 'Small',
        medium: 'Medium',
        large: 'Large',
        bold: 'Bold',
        italic: 'Italic',
        strikethrough: 'Strikethrough',
        link: 'Link',
        openLink: 'Open',
        unlink: 'Unlink',
        enterUrl: 'Enter URL...',
        invalidUrl: 'Invalid URL',
        bulletList: 'List',
        showAuthor: 'Show Author',
      },
    },
    docExtractor: {
      inputVar: 'Input Variable',
      outputVars: {
        text: 'Extracted Text',
      },
      supportFileTypes: 'Supported file types: {{types}}.',
      learnMore: 'Learn More',
    },
    listFilter: {
      inputVar: 'Input Variable',
      filterCondition: 'Filter Condition',
      filterConditionKey: 'Filter Condition Key',
      filterConditionComparisonOperator: 'Filter Condition Comparison Operator',
      filterConditionComparisonValue: 'Filter Condition Comparison Value',
      selectVariableKeyPlaceholder: 'Select Key of Sub-variable',
      limit: 'Take Top N Items',
      orderBy: 'Order By',
      asc: 'Ascending',
      desc: 'Descending',
      outputVars: {
        result: 'Filter Result',
        first_record: 'First Record',
        last_record: 'Last Record',
      },
    },
    agent: {
      rolePlaceholder: {
        assistant: 'Model replies based on user messages',
        system: 'Provide system-level guidance for agents, such as setting personas and response logic and constraints.',
        user: 'Provide the agent with instructions, queries, or any text-based input',
      },
      roleDescription: {
        assistant: 'Model replies based on user messages',
        user: 'Provide the agent with instructions, queries, or any text-based input',
        system: 'Provide system-level guidance for agents, such as setting personas and response logic and constraints.',
      },
      resolution: {
        low: 'low',
        high: 'high',
        name: 'resolution',
      },
      outputVars: {
        output: 'Generate content',
        type: 'Output type',
        usage: 'Model usage information',
      },
      singleRun: {
        variable: 'variable',
      },
      advance: {
        iterationNum: 'Maximum number of iterations',
      },
      variables: 'variable',
      addMessage: 'Add a message',
      createAgent: 'Create an agent',
      userPrompt: 'User prompt words',
      notSetContextInPromptTip: 'To enable contextual features, fill in the context variable in the prompt.',
      context: 'context',
      selectAgent: 'Select the agent',
      model: 'model',
      contextTooltip: 'Once the knowledge base is added, the large model will provide more accurate answers based on the uploaded documents',
      advanceSetting: 'Advanced settings',
      prompt: 'Prompt words',
      createModeTitle: 'Create a pattern',
      systemPrompt: 'System prompt words',
      abilityDevelopment: 'Capacity development',
      desc: 'Agent description',
      sysQueryInUser: 'The user message must include sys.query',
      vision: 'vision',
      files: 'file',
      sceneDesc: 'Description of the usage scenario',
      query: 'Input variables',
    },
    video: {
      outputVars: {
        timestamp: 'timestamp',
        url: 'Image address information',
        detected_events: 'Segment links',
        output: 'Output variables',
        device_id: 'Device ID',
        address_ip: 'Address information',
      },
      event: 'event',
      queryVariable: 'Input variables',
      device: 'equipment',
      name: 'Video Library',
      addVideoTip: 'Click the " " button to add the video library.',
      endTime: 'End time',
      startTime: 'Start time',
      location: 'place',
    },
  },
  tracing: {
    stopBy: 'Stopped by {{user}}',
  },
  dsl: {
    title: 'DSL management',
    export: 'Export',
    importDSL: 'Import the DSL',
    importWarning: 'Import exceptions',
    chooseDSL: 'Select the DSL (yml) file',
    exportFailed: 'Failed to export the DSL',
    importSuccess: 'The import was successful',
    importFailure: 'The import failed',
    overwriteAndImport: 'Overwrite and import',
    import: 'Import',
    importDSLTip: 'Import prompts',
    checkbox: 'Whether to allow the export of the sensitive information',
    importDSLTipContent: 'After the DSL is imported, the current canvas and configuration information will be overwritten.',
  },
}

export default translation
