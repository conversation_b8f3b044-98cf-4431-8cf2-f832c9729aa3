import { memo } from 'react'
import { capitalize } from 'lodash-es'
import style from '../styles/style.module.css'
import type { ConversationVariable } from '@/app/components/workflow/types'
// 公共组件
import { Delete, Edit } from '@/app/components/base/icons/src/vender/line/general'
import { VariableIcon } from '@/app/components/base/icons/src/public/workflow'
import cn from '@/utils/classnames'

type VariableItemProps = {
  item: ConversationVariable
  onEdit: (item: ConversationVariable) => void
  onDelete: (item: ConversationVariable) => void
}

const VariableItem = ({
  item,
  onEdit,
  onDelete,
}: VariableItemProps) => {
  return (
    <div className={cn(style.variableItemWrap, 'group')}>
      <div className={cn(style.header)}>
        <div className={style.variableTitle}>
          <VariableIcon className='w-5 h-5 text-[#118FE9]' />
          <div className={style.normalTextG1}>{item.name}</div>
          <div className={style.normalTextG2}>{capitalize(item.value_type)}</div>
        </div>
        <div className={cn(style.actionWrap, 'hidden group-hover:flex')}>
          <Edit className={style.iconBtn} onClick={() => onEdit(item)}/>
          <Delete className={style.iconBtn} onClick={() => onDelete(item)}/>
        </div>
      </div>
      <div className={style.desctext}>{item.description}</div>
    </div>
  )
}

export default memo(VariableItem)
