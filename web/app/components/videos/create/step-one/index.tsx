'use client'
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Form, Input } from 'antd'
import produce from 'immer'
import { debounce } from 'lodash-es'
import type { FileItem, VideoStreamConfig } from '@/types/videos'
import { MediaType } from '@/types/videos'

import { useVideoCreateMode } from '@/app/components/videos/hook'
// 公共能力
import { useFormDisabled } from '@/hooks/use-form'
import FileUploaderInDrag from '@/app/components/base/file-uploader/file-uploader-in-drag'
import TextButton from '@/app/components/base/button/text-button'
import { validateEmpty, validateUrl } from '@/utils/validate'
import { checkVideoStreamURL } from '@/service/videos'

type IStepOneProps = {
  id?: string
  type: MediaType
  updateReady: (ready: boolean) => void
}
type URLItemProps = {
  id?: string
  value?: {
    name?: string
    url?: string
  }
  onChange?: (value: {
    name?: string
    url?: string
  }) => void
}
const URLItem: React.FC<URLItemProps> = (props) => {
  const { id, value = {}, onChange } = props
  const { t } = useTranslation()
  // url名称
  const [name, setName] = useState('')
  // url网址信息
  const [url, setUrl] = useState('')

  const triggerChange = (changedValue: { name?: string; url?: string }) => {
    onChange?.({ name, url, ...value, ...changedValue })
  }

  // 名称更改
  const onNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value || ''

    if (!('name' in value))
      setName(newName)
    triggerChange({ name: newName })
  }
  // url更改
  const onUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newUrl = e.target.value || ''

    if (!('url' in value))
      setUrl(newUrl)

    triggerChange({ url: newUrl })
  }

  return (
    <div
      id={id}
      className='flex gap-3'>
      {/* url名称 */}

      <Input
        className='w-[200px]'
        placeholder={t('videos.create.form.urlName') || ''}
        value={value.name}
        onChange={onNameChange}
      />
      {/* url网址信息 */}
      <Input
        placeholder={t('videos.create.form.urlInfo') || ''}
        value={value.url}
        onChange={onUrlChange}
      />
    </div>
  )
}
const MAX_NUM = 10

const StepOne = forwardRef(({
  id,
  type,
  updateReady,
}: IStepOneProps, ref) => {
  const { t } = useTranslation()
  const [form] = Form.useForm()
  const disabled = useFormDisabled(form)
  const { isVideoStreamMode, isVideoMode, isImageMode } = useVideoCreateMode(type)

  // 视频流数据
  const [videoStream, setVideoStream] = useState<Array<VideoStreamConfig>>([
    {
      name: '',
      url: '',
    },
  ])
  // 文件列表
  const [fileList, setFiles] = useState<FileItem[]>([])
  const fileListRef = useRef<FileItem[]>([])

  // 添加视频流
  const handleAddVoiceStream = () => {
    const result = produce(videoStream, (draft) => {
      draft.push({
        name: '',
        url: '',
      })
    })
    setVideoStream(result)
  }
  // 删除视频流
  const handleDeleteVoiceStream = (index: number) => {
    const result = produce(videoStream, (draft) => {
      draft.splice(index, 1)
    })
    setVideoStream(result)
  }
  // 更新文件列表
  const updateFileList = (preparedFiles: FileItem[]) => {
    setFiles(preparedFiles)
    fileListRef.current = preparedFiles
  }
  // 更新文件
  const updateFile = (fileItem: FileItem, progress: number, list: FileItem[]) => {
    const targetIndex = list.findIndex(file => file.fileID === fileItem.fileID)
    list[targetIndex] = {
      ...list[targetIndex],
      progress,
    }
    setFiles([...list])
    fileListRef.current = [...list]
  }
  // 适配文件
  const wrapperFile = (res: any) => {
    return {
      id: res.data.id,
      name: res.data.file_name,
    }
  }
  // 获取表单数据
  const getFormInfo = useCallback(() => {
    return {
      ...form.getFieldsValue(),
      files: fileListRef.current,
    }
  }, [form])
  // 确认url是否存在
  const checkUrlExist = debounce(async (url: string) => {
    await checkVideoStreamURL(id!, url).then((res) => {
      if (res.data.exists)
        return Promise.reject(new Error(t('videos.create.form.urlInfoRepeat')!))
      else
        return Promise.resolve()
    }).catch(() => {
      return Promise.reject(new Error(t('videos.create.form.urlInfoRepeat')!))
    })
  }, 500, { leading: true, trailing: false })

  useEffect(() => {
    updateReady(!(
      disabled
      || ((isImageMode || isVideoMode) && (!fileList.length || fileList.some(file => !file.file.id)))
    ))
  }, [videoStream, disabled, updateReady, fileList, isImageMode, isVideoMode, isVideoStreamMode])
  useImperativeHandle(ref, () => ({
    getFormInfo,
  }))

  return (
    <Form form={form} layout='vertical'>
      {
        isVideoStreamMode
        && (
          <Form.Item
            label={t('videos.create.form.uploader')}
            required
            className='!mb-0'
          >
            {
              videoStream.map((item, index) => {
                return (
                  <div className='flex gap-3 w-full relative' key={index}>
                    <Form.Item
                      name={['voice_stream_config', index]}
                      rules={[{
                        validator: async (rule, value: { name: string; url: string }, callback) => {
                          // url名称为空
                          if (validateEmpty(value.name))
                            return Promise.reject(new Error(t('videos.create.form.urlNamePlaceholder')!))
                          // url网址信息
                          if (validateEmpty(value.url))
                            return Promise.reject(new Error(t('videos.create.form.urlInfoPlaceholder')!))
                          // url名称超过50个字符
                          if (value.name.length > 50)
                            return Promise.reject(new Error(t('videos.create.form.urlNameMaxLength')!))
                          // url网址信息超过200个字符
                          if (value.url.length > 255)
                            return Promise.reject(new Error(t('videos.create.form.urlInfoMaxLength')!))
                          // 网址信息不符合格式
                          if (!validateUrl(value.url, 'rtsp'))
                            return Promise.reject(new Error(t('videos.create.form.urlInfoFormat')!))
                          // 检查视频流url是否重复
                          if ((form.getFieldValue('voice_stream_config') as Array<any>).filter(item => item.url === value.url).length > 1)
                            return Promise.reject(new Error(t('videos.create.form.urlInfoRepeat')!))
                          if (id)
                            await checkUrlExist(value.url)
                        },
                      }]}
                      className='w-full'
                    >
                      <URLItem></URLItem>
                    </Form.Item>
                    {/* 操作按钮 */}
                    <div className='flex align-center absolute right-[-72px] gap-3 w-[60px] h-[36px]'>
                      { index === (videoStream.length - 1) && videoStream.length < MAX_NUM && <TextButton size='middle' onClick={() => handleAddVoiceStream()}>
                        {t('common.operation.add')}
                      </TextButton> }
                      { videoStream.length !== 1 && <TextButton size='middle' onClick={() => handleDeleteVoiceStream(index)}>
                        {t('common.operation.delete')}
                      </TextButton> }
                    </div>
                  </div>
                )
              })
            }
          </Form.Item>
        )
      }
      {
        isVideoMode && <Form.Item label={t('videos.create.form.uploader')} required>
          <FileUploaderInDrag
            max={10}
            maxSize={500}
            supportTypes={['avi', 'mp4', 'mov', 'wmv', 'flv']}
            fileList={fileList}
            url={`/video-libraries/upload?type=${MediaType.Video}`}
            prepareFileList={updateFileList}
            onFileListUpdate={updateFileList}
            onFileUpdate={updateFile}
            onFileUploaded={wrapperFile}
          />
        </Form.Item>
      }
      {
        isImageMode
        && <Form.Item label={t('videos.create.form.uploader')} required>
          <FileUploaderInDrag
            max={30}
            maxSize={15}
            supportTypes={['jpg', 'png', 'tif', 'gif']}
            url={`/video-libraries/upload?type=${MediaType.Img}`}
            fileList={fileList}
            prepareFileList={updateFileList}
            onFileListUpdate={updateFileList}
            onFileUpdate={updateFile}
            onFileUploaded={wrapperFile}
          />
        </Form.Item>
      }
    </Form>
  )
})

StepOne.displayName = 'StepOne'

export default StepOne
