'use client'
import { useContext } from 'use-context-selector'
import { useTranslation } from 'react-i18next'
import { useMemo } from 'react'
import type { Collection } from '../../types'
import { CollectionType } from '../../types'
import I18n from '@/context/i18n'
import { getLanguage } from '@/i18n/language'
import { useAppContext } from '@/context/app-context'

import cn from '@/utils/classnames'
// 公共组件
import Card from '@/app/components/base/card'
import Badge from '@/app/components/base/badge'
import Indicator from '@/app/components/base/indicator'
import { CustomTagIcon, WorkflowTagIcon } from '@/app/components/base/icons/src/vender/line/tag'
import { ForkCopy } from '@/app/components/base/icons/src/vender/line/general'
import Avatar from '@/app/components/base/avatar'
import Tag from '@/app/components/base/tag'
import Button from '@/app/components/base/button'
import TextButton from '@/app/components/base/button/text-button'

type Props = {
  type: CollectionType
  collection: Collection
  onSelect: () => void
  onAuth?: () => void
  onEdit?: () => void
  onDelete?: () => void
}

const ProviderCard = ({
  type,
  collection,
  onSelect,
  onAuth,
  onEdit,
  onDelete,
}: Props) => {
  const { t } = useTranslation()
  const { locale } = useContext(I18n)
  const { canAdmin } = useAppContext()
  const language = getLanguage(locale)

  // 是否发布
  const isPublish = useMemo(() => {
    return collection.status !== 'init'
  }, [collection])
  // 是否授权
  const isAuthed = useMemo(() => {
    return collection.is_team_authorization
  }, [collection])
  // 是否位内置工具
  const isBuiltin = useMemo(() => {
    return type === CollectionType.builtIn
  }, [type])
  // 是否需要授权
  const needAuth = useMemo(() => {
    return collection.allow_delete
  }, [collection])

  // 卡片头部
  const CardHead = () => {
    return (
      <Avatar
        size={48}
        className={cn('!rounded !bg-white')}
        avatar={collection.icon_url}
      ></Avatar>
    )
  }
  // 卡片tag
  const CardTag = () => {
    return (
      <>
        {[CollectionType.workflow, CollectionType.custom].includes(type)
          && <Tag color='gray' size='small' className='!bg-gray-G10'>
            {[CollectionType.workflow].includes(type) && (
              <>
                <WorkflowTagIcon className='w-3 h-3 mr-1 text-primary-P1'></WorkflowTagIcon>
                <div className='truncate'>{t('tools.createDropdown.workflow').toUpperCase()}</div>
              </>
            )}
            {CollectionType.custom === type && (
              <>
                <CustomTagIcon className='w-3 h-3 mr-1 text-primary-P1'></CustomTagIcon>
                <div className='truncate'>{t('tools.createDropdown.custom').toUpperCase()}</div>
              </>
            )}
          </Tag>
        }
        {
          collection.is_fork === 'fork' && (
            <Badge className='rounded-[2px] bg-gray-G10 text-gray-G2'>
              <ForkCopy className='w-3 h-3 mr-1 text-primary-P1'/>
              {t('app.publish.copy.forkTag')}
            </Badge>
          )
        }
      </>
    )
  }

  return (
    <Card
      title={collection.label[language]}
      onClick={onSelect}
      description={collection.description[language] || ''}
      generateHead={CardHead}
      generateTag={CardTag}
      layout='line'
    >
      <div className='flex justify-between items-center w-auto'>
        {/* 状态栏 */}
        {isBuiltin
          ? (needAuth && <Indicator containerClassName='shrink-0' color={isAuthed ? 'green' : 'gray'}>
            { isAuthed ? t('common.status.authorized') : t('common.status.notAuthorized') }
          </Indicator>)
          : <Indicator containerClassName='shrink-0' color={isPublish ? 'green' : 'gray'}>
            { isPublish ? t('common.status.canBeAdded') : t('common.status.notCanBeAdded') }
          </Indicator>}
        {/* 操作栏 */}
        {
          canAdmin
            && <div className='flex items-center ml-[40px] gap-6' onClick={e => e.stopPropagation()}>
              { isBuiltin
                ? (needAuth && <Button variant='secondary' onClick={onAuth}>{ t('tools.action.auth') }</Button>)
                : <>
                  <TextButton variant='primary' onClick={onEdit}>{t('common.operation.edit')}</TextButton>
                  <TextButton variant='primary' onClick={onDelete}>{t('common.operation.delete')}</TextButton>
                </>}
            </div>
        }
      </div>

    </Card>
  )
}
export default ProviderCard
