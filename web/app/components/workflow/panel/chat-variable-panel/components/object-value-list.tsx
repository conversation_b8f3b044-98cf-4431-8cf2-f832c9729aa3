import type { FC } from 'react'
import React from 'react'
import { useTranslation } from 'react-i18next'
import s from '../styles/style.module.css'
import ObjectValueItem from './object-value-item'
import cn from '@/utils/classnames'

type Props = {
  list: any[]
  onChange: (list: any[]) => void
}

const ObjectValueList: FC<Props> = ({
  list,
  onChange,
}) => {
  const { t } = useTranslation()

  return (
    <div className={s['object-value-table']}>
      {/* 表头 */}
      <div className={s['object-value-header']}>
        <div className={cn('w-[120px]', s['object-value-border-cell'])}>{t('workflow.chatVariable.modal.objectKey')}</div>
        <div className={cn('w-[96px]', s['object-value-border-cell'])}>{t('workflow.chatVariable.modal.objectType')}</div>
        <div className={cn('w-[230px] pr-1', s['object-value-cell'])}>{t('workflow.chatVariable.modal.objectValue')}</div>
      </div>
      {/* 表单项 */}
      {list.map((item, index) => (
        <ObjectValueItem
          key={index}
          index={index}
          list={list}
          onChange={onChange}
        />
      ))}
    </div>
  )
}
export default React.memo(ObjectValueList)
