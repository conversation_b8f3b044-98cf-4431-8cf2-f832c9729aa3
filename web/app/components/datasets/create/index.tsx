'use client'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useRouter } from 'next/navigation'
import { Divider } from 'antd'
import StepOne from './step-one'
import StepTwo from './step-two'
import type { CreateDocumentReq, DataSet, createDocumentResponse } from '@/models/datasets'
import { DataSourceType, DatasetFileType } from '@/models/datasets'
import { fetchDatasetDetail } from '@/service/datasets'
import type { FileItem } from '@/types/public/file'
import style from '@/app/components/datasets/styles/style.module.css'
import StepsNavBar from '@/app/components/datasets//common/steps-nav-bar'

import { useDefaultModel } from '@/app/components/account-setting/model-provider-page/hooks'
import AppUnavailable from '@/app/components/base/app-unavailable'
import { ModelTypeEnum } from '@/app/components/account-setting/model-provider-page/declarations'
import Scrollbar from '@/app/components/base/scrollbar'

type DatasetUpdateFormProps = {
  datasetId?: string
}

export type datasetInfoType = {
  name: string
  description?: string
}

const DatasetUpdateForm = ({ datasetId }: DatasetUpdateFormProps) => {
  const { t } = useTranslation()
  const router = useRouter()
  const { data: embeddingsDefaultModel } = useDefaultModel(ModelTypeEnum.textEmbedding)
  // 数据源类型
  const [dataSourceType, setDataSourceType] = useState<DataSourceType>(DataSourceType.FILE)
  // 索引方式
  const [indexingTypeCache, setIndexTypeCache] = useState('')
  // 知识库信息
  const [datasetFormInfo, setDatasetFormInfo] = useState<datasetInfoType>({ name: '' })
  // 文件类型
  const [fileType, setFileType] = useState<DatasetFileType>(DatasetFileType.Unstructured)
  // 文件列表
  const [fileList, setFiles] = useState<FileItem[]>([])
  // 创建知识库结果
  const [result, setResult] = useState<createDocumentResponse | undefined>()
  // 文档分段蚕食u
  const [documentParams, setDocumentParams] = useState<CreateDocumentReq | undefined>()
  // 知识库详情是否存在错误
  const [hasError, setHasError] = useState(false)
  // 知识库详情
  const [detail, setDetail] = useState<DataSet | null>(null)
  // 知识库创建是否准备完成
  const [ready, setReady] = useState(false)

  const currentFiles = useMemo(() => {
    return fileList.map(file => file.file)
  }, [fileList])

  // 更新知识库表单信息
  const updateFormInfo = (info: datasetInfoType) => {
    setDatasetFormInfo(info)
  }
  // 更新文件列表
  const updateFileList = (preparedFiles: FileItem[]) => {
    setFiles(preparedFiles)
  }
  // 更新文件类型
  const updateFileType = (type: DatasetFileType) => {
    setFileType(type)
  }
  // 更新文件
  const updateFile = (fileItem: FileItem, progress: number, list: FileItem[]) => {
    const targetIndex = list.findIndex(file => file.fileID === fileItem.fileID)
    list[targetIndex] = {
      ...list[targetIndex],
      progress,
    }
    setFiles([...list])
  }
  // 更新索引方式
  const updateIndexingTypeCache = (type: string) => {
    setIndexTypeCache(type)
  }
  // 更新结果缓存
  const updateResultCache = (res?: createDocumentResponse) => {
    setResult(res)
  }
  // 更新文档文段参数
  const updateDocumentParams = (params: CreateDocumentReq) => {
    setDocumentParams(params)
  }
  // 返回上一步
  const navBackHandle = useCallback((detail?: DataSet) => {
    if (!datasetId && !detail?.id)
      router.replace('/datasets')
    else
      router.replace(`/datasets/${datasetId || detail?.id}/documents`)
  }, [router, datasetId])

  useEffect(() => {
    (async () => {
      if (datasetId) {
        try {
          const detail = await fetchDatasetDetail(datasetId)
          setDetail(detail)
        }
        catch (e) {
          setHasError(true)
        }
      }
    })()
  }, [datasetId])

  if (hasError)
    return <AppUnavailable code={500} unknownReason={t('datasetCreation.error.unavailable') as string} />
  console.log('===知识库03===', datasetFormInfo)
  console.log('===更新文件===',  fileList)
  return (
    <>
      <StepsNavBar
        onBack={navBackHandle}
        datasetId={datasetId}
        backBtnText={!datasetId ? t('datasetCreation.steps.header.creation') : t('datasetCreation.steps.header.update')}
      />
      { <Scrollbar className={style['middle-wrap']} style={{ height: 'calc(100% - 60px)' }}>
        <StepOne
          datasetId={datasetId}
          files={fileList}
          updateFile={updateFile}
          updateFileList={updateFileList}
          updateFormInfo={updateFormInfo}
          updateReady={setReady}
          updateFileType={updateFileType}
        />
        <Divider type='horizontal' className='!mb-6 !mt-0'></Divider>
        <div className={style.title}>{t('datasetCreation.stepTwo.configSelect')}</div>
        {(!datasetId || (datasetId && !!detail)) && <StepTwo
          isAPIKeySet={!!embeddingsDefaultModel}
          indexingType={detail?.indexing_technique}
          datasetId={datasetId}
          dataSourceType={dataSourceType}
          datasetFormInfo={datasetFormInfo}
          fileType={fileType}
          files={currentFiles}
          ready={ready}
          onSave={navBackHandle}
          updateIndexingTypeCache={updateIndexingTypeCache}
          updateResultCache={updateResultCache}
          updateDocumentParams={updateDocumentParams}
          fileList={fileList}
        />}
      </Scrollbar>}
    </>
  )
}

export default DatasetUpdateForm
