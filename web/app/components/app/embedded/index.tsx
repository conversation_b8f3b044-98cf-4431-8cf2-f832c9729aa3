import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import copy from 'copy-to-clipboard'
import style from './style.module.css'
import type { Option } from './type'
import { OPTION_MAP } from './type'
import EmbeddedOption from './embedded-option'
import cn from '@/utils/classnames'
import Modal from '@/app/components/base/modal'
import type { SiteInfo } from '@/types/share'
import { useThemeContext } from '@/app/components/base/chat/embedded-chatbot/theme/theme-context'
import CopyBtn from '@/app/components/base/button/copy-button'
import { useSystemContext } from '@/context/system-context'

type Props = {
  siteInfo?: SiteInfo
  isShow: boolean
  onClose: () => void
  accessToken: string
  appBaseUrl: string
  className?: string
}

const prefixEmbedded = 'appOverview.overview.appInfo.embedded'

type OptionStatus = {
  iframe: boolean
  scripts: boolean
}

const Embedded = ({ siteInfo, isShow, onClose, appBaseUrl, accessToken, className }: Props) => {
  const { t } = useTranslation()
  const [option, setOption] = useState<Option>('iframe')
  const [isCopied, setIsCopied] = useState<OptionStatus>({ iframe: false, scripts: false })

  const { langeniusVersionInfo } = useSystemContext()
  const themeBuilder = useThemeContext()
  themeBuilder.buildTheme(siteInfo?.chat_color_theme ?? null, siteInfo?.chat_color_theme_inverted ?? false)
  const isTestEnv = langeniusVersionInfo.current_env === 'TESTING' || langeniusVersionInfo.current_env === 'DEVELOPMENT'
  const onClickCopy = () => {
    copy(OPTION_MAP[option].getContent(appBaseUrl, accessToken, themeBuilder.theme?.primaryColor ?? '#1C64F2', isTestEnv))
    setIsCopied({ ...isCopied, [option]: true })
  }

  // when toggle option, reset then copy status
  const resetCopyStatus = () => {
    const cache = { ...isCopied }
    Object.keys(cache).forEach((key) => {
      cache[key as keyof OptionStatus] = false
    })
    setIsCopied(cache)
  }

  useEffect(() => {
    resetCopyStatus()
  }, [isShow])

  return (
    <Modal
      title={t(`${prefixEmbedded}.title`)}
      isShow={isShow}
      onClose={onClose}
      className="!w-[500px]"
      wrapperClassName={className}
      closable={true}
    >
      <div className="mb-2 title-14-24">
        {t(`${prefixEmbedded}.explanation`)}
      </div>
      <div className="flex flex-wrap items-center justify-between gap-3">
        <EmbeddedOption
          value={option}
          onSelect={(option: Option) => {
            setOption(option)
            resetCopyStatus()
          }}
        />
      </div>
      <div className={cn(style.codeCardWrap, 'mt-6')}>
        <div className={style.codeCardHeader}>
          <div className={style.codeTitle}>
            {t(`${prefixEmbedded}.${option}`)}
          </div>
          <CopyBtn onCopy={onClickCopy}></CopyBtn>
        </div>
        <div className={style.codeContent}>
          <pre className='select-text break-all whitespace-pre-wrap'>{OPTION_MAP[option].getContent(appBaseUrl, accessToken, themeBuilder.theme?.primaryColor ?? '#1C64F2', isTestEnv)}</pre>
        </div>
      </div>
    </Modal>
  )
}

export default Embedded
