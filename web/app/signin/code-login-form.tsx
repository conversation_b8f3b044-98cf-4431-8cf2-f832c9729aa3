'use client'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useRouter } from 'next/navigation'
import { Divider, Form, Input } from 'antd'
import { debounce } from 'lodash-es'
import style from './page.module.css'
import classNames from '@/utils/classnames'
import { codeLogin, getLoginPicCode, getloginSmsCode } from '@/service/common'
import useRefreshToken from '@/hooks/use-refresh-token'

// 公共组件
import Button from '@/app/components/base/button'
import Scrollbar from '@/app/components/base/scrollbar'
import SmsVerify from '@/app/components/base/verify/sms-verify'
import Verify from '@/app/components/base/verify'
import { validatePhone } from '@/utils/validate'
type Props = {
  onLogin: () => void
}

const CodeLoginForm = ({ onLogin }: Props) => {
  const { t } = useTranslation()
  const { getNewAccessToken } = useRefreshToken()
  const router = useRouter()
  const [form] = Form.useForm()
  const values = Form.useWatch([], form)
  // 登录按钮disabled
  const [disabled, setDisabled] = useState(true)
  // 短信验证码disabled
  const [smsDisabled, setSmsDisabled] = useState(true)
  // 图形验证码配置
  const [picConfig, setPicConfig] = useState<{ uuid: string; captcha_image: string }>()

  // 获取图形验证码
  const getPicCode = async () => {
    try {
      setPicConfig(undefined)
      const res = await getLoginPicCode()
      setPicConfig(res)
    }
    catch {
    }
  }
  // 获取短信验证码
  const getSmsCode = async () => {
    const { phone, pic_code } = form.getFieldsValue()
    await getloginSmsCode({
      phone,
      pic_code,
      uuid: picConfig!.uuid,
    })
  }
  // 邮箱登录
  const handleCodeLogin = async () => {
    try {
      const res = await codeLogin({
        ...form.getFieldsValue(),
        uuid: picConfig?.uuid,
      })
      if (res.result === 'success') {
        localStorage.setItem('console_token', res.data?.access_token)
        localStorage.setItem('sso_token', res.data?.access_token)
        localStorage.setItem('refresh_token', res.data?.refresh_token)
        getNewAccessToken()
        router.replace('/apps')
        if (onLogin)
          onLogin()
      }
    }
    catch {
      getPicCode()
    }
  }

  useEffect(debounce(() => {
    const { phone, pic_code, sms_code } = form.getFieldsValue()
    // 获取验证码禁用判断
    if (phone && pic_code && picConfig && validatePhone(phone))
      setSmsDisabled(false)
    else
      setSmsDisabled(true)

    // 登录按钮禁用判断
    form.validateFields({ validateOnly: true }).then(() => {
      setDisabled(!phone || !pic_code || !sms_code)
    },
    ).catch(() => {
      setDisabled(true)
    })
  }, 100), [form, values])
  useEffect(() => {
    getPicCode()
  }, [])

  return (
    <>
      <Scrollbar className={style.content}>
        <Form form={form} layout='vertical'>
          {/* 手机号 */}
          <Form.Item
            name={'phone'}
            label={t('login.info.mobile')}
            rules={[() => ({
              validator: (_, value: string) => {
                return (validatePhone(value) ? Promise.resolve() : Promise.reject(t('login.error.phoneInValid')))
              },
            })]}
          >
            <Input
              className={classNames(style.noAutofillBg, 'h-[42px]')}
              autoComplete='off'
              placeholder={t('login.placeholder.mobile') || ''}
            ></Input>
          </Form.Item>
          {/* 验证码 */}
          <Form.Item name={'pic_code'} label={t('login.info.picCode')}>
            <Input
              className={classNames(style.noAutofillBg, 'h-[42px]')}
              autoComplete="off"
              placeholder={t('login.placeholder.picCode') || ''}
              suffix={
                <Verify onRefresh={getPicCode} width={86} height={30} url={picConfig ? `data:image/png;base64,${picConfig?.captcha_image}` : ''}></Verify>
              }
            />
          </Form.Item>
          {/* 短信验证码 */}
          <Form.Item className='!mb-10' name={'sms_code'} label={t('login.info.smsCode')}>
            <Input
              className={classNames(style.noAutofillBg, 'h-[42px]')}
              autoComplete="off"
              suffix={
                <div className='w-[102px] flex justify-between items-center'>
                  <Divider type='vertical' className='h-[22px] !ml-[16px] !mt-[3px]'></Divider>
                  <SmsVerify sendFunc={getSmsCode} interval={60} disabled={smsDisabled}></SmsVerify>
                </div>
              }
              placeholder={t('login.placeholder.smsCode') || ''}
            />
          </Form.Item>
        </Form>
        <Button
          disabled={disabled}
          variant='primary'
          onClick={handleCodeLogin}
          className="w-full !h-[48px] !text-S4"
        >{t('login.action.signBtn')}</Button>
      </Scrollbar>
    </>
  )
}

export default CodeLoginForm
