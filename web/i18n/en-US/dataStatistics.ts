const translation = {
  title: 'dataStatistics',
  action: {
    getPriceList: 'Price List',
    detail: 'Details'
  },
  dataSummary: {
    title: 'Overview of Data Summary',
    appNum: 'Number of applications',
    datasetCapacity: 'Knowledge base capacity',
    resourcePackPoints: 'Resource pack points',
    toolNum: 'Number of tools',
    datasetNum: 'Number of knowledge bases'
  },
  resourcePoints: {
    timeSelect: {
      last9Months: '7-9 months',
      last1Month: 'Within 1 month',
      last5Months: '3-5 months',
      placeholder: 'Expiration time',
      last7Months: '5-7 months',
      last12Months: '9-12 months',
      last3Months: '1-3 months'
    },
    expirationTime: 'Expiration time',
    purchasePoints: 'Purchase points',
    title: 'Resource Point Data Details',
    availablePoints: 'Available points',
    daysRemaining: 'Remaining days',
    purchaseDate: 'Purchase time'
  },
  modelPointsConsum: {
    all: 'Total consumption points',
    input: 'Input consumption points',
    title: 'Details of built-in model point consumption',
    output: 'Output consumption points',
    model: 'Built-in model'
  },
  modelTokenConsum: {
    model: 'Built-in model',
    input: 'Input consumes tokens',
    all: 'Total token consumption',
    title: 'Details of built-in model token consumption',
    output: 'Output consumption token'
  },
  appApiCalls: {
    name: 'Name',
    title: 'Number of API calls for published applications',
    callNum: 'Support for the number of calls'
  },
  modelPricing: {
    title: 'Built-in model price list',
    outputConsume: 'Output consumption (points/thousand tokens)',
    name: 'Name',
    inputConsume: 'Input consumption (points/thousand tokens)'
  },
  personalSpace: 'Personal Space',
  dayCount: '{{day}} days',
  primaryAccountDataSummary: 'Details of data consumption under the main account privileges',
  app: 'Application',
  personalSpaceConsume: 'Personal space consumption data',
  myConsume: 'The consumption belongs to me.',
  teamSpaceConsume: 'Overall consumption data of the team',
  purchase: 'Purchase resource pack',
  personalSpaceSubTitle: 'Model and point consumption data'
}

export default translation
