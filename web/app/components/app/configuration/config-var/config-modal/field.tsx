'use client'
import type { <PERSON> } from 'react'
import React from 'react'
import cn from '@/utils/classnames'

type Props = {
  className?: string
  title: string
  children: JSX.Element
}

const Field: FC<Props> = ({ className, title, children }) => {
  return (
    <div className={cn(className)}>
      <div className="title-14-24 xqnormal">{title}</div>
      <div style={{ display: 'block' }}>{children}</div>
    </div>
  )
}
export default React.memo(Field)
