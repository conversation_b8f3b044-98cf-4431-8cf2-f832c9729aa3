import { EChartsOption } from 'echarts-for-react'

export enum StatisticalTypeEnum {
  event = '件',
  time = '时',
  increase = '%'
}

export type DataType = {
  name: string
  value: number
}
export type size = 'small' | 'medium' | 'large'

// 代码节点返回图表数据结构
export type InputChartType = {
  title: string
  layout: {
    size: size
  }
  chartType: string
  xAxis?: Array<string>
  data: DataType[]
  options: any
}

export type Chart = {
  layout: {
    size: size
  },
  option: EChartsOption,
  externalConfig: any,
}
