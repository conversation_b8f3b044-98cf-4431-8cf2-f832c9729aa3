.video-mode-item {
  @apply grow h-[36px] flex items-center justify-center rounded border border-gray-G4 text-gray-G1 bg-white hover:border-primary-P1 hover:text-primary-P1;
  position: relative;
  padding: 0px 8px;
  cursor: pointer;
  width: calc(50% - 6px);
  flex-shrink: 0;
  flex-grow: inherit;
}
.video-active-mode-item {
  @apply !border-primary-P1 !text-primary-P1;
}
.mode-title {
  @apply text-S3 leading-H3 ml-2 mr-1;
}
.mode-icon {
  @apply w-4 h-4 text-primary-P1;
}
