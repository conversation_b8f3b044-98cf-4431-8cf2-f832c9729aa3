'use-client'

import type { FC } from 'react'
import { useTranslation } from 'react-i18next'
import { useContext } from 'use-context-selector'
import { useDebounceFn } from 'ahooks'
import { Divider, Popover, Switch } from 'antd'

import s from './styles/index.module.css'
import cn from '@/utils/classnames'

import { archiveDocument, deleteDocument, disableDocument, enableDocument, syncDocument, syncWebsite, unArchiveDocument } from '@/service/datasets'
import type { SimpleDocumentDetail } from '@/models/datasets'
import { DataSourceType } from '@/models/datasets'
import type { CommonResponse } from '@/models/common'

// 公共组件
import { getDocStatus, judgeDocOperable } from '@/app/components/datasets/utils'
import StatusItem from '@/app/components/datasets/common/status-indicator'
import { asyncRunSafe } from '@/utils'
import { ToastContext } from '@/app/components/base/toast'
import Tooltip from '@/app/components/base/tooltip'
import { Archive, Sync } from '@/app/components/base/icons/src/vender/line/general'
import DropdownItem from '@/app/components/base/dropdown/dowdown-item'
import { More } from '@/app/components/base/icons/src/vender/solid/general'

type OperationName = 'delete' | 'archive' | 'enable' | 'disable' | 'sync' | 'un_archive'
const OperationAction: FC<{
  embeddingAvailable: boolean
  detail?: SimpleDocumentDetail
  datasetId: string
  onUpdate: (operationName?: string) => void
  scene?: 'list' | 'detail'
}> = ({ embeddingAvailable, datasetId, detail, onUpdate, scene = 'list' }) => {
  const { notify } = useContext(ToastContext)
  const { t } = useTranslation()
  // 当前场景
  const isListScene = scene === 'list'

  const { id, enabled = false, archived = false, data_source_type } = detail || {}
  // 点击操作按钮
  const onOperate = async (operationName: OperationName) => {
    let opApi = deleteDocument
    let message = t('common.actionMsg.deleteSuccessfully')
    switch (operationName) {
      case 'archive':
        opApi = archiveDocument
        message = t('datasetDocuments.list.notify.archiveSuccessfully')
        break
      case 'un_archive':
        opApi = unArchiveDocument
        message = t('datasetDocuments.list.notify.unarchiveSuccessfully')
        break
      case 'enable':
        opApi = enableDocument
        message = t('common.actionMsg.enableSuccessfully')
        break
      case 'disable':
        opApi = disableDocument
        message = t('common.actionMsg.disableSuccessfully')
        break
      case 'sync':
        if (data_source_type === 'notion_import')
          opApi = syncDocument

        else
          opApi = syncWebsite
        message = t('datasetDocuments.list.notify.syncSuccessfully')
        break
      default:
        opApi = deleteDocument
        break
    }
    const [e] = await asyncRunSafe<CommonResponse>(opApi({ datasetId, documentId: id! }) as Promise<CommonResponse>)
    if (!e) {
      notify({ type: 'success', message })
      onUpdate(operationName)
    }
  }
  // 启用、禁用开关
  const { run: handleSwitch } = useDebounceFn((operationName: OperationName) => {
    if (operationName === 'enable' && enabled)
      return
    if (operationName === 'disable' && !enabled)
      return
    onOperate(operationName)
  }, { wait: 500 })

  return (
    detail && <div className='flex items-center' onClick={e => e.stopPropagation()}>
      {embeddingAvailable && (isListScene
      && (judgeDocOperable(detail)
        ? <>
          {archived
            ? <Tooltip
              popupContent={t('datasetDocuments.list.action.enableWarning')}
              needsDelay
            >
              <Switch defaultValue={false} onChange={() => { }} disabled={true} size='small' />
            </Tooltip>
            : <Switch defaultValue={enabled} onChange={v => handleSwitch(v ? 'enable' : 'disable')} size='small' />
          }
        </>
        : <div className='w-[28px]'></div>))
      }
      { !isListScene
      && <div className={cn(s['dataset-status'], judgeDocOperable(detail) && s['dataset-status-operable'])}>
        {/* 当前文档状态 */}
        <StatusItem status={getDocStatus(detail)} errorMessage={detail?.error || ''}></StatusItem>
        {/* 操作提示 */}
        { embeddingAvailable && judgeDocOperable(detail) && <>
          <div className={s['dataset-status-tip']}>{enabled ? t('datasetDocuments.list.index.enableTip') : t('datasetDocuments.list.index.disableTip')}</div>
          <Tooltip
            popupContent={t('datasetDocuments.list.action.enableWarning')}
            needsDelay
          >
            <Switch
              onChange={v => handleSwitch(v ? 'enable' : 'disable')}
              size='small'
              value={enabled}
            />
          </Tooltip>
        </> }
      </div>
      }
      {/* 暂时隐藏归档部分操作 */}
      { false && <Popover
        trigger='click'
        placement='bottomRight'
        arrow={false}
        content={
          <div className='w-full'>
            {/* 如果没有归档 */}
            {!archived && (
              <>
                {/* 同步按钮 */}
                {['notion_import', DataSourceType.WEB].includes(data_source_type!) && (
                  <DropdownItem
                    onClick={() => onOperate('sync')}
                    icon={<Sync className='w-4 h-4 text-gray-500' />}
                    title={t('common.operation.sync')}
                  />
                )}
                <Divider className='my-1' />
              </>
            )}
            {/* 归档 */}
            {!archived && <DropdownItem
              icon={<Archive className='w-4 h-4 text-gray-500' />}
              title={t('datasetDocuments.list.action.archive')}
              onClick={() => onOperate('archive')} />
            }
            {/* 归档撤销 */}
            {archived && (
              <DropdownItem
                onClick={() => onOperate('un_archive')}
                icon={<Archive className='w-4 h-4 text-gray-500' />}
                title={t('datasetDocuments.list.action.unarchive')}
              />
            )}
          </div>
        }
      >
        <More className='w-8 h-8 text-gray-G2 cursor-pointer' />
      </Popover>}
    </div>)
}

export default OperationAction
