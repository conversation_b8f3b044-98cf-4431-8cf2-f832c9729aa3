import type { FC } from 'react'
import type {
  Model,
  ModelItem,
  ModelProvider,
} from '@/app/components/account-setting/model-provider-page/declarations'
import { useLanguage } from '@/app/components/account-setting/model-provider-page/hooks'
import { CubeOutline } from '@/app/components/base/icons/src/vender/line/shapes'
import { OpenaiViolet } from '@/app/components/base/icons/src/public/llm'
import { getPurifyHref } from '@/utils'

type ModelIconProps = {
  provider?: Model | ModelProvider
  model?: ModelItem
  modelName?: string
  className?: string
}
const ModelIcon: FC<ModelIconProps> = ({
  provider,
  className,
  modelName,
  model,
}) => {
  const language = useLanguage()
  if (model?.icon) {
    return (
      <img
        alt='model-icon'
        src={model?.icon as string}
        className={`w-4 h-4 ${className}`}
      ></img>
    )
  }

  if (provider?.provider === 'openai' && (modelName?.startsWith('gpt-4') || modelName?.includes('4o')))
    return <OpenaiViolet className={`w-4 h-4 ${className}`}/>

  if (provider?.icon_small) {
    const url = getPurifyHref(`${provider.icon_small[language] || provider.icon_small.en_US}`)
    const src = encodeURI(`${url}?_token=${encodeURIComponent(localStorage.getItem('console_token') as string)}`)
    return (
      <img
        alt='model-icon'
        src={src}
        className={`w-4 h-4 ${className}`}
      />
    )
  }

  return (
    <div className={`
      flex items-center justify-center w-6 h-6 rounded border-[0.5px] border-black/5 bg-gray-50
      ${className}
    `}>
      <CubeOutline className='w-4 h-4 text-gray-400' />
    </div>
  )
}

export default ModelIcon
