import type { FC } from 'react'
import { memo } from 'react'
import { useTranslation } from 'react-i18next'
import { BlockEnum } from '../types'
// 公共组件
import {
  AgentIcon,
  Answer,
  Assigner,
  Code,
  DocExtractor,
  End,
  HttpRequest,
  IfE<PERSON>e,
  Iteration,
  KnowledgeRetrieval,
  ListFilter,
  LlmIcon,
  ParameterExtractor,
  QuestionClassifier,
  Start,
  TemplateTransform,
  VariableX,
  Video,
} from '@/app/components/base/icons/src/public/workflow'
import Avatar from '@/app/components/base/avatar'
import cn from '@/utils/classnames'
import Tooltip from '@/app/components/base/tooltip'
import { AlertTriangle } from '@/app/components/base/icons/src/vender/line/alertsAndFeedback'

const ICON_CONTAINER_CLASSNAME_SIZE_MAP: Record<string, string> = {
  xs: 'w-4 h-4',
  sm: 'w-5 h-5',
  md: 'w-6 h-6',
}

type BlockIconProps = {
  type: BlockEnum
  size?: keyof typeof ICON_CONTAINER_CLASSNAME_SIZE_MAP
  icon?: string | { content: string; background: string }
  className?: string
  title?: string
}
// 获取节点对应图标
const getIcon = (type: BlockEnum, className: string) => {
  return {
    [BlockEnum.Start]: <Start className={className} />,
    [BlockEnum.LLM]: <LlmIcon className={className} />,
    [BlockEnum.Code]: <Code className={className} />,
    [BlockEnum.End]: <End className={className} />,
    [BlockEnum.IfElse]: <IfElse className={className} />,
    [BlockEnum.HttpRequest]: <HttpRequest className={className} />,
    [BlockEnum.Answer]: <Answer className={className} />,
    [BlockEnum.KnowledgeRetrieval]: <KnowledgeRetrieval className={className} />,
    [BlockEnum.QuestionClassifier]: <QuestionClassifier className={className} />,
    [BlockEnum.TemplateTransform]: <TemplateTransform className={className} />,
    [BlockEnum.VariableAssigner]: <VariableX className={className} />,
    [BlockEnum.VariableAggregator]: <VariableX className={className} />,
    [BlockEnum.Assigner]: <Assigner className={className} />,
    [BlockEnum.Tool]: <VariableX className={className} />,
    [BlockEnum.Iteration]: <Iteration className={className} />,
    [BlockEnum.ParameterExtractor]: <ParameterExtractor className={className} />,
    [BlockEnum.DocExtractor]: <DocExtractor className={className} />,
    [BlockEnum.ListFilter]: <ListFilter className={className} />,
    [BlockEnum.Agent]: <AgentIcon className={className}></AgentIcon>,
    [BlockEnum.IterationStart]: <></>,
    [BlockEnum.Video]: <Video className={className} />,
  }[type]
}
const BlockIcon: FC<BlockIconProps> = ({
  type,
  icon: currentIcon,
  title,
  size = 'sm',
  className,
}) => {
  const { t } = useTranslation()
  return (
    <div className={cn('flex items-center gap-2 !font-normal', className)}>
      <div className={cn(
        'rounded shrink-0',
        ICON_CONTAINER_CLASSNAME_SIZE_MAP[size],
      )}
      >
        {
          type !== BlockEnum.Tool
            ? (getIcon(type, 'w-full h-full'))
            : (currentIcon
              ? <Avatar
                avatar={typeof currentIcon === 'string' ? currentIcon : currentIcon?.content}
                className='shrink-0 !w-full !h-full !rounded'
              ></Avatar>
              : <Tooltip popupContent={t('common.status.deleted')}>
                <div className='flex items-center justify-center w-full h-full bg-[#F5F8FF]'>
                  <AlertTriangle className='w-4 h-4 text-[#F79009]' />
                </div>
              </Tooltip>)
        }

      </div>
      { title && <div className='text-gray-G1 grow text-S3 leading-H3 truncate' title={title}>{ title }</div>}
    </div>
  )
}

export const VarBlockIcon: FC<BlockIconProps> = ({
  type,
  className,
}) => {
  return (
    <>
      {getIcon(type, `w-3 h-3 ${className}`)}
    </>
  )
}

export default memo(BlockIcon)
