'use client'
import type { FC } from 'react'
import React from 'react'
import { Radio } from 'antd'
import s from './styles/index.module.css'
import cn from '@/utils/classnames'

type Props = {
  className?: string
  descClassName?: string
  radioPosition?: 'left' | 'right' | 'top-right'
  size?: 'large' | 'middle' | 'small'
  disabled?: boolean
  title: React.ReactNode | string
  description: string
  noRadio?: boolean
  isChosen?: boolean
  onChosen?: () => void
  chosenConfig?: React.ReactNode
  chosenConfigWrapClassName?: string
}

const RadioCard: FC<Props> = ({
  className,
  descClassName,
  radioPosition = 'right',
  size = 'middle',
  disabled = false,
  title,
  description,
  noRadio,
  isChosen,
  onChosen = () => { },
  chosenConfig,
  chosenConfigWrapClassName,
}) => {
  return (
    <div
      className={cn(
        className,
        s['radio-card'],
        !disabled && s['abled-radio-card'],
        isChosen && s['radio-card-active'],
      )}
    >
      <div className={cn(
        'flex w-full gap-6',
        radioPosition === 'left' && 'flex-row-reverse gap-3 ',
        radioPosition === 'top-right' && 'items-start')
      } onClick={onChosen}>
        <div className='grow w-0'>
          <div className={s[`radio-title-${size}`]}>{title}</div>
          <div className={cn(s[`radio-description-${size}`], descClassName)}>{description}</div>
        </div>
        {!noRadio && (
          <Radio className='!me-0 shrink-0' checked={isChosen}></Radio>
        )}
      </div>
      <div className={cn(chosenConfigWrapClassName, ((isChosen && chosenConfig) || noRadio) ? 'border-t border-gray-G6 mt-4' : '!hidden')}>
        {chosenConfig}
      </div>
      {
        disabled && <div className='absolute opaticy-0 w-full h-full top-0 left-0 cursor-not-allowed z-20' ></div>
      }
    </div>
  )
}
export default React.memo(RadioCard)
