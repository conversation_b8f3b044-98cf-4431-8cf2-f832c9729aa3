import {
  memo,
  useMemo,
  useRef,
  useState,
} from 'react'
import { useKeyPress } from 'ahooks'
import { Divider } from 'antd'
import { RiEqualizer2Line } from '@remixicon/react'
import { useTranslation } from 'react-i18next'
import { useNodes } from 'reactflow'
import {
  useEdgesInteractions,
  useNodesInteractions,
  useWorkflowInteractions,
} from '../../hooks'
import { BlockEnum } from '../../types'
import type { StartNodeType } from '../../nodes/start/types'
import ChatWrapper from './chat-wrapper'
import { getCropperFontCss } from '@/app/components/app/app-bg-cropper/utils'

import cn from '@/utils/classnames'
import { RefreshCcw01 } from '@/app/components/base/icons/src/vender/line/arrows'
import { BubbleX } from '@/app/components/base/icons/src/vender/line/others'
import Tooltip from '@/app/components/base/tooltip'
import { useStore } from '@/app/components/workflow/store'
import { useFeatures } from '@/app/components/base/features/hooks'
import { FeatureEnum } from '@/app/components/base/features/types'
import CropperWrapper from '@/app/components/base/cropper/wrapper'
import Scrollbar from '@/app/components/base/scrollbar'
import TextButton from '@/app/components/base/button/text-button'
import { Close } from '@/app/components/base/icons/src/vender/line/general'

export type ChatWrapperRefType = {
  handleRestart: () => void
}
const DebugAndPreview = () => {
  const { t } = useTranslation()
  const chatRef = useRef({ handleRestart: () => { } })
  const { handleCancelDebugAndPreviewPanel } = useWorkflowInteractions()
  const { handleNodeCancelRunningStatus } = useNodesInteractions()
  const { handleEdgeCancelRunningStatus } = useEdgesInteractions()
  const varList = useStore(s => s.conversationVariables)
  const [expanded, setExpanded] = useState(true)
  const nodes = useNodes<StartNodeType>()
  const startNode = nodes.find(node => node.data.type === BlockEnum.Start)
  const variables = startNode?.data.variables || []
  const bgConfig = useFeatures(s => s.features[FeatureEnum.chatBg])

  const [showConversationVariableModal, setShowConversationVariableModal] = useState(false)
  // 第二风格字体颜色
  const secondColorCss = useMemo(() => {
    return bgConfig?.enabled ? ((bgConfig.fontColor === 'black' || bgConfig.fontColor === undefined) ? 'text-gray-G2' : 'text-gray-G5') : 'text-gray-G2'
  }, [bgConfig])

  const handleRestartChat = () => {
    handleNodeCancelRunningStatus()
    handleEdgeCancelRunningStatus()
    chatRef.current.handleRestart()
  }
  // 关闭预览窗口
  const handleClosePreview = () => {
    handleRestartChat()
    handleCancelDebugAndPreviewPanel()
  }
  useKeyPress('shift.r', () => {
    handleRestartChat()
  }, {
    exactMatch: true,
  })
  // 工作流点击预览弹出的聊天对话
  return (
    <CropperWrapper
      config={bgConfig?.enabled ? bgConfig?.narrow : undefined}
      className={cn(
        'flex flex-col w-[420px] bg-chatbot-bg h-full border-l border-gray-G6',
      )}
      header={
        <>
          <div className='flex items-center justify-between px-6 py-4'>
            <div className={getCropperFontCss(bgConfig)}>{t('workflow.common.debugAndPreview').toLocaleUpperCase()}</div>
            <div className='flex items-center gap-[10px]'>
              <Tooltip popupContent={t('common.operation.refresh')}>
                <TextButton onClick={() => handleRestartChat()}>
                  <RefreshCcw01 className={cn('w-4 h-4', secondColorCss)} />
                </TextButton>
              </Tooltip>
              {varList.length > 0 && (
                <Tooltip popupContent={t('workflow.chatVariable.panelTitle')}>
                  <TextButton onClick={() => setShowConversationVariableModal(true)}>
                    <BubbleX className={cn('w-4 h-4', secondColorCss)} />
                  </TextButton>
                </Tooltip>
              )}
              {variables.length > 0 && (
                <Tooltip popupContent={t('workflow.panel.userInputField')}>
                  <TextButton onClick={() => setExpanded(!expanded)}>
                    <RiEqualizer2Line className={cn('w-4 h-4', expanded ? 'text-primary-P1' : secondColorCss)} />
                  </TextButton>
                </Tooltip>
              )}
              <Divider className='mx-0' type='vertical'></Divider>
              <TextButton onClick={handleClosePreview}>
                <Close className={cn('w-4 h-4', secondColorCss)} />
              </TextButton>
            </div>
          </div>
          <div className='px-6'>
            <Divider className='my-0' type='horizontal'></Divider>
          </div>
        </>
      }
    >
      <Scrollbar>
        <ChatWrapper
          ref={chatRef}
          showConversationVariableModal={showConversationVariableModal}
          onConversationModalHide={() => setShowConversationVariableModal(false)}
          showInputsFieldsPanel={expanded}
          onHide={() => setExpanded(false)}
        />
      </Scrollbar>
    </CropperWrapper>
  )
}

export default memo(DebugAndPreview)
