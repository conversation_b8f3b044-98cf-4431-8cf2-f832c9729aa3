import React, { useEffect, useState } from 'react'
import { Card, Form, Input, List, Tooltip } from 'antd'
import s from './style.module.css'
type TableFormData = {
  [key: string]: string
}

type TableData = {
  headers: string[]
  values: string[]
  detail?: any
}

const { TextArea } = Input

type TableContentProps = {
  editable?: boolean
  detail?: any
  segInfo?: any
  onDataChange?: (data: TableData) => void
}

const TableContent: React.FC<TableContentProps> = ({
  editable = false,
  detail,
  segInfo,
  onDataChange,
}) => {
  const [form] = Form.useForm<TableFormData>()
  const [formFields, setFormFields] = useState<Array<{ name: string; label: string }>>([])

  const transformFormData = (formData: TableFormData): TableData => {
    const headers: string[] = []
    const values: string[] = []

    formFields.forEach((field) => {
      values.push(field.label)
      headers.push(formData[field.name] || '')
    })

    return { headers, values, detail }
  }

  // 处理表单提交
  const handleFinish = (values: TableFormData) => {
    const tableData = transformFormData(values)
    onDataChange?.(tableData)
  }

  // 处理表单值变化
  const handleValuesChange = (_: any, allValues: TableFormData) => {
    if (!editable)
      return
    const tableData = transformFormData(allValues)
    onDataChange?.(tableData)
  }

  // 从 detail 数据生成表单字段
  useEffect(() => {
    console.log('segInfo', segInfo)
    // segInfo.headers = ['所属模块', '相关研发需求', '用例标题', '前置条件', '步骤', '预期', '关键词', '优先级', '用例美型', '适用阶段', '用例状态']
    // segInfo.values = ['[logo自定义功能]', '1、用户已登录系统并具备超级管理员权限', ' 1、验证品牌定制页面是否符合预期', '1、顶部：\n展示【Log示意图】字段及其相应内容\n接顶部：\n左侧：【左侧Lg设置】字段及文案（参见ui）\n上传接钮：【Log替代文本】字段及输入框，【保存】，恢复默认】\n右侧：【右则Lg设置】字段及文案（参', ' P1', '功能测试', '功能测试阶段', '正常']
    const headers = segInfo?.headers || segInfo?.columns
    if (!headers)
      return
    // 生成表单字段配置
    const fields = headers.map((title: string, index: number) => ({
      name: `column${index}`,
      label: title,
    }))
    setFormFields(fields)

    // 设置表单初始值，只在首次加载时设置
    if (!form.getFieldsValue(true) || Object.keys(form.getFieldsValue(true)).length === 0) {
      const initialValues = fields.reduce((acc: TableFormData, field: { name: string; label: string }) => ({
        ...acc,
        [field.name]: segInfo.values?.[fields.indexOf(field)] || '',
      }), {})
      form.setFieldsValue(initialValues)
      onDataChange?.(transformFormData(initialValues))
    }
  }, [segInfo, form, onDataChange])

  const renderReadOnlyContent = () => {
    return (
      <List
        className={s.listStyle}
        header={
          <div className={s.listHeader}>
            <span>字段名</span>
            <span>字段值</span>
          </div>
        }
        dataSource={formFields}
        renderItem={(field) => {
          const fieldValue = form.getFieldValue(field.name)
          const hasLongContent = (fieldValue?.match(/\n/g) || []).length > 2

          return (
            <List.Item className={s.listItem}>
              <div className={s.fieldLabel}>{field.label}</div>
              {hasLongContent
                ? (
                  <Tooltip title={fieldValue}>
                    <div className={s.fieldValue}>{fieldValue}</div>
                  </Tooltip>
                )
                : (
                  <div className={s.fieldValue}>{fieldValue}</div>
                )}
            </List.Item>
          )
        }}
      />
    )
  }

  return (
    <Card bordered={false}>
      {editable
        ? (
          <Form
            form={form}
            layout="horizontal"
            onFinish={handleFinish}
            disabled={!editable}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}
            style={{ minWidth: 480, maxWidth: 480 }}
            onValuesChange={handleValuesChange}
          >
            {formFields.map(field => (
              <Form.Item
                key={field.name}
                name={field.name}
                label={field.label}
              >
                <TextArea
                  placeholder={`请输入${field.label}`}
                  autoComplete="off"
                  autoSize={{ minRows: 1 }}
                />
              </Form.Item>
            ))}
          </Form>
        )
        : renderReadOnlyContent()}
    </Card>
  )
}

export default TableContent
