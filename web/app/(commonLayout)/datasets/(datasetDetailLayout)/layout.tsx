'use client'

import { useRouter } from 'next/navigation'
import type { <PERSON> } from 'react'
import React, { useEffect } from 'react'
import { useAppContext } from '@/context/app-context'

export type IDatasetDetail = {
  children: React.ReactNode
}

const AppDetail: FC<IDatasetDetail> = ({ children }) => {
  const router = useRouter()
  const { canDataset } = useAppContext()

  useEffect(() => {
    if (!canDataset)
      router.replace('/square')
  }, [canDataset, router])

  return (
    <>
      {children}
    </>
  )
}

export default React.memo(AppDetail)
