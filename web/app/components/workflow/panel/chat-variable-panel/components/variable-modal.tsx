import React, { use<PERSON>allback, useEffect, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { useContext } from 'use-context-selector'
import { v4 as uuid4 } from 'uuid'
import { RiDraftLine, RiInputField } from '@remixicon/react'
import { Form, Input, InputNumber } from 'antd'
import ObjectValueList from '@/app/components/workflow/panel/chat-variable-panel/components/object-value-list'
import { DEFAULT_OBJECT_VALUE } from '@/app/components/workflow/panel/chat-variable-panel/components/object-value-item'
import ArrayValueList from '@/app/components/workflow/panel/chat-variable-panel/components/array-value-list'
import CodeEditor from '@/app/components/workflow/nodes/_base/components/editor/code-editor'
import { useStore } from '@/app/components/workflow/store'
import type { ConversationVariable } from '@/app/components/workflow/types'
import { CodeLanguage } from '@/app/components/workflow/nodes/code/types'
import { ChatVarType } from '@/app/components/workflow/types'

import { useFormDisabled } from '@/hooks/use-form'
// 公共组件
import { ToastContext } from '@/app/components/base/toast'
import Button from '@/app/components/base/button'
import { checkKeys } from '@/utils/var'
import Select from '@/app/components/base/select/new-index'
import Modal from '@/app/components/base/modal'

const Textarea = Input.TextArea

export type ModalPropsType = {
  chatVar?: ConversationVariable
  onClose: () => void
  onSave: (chatVar: ConversationVariable) => void
}

type ObjectValueItem = {
  key: string
  type: ChatVarType
  value: string | number | undefined
}

const typeList = [
  ChatVarType.String,
  ChatVarType.Number,
  ChatVarType.Object,
  ChatVarType.ArrayString,
  ChatVarType.ArrayNumber,
  ChatVarType.ArrayObject,
]

const objectPlaceholder = `#  example
#  {
#     "name": "ray",
#     "age": 20
#  }`
const arrayStringPlaceholder = `#  example
#  [
#     "value1",
#     "value2"
#  ]`
const arrayNumberPlaceholder = `#  example
#  [
#     100,
#     200
#  ]`
const arrayObjectPlaceholder = `#  example
#  [
#     {
#       "name": "ray",
#       "age": 20
#     },
#     {
#       "name": "lily",
#       "age": 18
#     }
#  ]`

const ChatVariableModal = ({
  chatVar,
  onClose,
  onSave,
}: ModalPropsType) => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  const varList = useStore(s => s.conversationVariables)
  const [form] = Form.useForm()
  const formDisabled = useFormDisabled(form)

  // 变量类型
  const [type, setType] = React.useState<ChatVarType>(ChatVarType.String)
  // 默认值
  const [value, setValue] = React.useState<any>()
  const [objectValue, setObjectValue] = React.useState<ObjectValueItem[]>([DEFAULT_OBJECT_VALUE])
  const [editorContent, setEditorContent] = React.useState<string>()
  const [editInJSON, setEditInJSON] = React.useState(false)

  // 获取编辑器最低奥杜
  const editorMinHeight = useMemo(() => {
    if (type === ChatVarType.ArrayObject)
      return '240px'
    return '120px'
  }, [type])
  // 提示语
  const placeholder = useMemo(() => {
    if (type === ChatVarType.ArrayString)
      return arrayStringPlaceholder
    if (type === ChatVarType.ArrayNumber)
      return arrayNumberPlaceholder
    if (type === ChatVarType.ArrayObject)
      return arrayObjectPlaceholder
    return objectPlaceholder
  }, [type])

  // 获取对象值
  const getObjectValue = useCallback(() => {
    if (!chatVar)
      return [DEFAULT_OBJECT_VALUE]
    return Object.keys(chatVar.value).map((key) => {
      return {
        key,
        type: typeof chatVar.value[key] === 'string' ? ChatVarType.String : ChatVarType.Number,
        value: chatVar.value[key],
      }
    })
  }, [chatVar])
  // 格式化对象值
  const formatValueFromObject = useCallback((list: ObjectValueItem[]) => {
    return list.reduce((acc: any, curr) => {
      if (curr.key)
        acc[curr.key] = curr.value || null
      return acc
    }, {})
  }, [])
  // 格式化值
  const formatValue = (value: any) => {
    switch (type) {
      case ChatVarType.String:
        return value || ''
      case ChatVarType.Number:
        return value || 0
      case ChatVarType.Object:
        return formatValueFromObject(objectValue)
      case ChatVarType.ArrayString:
      case ChatVarType.ArrayNumber:
      case ChatVarType.ArrayObject:
        return value?.filter(Boolean) || []
    }
  }
  // 变量类型变更
  const handleTypeChange = (v: ChatVarType) => {
    setValue(undefined)
    setEditorContent(undefined)
    if (v === ChatVarType.ArrayObject)
      setEditInJSON(true)
    if (v === ChatVarType.String || v === ChatVarType.Number || v === ChatVarType.Object)
      setEditInJSON(false)
    setType(v)
  }
  // 变更编辑模式
  const handleEditorChange = (editInJSON: boolean) => {
    if (type === ChatVarType.Object) {
      if (editInJSON) {
        const newValue = !objectValue[0].key ? undefined : formatValueFromObject(objectValue)
        setValue(newValue)
        setEditorContent(JSON.stringify(newValue))
      }
      else {
        if (!editorContent) {
          setValue(undefined)
          setObjectValue([DEFAULT_OBJECT_VALUE])
        }
        else {
          try {
            const newValue = JSON.parse(editorContent)
            setValue(newValue)
            const newObjectValue = Object.keys(newValue).map((key) => {
              return {
                key,
                type: typeof newValue[key] === 'string' ? ChatVarType.String : ChatVarType.Number,
                value: newValue[key],
              }
            })
            setObjectValue(newObjectValue)
          }
          catch (e) {
            // ignore JSON.parse errors
          }
        }
      }
    }
    if (type === ChatVarType.ArrayString || type === ChatVarType.ArrayNumber) {
      if (editInJSON) {
        const newValue = (value?.length && value.filter(Boolean).length) ? value.filter(Boolean) : undefined
        setValue(newValue)
        if (!editorContent)
          setEditorContent(JSON.stringify(newValue))
      }
      else {
        setValue(value?.length ? value : [undefined])
      }
    }
    setEditInJSON(editInJSON)
  }
  // 编辑器值变更
  const handleEditorValueChange = (content: string) => {
    if (!content) {
      setEditorContent(content)
      return setValue(undefined)
    }
    else {
      setEditorContent(content)
      try {
        const newValue = JSON.parse(content)
        setValue(newValue)
      }
      catch (e) {
        // ignore JSON.parse errors
      }
    }
  }
  // 保存对话变量
  const handleSave = () => {
    const { name, des } = form.getFieldsValue()

    if (type === ChatVarType.Object && objectValue.some(item => !item.key && !!item.value))
      return notify({ type: 'error', message: t('workflow.chatVariable.modal.objectKeyEmpty') })
    onSave({
      id: chatVar ? chatVar.id : uuid4(),
      name,
      value_type: type,
      value: formatValue(value),
      description: des,
    })
    onClose()
  }

  useEffect(() => {
    if (chatVar) {
      setType(chatVar.value_type)
      setValue(chatVar.value)
      form.setFieldsValue({
        name: chatVar.name,
        des: chatVar.description || '',
      })
      setObjectValue(getObjectValue())
      if (chatVar.value_type === ChatVarType.ArrayObject) {
        setEditorContent(JSON.stringify(chatVar.value))
        setEditInJSON(true)
      }
      else {
        setEditInJSON(false)
      }
    }
  }, [chatVar, form, getObjectValue])

  return (
    <Modal
      isShow
      closable
      onClose={onClose}
      title={(!chatVar ? t('workflow.chatVariable.modal.title') : t('workflow.chatVariable.modal.editTitle')) || '' }
      footer={
        <>
          <Button className='mr-4' variant={'secondary-accent'} onClick={onClose}>{t('common.operation.cancel')}</Button>
          <Button disabled={formDisabled} variant='primary' onClick={handleSave}>{t('common.operation.save')}</Button>
        </>
      }
    >
      <Form form={form} layout='vertical' initialValues={{
        name: chatVar?.name || '',
        des: chatVar?.description || '',
      }}>
        {/* 变量名 */}
        <Form.Item
          name={'name'}
          label={t('workflow.chatVariable.modal.name')}
          validateFirst={true}
          validateTrigger='onBlur'
          rules={[{
            required: true,
            whitespace: true,
          }, {
            validator: (_, value) => {
              const { isValid, errorMessageKey } = checkKeys([value], false)
              if (!isValid)
                return Promise.reject(new Error(t(`appDebug.varKeyError.${errorMessageKey}`, { key: t('workflow.env.modal.name') })!))
              if (!chatVar && varList.some(chatVar => chatVar.name === value))
                return Promise.reject(new Error(t('workflow.chatVariable.modal.nameExist')!))
              return Promise.resolve()
            },
          }]}
        >
          <Input placeholder={t('workflow.chatVariable.modal.namePlaceholder') || ''}/>
        </Form.Item>
        {/* 变量类型 */}
        <Form.Item required label={t('workflow.chatVariable.modal.type')}>
          <Select
            value={type}
            style={{ width: '100%' }}
            onChange={handleTypeChange}
            options={typeList.map((item) => {
              return {
                label: item,
                value: item,
              }
            })}
          >
          </Select>
        </Form.Item>
        {/* default value */}
        <Form.Item label={t('workflow.chatVariable.modal.value')}>
          <div className='flex flex-col gap-2'>
            {/* 如果是字符串数组或者数字数组的情况下 */}
            {(type === ChatVarType.ArrayString || type === ChatVarType.ArrayNumber) && (
              /* 编辑模式 */
              <Button
                size='small'
                variant={'secondary-accent'}
                className='mb-2'
                onClick={() => handleEditorChange(!editInJSON)}
              >
                {editInJSON ? <RiInputField className='mr-1 w-3.5 h-3.5' /> : <RiDraftLine className='mr-1 w-3.5 h-3.5' />}
                {editInJSON ? t('workflow.chatVariable.modal.oneByOne') : t('workflow.chatVariable.modal.editInJSON')}
              </Button>
            )}
            {type === ChatVarType.Object && (
              <Button
                size='small'
                variant={'secondary-accent'}
                className='mb-2'
                onClick={() => handleEditorChange(!editInJSON)}
              >
                {editInJSON ? <RiInputField className='mr-1 w-3.5 h-3.5' /> : <RiDraftLine className='mr-1 w-3.5 h-3.5' />}
                {editInJSON ? t('workflow.chatVariable.modal.editInForm') : t('workflow.chatVariable.modal.editInJSON')}
              </Button>
            )}
            {type === ChatVarType.String && (
              <Input
                placeholder={t('workflow.chatVariable.modal.valuePlaceholder') || ''}
                value={value}
                onChange={e => setValue(e.target.value)}
              />
            )}
            {type === ChatVarType.Number && (
              <InputNumber
                placeholder={t('workflow.chatVariable.modal.valuePlaceholder') || ''}
                value={value}
                style={{ width: '100%' }}
                onChange={setValue}
              />
            )}
            {type === ChatVarType.Object && !editInJSON && (
              <ObjectValueList
                list={objectValue}
                onChange={setObjectValue}
              />
            )}
            {type === ChatVarType.ArrayString && !editInJSON && (
              <ArrayValueList
                isString
                list={value || [undefined]}
                onChange={setValue}
              />
            )}
            {type === ChatVarType.ArrayNumber && !editInJSON && (
              <ArrayValueList
                isString={false}
                list={value || [undefined]}
                onChange={setValue}
              />
            )}
            {editInJSON && (
              <div className='w-full py-2 pl-3 pr-1 rounded-[10px] bg-components-input-bg-normal' style={{ height: editorMinHeight }}>
                <CodeEditor
                  isExpand
                  noWrapper
                  language={CodeLanguage.json}
                  value={editorContent}
                  placeholder={<div className='whitespace-pre'>{placeholder}</div>}
                  onChange={handleEditorValueChange}
                />
              </div>
            )}
          </div>
        </Form.Item>
        {/* 描述 */}
        <Form.Item name={'des'} label={t('workflow.chatVariable.modal.description')}>
          <Textarea
            placeholder={t('workflow.chatVariable.modal.descriptionPlaceholder') || ''}
          />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default ChatVariableModal
