{"icon": {"type": "element", "isRootNode": true, "name": "svg", "attributes": {"width": "37", "height": "37", "viewBox": "0 0 37 37", "fill": "none", "xmlns": "http://www.w3.org/2000/svg"}, "children": [{"type": "element", "name": "g", "attributes": {"id": "<PERSON>ame"}, "children": [{"type": "element", "name": "foreignObject", "attributes": {"x": "0.886199", "y": "-0.980012", "width": "36.5733", "height": "36.5733"}, "children": [{"type": "element", "name": "div", "attributes": {"xmlns": "http://www.w3.org/1999/xhtml", "style": "backdrop-filter:blur(2.12px);clip-path:url(#bgblur_0_12415_22355_clip_path);height:100%;width:100%"}, "children": []}]}, {"type": "element", "name": "g", "attributes": {"id": "Vector", "filter": "url(#filter0_i_12415_22355)", "data-figma-bg-blur-radius": "4.24271"}, "children": [{"type": "element", "name": "path", "attributes": {"d": "M24.9079 18.2265C24.5174 18.617 23.8842 18.617 23.4937 18.2265L14.5058 9.23859C14.4846 9.21742 14.4846 9.1831 14.5058 9.16193C14.5226 9.14509 14.5265 9.11923 14.5153 9.09819L12.9021 6.05778C12.8353 5.932 12.7425 5.82192 12.6298 5.73491L9.69701 3.47104C9.2989 3.16372 8.73448 3.19989 8.37885 3.55552L5.42271 6.51167C5.06708 6.86729 5.03091 7.43171 5.33822 7.82983L7.60209 10.7626C7.6891 10.8753 7.79918 10.9681 7.92497 11.0349L10.9654 12.6481C10.9864 12.6593 11.0123 12.6554 11.0291 12.6386C11.0503 12.6174 11.0846 12.6174 11.1058 12.6386L20.0937 21.6265C20.4842 22.017 20.4842 22.6502 20.0937 23.0407L20.0412 23.0932C19.6507 23.4837 19.6507 24.1168 20.0412 24.5074L25.9341 30.4003C27.2008 31.6669 29.3341 31.6669 30.6008 30.4003L32.2674 28.7336C33.5341 27.4669 33.5341 25.3336 32.2674 24.0669L26.3746 18.174C25.984 17.7835 25.3509 17.7835 24.9603 18.174L24.9079 18.2265Z", "fill": "#CCDAFB", "fill-opacity": "0.3"}, "children": []}]}, {"type": "element", "name": "g", "attributes": {"id": "Vector_2", "filter": "url(#filter1_di_12415_22355)"}, "children": [{"type": "element", "name": "path", "attributes": {"d": "M31.3337 14.2665C33.13 12.4702 33.6974 9.87043 33.0359 7.54167C32.8506 6.88921 32.0428 6.75747 31.5631 7.23708L28.9075 9.89272C28.517 10.2832 27.8838 10.2832 27.4933 9.89272L26.0463 8.44577C25.6537 8.05311 25.6561 7.41571 26.0518 7.0261L28.681 4.43741C29.1666 3.95925 29.0372 3.14596 28.3815 2.96016C26.0701 2.30522 23.5156 2.874 21.6671 4.6665C20.0144 6.31912 19.4296 8.5899 19.8092 10.7049C19.8721 11.0555 19.7788 11.4215 19.5269 11.6733L5.26706 25.9332C5.06106 26.1387 4.89762 26.3829 4.7861 26.6518C4.67459 26.9206 4.61719 27.2088 4.61719 27.4998C4.61719 27.7909 4.67459 28.0791 4.7861 28.3479C4.89762 28.6167 5.06106 28.8609 5.26706 29.0665L7.0004 30.6665C7.86706 31.5332 9.26706 31.5332 10.1337 30.6665L24.3806 16.4196C24.6394 16.1608 25.0178 16.0703 25.3768 16.1414C27.4778 16.5569 29.6853 15.9149 31.3337 14.2665Z", "fill": "#3068F5"}, "children": []}]}, {"type": "element", "name": "path", "attributes": {"id": "Vector_3", "d": "M7 27C7 28.1429 7.93333 29 9 29C10.0667 29 11 28.1429 11 27C11 25.8571 10.0667 25 9 25C7.93333 25 7 25.8571 7 27Z", "fill": "white"}, "children": []}]}, {"type": "element", "name": "defs", "attributes": {}, "children": [{"type": "element", "name": "filter", "attributes": {"id": "filter0_i_12415_22355", "x": "0.886199", "y": "-0.980012", "width": "36.5733", "height": "36.5733", "filterUnits": "userSpaceOnUse", "color-interpolation-filters": "sRGB"}, "children": [{"type": "element", "name": "feFlood", "attributes": {"flood-opacity": "0", "result": "BackgroundImageFix"}, "children": []}, {"type": "element", "name": "feBlend", "attributes": {"mode": "normal", "in": "SourceGraphic", "in2": "BackgroundImageFix", "result": "shape"}, "children": []}, {"type": "element", "name": "feColorMatrix", "attributes": {"in": "SourceAlpha", "type": "matrix", "values": "0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0", "result": "hardAl<PERSON>"}, "children": []}, {"type": "element", "name": "feOffset", "attributes": {"dx": "0.909152", "dy": "3.63661"}, "children": []}, {"type": "element", "name": "feG<PERSON><PERSON><PERSON>lur", "attributes": {"stdDeviation": "4.54576"}, "children": []}, {"type": "element", "name": "feComposite", "attributes": {"in2": "hardAl<PERSON>", "operator": "arithmetic", "k2": "-1", "k3": "1"}, "children": []}, {"type": "element", "name": "feColorMatrix", "attributes": {"type": "matrix", "values": "0 0 0 0 0.0524819 0 0 0 0 0.0428362 0 0 0 0 0.869173 0 0 0 0.2 0"}, "children": []}, {"type": "element", "name": "feBlend", "attributes": {"mode": "normal", "in2": "shape", "result": "effect1_innerShadow_12415_22355"}, "children": []}]}, {"type": "element", "name": "clipPath", "attributes": {"id": "bgblur_0_12415_22355_clip_path", "transform": "translate(-0.886199 0.980012)"}, "children": [{"type": "element", "name": "path", "attributes": {"d": "M24.9079 18.2265C24.5174 18.617 23.8842 18.617 23.4937 18.2265L14.5058 9.23859C14.4846 9.21742 14.4846 9.1831 14.5058 9.16193C14.5226 9.14509 14.5265 9.11923 14.5153 9.09819L12.9021 6.05778C12.8353 5.932 12.7425 5.82192 12.6298 5.73491L9.69701 3.47104C9.2989 3.16372 8.73448 3.19989 8.37885 3.55552L5.42271 6.51167C5.06708 6.86729 5.03091 7.43171 5.33822 7.82983L7.60209 10.7626C7.6891 10.8753 7.79918 10.9681 7.92497 11.0349L10.9654 12.6481C10.9864 12.6593 11.0123 12.6554 11.0291 12.6386C11.0503 12.6174 11.0846 12.6174 11.1058 12.6386L20.0937 21.6265C20.4842 22.017 20.4842 22.6502 20.0937 23.0407L20.0412 23.0932C19.6507 23.4837 19.6507 24.1168 20.0412 24.5074L25.9341 30.4003C27.2008 31.6669 29.3341 31.6669 30.6008 30.4003L32.2674 28.7336C33.5341 27.4669 33.5341 25.3336 32.2674 24.0669L26.3746 18.174C25.984 17.7835 25.3509 17.7835 24.9603 18.174L24.9079 18.2265Z"}, "children": []}]}, {"type": "element", "name": "filter", "attributes": {"id": "filter1_di_12415_22355", "x": "0.980581", "y": "0.878963", "width": "35.9588", "height": "35.8924", "filterUnits": "userSpaceOnUse", "color-interpolation-filters": "sRGB"}, "children": [{"type": "element", "name": "feFlood", "attributes": {"flood-opacity": "0", "result": "BackgroundImageFix"}, "children": []}, {"type": "element", "name": "feColorMatrix", "attributes": {"in": "SourceAlpha", "type": "matrix", "values": "0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0", "result": "hardAl<PERSON>"}, "children": []}, {"type": "element", "name": "feOffset", "attributes": {"dy": "1.8183"}, "children": []}, {"type": "element", "name": "feG<PERSON><PERSON><PERSON>lur", "attributes": {"stdDeviation": "1.8183"}, "children": []}, {"type": "element", "name": "feComposite", "attributes": {"in2": "hardAl<PERSON>", "operator": "out"}, "children": []}, {"type": "element", "name": "feColorMatrix", "attributes": {"type": "matrix", "values": "0 0 0 0 0.017679 0 0 0 0 0.198 0 0 0 0 0.653548 0 0 0 0.2 0"}, "children": []}, {"type": "element", "name": "feBlend", "attributes": {"mode": "normal", "in2": "BackgroundImageFix", "result": "effect1_dropShadow_12415_22355"}, "children": []}, {"type": "element", "name": "feBlend", "attributes": {"mode": "normal", "in": "SourceGraphic", "in2": "effect1_dropShadow_12415_22355", "result": "shape"}, "children": []}, {"type": "element", "name": "feColorMatrix", "attributes": {"in": "SourceAlpha", "type": "matrix", "values": "0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0", "result": "hardAl<PERSON>"}, "children": []}, {"type": "element", "name": "feOffset", "attributes": {"dx": "1.8183", "dy": "1.8183"}, "children": []}, {"type": "element", "name": "feG<PERSON><PERSON><PERSON>lur", "attributes": {"stdDeviation": "1.8183"}, "children": []}, {"type": "element", "name": "feComposite", "attributes": {"in2": "hardAl<PERSON>", "operator": "arithmetic", "k2": "-1", "k3": "1"}, "children": []}, {"type": "element", "name": "feColorMatrix", "attributes": {"type": "matrix", "values": "0 0 0 0 0.0272837 0 0 0 0 0.208621 0 0 0 0 0.665202 0 0 0 0.2 0"}, "children": []}, {"type": "element", "name": "feBlend", "attributes": {"mode": "normal", "in2": "shape", "result": "effect2_innerShadow_12415_22355"}, "children": []}]}]}]}, "name": "ToolNum"}