import type { ImageItem, VideoItem } from '@/types/videos'
import { ProcessStatus } from '@/types/videos'

// 是否正在处理中
export const judgeIsProcessing = (doc: VideoItem | ImageItem) => {
  return doc.process_status === ProcessStatus.Processing
}

// 变更文档索引状态
export const changeProcessStatus = (doc: VideoItem | ImageItem, status: {
  process_status: ProcessStatus
}) => {
  doc.process_status = status.process_status
}
