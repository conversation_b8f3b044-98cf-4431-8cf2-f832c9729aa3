  .segModalContent {
    @apply h-auto bg-gray-G7 rounded p-4  break-all overflow-y-scroll grow border !border-gray-G7;
    white-space: pre-line;
  }
  .segModalContentActive:hover {
    @apply !border-primary-P1;
  }
  .segModal {
    @apply leading-H3 text-S3 text-gray-G2 !bg-transparent
  }

  .segInfoWrapper {
    @apply w-[160px] shrink-0 h-auto flex flex-col;
  }
  .segInfoItem {
    @apply rounded bg-gray-G7 px-3 h-8 gap-2 flex items-center text-S3 leading-H1 text-gray-G2;
  }
  .segInfoItemTitle {
    @apply text-S3 leading-H3 text-gray-G1 shrink-0;
  }
  .hashValue {
    @apply text-S3 leading-H1 text-gray-G2 break-all shrink-0 line-clamp-4;
    white-space: pre-line;
    max-height: 80px;
    overflow: hidden;
  }
  .keywordWrapper {
    @apply w-full h-auto shrink overflow-auto flex flex-wrap mb-6;
  }
  .commonIcon {
    @apply w-4 h-4;
  }
 .KnowledgeCardConSty{
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; 
  overflow: hidden;
  text-overflow: ellipsis; 
 }
 .sourceKnowSty{
   color: #bec3cf;
 }
 .knowledCardSty .ant-card-body {
  padding: 12px; 
}
.radio-card-active {
  @apply  border-primary-P1;
  background: linear-gradient(180deg, #F1F7FF 0%, #FFF 100%);
}
.abled-radio-card:hover {
  @apply border-primary-P1;
  background: linear-gradient(180deg, #F1F7FF 0%, #FFF 100%);
}

.textAreaStyle {
  @apply resize-none !bg-transparent !border-none !p-0 !text-gray-G1;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  line-height: 1.5;
  cursor: default;
}

.textAreaStyle:hover {
  @apply !border-none;
}

.listStyle {
  @apply min-w-[480px] max-w-[800px] border border-gray-G7 rounded-md overflow-hidden;
}

.listHeader {
  @apply flex items-center px-4 py-2 bg-gray-G7 w-full pl-6;
}

.listHeader span {
  @apply text-gray-G2 text-S3;
}

.listHeader span:first-child {
  @apply w-[160px] shrink-0;
}

.listItem {
  @apply flex items-start px-4 py-2 border-b border-gray-G7 !mb-0 last:border-b-0;
}

.fieldLabel {
  @apply w-[160px] shrink-0 text-gray-G1 pl-6;
}

.fieldValue {
  @apply flex-1 text-gray-G1 break-all;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  white-space: pre-wrap;
}