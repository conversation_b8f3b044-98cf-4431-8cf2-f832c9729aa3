'use client'
import type { FC } from 'react'
import React, { useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import useSWR from 'swr'
import { cloneDeep, omit } from 'lodash-es'
import { useContext } from 'use-context-selector'
import { Divider, Input, Pagination, Table, type TableColumnsType } from 'antd'
import SegmentCard from '../documents/detail/segment-card'
import HitSegmentCard from './hit-segment-card'
import { fetchTestingRecords, hitTesting } from '@/service/datasets'
import DatasetDetailContext from '@/context/dataset-detail'
import type {
  HitTestingRecord,
  HitTestingResponse,
  HitTesting as HitTestingType,
} from '@/models/datasets'
import { RETRIEVE_METHOD, type RetrievalConfig } from '@/types/datasets'

import { useModelListAndDefaultModelAndCurrentProviderAndModel } from '@/app/components/account-setting/model-provider-page/hooks'
import { ModelTypeEnum } from '@/app/components/account-setting/model-provider-page/declarations'
import RetrievalMethodConfig from '@/app/components/datasets/common/retrieval-method-config'
import {
  ensureRerankModelSelected,
  isReRankModelSelected,
} from '@/app/components/datasets/common/check-rerank-model'
import SegmentDetail from '@/app/components/datasets/common/segment-detail'
import style from '@/app/components/datasets/styles/style.module.css'
import cn from '@/utils/classnames'
import useTimestamp from '@/hooks/use-timestamp'
import Empty from '@/app/components/base/empty'
import Scrollbar from '@/app/components/base/scrollbar'
import Button from '@/app/components/base/button'
import Toast from '@/app/components/base/toast'
import { asyncRunSafe } from '@/utils'

const limit = 6

type Props = {
  datasetId: string
}

const HitTesting: FC<Props> = ({ datasetId }: Props) => {
  const { t } = useTranslation()
  const { formatTime } = useTimestamp()
  const { dataset: currentDataset } = useContext(DatasetDetailContext)
  const {
    modelList: rerankModelList,
    defaultModel: rerankDefaultModel,
    currentModel: isRerankDefaultModelValid,
  } = useModelListAndDefaultModelAndCurrentProviderAndModel(
    ModelTypeEnum.rerank,
  )

  // 提交加载
  const [submitLoading, setSubmitLoading] = useState(false)
  // 召回测试节瓜哦
  const [hitResult, setHitResult] = useState<HitTestingResponse | undefined>() // 初始化记录为空数组
  // 当前分段信息
  const [currParagraph, setCurrParagraph] = useState<{
    paraInfo?: HitTestingType
    showModal: boolean
  }>({ showModal: false })
  // 召回测试文本
  const [text, setText] = useState('')
  // 当前页
  const [currPage, setCurrPage] = React.useState<number>(1)
  // 检索配置disabled
  const [retrievalConfigDisabled, setRetrievalConfigDisabled] = useState(true)
  // 检索配置
  const [retrievalConfig, setRetrievalConfig] = useState(
    currentDataset?.retrieval_model_dict as RetrievalConfig,
  )
  // 临时检索配置
  const [tempRetrievalConfig, setTempRetrievalConfig] = useState(
    cloneDeep(currentDataset?.retrieval_model_dict) as RetrievalConfig,
  )
  const retrievalMethodConfigRef = useRef<{
    resetConfig: (value: RetrievalConfig) => void
  }>()

  const {
    data: recordsRes,
    error,
    mutate: recordsMutate,
  } = useSWR(
    {
      action: 'fetchTestingRecords',
      datasetId,
      params: { limit, page: currPage },
    },
    apiParams => fetchTestingRecords(omit(apiParams, 'action')),
  )

  const total = recordsRes?.total || 0

  // 点击召回测试卡片
  const onClickCard = (detail: HitTestingType) => {
    setCurrParagraph({ paraInfo: detail, showModal: true })
  }
  // 保存检索配置
  const handleSave = () => {
    if (
      !isReRankModelSelected({
        rerankDefaultModel,
        isRerankDefaultModelValid: !!isRerankDefaultModelValid,
        rerankModelList,
        retrievalConfig,
        indexMethod: currentDataset?.indexing_technique || '',
      })
    ) {
      Toast.notify({
        type: 'error',
        message: t('dataset.placeholder.rerankModel'),
      })
      return
    }
    setRetrievalConfig(
      ensureRerankModelSelected({
        rerankDefaultModel: rerankDefaultModel!,
        retrievalConfig: cloneDeep(tempRetrievalConfig),
        indexMethod: currentDataset?.indexing_technique || '',
      }),
    )
    setRetrievalConfigDisabled(true)
    Toast.notify({
      type: 'success',
      message: t('common.actionMsg.saveSuccessfully'),
    })
  }
  // 取消保存检索配置
  const handleCancel = () => {
    setTempRetrievalConfig(cloneDeep(retrievalConfig))
    retrievalMethodConfigRef.current?.resetConfig(retrievalConfig)
    setRetrievalConfigDisabled(true)
  }
  // 提交检索文本
  const submitText = async () => {
    setSubmitLoading(true)
    const [e, res] = await asyncRunSafe<HitTestingResponse>(
      hitTesting({
        datasetId,
        queryText: text,
        retrieval_model: {
          ...retrievalConfig,
          search_method:
            currentDataset?.indexing_technique === 'economy'
              ? RETRIEVE_METHOD.keywordSearch
              : retrievalConfig.search_method,
        },
      }) as Promise<HitTestingResponse>,
    )
    if (!e) {
      setHitResult(res)
      recordsMutate()
    }
    setSubmitLoading(false)
  }

  // 表格列
  const columns: TableColumnsType<HitTestingRecord> = [
    {
      title: t('dataset.hit.source'),
      key: 'source',
      render: (_: any, record) => (
        <span className="capitalize truncate">
          {t(`dataset.hit.sourceValue.${record.source}`)}
        </span>
      ),
      width: 120,
      ellipsis: true,
    },
    {
      title: t('dataset.hit.text'),
      key: 'content',
      dataIndex: 'content',
      ellipsis: true,
    },
    {
      title: t('dataset.hit.time'),
      key: 'created_at',
      render: (_: any, record) =>
        formatTime(
          record.created_at,
          t('common.dateFormat.dateTime') as string,
        ),
      width: 190,
      ellipsis: true,
    },
  ]

  return (
    <div className={style.wrap}>
      {/* 配置 */}
      <div className={cn(style['left-part'], 'w-0 grow !flex-row !pt-0')}>
        <Scrollbar className="w-0 grow min-w-[350px]">
          <div className="mx-6 py-6 shrink">
            {/* 检索配置 */}
            <div className={cn(style['left-title'], '!px-0')}>
              {t('dataset.action.setting')}
            </div>
            <RetrievalMethodConfig
              ref={retrievalMethodConfigRef}
              disabled={retrievalConfigDisabled}
              value={tempRetrievalConfig}
              onChange={setTempRetrievalConfig}
            />
            <div className="flex mt-3 mb-8 items-center gap-4">
              {retrievalConfigDisabled
                ? (
                  <Button
                    className="w-[92px]"
                    variant="primary"
                    onClick={() => setRetrievalConfigDisabled(false)}
                  >
                    {t('common.operation.edit')}
                  </Button>
                )
                : (
                  <>
                    <Button
                      className="w-[92px]"
                      variant="secondary-accent"
                      onClick={handleCancel}
                    >
                      {t('common.operation.cancel')}
                    </Button>
                    <Button
                      className="w-[92px]"
                      variant="primary"
                      onClick={handleSave}
                    >
                      {t('common.operation.save')}
                    </Button>
                  </>
                )}
            </div>

            {/* 查询结果 */}
            {(
              <div className={cn(style['left-title'], '!px-0')}>
                {t('dataset.hit.recents')}
              </div>
            )}
            {(
              <>
                <Table
                  size="middle"
                  columns={columns}
                  pagination={{
                    pageSize: limit,
                    position: ['none', 'none'],
                  }}
                  scroll={{ x: '300px' }}
                  dataSource={recordsRes?.data}
                  className="border-gray-G5 rounded border shrink-0"
                  rowClassName="cursor-pointer"
                  onRow={(record) => {
                    return {
                      onClick: () => {
                        setText(record.content)
                      },
                    }
                  }}
                ></Table>
                <Pagination
                  className="mt-3"
                  align="center"
                  current={currPage}
                  hideOnSinglePage
                  onChange={setCurrPage}
                  total={total}
                  pageSize={limit}
                  showQuickJumper={false}
                  showSizeChanger={false}
                ></Pagination>
              </>
            )}
          </div>
        </Scrollbar>
        <Divider type="vertical" className="mx-0 h-full"></Divider>
        {/* 测试文案 */}
        <div className="flex flex-col w-0 grow min-w-[100px] max-w-[300px] h-full shrink-0 mx-6 py-6">
          <div className={cn(style['left-title'], '!px-0')}>
            {t('dataset.hit.input')}
          </div>
          <Input.TextArea
            value={text}
            maxLength={250}
            onChange={e => setText(e.target.value)}
            className="!h-full shrink mb-4"
            placeholder={t('dataset.hit.textPlaceholder')!}
          ></Input.TextArea>
          <Button
            disabled={!text}
            className="w-[92px] shrink-0"
            variant={'primary'}
            onClick={submitText}
          >
            {t('common.operation.confirm')}
          </Button>
        </div>
      </div>

      <div className={cn(style['right-part'], '')}>
        <div className={style['right-title']}>{t('dataset.hit.result')}</div>
        {submitLoading
          ? (
            <div className={cn(style['right-content'], 'gap-3')}>
              <SegmentCard
                loading={true}
                scene="hitTesting"
                className="h-[216px]"
              />
              <SegmentCard
                loading={true}
                scene="hitTesting"
                className="h-[216px]"
              />
            </div>
          )
          : !hitResult?.records.length
            ? (
              <Empty
                text={t('dataset.hit.emptyTip')}
                icon="/assets/hit-testing-empty.svg"
              ></Empty>
            )
            : (
              <Scrollbar className={cn(style['right-content'], 'gap-3')}>
                {hitResult?.records.map((record, idx) => {
                  return (
                    <HitSegmentCard
                      key={idx}
                      detail={record.segment as any}
                      score={record.score}
                      onClick={() => onClickCard(record as any)}
                    />
                  )
                })}
              </Scrollbar>
            )}
      </div>
      {currParagraph.showModal && (
        <SegmentDetail
          onCancel={() => setCurrParagraph({ showModal: false })}
          canEdit={false}
          segInfo={currParagraph.paraInfo?.segment}
        />
      )}
    </div>
  )
}

export default HitTesting
