/* eslint-disable multiline-ternary */
'use client'
import type { FC } from 'react'
import React, { useEffect, useState } from 'react'
import { PencilIcon } from '@heroicons/react/24/outline'
import { useTranslation } from 'react-i18next'
import { useContext } from 'use-context-selector'
import { get } from 'lodash-es'
import { Divider } from 'antd'
import { DocumentContext } from '../index'
import s from './style.module.css'
import cn from '@/utils/classnames'
import { asyncRunSafe } from '@/utils'
import { modifyDocMetadata } from '@/service/datasets'
import type { CommonResponse } from '@/models/common'
import type { DocType, FullDocumentDetail } from '@/models/datasets'
import { CUSTOMIZABLE_DOC_TYPES } from '@/models/datasets'
import type { metadataType } from '@/hooks/use-metadata'
import {
  useBookCategories,
  useBusinessDocCategories,
  useLanguages,
  useMetadataMap,
  usePersonalDocCategories,
} from '@/hooks/use-metadata'

import style from '@/app/components/datasets/styles/style.module.css'
import FieldInfo from '@/app/components/datasets/common/field-info'

import { ToastContext } from '@/app/components/base/toast'
import Loading from '@/app/components/base/loading'
import TextButton from '@/app/components/base/button/text-button'
import Button from '@/app/components/base/button'
import Tooltip from '@/app/components/base/tooltip'
import Radio from '@/app/components/base/radio'
import { useProviderContext } from '@/context/provider-context'

const map2Options = (map: { [key: string]: string }) => {
  return Object.keys(map).map(key => ({ value: key, label: map[key] }))
}

const TypeIcon: FC<{ iconName: string; className?: string }> = ({
  iconName,
  className = '',
}) => {
  return <div className={cn(s.commonIcon, s[`${iconName}Icon`], className)} />
}
const IconButton: FC<{
  type: DocType
  isChecked: boolean
}> = ({ type, isChecked = false }) => {
  const metadataMap = useMetadataMap()

  return (
    <Tooltip popupContent={metadataMap[type].text}>
      <button
        className={cn(s.iconWrapper, 'group', isChecked ? s.iconCheck : '')}
      >
        <TypeIcon
          iconName={metadataMap[type].iconName || ''}
          className={`group-hover:bg-primary-600 ${
            isChecked ? '!bg-primary-600' : ''
          }`}
        />
      </button>
    </Tooltip>
  )
}

type IMetadataProps = {
  docDetail?: FullDocumentDetail
  loading: boolean
  // 为了新需求，暂时将文件信息也放在这边暂时
  onlyInfo?: boolean
  onUpdate: () => void
}

const Metadata: FC<IMetadataProps> = ({
  docDetail,
  loading,
  onUpdate,
  onlyInfo = false,
}) => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  const { datasetId = '', documentId = '' } = useContext(DocumentContext)
  const metadataMap = useMetadataMap()
  const { useXIYANRag } = useProviderContext()
  const languageMap = useLanguages()
  const bookCategoryMap = useBookCategories()
  const personalDocCategoryMap = usePersonalDocCategories()
  const businessDocCategoryMap = useBusinessDocCategories()

  // 是否在编辑状态
  const [editStatus, setEditStatus] = useState(false)
  // 主数据参数
  const [metadataParams, setMetadataParams] = useState<{
    documentType: DocType | ''
    metadata: { [key: string]: string }
  }>({
    documentType: docDetail?.doc_type || '',
    metadata: docDetail?.doc_metadata || {},
  })
  // 显示文档类型变更器
  const [showDocTypes, setShowDocTypes] = useState(false)
  // 临时文档类型
  const [tempDocType, setTempDocType] = useState<DocType | undefined | ''>('') // for remember icon click

  useEffect(() => {
    setShowDocTypes(!docDetail?.doc_type)
    setTempDocType(docDetail?.doc_type || '')
    setMetadataParams({
      documentType: docDetail?.doc_type || '',
      metadata: docDetail?.doc_metadata || {},
    })
  }, [docDetail])

  // 取消编辑主数据
  const onCancel = () => {
    setMetadataParams({
      documentType: metadataParams!.documentType,
      metadata: { ...(docDetail?.doc_metadata || {}) },
    })
    setEditStatus(false)
  }
  // 保存主数据
  const onSave = async () => {
    const [e] = await asyncRunSafe<CommonResponse>(
      modifyDocMetadata({
        datasetId,
        documentId,
        body: {
          doc_type: metadataParams!.documentType,
          doc_metadata: metadataParams!.metadata,
        },
      }) as Promise<CommonResponse>,
    )
    if (!e) {
      notify({
        type: 'success',
        message: t('common.actionMsg.modifiedSuccessfully'),
      })
    }
    onUpdate?.()
    setEditStatus(false)
  }
  // 确认变更文档类型
  const confirmDocType = () => {
    if (!tempDocType)
      return
    setMetadataParams({
      documentType: tempDocType,
      metadata:
        tempDocType === metadataParams!.documentType
          ? metadataParams!.metadata
          : {}, // change doc type, clear metadata
    })
    setEditStatus(true)
    setShowDocTypes(false)
  }
  // 取消变更文档类型
  const cancelDocType = () => {
    setTempDocType(metadataParams!.documentType)
    setEditStatus(true)
    setShowDocTypes(false)
  }

  // 渲染文档类型选择
  const renderSelectDocType = () => {
    const { documentType } = metadataParams

    return (
      <>
        {!documentType && (
          <>
            <div className={s.desc}>{t('datasetDocuments.metadata.desc')}</div>
          </>
        )}
        <div className={s.operationWrapper}>
          {!documentType && (
            <>
              <span className={s.title}>
                {t('datasetDocuments.metadata.docTypeSelectTitle')}
              </span>
            </>
          )}
          {/* 变更时的提示文本 */}
          {documentType && (
            <>
              <span className={s.title}>
                {t('datasetDocuments.metadata.docTypeChangeTitle')}
              </span>
              <span className={s.changeTip}>
                {t('datasetDocuments.metadata.docTypeSelectWarning')}
              </span>
            </>
          )}
          {/* docType */}
          <Radio.Group
            value={tempDocType ?? documentType}
            onChange={setTempDocType}
            className={s.radioGroup}
          >
            {CUSTOMIZABLE_DOC_TYPES.map((type, index) => {
              const currValue = tempDocType ?? documentType
              return (
                <Radio
                  key={index}
                  value={type}
                  className={`${s.radio} ${
                    currValue === type ? 'shadow-none' : ''
                  }`}
                >
                  <IconButton type={type} isChecked={currValue === type} />
                </Radio>
              )
            })}
          </Radio.Group>
          {/* 第一次操作 */}
          {!documentType && (
            <Button
              variant="primary"
              onClick={confirmDocType}
              disabled={!tempDocType}
            >
              {t('datasetDocuments.metadata.firstMetaAction')}
            </Button>
          )}
          {documentType && (
            <div className={s.opBtnWrapper}>
              <Button onClick={confirmDocType} variant="primary">
                {t('common.operation.save')}
              </Button>
              <Button onClick={cancelDocType} variant={'secondary-accent'}>
                {t('common.operation.cancel')}
              </Button>
            </div>
          )}
        </div>
      </>
    )
  }
  // 展示metainfo信息，根据不同的字段类型
  const renderFieldInfos = ({
    mainField = 'book',
    canEdit,
    layout = 'col',
  }: {
    mainField?: metadataType | ''
    canEdit?: boolean
    layout?: 'col' | 'grid'
  }) => {
    if (!mainField)
      return null
    const fieldMap = metadataMap[mainField]?.subFieldsMap
    if (useXIYANRag && mainField === 'technicalParameters') {
      delete fieldMap?.tokens
      delete fieldMap?.average_segment_length
      delete fieldMap?.indexing_latency
    }
    // 源数据
    const sourceData = ['originInfo', 'technicalParameters'].includes(mainField)
      ? docDetail
      : metadataParams.metadata

    const getTargetMap = (field: string) => {
      if (field === 'language')
        return languageMap
      if (field === 'category' && mainField === 'book')
        return bookCategoryMap

      if (field === 'document_type') {
        if (mainField === 'personal_document')
          return personalDocCategoryMap
        if (mainField === 'business_document')
          return businessDocCategoryMap
      }
      return {} as any
    }

    const getTargetValue = (field: string) => {
      const val = get(sourceData, field, '')
      if (!val && val !== 0)
        return '-'
      if (fieldMap[field]?.inputType === 'select')
        return getTargetMap(field)[val]
      if (fieldMap[field]?.render) {
        return fieldMap[field]?.render?.(
          val,
          field === 'hit_count'
            ? (get(sourceData, 'segment_count', 0) as number)
            : undefined,
        )
      }
      return val
    }
    if (layout === 'col') {
      return (
        <div className="flex flex-col gap-1">
          {Object.keys(fieldMap).map((field) => {
            return (
              <FieldInfo
                key={fieldMap[field]?.label}
                label={fieldMap[field]?.label}
                displayedValue={getTargetValue(field)}
                value={get(sourceData, field, '')}
                inputType={fieldMap[field]?.inputType || 'input'}
                showEdit={canEdit}
                onUpdate={(val) => {
                  setMetadataParams(pre => ({
                    ...pre,
                    metadata: { ...pre.metadata, [field]: val },
                  }))
                }}
                selectOptions={map2Options(getTargetMap(field))}
              />
            )
          })}
        </div>
      )
    }
    if (layout === 'grid') {
      return (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 lg:mr-[25%]">
          {Object.keys(fieldMap).map((field) => {
            return (
              <FieldInfo
                key={fieldMap[field]?.label}
                label={fieldMap[field]?.label}
                displayedValue={getTargetValue(field)}
                value={get(sourceData, field, '')}
                inputType={fieldMap[field]?.inputType || 'input'}
                showEdit={canEdit}
                onUpdate={(val) => {
                  setMetadataParams(pre => ({
                    ...pre,
                    metadata: { ...pre.metadata, [field]: val },
                  }))
                }}
                selectOptions={map2Options(getTargetMap(field))}
              />
            )
          })}
        </div>
      )
    }
  }

  return !onlyInfo ? (
    loading ? (
      <Loading type="app" />
    ) : (
      <>
        <div className={style['right-title']}>
          <div className="flex flex-col">
            {t('datasetDocuments.metadata.title')}
            {/* 如果在编辑状态，则展示文档类型并且展示更改按钮 */}
            {editStatus ? (
              <div className={s.documentTypeShow}>
                {metadataParams.documentType ? (
                  <>
                    <TypeIcon
                      iconName={
                        metadataMap[metadataParams.documentType].iconName || ''
                      }
                      className={s.iconShow}
                    />
                    {metadataMap[metadataParams.documentType].text}
                    {editStatus && (
                      <>
                        <span>.</span>
                        <TextButton
                          variant={'hover'}
                          onClick={() => {
                            setShowDocTypes(true)
                          }}
                        >
                          {t('common.operation.change')}
                        </TextButton>
                      </>
                    )}
                  </>
                ) : (
                  <></>
                )}
              </div>
            ) : showDocTypes ? null : (
              <div className={s.documentTypeShow}>
                {metadataParams.documentType && (
                  <>
                    <TypeIcon
                      iconName={
                        metadataMap[metadataParams.documentType]?.iconName || ''
                      }
                      className={s.iconShow}
                    />
                    {metadataMap[metadataParams.documentType].text}
                  </>
                )}
              </div>
            )}
          </div>
          {/* 如果在编辑状态，展示保存按钮，不在编辑状态，根据是否展示文档类型选择器决定 */}
          {editStatus ? (
            <div className={s.opBtnWrapper}>
              <Button onClick={onCancel} variant={'secondary-accent'}>
                {t('common.operation.cancel')}
              </Button>
              <Button onClick={onSave} variant="primary">
                {t('common.operation.save')}
              </Button>
            </div>
          ) : showDocTypes ? null : (
            <Button variant={'primary'} onClick={() => setEditStatus(true)}>
              <PencilIcon className={s.opIcon} />
              {t('common.operation.edit')}
            </Button>
          )}
        </div>
        <div className={style['right-content']}>
          {showDocTypes
            ? renderSelectDocType()
            : renderFieldInfos({
              mainField: metadataParams.documentType,
              canEdit: editStatus,
            })}
          {/* show fixed fields */}
          <Divider className="!my-4" />
          {renderFieldInfos({ mainField: 'originInfo', canEdit: false })}
          {/* <div className={`${s.title} mt-8`}>{metadataMap.technicalParameters.text}</div> */}
          <Divider className="!my-4" />
          {renderFieldInfos({
            mainField: 'technicalParameters',
            canEdit: false,
          })}
        </div>
      </>
    )
  ) : (
    renderFieldInfos({
      mainField: 'technicalParameters',
      canEdit: false,
      layout: 'grid',
    })
  )
}

export default Metadata
