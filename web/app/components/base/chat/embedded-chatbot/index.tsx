import { useEffect } from 'react'
import {
  EmbeddedChatbotContext,
  useEmbeddedChatbotContext,
} from './context'
import { useEmbeddedChatbot } from './hooks'
import { useThemeContext } from './theme/theme-context'
import style from './styles/index.module.css'
import ChatWrapper from './chat-wrapper'
// 公共能力
import cn from '@/utils/classnames'
import AppUnavailable from '@/app/components/base/app-unavailable'
import useBreakpoints, { MediaType } from '@/hooks/use-breakpoints'
import Logo from '@/app/components/base/logo'
import { APP_NAME } from '@/config'
import Loading from '@/app/components/base/loading'

const Chatbot = () => {
  const {
    appInfoLoading,
    appInfoError,
    appData,
    themeBuilder,
  } = useEmbeddedChatbotContext()

  const customConfig = appData?.custom_config
  const site = appData?.site

  useEffect(() => {
    themeBuilder?.buildTheme(site?.chat_color_theme, site?.chat_color_theme_inverted)
    if (site) {
      if (customConfig)
        document.title = `${site.title}`
      else
        document.title = `${site.title} - Powered by ${APP_NAME}`
    }
  }, [site, customConfig, themeBuilder])

  if (appInfoLoading)
    return <Loading type="app" />
  if (appInfoError)
    return <AppUnavailable />

  return (
    <div className={style.bg}>
      <div className={style.header}>
        <Logo />
      </div>
      <div className={cn(style.content)}>
        <ChatWrapper />
      </div>
    </div>
  )
}

const EmbeddedChatbotWrapper = () => {
  const media = useBreakpoints()
  const isMobile = media === MediaType.mobile
  const themeBuilder = useThemeContext()

  const {
    appInfoError,
    appInfoLoading,
    appData,
    appParams,
    appMeta,
    appChatListDataLoading,
    currentConversationId,
    currentConversationItem,
    appPrevChatList,
    pinnedConversationList,
    conversationList,
    showConfigPanelBeforeChat,
    newConversationInputs,
    newConversationInputsRef,
    handleNewConversationInputsChange,
    inputsForms,
    handleNewConversation,
    handleStartChat,
    handleChangeConversation,
    handleNewConversationCompleted,
    chatShouldReloadKey,
    isInstalledApp,
    appId,
    handleFeedback,
    currentChatInstanceRef,
  } = useEmbeddedChatbot()

  return (
    <EmbeddedChatbotContext.Provider
      value={{
        appInfoError,
        appInfoLoading,
        appData,
        appParams,
        appMeta,
        appChatListDataLoading,
        currentConversationId,
        currentConversationItem,
        appPrevChatList,
        pinnedConversationList,
        conversationList,
        showConfigPanelBeforeChat,
        newConversationInputs,
        newConversationInputsRef,
        handleNewConversationInputsChange,
        inputsForms,
        handleNewConversation,
        handleStartChat,
        handleChangeConversation,
        handleNewConversationCompleted,
        chatShouldReloadKey,
        isMobile,
        isInstalledApp,
        appId,
        handleFeedback,
        currentChatInstanceRef,
        themeBuilder,
      }}
    >
      <Chatbot />
    </EmbeddedChatbotContext.Provider>
  )
}

export default EmbeddedChatbotWrapper
