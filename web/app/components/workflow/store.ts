import { useContext } from 'react'
import {
  useStore as useZustandStore,
} from 'zustand'
import { createStore } from 'zustand/vanilla'
import { debounce } from 'lodash-es'
import type { Viewport } from 'reactflow'
import type {
  HelpLineHorizontalPosition,
  HelpLineVerticalPosition,
} from './common/help-line/types'
import type { VariableAssignerNodeType } from './nodes/variable-assigner/types'
import type {
  ConversationVariable,
  Edge,
  EnvironmentVariable,
  HistoryWorkflowData,
  Node,
  RunFile,
  ToolWithProvider,
  WorkflowRunningData,
} from './types'
import { WorkflowContext } from './context'
import type { AgentDefaultValue } from './nodes/agent/types'

type PreviewRunningData = WorkflowRunningData & {
  resultTabActive?: boolean
  resultText?: string
}

type Shape = {
  // 应用id
  appId: string
  // 面板宽度
  panelWidth: number
  // 发布时间
  publishedAt: number
  setPublishedAt: (publishedAt: number) => void
  // 环境变量
  environmentVariables: EnvironmentVariable[]
  setEnvironmentVariables: (environmentVariables: EnvironmentVariable[]) => void
  // 工作流节点默认配置
  nodesDefaultConfigs: Record<string, any>
  setNodesDefaultConfigs: (nodesDefaultConfigs: Record<string, any>) => void
  // 机密-环境变量
  envSecrets: Record<string, string>
  setEnvSecrets: (envSecrets: Record<string, string>) => void
  // 谈话变量
  conversationVariables: ConversationVariable[]
  setConversationVariables: (conversationVariables: ConversationVariable[]) => void
  // 工作流画布的hash码
  syncWorkflowDraftHash: string
  setSyncWorkflowDraftHash: (hash: string) => void
  // 工作流画布更新时间
  draftUpdatedAt: number
  setDraftUpdatedAt: (draftUpdatedAt: number) => void
  // 工作流画布重新渲染时间点
  controlPromptEditorRerenderKey: number
  setControlPromptEditorRerenderKey: (controlPromptEditorRerenderKey: number) => void
  // 鼠标位置
  mousePosition: { pageX: number; pageY: number; elementX: number; elementY: number }
  setMousePosition: (mousePosition: Shape['mousePosition']) => void
  // 水平位置辅助线
  helpLineHorizontal?: HelpLineHorizontalPosition
  setHelpLineHorizontal: (helpLineHorizontal?: HelpLineHorizontalPosition) => void
  // 竖直位置辅助线
  helpLineVertical?: HelpLineVerticalPosition
  setHelpLineVertical: (helpLineVertical?: HelpLineVerticalPosition) => void
  // 鼠标进入的节点数据
  enteringNodePayload?: {
    nodeId: string
    nodeData: VariableAssignerNodeType
  }
  setEnteringNodePayload: (enteringNodePayload?: Shape['enteringNodePayload']) => void
  // 工作流应用运行数据
  workflowRunningData?: PreviewRunningData
  setWorkflowRunningData: (workflowData: PreviewRunningData) => void
  // 控制模式
  controlMode: 'pointer' | 'hand'
  setControlMode: (controlMode: Shape['controlMode']) => void

  // 是否正在同步工作流画布草稿-刷新数据
  isSyncingWorkflowDraft: boolean
  setIsSyncingWorkflowDraft: (isSyncingWorkflowDraft: boolean) => void
  // 显示预览和调试面板
  showDebugAndPreviewPanel: boolean
  setShowDebugAndPreviewPanel: (showDebugAndPreviewPanel: boolean) => void
  // 显示创建智能体模式弹窗
  agentModeConfig: {
    show: boolean
    callback: (value: AgentDefaultValue) => void
  }
  setAgentModeConfig: (agentModeConfig: {
    show: boolean
    callback: (value: AgentDefaultValue) => void
  }) => void
  // 显示单次运行面板
  showSingleRunPanel: boolean
  setShowSingleRunPanel: (showSingleRunPanel: boolean) => void
  // 显示运行历史面板
  showRunHistory: boolean
  setShowRunHistory: (showRunHistory: boolean) => void
  // 显示功能面板
  showFeaturesPanel: boolean
  setShowFeaturesPanel: (showFeaturesPanel: boolean) => void
  // 显示运行时的输入面板
  showInputsPanel: boolean
  setShowInputsPanel: (showInputsPanel: boolean) => void

  // 防抖更新工作流画布
  debouncedSyncWorkflowDraft: (fn: () => void) => void

  historyWorkflowData?: HistoryWorkflowData
  setHistoryWorkflowData: (historyWorkflowData?: HistoryWorkflowData) => void

  inputs: Record<string, string>
  setInputs: (inputs: Record<string, string>) => void
  toolPublished: boolean
  setToolPublished: (toolPublished: boolean) => void
  files: RunFile[]
  setFiles: (files: RunFile[]) => void
  backupDraft?: {
    nodes: Node[]
    edges: Edge[]
    viewport: Viewport
    features: Record<string, any>
    environmentVariables: EnvironmentVariable[]
  }
  setBackupDraft: (backupDraft?: Shape['backupDraft']) => void
  notInitialWorkflow: boolean
  setNotInitialWorkflow: (notInitialWorkflow: boolean) => void

  nodeAnimation: boolean
  setNodeAnimation: (nodeAnimation: boolean) => void
  isRestoring: boolean
  setIsRestoring: (isRestoring: boolean) => void
  buildInTools: ToolWithProvider[]
  setBuildInTools: (tools: ToolWithProvider[]) => void
  customTools: ToolWithProvider[]
  setCustomTools: (tools: ToolWithProvider[]) => void
  // workflowTools: ToolWithProvider[]
  // setWorkflowTools: (tools: ToolWithProvider[]) => void
  clipboardElements: Node[]
  setClipboardElements: (clipboardElements: Node[]) => void

  candidateNode?: Node
  setCandidateNode: (candidateNode?: Node) => void
  panelMenu?: {
    top: number
    left: number
  }
  setPanelMenu: (panelMenu: Shape['panelMenu']) => void

  showConfirm?: { title: string; desc?: string; onConfirm: () => void }
  setShowConfirm: (showConfirm: Shape['showConfirm']) => void
  showAssignVariablePopup?: {
    nodeId: string
    nodeData: Node['data']
    variableAssignerNodeId: string
    variableAssignerNodeData: VariableAssignerNodeType
    variableAssignerNodeHandleId: string
    parentNode?: Node
    x: number
    y: number
  }
  setShowAssignVariablePopup: (showAssignVariablePopup: Shape['showAssignVariablePopup']) => void
  hoveringAssignVariableGroupId?: string
  setHoveringAssignVariableGroupId: (hoveringAssignVariableGroupId?: string) => void
  connectingNodePayload?: { nodeId: string; nodeType: string; handleType: string; handleId: string | null }
  setConnectingNodePayload: (startConnectingPayload?: Shape['connectingNodePayload']) => void
  showTips: string
  setShowTips: (showTips: string) => void
  chatAllTypeResultflow: any
  setChatAllTypeResultflow: (chatAllTypeResult: any) => void
}

export const createWorkflowStore = () => {
  return createStore<Shape>(set => ({
    appId: '',
    panelWidth: localStorage.getItem('workflow-node-panel-width') ? parseFloat(localStorage.getItem('workflow-node-panel-width')!) : 420,
    showSingleRunPanel: false,
    setShowSingleRunPanel: showSingleRunPanel => set(() => ({ showSingleRunPanel })),
    workflowRunningData: undefined,
    setWorkflowRunningData: workflowRunningData => set(() => ({ workflowRunningData })),
    historyWorkflowData: undefined,
    setHistoryWorkflowData: historyWorkflowData => set(() => ({ historyWorkflowData })),
    showRunHistory: false,
    setShowRunHistory: showRunHistory => set(() => ({ showRunHistory })),
    showFeaturesPanel: false,
    setShowFeaturesPanel: showFeaturesPanel => set(() => ({ showFeaturesPanel })),
    helpLineHorizontal: undefined,
    setHelpLineHorizontal: helpLineHorizontal => set(() => ({ helpLineHorizontal })),
    helpLineVertical: undefined,
    setHelpLineVertical: helpLineVertical => set(() => ({ helpLineVertical })),
    draftUpdatedAt: 0,
    setDraftUpdatedAt: draftUpdatedAt => set(() => ({ draftUpdatedAt: draftUpdatedAt ? draftUpdatedAt * 1000 : 0 })),
    publishedAt: 0,
    setPublishedAt: publishedAt => set(() => ({ publishedAt: publishedAt ? publishedAt * 1000 : 0 })),
    showInputsPanel: false,
    setShowInputsPanel: showInputsPanel => set(() => ({ showInputsPanel })),
    inputs: {},
    setInputs: inputs => set(() => ({ inputs })),
    toolPublished: false,
    setToolPublished: toolPublished => set(() => ({ toolPublished })),
    files: [],
    setFiles: files => set(() => ({ files })),
    backupDraft: undefined,
    setBackupDraft: backupDraft => set(() => ({ backupDraft })),
    notInitialWorkflow: false,
    setNotInitialWorkflow: notInitialWorkflow => set(() => ({ notInitialWorkflow })),
    nodesDefaultConfigs: {},
    setNodesDefaultConfigs: nodesDefaultConfigs => set(() => ({ nodesDefaultConfigs })),
    nodeAnimation: false,
    setNodeAnimation: nodeAnimation => set(() => ({ nodeAnimation })),
    isRestoring: false,
    setIsRestoring: isRestoring => set(() => ({ isRestoring })),
    debouncedSyncWorkflowDraft: debounce((syncWorkflowDraft) => {
      syncWorkflowDraft()
    }, 5000),
    buildInTools: [],
    setBuildInTools: buildInTools => set(() => ({ buildInTools })),
    customTools: [],
    setCustomTools: customTools => set(() => ({ customTools })),
    // workflowTools: [],
    // setWorkflowTools: workflowTools => set(() => ({ workflowTools })),
    clipboardElements: [],
    setClipboardElements: clipboardElements => set(() => ({ clipboardElements })),
    showDebugAndPreviewPanel: false,
    setShowDebugAndPreviewPanel: showDebugAndPreviewPanel => set(() => ({ showDebugAndPreviewPanel })),
    environmentVariables: [],
    setEnvironmentVariables: environmentVariables => set(() => ({ environmentVariables })),
    envSecrets: {},
    setEnvSecrets: envSecrets => set(() => ({ envSecrets })),
    conversationVariables: [],
    setConversationVariables: conversationVariables => set(() => ({ conversationVariables })),
    controlMode: localStorage.getItem('workflow-operation-mode') === 'pointer' ? 'pointer' : 'hand',
    setControlMode: (controlMode) => {
      set(() => ({ controlMode }))
      localStorage.setItem('workflow-operation-mode', controlMode)
    },
    candidateNode: undefined,
    setCandidateNode: candidateNode => set(() => ({ candidateNode })),
    panelMenu: undefined,
    setPanelMenu: panelMenu => set(() => ({ panelMenu })),
    mousePosition: { pageX: 0, pageY: 0, elementX: 0, elementY: 0 },
    setMousePosition: mousePosition => set(() => ({ mousePosition })),
    syncWorkflowDraftHash: '',
    setSyncWorkflowDraftHash: syncWorkflowDraftHash => set(() => ({ syncWorkflowDraftHash })),
    showConfirm: undefined,
    setShowConfirm: showConfirm => set(() => ({ showConfirm })),
    showAssignVariablePopup: undefined,
    setShowAssignVariablePopup: showAssignVariablePopup => set(() => ({ showAssignVariablePopup })),
    hoveringAssignVariableGroupId: undefined,
    setHoveringAssignVariableGroupId: hoveringAssignVariableGroupId => set(() => ({ hoveringAssignVariableGroupId })),
    connectingNodePayload: undefined,
    setConnectingNodePayload: connectingNodePayload => set(() => ({ connectingNodePayload })),
    enteringNodePayload: undefined,
    setEnteringNodePayload: enteringNodePayload => set(() => ({ enteringNodePayload })),
    isSyncingWorkflowDraft: false,
    setIsSyncingWorkflowDraft: isSyncingWorkflowDraft => set(() => ({ isSyncingWorkflowDraft })),
    controlPromptEditorRerenderKey: 0,
    setControlPromptEditorRerenderKey: controlPromptEditorRerenderKey => set(() => ({ controlPromptEditorRerenderKey })),
    showTips: '',
    setShowTips: showTips => set(() => ({ showTips })),

    agentModeConfig: {
      show: false,
      callback: () => {},
    },
    setAgentModeConfig: agentModeConfig => set(() => ({ agentModeConfig })),
    chatAllTypeResultflow:"",
    setChatAllTypeResultflow: chatAllTypeResultflow => set(() => ({ chatAllTypeResultflow }))
    //setChatAllTypeResultflow: chatAllTypeResultflow => set(() => ({ chatAllTypeResultflow })),
  }))
}

export function useStore<T>(selector: (state: Shape) => T): T {
  const store = useContext(WorkflowContext)
  if (!store)
    throw new Error('Missing WorkflowContext.Provider in the tree')

  return useZustandStore(store, selector)
}

export const useWorkflowStore = () => {
  return useContext(WorkflowContext)!
}
