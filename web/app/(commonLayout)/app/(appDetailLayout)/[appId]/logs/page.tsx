'use client'
import React, { useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { useStore as useAppStore } from '@/app/components/app/store'

import { useAppContext } from '@/context/app-context'
import TabMainContainer from '@/app/components/base/tab-main-container'
import Log from '@/app/components/app/log'
// import Overview from '@/app/components/app/overview'
import DataReflux from '@/app/components/app/data-reflux'

export const enum TabType {
  log = 'log',
  // overview = 'overview',
  dataReflux = 'dataReflux',
}

const Logs = () => {
  const { t } = useTranslation()
  const { isMicroApp } = useAppContext()
  const appDetail = useAppStore(state => state.appDetail!)
  const [currentTab, setCurrentTab] = React.useState<TabType>()
  // tab选项
  const options = useMemo(() => {
    const options = [
      { key: 'log', label: t('appLog.title'), tabChildren: (<Log appDetail={appDetail} />) },
      // { key: 'overview', label: t('appOverview.title'), tabChildren: (<Overview appId={appDetail.id} />) },
    ]
    if (isMicroApp && appDetail.mode === 'agent-chat')
      options.splice(1, 0, { key: 'dataReflux', label: t('appDataReflux.title'), tabChildren: (<DataReflux appId={appDetail.id} />) })
    return options
  }, [t, isMicroApp])
  return (
    <TabMainContainer options={options} defaultTab={TabType.log} />
  )
}

export default Logs
