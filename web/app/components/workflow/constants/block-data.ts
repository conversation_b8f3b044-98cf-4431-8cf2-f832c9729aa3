import type { Block } from '../types'
import { BlockClassificationEnum, BlockEnum } from '../types'
import StartNodeDefault from '../nodes/start/default'
import AnswerDefault from '../nodes/answer/default'
import LLMDefault from '../nodes/llm/default'
import KnowledgeRetrievalDefault from '../nodes/knowledge-retrieval/default'
import QuestionClassifierDefault from '../nodes/question-classifier/default'
import IfElseDefault from '../nodes/if-else/default'
import CodeDefault from '../nodes/code/default'
import TemplateTransformDefault from '../nodes/template-transform/default'
import HttpRequestDefault from '../nodes/http/default'
import ParameterExtractorDefault from '../nodes/parameter-extractor/default'
import ToolDefault from '../nodes/tool/default'
import VariableAssignerDefault from '../nodes/variable-assigner/default'
import AssignerDefault from '../nodes/assigner/default'
import EndNodeDefault from '../nodes/end/default'
import IterationDefault from '../nodes/iteration/default'
import DocExtractorDefault from '../nodes/document-extractor/default'
import ListFilterDefault from '../nodes/list-operator/default'
import IterationStartDefault from '../nodes/iteration-start/default'
import AgentDefault from '../nodes/agent/default'
import VideoDefault from '../nodes/video/default'
import { APP_NAME } from '@/config'
import { RETRIEVE_TYPE } from '@/types/datasets'

type NodesExtraData = {
  author: string
  about: string
  title?: string
  availablePrevNodes: BlockEnum[]
  availableNextNodes: BlockEnum[]
  getAvailablePrevNodes: (isChatMode: boolean) => BlockEnum[]
  getAvailableNextNodes: (isChatMode: boolean) => BlockEnum[]
  checkValid: any
}

// 全部节点数据
export const NODES_EXTRA_DATA: Record<BlockEnum, NodesExtraData> = {
  [BlockEnum.Agent]: {
    author: APP_NAME,
    about: '',
    availablePrevNodes: [],
    availableNextNodes: [],
    getAvailablePrevNodes: AgentDefault.getAvailablePrevNodes,
    getAvailableNextNodes: AgentDefault.getAvailableNextNodes,
    checkValid: AgentDefault.checkValid,
  },
  [BlockEnum.Start]: {
    author: APP_NAME,
    about: '',
    availablePrevNodes: [],
    availableNextNodes: [],
    getAvailablePrevNodes: StartNodeDefault.getAvailablePrevNodes,
    getAvailableNextNodes: StartNodeDefault.getAvailableNextNodes,
    checkValid: StartNodeDefault.checkValid,
  },
  [BlockEnum.End]: {
    author: APP_NAME,
    about: '',
    availablePrevNodes: [],
    availableNextNodes: [],
    getAvailablePrevNodes: EndNodeDefault.getAvailablePrevNodes,
    getAvailableNextNodes: EndNodeDefault.getAvailableNextNodes,
    checkValid: EndNodeDefault.checkValid,
  },
  [BlockEnum.Answer]: {
    author: APP_NAME,
    about: '',
    availablePrevNodes: [],
    availableNextNodes: [],
    getAvailablePrevNodes: AnswerDefault.getAvailablePrevNodes,
    getAvailableNextNodes: AnswerDefault.getAvailableNextNodes,
    checkValid: AnswerDefault.checkValid,
  },
  [BlockEnum.LLM]: {
    author: APP_NAME,
    about: '',
    availablePrevNodes: [],
    availableNextNodes: [],
    getAvailablePrevNodes: LLMDefault.getAvailablePrevNodes,
    getAvailableNextNodes: LLMDefault.getAvailableNextNodes,
    checkValid: LLMDefault.checkValid,
  },
  [BlockEnum.KnowledgeRetrieval]: {
    author: APP_NAME,
    about: '',
    availablePrevNodes: [],
    availableNextNodes: [],
    getAvailablePrevNodes: KnowledgeRetrievalDefault.getAvailablePrevNodes,
    getAvailableNextNodes: KnowledgeRetrievalDefault.getAvailableNextNodes,
    checkValid: KnowledgeRetrievalDefault.checkValid,
  },
  [BlockEnum.IfElse]: {
    author: APP_NAME,
    about: '',
    availablePrevNodes: [],
    availableNextNodes: [],
    getAvailablePrevNodes: IfElseDefault?.getAvailablePrevNodes,
    getAvailableNextNodes: IfElseDefault?.getAvailableNextNodes,
    checkValid: IfElseDefault?.checkValid,
  },
  [BlockEnum.Iteration]: {
    author: APP_NAME,
    about: '',
    availablePrevNodes: [],
    availableNextNodes: [],
    getAvailablePrevNodes: IterationDefault.getAvailablePrevNodes,
    getAvailableNextNodes: IterationDefault.getAvailableNextNodes,
    checkValid: IterationDefault.checkValid,
  },
  [BlockEnum.IterationStart]: {
    author: APP_NAME,
    about: '',
    availablePrevNodes: [],
    availableNextNodes: [],
    getAvailablePrevNodes: IterationStartDefault.getAvailablePrevNodes,
    getAvailableNextNodes: IterationStartDefault.getAvailableNextNodes,
    checkValid: IterationStartDefault.checkValid,
  },
  [BlockEnum.Code]: {
    author: APP_NAME,
    about: '',
    availablePrevNodes: [],
    availableNextNodes: [],
    getAvailablePrevNodes: CodeDefault.getAvailablePrevNodes,
    getAvailableNextNodes: CodeDefault.getAvailableNextNodes,
    checkValid: CodeDefault.checkValid,
  },
  [BlockEnum.TemplateTransform]: {
    author: APP_NAME,
    about: '',
    availablePrevNodes: [],
    availableNextNodes: [],
    getAvailablePrevNodes: TemplateTransformDefault.getAvailablePrevNodes,
    getAvailableNextNodes: TemplateTransformDefault.getAvailableNextNodes,
    checkValid: TemplateTransformDefault.checkValid,
  },
  [BlockEnum.QuestionClassifier]: {
    author: APP_NAME,
    about: '',
    availablePrevNodes: [],
    availableNextNodes: [],
    getAvailablePrevNodes: QuestionClassifierDefault.getAvailablePrevNodes,
    getAvailableNextNodes: QuestionClassifierDefault.getAvailableNextNodes,
    checkValid: QuestionClassifierDefault.checkValid,
  },
  [BlockEnum.HttpRequest]: {
    author: APP_NAME,
    about: '',
    availablePrevNodes: [],
    availableNextNodes: [],
    getAvailablePrevNodes: HttpRequestDefault.getAvailablePrevNodes,
    getAvailableNextNodes: HttpRequestDefault.getAvailableNextNodes,
    checkValid: HttpRequestDefault.checkValid,
  },
  [BlockEnum.VariableAssigner]: {
    author: APP_NAME,
    about: '',
    availablePrevNodes: [],
    availableNextNodes: [],
    getAvailablePrevNodes: VariableAssignerDefault.getAvailablePrevNodes,
    getAvailableNextNodes: VariableAssignerDefault.getAvailableNextNodes,
    checkValid: VariableAssignerDefault.checkValid,
  },
  [BlockEnum.Assigner]: {
    author: APP_NAME,
    about: '',
    availablePrevNodes: [],
    availableNextNodes: [],
    getAvailablePrevNodes: AssignerDefault.getAvailablePrevNodes,
    getAvailableNextNodes: AssignerDefault.getAvailableNextNodes,
    checkValid: AssignerDefault.checkValid,
  },
  [BlockEnum.VariableAggregator]: {
    author: APP_NAME,
    about: '',
    availablePrevNodes: [],
    availableNextNodes: [],
    getAvailablePrevNodes: VariableAssignerDefault.getAvailablePrevNodes,
    getAvailableNextNodes: VariableAssignerDefault.getAvailableNextNodes,
    checkValid: VariableAssignerDefault.checkValid,
  },
  [BlockEnum.ParameterExtractor]: {
    author: APP_NAME,
    about: '',
    availablePrevNodes: [],
    availableNextNodes: [],
    getAvailablePrevNodes: ParameterExtractorDefault.getAvailablePrevNodes,
    getAvailableNextNodes: ParameterExtractorDefault.getAvailableNextNodes,
    checkValid: ParameterExtractorDefault.checkValid,
  },
  [BlockEnum.Tool]: {
    author: APP_NAME,
    about: '',
    availablePrevNodes: [],
    availableNextNodes: [],
    getAvailablePrevNodes: ToolDefault.getAvailablePrevNodes,
    getAvailableNextNodes: ToolDefault.getAvailableNextNodes,
    checkValid: ToolDefault.checkValid,
  },
  [BlockEnum.DocExtractor]: {
    author: 'Agent',
    about: '',
    availablePrevNodes: [],
    availableNextNodes: [],
    getAvailablePrevNodes: DocExtractorDefault.getAvailablePrevNodes,
    getAvailableNextNodes: DocExtractorDefault.getAvailableNextNodes,
    checkValid: DocExtractorDefault.checkValid,
  },
  [BlockEnum.ListFilter]: {
    author: 'Agent',
    about: '',
    availablePrevNodes: [],
    availableNextNodes: [],
    getAvailablePrevNodes: ListFilterDefault.getAvailablePrevNodes,
    getAvailableNextNodes: ListFilterDefault.getAvailableNextNodes,
    checkValid: ListFilterDefault.checkValid,
  },
  [BlockEnum.Video]: {
    author: APP_NAME,
    about: '',
    availablePrevNodes: [],
    availableNextNodes: [],
    getAvailablePrevNodes: VideoDefault.getAvailablePrevNodes,
    getAvailableNextNodes: VideoDefault.getAvailableNextNodes,
    checkValid: VideoDefault.checkValid,
  },
}
// 工作流应用流程图初始可配置节点
export const NODES_INITIAL_DATA = {
  [BlockEnum.Start]: {
    type: BlockEnum.Start,
    title: '',
    desc: '工作流的起始点，设定启动所需要的信息',
    ...StartNodeDefault.defaultValue,
  },
  [BlockEnum.End]: {
    type: BlockEnum.End,
    title: '',
    desc: '',
    ...EndNodeDefault.defaultValue,
  },
  [BlockEnum.Answer]: {
    type: BlockEnum.Answer,
    title: '',
    desc: '',
    ...AnswerDefault.defaultValue,
  },

  [BlockEnum.KnowledgeRetrieval]: {
    type: BlockEnum.KnowledgeRetrieval,
    title: '',
    desc: '',
    query_variable_selector: [],
    dataset_ids: [],
    retrieval_mode: RETRIEVE_TYPE.oneWay,
    ...KnowledgeRetrievalDefault.defaultValue,
  },
  [BlockEnum.IfElse]: {
    type: BlockEnum.IfElse,
    title: '',
    desc: '',
    ...IfElseDefault?.defaultValue,
  },
  [BlockEnum.Iteration]: {
    type: BlockEnum.Iteration,
    title: '',
    desc: '',
    ...IterationDefault.defaultValue,
  },
  [BlockEnum.IterationStart]: {
    type: BlockEnum.IterationStart,
    title: '',
    desc: '',
    ...IterationStartDefault.defaultValue,
  },
  [BlockEnum.Code]: {
    type: BlockEnum.Code,
    title: '',
    desc: '',
    variables: [],
    code_language: 'python3',
    code: '',
    outputs: [],
    ...CodeDefault.defaultValue,
  },
  [BlockEnum.TemplateTransform]: {
    type: BlockEnum.TemplateTransform,
    title: '',
    desc: '',
    variables: [],
    template: '',
    ...TemplateTransformDefault.defaultValue,
  },
  [BlockEnum.QuestionClassifier]: {
    type: BlockEnum.QuestionClassifier,
    title: '',
    desc: '',
    query_variable_selector: [],
    topics: [],
    ...QuestionClassifierDefault.defaultValue,
  },
  [BlockEnum.HttpRequest]: {
    type: BlockEnum.HttpRequest,
    title: '',
    desc: '',
    variables: [],
    ...HttpRequestDefault.defaultValue,
  },
  [BlockEnum.ParameterExtractor]: {
    type: BlockEnum.ParameterExtractor,
    title: '',
    desc: '',
    variables: [],
    ...ParameterExtractorDefault.defaultValue,
  },
  [BlockEnum.VariableAssigner]: {
    type: BlockEnum.VariableAssigner,
    title: '',
    desc: '',
    variables: [],
    output_type: '',
    ...VariableAssignerDefault.defaultValue,
  },
  [BlockEnum.VariableAggregator]: {
    type: BlockEnum.VariableAggregator,
    title: '',
    desc: '',
    variables: [],
    output_type: '',
    ...VariableAssignerDefault.defaultValue,
  },
  [BlockEnum.Assigner]: {
    type: BlockEnum.Assigner,
    title: '',
    desc: '',
    ...AssignerDefault.defaultValue,
  },
  [BlockEnum.Tool]: {
    type: BlockEnum.Tool,
    title: '',
    desc: '',
    ...ToolDefault.defaultValue,
  },
  [BlockEnum.DocExtractor]: {
    type: BlockEnum.DocExtractor,
    title: '',
    desc: '',
    ...DocExtractorDefault.defaultValue,
  },
  [BlockEnum.ListFilter]: {
    type: BlockEnum.ListFilter,
    title: '',
    desc: '',
    ...ListFilterDefault.defaultValue,
  },
  [BlockEnum.LLM]: {
    type: BlockEnum.LLM,
    title: '',
    desc: '',
    variables: [],
    ...LLMDefault.defaultValue,
  },
  [BlockEnum.Agent]: {
    type: BlockEnum.Agent,
    title: '',
    desc: '',
    ...AgentDefault.defaultValue,
  },
  [BlockEnum.Video]: {
    type: BlockEnum.Video,
    title: '',
    desc: '',
    ...VideoDefault.defaultValue,
  },
}
// 全部节点
export const BLOCKS: Block[] = [
  {
    classification: BlockClassificationEnum.Default,
    type: BlockEnum.Start,
    title: 'Start',
    description: '',
  },
  {
    classification: BlockClassificationEnum.Default,
    type: BlockEnum.LLM,
    title: 'LLM',
  },
  {
    classification: BlockClassificationEnum.Default,
    type: BlockEnum.QuestionClassifier,
    title: 'Question Classifier',
  },
  {
    classification: BlockClassificationEnum.Default,
    type: BlockEnum.End,
    title: 'End',
  },
  {
    classification: BlockClassificationEnum.Default,
    type: BlockEnum.Answer,
    title: 'Direct Answer',
  },
  {
    classification: BlockClassificationEnum.Logic,
    type: BlockEnum.IfElse,
    title: 'IF/ELSE',
  },
  {
    classification: BlockClassificationEnum.Logic,
    type: BlockEnum.Iteration,
    title: 'Iteration',
  },
  {
    classification: BlockClassificationEnum.KnowledgeAndMemory,
    type: BlockEnum.KnowledgeRetrieval,
    title: 'Knowledge Retrieval',
  },
  {
    classification: BlockClassificationEnum.KnowledgeAndMemory,
    type: BlockEnum.Assigner,
    title: 'Variable Assigner',
  },
  {
    classification: BlockClassificationEnum.InformationProcessing,
    type: BlockEnum.TemplateTransform,
    title: 'Templating Transform',
  },
  {
    classification: BlockClassificationEnum.InformationProcessing,
    type: BlockEnum.ListFilter,
    title: 'List Filter',
  },
  {
    classification: BlockClassificationEnum.InformationProcessing,
    type: BlockEnum.VariableAggregator,
    title: 'Variable Aggregator',
  },
  {
    classification: BlockClassificationEnum.InformationProcessing,
    type: BlockEnum.DocExtractor,
    title: 'Doc Extractor',
  },
  {
    classification: BlockClassificationEnum.InformationProcessing,
    type: BlockEnum.ParameterExtractor,
    title: 'Parameter Extractor',
  },
  {
    classification: BlockClassificationEnum.Development,
    type: BlockEnum.Code,
    title: 'Code',
  },
  {
    classification: BlockClassificationEnum.Development,
    type: BlockEnum.HttpRequest,
    title: 'HTTP Request',
  },
  {
    classification: BlockClassificationEnum.Development,
    type: BlockEnum.Agent,
    title: 'Agent',
  },
  {
    classification: BlockClassificationEnum.KnowledgeAndMemory,
    type: BlockEnum.Video,
    title: 'Video',
  },
]
// 节点分类
export const BLOCK_CLASSIFICATIONS: string[] = [
  BlockClassificationEnum.Default,
  BlockClassificationEnum.Logic,
  BlockClassificationEnum.KnowledgeAndMemory,
  BlockClassificationEnum.InformationProcessing,
  BlockClassificationEnum.Development,
]
