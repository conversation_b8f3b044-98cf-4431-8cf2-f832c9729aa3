'use client'
import { useEffect, useState } from 'react'
import { useMount } from 'ahooks'
import { useContext } from 'use-context-selector'
import { Form as AForm } from 'antd'
import { useTranslation } from 'react-i18next'
import { useSWRConfig } from 'swr'
import { unstable_serialize } from 'swr/infinite'
import { debounce } from 'lodash-es'
import { updateDatasetSetting } from '@/service/datasets'
import { fetchMembers } from '@/service/common'
import { type RetrievalConfig } from '@/types/datasets'
import DatasetDetailContext from '@/context/dataset-detail'
import type { Member } from '@/models/common'
import type { DataSetListResponse } from '@/models/datasets'
import KnowledgeEnhancementSetting from '@/app/components/datasets/create/knowledge-enhancement-setting'

// 公共能力
import {
  useModelList,
  useModelListAndDefaultModelAndCurrentProviderAndModel,
} from '@/app/components/account-setting/model-provider-page/hooks'
import { ModelTypeEnum } from '@/app/components/account-setting/model-provider-page/declarations'
import type { DefaultModel } from '@/app/components/account-setting/model-provider-page/declarations'
import ModelSelector from '@/app/components/account-setting/model-provider-page/model-selector'

import datasetsStyle from '@/app/components/datasets/styles/style.module.css'
import RetrievalMethodConfig from '@/app/components/datasets/common/retrieval-method-config'
import { ensureRerankModelSelected, isReRankModelSelected } from '@/app/components/datasets/common/check-rerank-model'
import EconomicalRetrievalMethodConfig from '@/app/components/datasets/common/economical-retrieval-method-config'
import cn from '@/utils/classnames'
import Button from '@/app/components/base/button'
import Toast, { ToastContext } from '@/app/components/base/toast'
import Scrollbar from '@/app/components/base/scrollbar'
import { useProviderContext } from '@/context/provider-context'

type KnowledgeEnhanceType = {
  enhanced_enable: boolean // 知识增强开关
  enhanced_feature?: string[] // 增强内容复选
}
const getKey = (pageIndex: number, previousPageData: DataSetListResponse) => {
  if (!pageIndex || previousPageData.has_more)
    return { url: 'datasets', params: { page: pageIndex + 1, limit: 30 } }
  return null
}

const DatasetSetting = () => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  const { mutate } = useSWRConfig()
  const [form] = AForm.useForm()
  const { useXIYANRag } = useProviderContext()
  const values = AForm.useWatch([], form)
  const { dataset: currentDataset, mutateDatasetRes: mutateDatasets } = useContext(DatasetDetailContext)

  // 是否可提交
  const [disabled, setDisabled] = useState(false)
  // 知识库权限
  const [permission, setPermission] = useState(currentDataset?.permission)
  // 选中的成员id
  const [selectedMemberIDs, setSelectedMemberIDs] = useState<string[]>(currentDataset?.partial_member_list || [])
  // 成员列表
  const [memberList, setMemberList] = useState<Member[]>([])
  // 索引方法
  const [indexMethod, setIndexMethod] = useState(currentDataset?.indexing_technique)
  // 检索配置
  const [retrievalConfig, setRetrievalConfig] = useState(currentDataset?.retrieval_model_dict as RetrievalConfig)
  // 嵌入模型配置
  const [embeddingModel, setEmbeddingModel] = useState<DefaultModel>(
    currentDataset?.embedding_model
      ? {
        provider: currentDataset.embedding_model_provider,
        model: currentDataset.embedding_model,
      }
      : {
        provider: '',
        model: '',
      },
  )

  // 知识增强开关
  const [knowledgeEnhanceSet, setKnowledgeEnhanceSet] = useState<KnowledgeEnhanceType>({
    enhanced_enable: false, // 知识增强开关
    enhanced_feature: ['paragraphSummary', 'problemGeneration'], // 增强内容复选
  })

  const {
    modelList: rerankModelList,
    defaultModel: rerankDefaultModel,
    currentModel: isRerankDefaultModelValid,
  } = useModelListAndDefaultModelAndCurrentProviderAndModel(ModelTypeEnum.rerank)
  const { data: embeddingModelList, mutate: mutateEmbeddingModelList, isValidating: isEmbeddingModelListLoading } = useModelList(ModelTypeEnum.textEmbedding)

  // 获取成员列表
  const getMembers = async () => {
    const { accounts } = await fetchMembers({ url: '/workspaces/current/members', params: {} })
    if (!accounts)
      setMemberList([])
    else
      setMemberList(accounts)
  }
  // 增强内容
  const FEATURE_MAP = {
    paragraphSummary: '3',
    problemGeneration: '2',
  }
  const enhancedFeatureCheck = () => {
    const enhanced_feature_val = knowledgeEnhanceSet.enhanced_feature
    if (enhanced_feature_val?.length === 0) {
      Toast.notify({
        type: 'error',
        message: t('datasetCreation.stepTwo.enhancedFeatureTip'),
      })
      return []
    }
    const values = []
    if (knowledgeEnhanceSet.enhanced_enable) {
      Object.entries(FEATURE_MAP).forEach(([featureKey, featureValue]) => {
        if (enhanced_feature_val.includes(featureKey))
          values.push(featureValue)
      })
    }
    return values
  }
  // 保存知识库配置
  const handleSave = async () => {
    console.log(knowledgeEnhanceSet, '====知识增强===')
    form.validateFields().then(async (values) => {
      // 判断rerank模型是否配置完成
      if (
        !isReRankModelSelected({
          rerankDefaultModel,
          isRerankDefaultModelValid: !!isRerankDefaultModelValid,
          rerankModelList,
          retrievalConfig,
          indexMethod,
        })
      ) {
        notify({ type: 'error', message: t('dataset.placeholder.rerankModel') })
        return
      }
      // 补充参数了
      const postRetrievalConfig = ensureRerankModelSelected({
        rerankDefaultModel: rerankDefaultModel!,
        retrievalConfig,
        indexMethod,
      })
      if (postRetrievalConfig.weights) {
        postRetrievalConfig.weights.vector_setting.embedding_provider_name = currentDataset?.embedding_model_provider || ''
        postRetrievalConfig.weights.vector_setting.embedding_model_name = currentDataset?.embedding_model || ''
      }
      // 知识增强
      const enhancedFeatureList = enhancedFeatureCheck()
      // 检测到开启增强功能，未选中增强内容，直接返回
      if (knowledgeEnhanceSet.enhanced_enable && enhancedFeatureList.length === 0)
        return

      try {
        const body = {
          name: currentDataset?.name,
          description: currentDataset?.description,
          permission,
          indexing_technique: indexMethod,
          retrieval_model: {
            ...postRetrievalConfig,
            score_threshold: postRetrievalConfig.score_threshold,
            score_threshold_enabled: true,
          },
          embedding_model: embeddingModel.model,
          embedding_model_provider: embeddingModel.provider,
        
          // enhanced_enable: knowledgeEnhanceSet.enhanced_enable ? '1' : '0',
          // enhanced_feature: enhancedFeatureList,
        }
        console.log(body, '===提交参数===')
        const requestParams = {
          datasetId: currentDataset!.id,
          body,
        } as any
        if (permission === 'partial_members') {
          requestParams.body.partial_member_list = selectedMemberIDs.map((id) => {
            return {
              user_id: id,
              role: memberList.find(member => member.id === id)?.role,
            }
          })
        }
        await updateDatasetSetting(requestParams)
        notify({ type: 'success', message: t('common.actionMsg.modifiedSuccessfully') })
        if (mutateDatasets) {
          await mutateDatasets()
          mutate(unstable_serialize(getKey))
        }
      }
      catch (e) {
        notify({ type: 'error', message: t('common.actionMsg.modifiedUnsuccessfully') })
      }
    })
  }

  useMount(() => {
    getMembers()
  })
  const KnoEnh = () => {
    if (!currentDataset?.enhanced_feature)
      return knowledgeEnhanceSet.enhanced_feature // 知识增强开关关闭时，返回默认值
    const list = []
    currentDataset?.enhanced_feature.forEach((i) => {
      if (i === '2')
        list.push('problemGeneration')
      if (i === '3')
        list.push('paragraphSummary')
    })
    // console.log(list, '===获取知识增强===')
    return list
  }
  useEffect(() => {
    if (currentDataset) {
      form.setFieldsValue({
        name: currentDataset.name,
        description: currentDataset.description,
      })
    }
    setKnowledgeEnhanceSet({
      ...knowledgeEnhanceSet,
      enhanced_enable: currentDataset?.enhanced_enable === '1',
      enhanced_feature: KnoEnh(),
    })
  }, [currentDataset, form])
  useEffect(debounce(() => {
    form.validateFields({ validateOnly: true }).then(() => {
      setDisabled(false)
    },
    ).catch(() => {
      setDisabled(true)
    })
  }, 100), [form, values])
  console.log(currentDataset, '===知识库设置查询数据===')
  return (
    <Scrollbar className={datasetsStyle['middle-wrap']}>
      <div className={cn(datasetsStyle['left-title'], '!px-0')}>{t('dataset.action.config')}</div>
      <AForm form={form} layout='vertical'>
        {/* 知识库名称 */}
        {/* <AForm.Item
        label={t('datasetCreation.stepOne.form.name')}
        tooltip={t('datasetCreation.stepOne.form.nameDescription')}
        name={'name'}
        validateTrigger='onBlur'
        validateFirst={true}
        rules={[
          {
            required: true,
            whitespace: true,
            pattern: /^[a-zA-Z0-9\u4E00-\u9FA5()-.]{1,50}$/,
          },
        ]}
      >
        <AInput
          disabled={!currentDataset?.embedding_available}
        />
      </AForm.Item> */}
        {/* 知识库描述 */}
        {/* <AForm.Item
        name={'description'}
        label={t('dataset.info.desc')}
        rules={[
          {
            required: true,
            whitespace: true,
          },
        ]}
        validateTrigger='onBlur'
      >
        <AInput.TextArea
          disabled={!currentDataset?.embedding_available}
          placeholder={t('dataset.placeholder.descPlaceholder') || ''}
        />
      </AForm.Item> */}
        {/* 索引模式设置 */}
        {/* {currentDataset && currentDataset.indexing_technique && (
        <>
          <Divider className='my-2'></Divider>
          <AForm.Item label={t('dataset.info.indexMethod')}>
            <IndexMethodRadio
              disable={!currentDataset?.embedding_available}
              value={indexMethod}
              onChange={v => setIndexMethod(v)}
            />
          </AForm.Item>
        </>
      )} */}
        {indexMethod === 'high_quality' && (
          <AForm.Item label={t('dataset.info.embeddingModel')}>
            <ModelSelector
              readonly={useXIYANRag}
              loading={isEmbeddingModelListLoading}
              triggerClassName=''
              defaultModel={embeddingModel}
              modelList={embeddingModelList}
              onSelect={(model: DefaultModel) => {
                setEmbeddingModel(model)
              }}
              onFetch={mutateEmbeddingModelList}
            />
          </AForm.Item>
        )}
        {/* Retrieval Method Config */}
        <AForm.Item label={t('dataset.action.setting')}>
          {indexMethod === 'high_quality'
            ? (
              <RetrievalMethodConfig
                value={retrievalConfig}
                onChange={setRetrievalConfig}
              />
            )
            : (
              <EconomicalRetrievalMethodConfig
                value={retrievalConfig}
                onChange={setRetrievalConfig}
              />
            )}
        </AForm.Item>
        {/* 可见权限 */}
        {/* <AForm.Item label={t('dataset.permission.permissions')}>
          <PermissionSelector
            disabled={!currentDataset?.embedding_available}
            permission={permission}
            value={selectedMemberIDs}
            onChange={v => setPermission(v)}
            onMemberSelect={setSelectedMemberIDs}
            memberList={memberList}
          />
        </AForm.Item> */}
        {/* 知识库设置-知识增强 => 勿删*/}
        {/* <AForm.Item label={t('datasetCreation.stepTwo.knowledgeEnhancement.title')}>
          <KnowledgeEnhancementSetting
            type='edit'
            value={knowledgeEnhanceSet}
            onChange={setKnowledgeEnhanceSet}
          />
        </AForm.Item> */}

        <Button
          className='w-[92px]'
          variant='primary'
          onClick={handleSave}
          disabled={disabled}
        >
          {t('common.operation.save')}
        </Button>
      </AForm>
    </Scrollbar>
  )
}

export default DatasetSetting
