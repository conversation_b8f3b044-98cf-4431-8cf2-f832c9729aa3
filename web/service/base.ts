import qs from 'qs'
import { getQueryUserInfoByCode } from './common'
import { API_PREFIX, IS_CE_EDITION, PUBLIC_API_PREFIX } from '@/config'
import Toast from '@/app/components/base/toast'
import type { VisionFile } from '@/types/model'
import type { ThoughtItem } from '@/app/components/base/chat/chat/type'
import type {
  AnnotationReply,
  IterationFinishedResponse,
  IterationNextResponse,
  IterationStartedResponse,
  MessageEnd,
  MessageReplace,
  NodeFinishedResponse,
  NodeStartedResponse,
  ParallelBranchFinishedResponse,
  ParallelBranchStartedResponse,
  TextChunkResponse,
  TextReplaceResponse,
  VoiceAnswerSafetyFence,
  VoiceChannelBuild,
  VoiceQuery,
  VoiceQuerySafetyFence,
  VoiceTimeout,
  WorkflowFinishedResponse,
  WorkflowStartedResponse,
} from '@/types/api/chat'
import { aesEncrypt, generateAesIv, generateAes<PERSON><PERSON>, rsaEncrypt, urlJudge } from '@/utils/crypto'
import { removeAuth } from '@/utils/user'
import { getLocaleHeader } from '@/i18n'

const TIME_OUT = 100000

const ContentType = {
  json: 'application/json',
  stream: 'text/event-stream',
  audio: 'audio/mpeg',
  form: 'application/x-www-form-urlencoded; charset=UTF-8',
  download: 'application/octet-stream', // for download
  upload: 'multipart/form-data', // for upload
}

const baseOptions = {
  method: 'GET',
  mode: 'cors',
  credentials: 'include', // always send cookies、HTTP Basic authentication.
  headers: new Headers({
    'Content-Type': ContentType.json,
  }),
  redirect: 'follow',
}

export type IOnDataMoreInfo = {
  conversationId?: string
  taskId?: string
  messageId: string
  errorMessage?: string
  errorCode?: string
}

export type IOnData = (message: string, isFirstMessage: boolean, moreInfo: IOnDataMoreInfo) => void
export type IOnThought = (though: ThoughtItem) => void
export type IOnFile = (file: VisionFile) => void
export type IOnMessageEnd = (messageEnd: MessageEnd) => void
export type IOnMessageReplace = (messageReplace: MessageReplace) => void
export type IOnCheckFailedReplace = () => void
export type IOnAnnotationReply = (messageReplace: AnnotationReply) => void
export type IOnCompleted = (hasError?: boolean, errorMessage?: string) => void
export type IOnError = (msg: string, code?: string) => void

export type IOnWorkflowStarted = (workflowStarted: WorkflowStartedResponse) => void
export type IOnWorkflowFinished = (workflowFinished: WorkflowFinishedResponse) => void
export type IOnNodeStarted = (nodeStarted: NodeStartedResponse) => void
export type IOnNodeFinished = (nodeFinished: NodeFinishedResponse) => void
export type IOnIterationStarted = (workflowStarted: IterationStartedResponse) => void
export type IOnIterationNext = (workflowStarted: IterationNextResponse) => void
export type IOnIterationFinished = (workflowFinished: IterationFinishedResponse) => void
export type IOnParallelBranchStarted = (parallelBranchStarted: ParallelBranchStartedResponse) => void
export type IOnParallelBranchFinished = (parallelBranchFinished: ParallelBranchFinishedResponse) => void
export type IOnTextChunk = (textChunk: TextChunkResponse) => void
export type IOnTTSChunk = (messageId: string, audioStr: string, audioType?: string) => void
export type IOnTTSEnd = (messageId: string, audioStr: string, audioType?: string) => void
export type IOnTextReplace = (textReplace: TextReplaceResponse) => void
export type IOnVoiceChannelBuild = (voiceChannelBuild: VoiceChannelBuild) => void
export type IOnVoiceQuery = (voiceQuery: VoiceQuery) => void
export type IOnVoiceTimeout = (VoiceTimeout: VoiceTimeout) => void
export type IOnVoiceQuerySafetyFence = (voiceSafetyFence: VoiceQuerySafetyFence) => void
export type IOnVoiceAnswerSafetyFence = (voiceSafetyFence: VoiceAnswerSafetyFence) => void

export type IOtherOptions = {
  isPublicAPI?: boolean
  bodyStringify?: boolean
  needAllResponseContent?: boolean
  deleteContentType?: boolean
  silent?: boolean
  sharedtoken?: string
  onData?: IOnData // for stream
  onThought?: IOnThought
  onFile?: IOnFile
  onMessageEnd?: IOnMessageEnd
  onMessageReplace?: IOnMessageReplace
  onCheckFailedReplace?: IOnCheckFailedReplace
  onError?: IOnError
  onCompleted?: IOnCompleted // for stream
  getAbortController?: (abortController: AbortController) => void
  onWorkflowStarted?: IOnWorkflowStarted
  onWorkflowFinished?: IOnWorkflowFinished
  onNodeStarted?: IOnNodeStarted
  onNodeFinished?: IOnNodeFinished
  onIterationStart?: IOnIterationStarted
  onIterationNext?: IOnIterationNext
  onIterationFinish?: IOnIterationFinished
  onParallelBranchStarted?: IOnParallelBranchStarted
  onParallelBranchFinished?: IOnParallelBranchFinished
  onTextChunk?: IOnTextChunk
  onTTSChunk?: IOnTTSChunk
  onTTSEnd?: IOnTTSEnd
  onTextReplace?: IOnTextReplace
  onVoiceChannelBuild?: IOnVoiceChannelBuild
  onVoiceQuery?: IOnVoiceQuery
  onVoiceAnswer?: IOnData
  onVoiceTimeout?: IOnVoiceTimeout
  onVoiceAnswerSafetyFence?: IOnVoiceAnswerSafetyFence
  onVoiceQuerySafetyFence?: IOnVoiceQuerySafetyFence
}

type ResponseError = {
  code: string
  message: string
  status: number
  data?: string
}

type FetchOptionType = Omit<RequestInit, 'body'> & {
  params?: Record<string, any>
  body?: BodyInit | Record<string, any> | null
}
function unicodeToChar(text: string) {
  if (!text)
    return ''

  return text.replace(/\\u[0-9a-f]{4}/g, (_match, p1) => {
    return String.fromCharCode(parseInt(p1, 16))
  })
}
function requiredWebSSOLogin() {
  globalThis.location.href = `/webapp-signin?redirect_url=${globalThis.location.pathname}`
}

export function format(text: string) {
  let res = text.trim()
  if (res.startsWith('\n'))
    res = res.replace('\n', '')

  return res.replaceAll('\n', '<br/>').replaceAll('```', '')
}

// 处理流数据
const handleStream = (
  response: Response,
  onData: IOnData,
  onCompleted?: IOnCompleted,
  onThought?: IOnThought,
  onMessageEnd?: IOnMessageEnd,
  onMessageReplace?: IOnMessageReplace,
  onCheckFailedReplace?: IOnCheckFailedReplace,
  onFile?: IOnFile,
  onWorkflowStarted?: IOnWorkflowStarted,
  onWorkflowFinished?: IOnWorkflowFinished,
  onNodeStarted?: IOnNodeStarted,
  onNodeFinished?: IOnNodeFinished,
  onIterationStart?: IOnIterationStarted,
  onIterationNext?: IOnIterationNext,
  onIterationFinish?: IOnIterationFinished,
  onParallelBranchStarted?: IOnParallelBranchStarted,
  onParallelBranchFinished?: IOnParallelBranchFinished,
  onTextChunk?: IOnTextChunk,
  onTTSChunk?: IOnTTSChunk,
  onTTSEnd?: IOnTTSEnd,
  onTextReplace?: IOnTextReplace,
  onVoiceChannelBuild?: IOnVoiceChannelBuild,
  onVoiceQuery?: IOnVoiceQuery,
  onVoiceAnswer?: IOnData,
  onVoiceTimeout?: IOnVoiceTimeout,
  onVoiceAnswerSafetyFence?: IOnVoiceAnswerSafetyFence,
  onVoiceQuerySafetyFence?: IOnVoiceQuerySafetyFence,
) => {
  if (!response.ok)
    throw new Error('Network response was not ok')

  const reader = response.body?.getReader()
  const decoder = new TextDecoder('utf-8')
  let buffer = ''
  let bufferObj: Record<string, any>
  let isFirstMessage = true
  // 读取流数据，根据event类型来决定调用的回调方法
  function read() {
    let hasError = false
    reader?.read().then((result: any) => {
      if (result.done) {
        onCompleted && onCompleted()
        return
      }
      buffer += decoder.decode(result.value, { stream: true })
      const lines = buffer.split('\n')
      try {
        let jsonError = false
        lines.forEach((message) => {
          if (message.startsWith('data: ')) { // check if it starts with data:
            try {
              const jsonStr = message.substring(6)
              bufferObj = JSON.parse(jsonStr) as Record<string, any>
            }
            catch (e) {
              // mute handle message cut off
              onData('', isFirstMessage, {
                conversationId: bufferObj?.conversation_id,
                messageId: bufferObj?.message_id,
                taskId: bufferObj?.task_id,
              })
              jsonError = true
              return
            }
            if (bufferObj.status === 400 || !bufferObj.event) {
              onData('', false, {
                conversationId: undefined,
                messageId: '',
                taskId: '',
                errorMessage: bufferObj?.message,
                errorCode: bufferObj?.code,
              })
              hasError = true
              onCompleted?.(true, bufferObj?.message)
              return
            }
            if (bufferObj.event === 'message' || bufferObj.event === 'agent_message') {
              const handleFunc = onVoiceAnswer || onData
              if (!handleFunc)
                return
              // can not use format here. Because message is splitted.
              handleFunc(unicodeToChar(bufferObj.answer), isFirstMessage, {
                conversationId: bufferObj.conversation_id,
                taskId: bufferObj.task_id,
                messageId: bufferObj.id,
              })
              isFirstMessage = false
            }
            else if (bufferObj.event === 'agent_thought') {
              onThought?.(bufferObj as ThoughtItem)
            }
            else if (bufferObj.event === 'voice_channel_build') {
              onVoiceChannelBuild?.(bufferObj as VoiceChannelBuild)
            }
            else if (bufferObj.event === 'voice_channel_user_query') {
              onVoiceQuery?.(bufferObj as VoiceQuery)
            }
            else if (bufferObj.event === 'voice_channel_user_timeout') {
              onVoiceTimeout?.(bufferObj as VoiceTimeout)
            }
            else if (bufferObj.event === 'ping') {
              console.log('event ping')
            }
            else if (bufferObj.event === 'message_file') {
              onFile?.(bufferObj as VisionFile)
            }
            else if (bufferObj.event === 'message_end') {
              onMessageEnd?.(bufferObj as MessageEnd)
            }
            else if (bufferObj.event === 'message_replace') {
              onMessageReplace?.(bufferObj as MessageReplace)
            }
            else if (bufferObj.event === 'check_failed') {
              onCheckFailedReplace?.()
            }
            else if (bufferObj.event === 'workflow_started') {
              onWorkflowStarted?.(bufferObj as WorkflowStartedResponse)
            }
            else if (bufferObj.event === 'workflow_finished') {
              onWorkflowFinished?.(bufferObj as WorkflowFinishedResponse)
            }
            else if (bufferObj.event === 'node_started') {
              onNodeStarted?.(bufferObj as NodeStartedResponse)
            }
            else if (bufferObj.event === 'node_finished') {
              onNodeFinished?.(bufferObj as NodeFinishedResponse)
            }
            else if (bufferObj.event === 'iteration_started') {
              onIterationStart?.(bufferObj as IterationStartedResponse)
            }
            else if (bufferObj.event === 'iteration_next') {
              onIterationNext?.(bufferObj as IterationNextResponse)
            }
            else if (bufferObj.event === 'iteration_completed') {
              onIterationFinish?.(bufferObj as IterationFinishedResponse)
            }
            else if (bufferObj.event === 'parallel_branch_started') {
              onParallelBranchStarted?.(bufferObj as ParallelBranchStartedResponse)
            }
            else if (bufferObj.event === 'parallel_branch_finished') {
              onParallelBranchFinished?.(bufferObj as ParallelBranchFinishedResponse)
            }
            else if (bufferObj.event === 'text_chunk') {
              onTextChunk?.(bufferObj as TextChunkResponse)
            }
            else if (bufferObj.event === 'text_replace') {
              onTextReplace?.(bufferObj as TextReplaceResponse)
            }
            else if (bufferObj.event === 'tts_message') {
              onTTSChunk?.(bufferObj.message_id, bufferObj.audio, bufferObj.audio_type)
            }
            else if (bufferObj.event === 'tts_message_end') {
              onTTSEnd?.(bufferObj.message_id, bufferObj.audio)
            }
            else if (bufferObj.event === 'voice_channel_answer_safety_fence') {
              onVoiceAnswerSafetyFence?.(bufferObj as VoiceAnswerSafetyFence)
            }
            else if (bufferObj.event === 'voice_channel_query_safety_fence') {
              onVoiceQuerySafetyFence?.(bufferObj as VoiceQuerySafetyFence)
            }
          }
        })
        if (lines.length === 1 && !jsonError)
          buffer = ''
        else
          buffer = lines[lines.length - 1]
      }
      catch (e) {
        console.log(e, '1124测试catch e')
        // 这里处理聊天ID错误之类的后端报错信息
        onData('', false, {
          conversationId: undefined,
          messageId: '',
          taskId: '',
          errorMessage: `${e}`,
        })
        hasError = true
        onCompleted?.(true, e as string)
        return
      }
      // 接口没有报错的话继续接收流数据
      if (!hasError)
        read()
    }).catch(() => {
      // // 接收主动中止对话的报错
      // if(e.name === 'AbortError') {
      //   console.log('The user aborted a request', e)
      // }
    })
  }
  read()
}

// 基础请求
const baseFetch = <T>(
  url: string,
  fetchOptions: FetchOptionType,
  {
    isPublicAPI = false,
    bodyStringify = true,
    needAllResponseContent,
    deleteContentType,
    getAbortController,
    silent,
    sharedtoken,
  }: IOtherOptions,
): Promise<T> => {
  const options: typeof baseOptions & FetchOptionType = Object.assign({}, baseOptions, fetchOptions)
  const { method, params, body } = options
  // 先清理请求头
  options.headers.delete('Authorization')
  options.headers.delete('Auth-Token')
  options.headers.delete('operToken')

  if (getAbortController) {
    const abortController = new AbortController()
    getAbortController(abortController)
    options.signal = abortController.signal
  }
  // token取值类型
  if (isPublicAPI) {
    let sharedToken = sharedtoken || globalThis.location.pathname.split('/').slice(-1)[0]
    // 应用token
    const accessToken = localStorage.getItem('token') || JSON.stringify({ [sharedToken]: '' })

    // hq workflow 特殊处理
    if (sharedToken == 'workflow' && url == '/template-config') {
      const keys = Object.keys(JSON.parse(accessToken))
      sharedToken = keys[0]
    }
    // 账号token
    const authToken = localStorage.getItem('console_token') || ''
    let accessTokenJson = { [sharedToken]: '' }
    try {
      accessTokenJson = JSON.parse(accessToken)
    }
    catch (e) {
    }
    if (accessTokenJson[sharedToken])
      options.headers.set('Authorization', `Bearer ${accessTokenJson[sharedToken]}`)
    if (authToken && url != 'chat-messages') { // chat-messages特殊处理，标题优化时有Auth-Token会生成新的对话
      options.headers.set('Auth-Token', `Bearer ${authToken}`)
    }
  }
  else {
    const accessToken = localStorage.getItem('console_token') || ''
    if (accessToken)
      options.headers.set('Authorization', `Bearer ${accessToken}`)
    // 全局添加ssotoken
    if (localStorage.getItem('sso_token'))
      options.headers.set('operToken', localStorage.getItem('sso_token')!)
  }

  // 是否删除contentType
  if (deleteContentType) {
    options.headers.delete('Content-Type')
  }
  else {
    const contentType = options.headers.get('Content-Type')
    if (!contentType)
      options.headers.set('Content-Type', ContentType.json)
  }
  // 添加语言请求头
  options.headers.set('Accept-Language', `${getLocaleHeader()},zh;q=0.9,en;q=0.8`)

  const urlPrefix = isPublicAPI ? PUBLIC_API_PREFIX : API_PREFIX
  let urlWithPrefix = `${url.startsWith('http') ? url : urlPrefix + (url.startsWith('/') ? url : `/${url}`)}`
  // handle query
  if (method === 'GET' && params) {
    const paramsArray: string[] = []
    Object.keys(params).forEach(key =>
      paramsArray.push(`${key}=${encodeURIComponent(params[key])}`),
    )
    if (urlWithPrefix.search(/\?/) === -1)
      urlWithPrefix += `?${paramsArray.join('&')}`

    else
      urlWithPrefix += `&${paramsArray.join('&')}`
    delete options.params
  }

  if (body && bodyStringify)
    options.body = JSON.stringify(body)
  // 如果需要加密
  if (urlJudge(url)) {
    const key = generateAesKey()
    const iv = generateAesIv()
    // 生成加密后的数据
    if (body) {
      options.body = JSON.stringify({
        data: aesEncrypt(key, iv, JSON.stringify(body)),
      })
    }
    options.headers.set('gakey', rsaEncrypt(key))
    options.headers.set('gaiv', rsaEncrypt(iv))
  }
  else {
    options.headers.delete('gakey')
    options.headers.delete('gaiv')
  }

  // Handle timeout
  return Promise.race([
    new Promise((resolve, reject) => {
      setTimeout(() => {
        reject(new Error('request timeout'))
      }, TIME_OUT)
    }),
    new Promise((resolve, reject) => {
      globalThis.fetch(urlWithPrefix, options as RequestInit)
        .then((res) => {
          const resClone = res.clone()
          // Error handler
          if (!/^(2|3)\d{2}$/.test(String(res.status))) {
            const bodyJson = res.json()
            switch (res.status) {
              case 401: {
                if (isPublicAPI) {
                  return bodyJson.then((data: ResponseError) => {
                    if (data.code === 'unauthorized') {
                      removeAuth()
                      globalThis.location.reload()
                    }

                    return Promise.reject(data)
                  })
                }

                const loginUrl = `${globalThis.location.origin}/signin`
                bodyJson.then(async (data: ResponseError) => {
                  removeAuth()
                  // 接入Oauth登录
                  if (data.code === '4001') {
                    const params = qs.parse(location.search.slice(1))
                    if (params.code) {
                      const storageKey = 'oauth_temp_account_info'
                      const accountInfo = JSON.parse(localStorage.getItem(storageKey) || '{}')
                      const code = params.code as string
                      const accountId = accountInfo?.accountId || ''
                      const tokenRes = await getQueryUserInfoByCode({ accountId, code })
                      localStorage.setItem(storageKey, JSON.stringify(tokenRes.data))
                      // 注册成功，可以继续
                      if (tokenRes.code === '********') {
                        const newUrl = location.origin + location.pathname + location.search.replace(/code=[^&]+/, '')
                        localStorage.removeItem(storageKey)
                        history.replaceState(null, '', newUrl)
                        location.reload()
                      }
                      // 用户不存在，绑定邀请码
                      else if (tokenRes.code === '********') {
                        globalThis.location.href = `/register?code=${params.code}`
                      }
                      else {
                        Toast.notify({ type: 'error', message: data.message, duration: 4000 })
                      }
                    }
                    else {
                      globalThis.location.href = data.data || ''
                    }
                  }
                  else if (data.code === 'init_validate_failed' && IS_CE_EDITION && !silent) {
                    Toast.notify({ type: 'error', message: data.message, duration: 4000 })
                  }
                  else if (location.pathname !== '/signin' || !IS_CE_EDITION) {
                    globalThis.location.href = loginUrl
                  }
                  else if (!silent) {
                    Toast.notify({ type: 'error', message: data.message })
                  }
                }).catch(() => {
                  // Handle any other errors
                  globalThis.location.href = loginUrl
                })

                break
              }
              case 403:
                bodyJson.then((data: ResponseError) => {
                  if (!silent)
                    Toast.notify({ type: 'error', message: data.message })
                  if (data.code === 'already_setup')
                    globalThis.location.href = `${globalThis.location.origin}/signin`
                  if (data.code === 'user_removed_from_team')
                    globalThis.location.href = '/apps'
                })
                break
              // fall through
              default:
                bodyJson.then((data: ResponseError) => {
                  if (!silent)
                    Toast.notify({ type: 'error', message: data.message })
                })
            }
            return Promise.reject(resClone)
          }

          // handle delete api. Delete api not return content.
          if (res.status === 204) {
            resolve({ result: 'success' })
            return
          }

          // return data
          if (options.headers.get('Content-type') === ContentType.download || options.headers.get('Content-type') === ContentType.audio)
            resolve(needAllResponseContent ? resClone : res.blob())

          else resolve(needAllResponseContent ? resClone : res.json())
        })
        .catch((err) => {
          if (!silent)
            Toast.notify({ type: 'error', message: err })
          reject(err)
        })
    }),
  ]) as Promise<T>
}
// 文件上传
export const upload = (options: any, isPublicAPI?: boolean, url?: string, searchParams?: string): Promise<any> => {
  const urlPrefix = isPublicAPI ? PUBLIC_API_PREFIX : API_PREFIX
  let token = ''
  if (isPublicAPI) {
    const sharedToken = globalThis.location.pathname.split('/').slice(-1)[0]
    const accessToken = localStorage.getItem('token') || JSON.stringify({ [sharedToken]: '' })
    // 账号token
    const authToken = localStorage.getItem('console_token') || ''
    let accessTokenJson = { [sharedToken]: '' }
    try {
      accessTokenJson = JSON.parse(accessToken)
    }
    catch (e) {

    }
    token = accessTokenJson[sharedToken]
    if (authToken) {
      options = {
        ...options,
        headers: {
          ...options.headers,
          'Auth-Token': `Bearer ${authToken}`,
        },
      }
    }
  }
  else {
    const accessToken = localStorage.getItem('console_token') || ''
    token = accessToken
  }
  const defaultOptions = {
    method: 'POST',
    url: (url ? `${urlPrefix}${url}` : `${urlPrefix}/files/upload`) + (searchParams || ''),
    headers: {
      Authorization: `Bearer ${token}`,
    },
    data: {},
  }
  options = {
    ...defaultOptions,
    ...options,
    headers: { ...defaultOptions.headers, ...options.headers },
  }
  // 全局添加ssotoken
  if (localStorage.getItem('sso_token'))
    options.headers.operToken = localStorage.getItem('sso_token')
  // 添加语言请求头
  options.headers['Accept-Language'] = `${getLocaleHeader()},zh;q=0.9,en;q=0.8`
  return new Promise((resolve, reject) => {
    const xhr = options.xhr
    xhr.open(options.method, options.url)
    for (const key in options.headers)
      xhr.setRequestHeader(key, options.headers[key])

    xhr.withCredentials = true
    xhr.responseType = 'json'
    xhr.onreadystatechange = function () {
      if (xhr.readyState === 4) {
        if (xhr.status === 201 || xhr.status === 200)
          resolve(xhr.response)
        else
          reject(xhr?.response?.message)
      }
    }
    xhr.upload.onprogress = options.onprogress
    xhr.send(options.data)
  })
}

export const uploadThumb = (options: any, isPublicAPI?: boolean, url?: string, searchParams?: string): Promise<any> => {
  const urlPrefix = isPublicAPI ? PUBLIC_API_PREFIX : API_PREFIX
  let token = ''
  if (isPublicAPI) {
    const sharedToken = globalThis.location.pathname.split('/').slice(-1)[0]
    const accessToken = localStorage.getItem('token') || JSON.stringify({ [sharedToken]: '' })
    // 账号token
    const authToken = localStorage.getItem('console_token') || ''
    let accessTokenJson = { [sharedToken]: '' }
    try {
      accessTokenJson = JSON.parse(accessToken)
    }
    catch (e) {

    }
    token = accessTokenJson[sharedToken]
    if (authToken) {
      options = {
        ...options,
        headers: {
          ...options.headers,
          'Auth-Token': `Bearer ${authToken}`,
        },
      }
    }
  }
  else {
    const accessToken = localStorage.getItem('console_token') || ''
    token = accessToken
  }
  const defaultOptions = {
    method: 'POST',
    url: (url ? `${urlPrefix}${url}` : `${urlPrefix}/files/thumbnail`) + (searchParams || ''),
    headers: {
      Authorization: `Bearer ${token}`,
    },
    data: {},
  }
  options = {
    ...defaultOptions,
    ...options,
    headers: { ...defaultOptions.headers, ...options.headers },
  }
  // 全局添加ssotoken
  if (localStorage.getItem('sso_token'))
    options.headers.operToken = localStorage.getItem('sso_token')
  return new Promise((resolve, reject) => {
    const xhr = options.xhr
    xhr.open(options.method, options.url)
    for (const key in options.headers)
      xhr.setRequestHeader(key, options.headers[key])

    xhr.withCredentials = true
    xhr.responseType = 'json'
    xhr.onreadystatechange = function () {
      if (xhr.readyState === 4) {
        if (xhr.status === 201 || xhr.status === 200)
          resolve(xhr.response)
        else
          reject(xhr?.response?.message)
      }
    }
    xhr.upload.onprogress = options.onprogress
    xhr.send(options.data)
  })
}
// 实时推送请求
export const ssePost = (
  url: string,
  fetchOptions: FetchOptionType,
  {
    isPublicAPI = false,
    sharedtoken,
    onData,
    onCompleted,
    onThought,
    onFile,
    onMessageEnd,
    onMessageReplace,
    onCheckFailedReplace,
    onWorkflowStarted,
    onWorkflowFinished,
    onNodeStarted,
    onNodeFinished,
    onIterationStart,
    onIterationNext,
    onIterationFinish,
    onParallelBranchStarted,
    onParallelBranchFinished,
    onTextChunk,
    onTTSChunk,
    onTTSEnd,
    onTextReplace,
    onError,
    getAbortController,
    onVoiceChannelBuild,
    onVoiceQuery,
    onVoiceAnswer,
    onVoiceTimeout,
    onVoiceAnswerSafetyFence,
    onVoiceQuerySafetyFence,
  }: IOtherOptions,
) => {
  const abortController = new AbortController()

  const options = Object.assign({}, baseOptions, {
    method: 'POST',
    signal: abortController.signal,
  }, fetchOptions)
  // 先清理请求头
  options.headers.delete('Authorization')
  options.headers.delete('Auth-Token')
  options.headers.delete('operToken')

  const contentType = options.headers.get('Content-Type')
  if (!contentType)
    options.headers.set('Content-Type', ContentType.json)
  // 添加语言请求头
  options.headers.set('Accept-Language', `${getLocaleHeader()},zh;q=0.9,en;q=0.8`)

  getAbortController?.(abortController)
  if (isPublicAPI) {
    const sharedToken = sharedtoken || globalThis.location.pathname.split('/').slice(-1)[0]
    const accessToken = localStorage.getItem('token') || JSON.stringify({ [sharedToken]: '' })
    // 账号token
    const authToken = localStorage.getItem('console_token') || '' // 已有
    let accessTokenJson = { [sharedToken]: '' }
    try {
      accessTokenJson = JSON.parse(accessToken)
    }
    catch (e) {

    }
    if (accessTokenJson[sharedToken])
      options.headers.set('Authorization', `Bearer ${accessTokenJson[sharedToken]}`)
    if (authToken)
      options.headers.set('Auth-Token', `Bearer ${authToken}`)
  }
  else {
    const accessToken = localStorage.getItem('console_token') || ''
    // 全局添加应用token
    if (accessToken)
      options.headers.set('Authorization', `Bearer ${accessToken}`)
    // 全局添加ssotoken
    if (localStorage.getItem('sso_token'))
      options.headers.set('operToken', localStorage.getItem('sso_token')!)
  }

  const urlPrefix = isPublicAPI ? PUBLIC_API_PREFIX : API_PREFIX
  const urlWithPrefix = `${urlPrefix}${url.startsWith('/') ? url : `/${url}`}`

  const { body } = options
  if (body)
    options.body = JSON.stringify(body)
  globalThis.fetch(urlWithPrefix, options as RequestInit)
    .then((res) => {
      if (!/^(2|3)\d{2}$/.test(String(res.status))) {
        let resData: any
        res.json().then((data: any) => {
          resData = data
          if (isPublicAPI) {
            if (data.code === 'web_sso_auth_required')
              requiredWebSSOLogin()
            if (data.code === 'unauthorized') {
              removeAuth()
              globalThis.location.reload()
            }
            if (res.status === 401)
              return res
          }
          Toast.notify({ type: 'error', message: data.message || 'Server Error' })
        }).finally(() => {
          // 图文审核不通过，返回错误信息 data.code = 'checkcontent_result'
          onError?.(resData?.code || 'Server Error')
        })
        return
      }
      // 流式接口实现
      return handleStream(res, (str: string, isFirstMessage: boolean, moreInfo: IOnDataMoreInfo) => {
        // console.log(685, str, isFirstMessage, moreInfo);
        if (moreInfo.errorMessage) {
          onError?.(moreInfo.errorMessage, moreInfo.errorCode)
          // TypeError: Cannot assign to read only property ... will happen in page leave, so it should be ignored.
          if (moreInfo.errorMessage !== 'AbortError: The user aborted a request.' && !moreInfo.errorMessage.includes('TypeError: Cannot assign to read only property'))
            Toast.notify({ type: 'error', message: moreInfo.errorMessage })
          return
        }
        onData?.(str, isFirstMessage, moreInfo)
      },
      onCompleted,
      onThought,
      onMessageEnd,
      onMessageReplace,
      onCheckFailedReplace,
      onFile,
      onWorkflowStarted,
      onWorkflowFinished,
      onNodeStarted,
      onNodeFinished,
      onIterationStart,
      onIterationNext,
      onIterationFinish,
      onParallelBranchStarted,
      onParallelBranchFinished,
      onTextChunk,
      onTTSChunk,
      onTTSEnd,
      onTextReplace,
      onVoiceChannelBuild,
      onVoiceQuery,
      onVoiceAnswer,
      onVoiceTimeout,
      onVoiceAnswerSafetyFence,
      onVoiceQuerySafetyFence,
      )
    }).catch((e) => {
      if (e.name === 'AbortError') {
        // 接收主动中止对话的报错，有时候会在handleStream里捕获，有时候会在这里捕获，不确定原因
        // console.log('The user aborted a request', e)
      }
      else {
        // if (e.toString() !== 'AbortError: The user aborted a request.' && !e.toString().errorMessage.includes('TypeError: Cannot assign to read only property'))
        //   Toast.notify({ type: 'error', message: e })
        onError?.(e)
      }
    })
}

// base request
export const request = <T>(url: string, options = {}, otherOptions?: IOtherOptions) => {
  return baseFetch<T>(url, options, otherOptions || {})
}
// request methods
export const get = <T>(url: string, options = {}, otherOptions?: IOtherOptions) => {
  return request<T>(url, Object.assign({}, options, { method: 'GET' }), otherOptions)
}
export const post = <T>(url: string, options = {}, otherOptions?: IOtherOptions) => {
  return request<T>(url, Object.assign({}, options, { method: 'POST' }), otherOptions)
}
export const put = <T>(url: string, options = {}, otherOptions?: IOtherOptions) => {
  return request<T>(url, Object.assign({}, options, { method: 'PUT' }), otherOptions)
}
export const del = <T>(url: string, options = {}, otherOptions?: IOtherOptions) => {
  return request<T>(url, Object.assign({}, options, { method: 'DELETE' }), otherOptions)
}
export const patch = <T>(url: string, options = {}, otherOptions?: IOtherOptions) => {
  return request<T>(url, Object.assign({}, options, { method: 'POST' }), otherOptions)
}

// For public API
export const getPublic = <T>(url: string, options = {}, otherOptions?: IOtherOptions) => {
  return get<T>(url, options, { ...otherOptions, isPublicAPI: true })
}
export const patchPublic = <T>(url: string, options = {}, otherOptions?: IOtherOptions) => {
  return patch<T>(url, options, { ...otherOptions, isPublicAPI: true })
}
export const delPublic = <T>(url: string, options = {}, otherOptions?: IOtherOptions) => {
  return del<T>(url, options, { ...otherOptions, isPublicAPI: true })
}
export const putPublic = <T>(url: string, options = {}, otherOptions?: IOtherOptions) => {
  return put<T>(url, options, { ...otherOptions, isPublicAPI: true })
}
export const postPublic = <T>(url: string, options = {}, otherOptions?: IOtherOptions) => {
  return post<T>(url, options, { ...otherOptions, isPublicAPI: true })
}
