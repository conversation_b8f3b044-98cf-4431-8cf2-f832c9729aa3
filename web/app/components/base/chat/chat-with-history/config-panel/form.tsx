import { useTranslation } from 'react-i18next'
import { ContainerOutlined, FileTextOutlined } from '@ant-design/icons'
import { useEffect, useState } from 'react'
import {
  message,
} from 'antd'
import { useChatWithHistoryContext } from '../context'
import Input from './form-input'
import TemplatePage from './comments/TemplatePage'
import TypeSelector from './comments/topicType'
import { InputVarType } from '@/app/components/workflow/types'
// 公共能力
import { PortalSelect } from '@/app/components/base/select'
import '@/app/styles/switchLongTxt.scss'
import { FileUploaderInAttachmentWrapper } from '@/app/components/base/file-uploader'
import Button from '@/app/components/base/button'
import style from '@/app/components/base/chat/chat-with-history/styles/style.module.css'

const Form = () => {
  const iconMap = {
    template: <ContainerOutlined />,
    topic: <FileTextOutlined />,
  }
  const { t } = useTranslation()
  const {
    appData,
    inputsForms,
    newConversationInputs,
    newConversationInputsRef,
    handleNewConversationInputsChange,
    isMobile,
    chatAllTypeResult,
    setChatAllTypeResult,
  } = useChatWithHistoryContext()
  const [selectedType, setSelectedType] = useState<string>('')
  const [selectedchildType, setSelectedchildType] = useState<string>('')
  const [selectedposType, setSelectedposType] = useState<any>(0)

  // 输入表单值变化
  const handleFormChange = (variable: string, value: any) => {
    handleNewConversationInputsChange({
      ...newConversationInputsRef.current,
      [variable]: value,
    })
  }

  useEffect(() => {
    if (Object.keys(chatAllTypeResult).length === 0)
      setSelectedType('')
  }, [chatAllTypeResult])

  // 渲染字段
  const renderField = (form: any) => {
    const { label, required, variable, options } = form

    if (form.type === 'text-input' || form.type === 'paragraph') {
      return (
        <Input form={form} value={newConversationInputs[variable]} onChange={handleFormChange} />
      )
    }
    if (form.type === 'number') {
      return (
        <input
          className="grow h-9 rounded-lg bg-gray-100 px-2.5 outline-none appearance-none"
          type="number"
          value={newConversationInputs[variable] || ''}
          onChange={e => handleFormChange(variable, e.target.value)}
          placeholder={`${label}${!required ? `(${t('appDebug.variableTable.optional')})` : ''}`}
        />
      )
    }
    if (form.type === InputVarType.singleFile) {
      return (
        <div className='grow'>
          <FileUploaderInAttachmentWrapper
            value={newConversationInputs[variable] ? [newConversationInputs[variable]] : []}
            onChange={files => handleFormChange(variable, files[0])}
            fileConfig={{
              allowed_file_types: form.allowed_file_types,
              allowed_file_extensions: form.allowed_file_extensions,
              allowed_file_upload_methods: form.allowed_file_upload_methods,
              number_limits: 1,
            }}
          />
        </div>
      )
    }
    if (form.type === InputVarType.multiFiles) {
      return (
        <div className='grow'>
          <FileUploaderInAttachmentWrapper
            value={newConversationInputs[variable]}
            onChange={files => handleFormChange(variable, files)}
            fileConfig={{
              allowed_file_types: form.allowed_file_types,
              allowed_file_extensions: form.allowed_file_extensions,
              allowed_file_upload_methods: form.allowed_file_upload_methods,
              number_limits: form.max_length,
            }}
          />
        </div>
      )
    }

    return (
      <PortalSelect
        popupClassName="w-[200px]"
        value={newConversationInputs[variable]}
        items={options.map((option: string) => ({ value: option, name: option }))}
        onSelect={item => handleFormChange(variable, item.value as string)}
        placeholder={`${label}${!required ? `(${t('appDebug.variableTable.optional')})` : ''}`}
      />
    )
  }

  useEffect(() => {
    if (Object.keys(chatAllTypeResult).length === 0) {
      setSelectedType('')
      setSelectedchildType('')
      setSelectedposType(0)
    }
  }, [chatAllTypeResult])

  useEffect(() => {
    if (selectedType != '')
      setSelectedchildType('')
  }, [selectedType])

  const selectedFn = (type: any) => {
    if (type == 0) {
      setSelectedposType(type)
    }
    else if (type == 1) {
      if (selectedType)
        setSelectedposType(type)
      else
        message.warning(`${t('sampleTemplate.longText.warn1')}`)
    }
    else {
      if (selectedType) {
        if (selectedchildType != 'Report') {
          if (selectedType == t('sampleTemplate.IntelligentAgent.CreationTypeTemplate'))
            message.warning(`${t('sampleTemplate.longText.warn2')}`)
          else
            message.warning(`${t('sampleTemplate.longText.warn3')}`)
        }
        else {
          setSelectedposType(type)
        }
      }
      else {
        message.warning(`${t('sampleTemplate.longText.warn1')}`)
      }
    }
  }

  // 渲染长文档表单
  const renderLongTextForm = () => {
    return (
      <div className="long-txt-container">
        <div className={`xq-nowrap xq-flex-vcenter ${style.pro}`}>
          {selectedposType == 0 ? <div className={`${style.pross} ${style.prossOn}`}>Step1 {t('sampleTemplate.longText.step1')}</div> : <div onClick={() => selectedFn(0)} className={`${style.pross} ${style.prossOng}`}><span>Step1</span> {t('sampleTemplate.longText.step1')}</div>}
          <div className={`${style.prom}`}>
            {selectedposType == 0
              ? <svg className={`${style.prosvg}`} xmlns="http://www.w3.org/2000/svg" width="19" height="6" viewBox="0 0 19 6" fill="none">
                <path opacity="0.4" d="M0.000797272 6H3.44437L5.99556 2.99737L3.43388 0.00525437H0.000797272L2.54149 2.97638L0.000797272 6ZM6.31052 5.99476H9.75411L12.3053 2.99213L9.74357 0H6.31052L8.8512 2.97113L6.31052 5.99476ZM12.5677 5.99476H16.0113L18.5625 2.99213L16.0008 0H12.5677L15.1084 2.97113L12.5677 5.99476Z" fill="#BCC9EA"/>
              </svg>
              : <svg xmlns="http://www.w3.org/2000/svg" width="19" height="6" viewBox="0 0 19 6" fill="none">
                <path d="M0.000797272 6H3.44437L5.99556 2.99737L3.43388 0.00525437H0.000797272L2.54149 2.97638L0.000797272 6ZM6.31052 5.99476H9.75411L12.3053 2.99213L9.74357 0H6.31052L8.8512 2.97113L6.31052 5.99476ZM12.5677 5.99476H16.0113L18.5625 2.99213L16.0008 0H12.5677L15.1084 2.97113L12.5677 5.99476Z" fill="#BCC9EA"/>
              </svg>
            }
          </div>
          {selectedposType == '1'
            ? <div className={`${style.pross} ${style.prossOn}`}><span>Step2</span> {t('sampleTemplate.longText.step2')}</div>
            : <div className={`${style.pross} ${selectedposType == 2 ? style.prossDiv : style.prossDivHey}`} onClick={() => selectedFn(1)}><span>Step2</span> {t('sampleTemplate.longText.step2')}</div>
          }

          <div className={`${style.prom}`}>
            {selectedposType == 2
              ? <svg className={`${style.prosvg}`} xmlns="http://www.w3.org/2000/svg" width="19" height="6" viewBox="0 0 19 6" fill="none">
                <path d="M0.000797272 6H3.44437L5.99556 2.99737L3.43388 0.00525437H0.000797272L2.54149 2.97638L0.000797272 6ZM6.31052 5.99476H9.75411L12.3053 2.99213L9.74357 0H6.31052L8.8512 2.97113L6.31052 5.99476ZM12.5677 5.99476H16.0113L18.5625 2.99213L16.0008 0H12.5677L15.1084 2.97113L12.5677 5.99476Z" fill="#BCC9EA"/>
              </svg>
              : <svg xmlns="http://www.w3.org/2000/svg" width="19" height="6" viewBox="0 0 19 6" fill="none">
                <path opacity="0.4" d="M0.000797272 6H3.44437L5.99556 2.99737L3.43388 0.00525437H0.000797272L2.54149 2.97638L0.000797272 6ZM6.31052 5.99476H9.75411L12.3053 2.99213L9.74357 0H6.31052L8.8512 2.97113L6.31052 5.99476ZM12.5677 5.99476H16.0113L18.5625 2.99213L16.0008 0H12.5677L15.1084 2.97113L12.5677 5.99476Z" fill="#BCC9EA"/>
              </svg>
            }
          </div>
          {selectedposType == '2'
            ? <div className={`${style.pross} ${style.prossOn}`}><span>Step3</span> {t('sampleTemplate.longText.step3')}</div>
            : <div className={`${style.pross} ${style.prossDivHey}`} onClick={() => selectedFn(2)}><span>Step3</span> {t('sampleTemplate.longText.step3')}</div>
          }
        </div>

        <div>
          {selectedposType == '0'
        && <>
          <div className={`${style.smallTel}`}><span>Step1</span>{t('sampleTemplate.IntelligentAgent.CreationType')}</div>
          <div>
            {(
              t('sampleTemplate.IntelligentAgent.CreationTypeOptions', {
                returnObjects: true,
              }) as any[]
            ).map((item: any, index: number) => (
              <Button
                key={index}
                variant="secondary-accent"
                size="small"
                className={`shrink-0 buttons ${selectedType === item.text ? 'active' : ''} ${style.abtn}`}
                onClick={() => {
                  setChatAllTypeResult({})
                  setSelectedType(item.text)
                  setChatAllTypeResult({ type: item })
                  setSelectedposType(1)
                }}
              >
                {/* <i className="mr-2 f16">{iconMap[item.icon as keyof typeof iconMap]}</i> */}
                {index == 0 ? <img src='/assets/fwicon0.svg' /> : <img src='/assets/fwicon1.svg' />}
                {item.text}
              </Button>
            ))}
          </div>
        </>
          }

          {selectedType && chatAllTypeResult
            ? (
              selectedType === t('sampleTemplate.IntelligentAgent.CreationTypeTemplate')
                ? (
                  <TemplatePage
                    selectedType={selectedType}
                    selectedchildType={selectedchildType}
                    selectedposType={selectedposType}
                    onTypeSelect={(type, item) => {
                      switch (type) {
                        case 'topic':
                          setChatAllTypeResult({
                            topicType: item,
                            form: {},
                            files: {},
                            reportType: {},
                          })
                          setSelectedchildType('topic')
                          break
                        case 'Report':
                          setChatAllTypeResult({ reportType: item })
                          setSelectedchildType('Report')
                          break
                        default:
                          break
                      }
                    }}
                    onChildTypeSelect={(item, pos) => {
                      setSelectedposType(pos)
                    }}
                  />
                )
                : selectedType === t('sampleTemplate.IntelligentAgent.CreationTypeTopic')
                  ? (
                    <div>
                      <TypeSelector
                        selectedType={selectedType}
                        selectedchildType={selectedchildType}
                        selectedposType={selectedposType}
                        onTypeSelect={(type, item) => {
                          switch (type) {
                            case 'topic':
                              setChatAllTypeResult({
                                topicType: item,
                                form: {},
                                reportType: {},
                              })
                              setSelectedchildType('topic')
                              break
                            case 'Report':
                              setChatAllTypeResult({ reportType: item })
                              setSelectedchildType('Report')
                              break
                            default:
                              break
                          }
                        }}
                        onChildTypeSelect={(item, pos) => {
                          setSelectedposType(pos)
                        }}
                      />
                    </div>
                  )
                  : null
            )
            : null}
        </div>
      </div>
    )
  }
  // 渲染通用表单
  const renderRegularForms = () => {
    if (!inputsForms.length)
      return null
    return inputsForms.map((form, index) => (
      <div
        key={`${form.variable}_${index}`}
        className={`flex mb-3 last-of-type:mb-0 text-sm text-gray-900 ${isMobile && '!flex-wrap'}`}
      >
        <div className={`shrink-0 mr-2 pb-2  ${isMobile && '!w-full'}`}>{form.label}</div>
        {renderField(form)}
      </div>
    ))
  }

  return (
    <div>
      {inputsForms.some(form => form.type === 'longTxt')
        ? renderLongTextForm()
        : renderRegularForms()}
    </div>
  )
}

export default Form
