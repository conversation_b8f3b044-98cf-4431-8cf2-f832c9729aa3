'use client'

import type { ReactNode } from 'react'
import { createContext, useContext, useContextSelector } from 'use-context-selector'
import type { ChatProps } from './index'

export type ChatContextValue = Pick<
ChatProps,
  | 'config'
  | 'isResponding'
  | 'noChatInput'
  | 'chatList'
  | 'showPromptLog'
  | 'questionIcon'
  | 'answerIcon'
  | 'onSend'
  | 'onRegenerate'
  | 'onAnnotationEdited'
  | 'onAnnotationAdded'
  | 'onAnnotationRemoved'
  | 'onFeedback'
  | 'showInputBottomTip'
  | 'isVoiceCall'
  | 'onVoiceCall'
  | 'onCancelVoiceCall'
  | 'onStopResponding'
  | 'handleNewConversation'
  | 'isConnecting'
  | 'isThinking'
  | 'onPlayVoice'
> & {
  isLongWeb?: boolean
}

const ChatContext = createContext<ChatContextValue>({
  chatList: [],
})

type ChatContextProviderProps = {
  children: ReactNode
} & ChatContextValue

export const ChatContextProvider = ({
  children,
  config,
  chatList,
  showPromptLog,
  questionIcon,
  answerIcon,

  // 决策部分
  isResponding,
  isVoiceCall,
  showInputBottomTip = true,
  isConnecting,
  isThinking,
  isLongWeb,
  noChatInput,
  // 事件部分
  onSend,
  onStopResponding,
  onRegenerate,
  onAnnotationEdited,
  onAnnotationAdded,
  onAnnotationRemoved,
  onFeedback,
  onVoiceCall,
  onCancelVoiceCall,
  handleNewConversation,
  onPlayVoice,
}: ChatContextProviderProps) => {
  return (
    <ChatContext.Provider value={{
      config,
      chatList: chatList || [],
      showPromptLog,
      questionIcon,
      answerIcon,
      showInputBottomTip,
      isResponding,
      isVoiceCall,
      isConnecting,
      isThinking,
      isLongWeb,
      noChatInput,
      onSend,
      onRegenerate,
      onAnnotationEdited,
      onAnnotationAdded,
      onAnnotationRemoved,
      onFeedback,
      onVoiceCall,
      onCancelVoiceCall,
      onStopResponding,
      handleNewConversation,
      onPlayVoice,
    }}>
      {children}
    </ChatContext.Provider>
  )
}

export const useChatContext = () => useContext(ChatContext)

// 自定义 Hook，用于选择性订阅
// eslint-disable-next-line @typescript-eslint/comma-dangle
export const useChatContextSelector = <T,>(selector: (state: ChatContextValue) => T): T =>
  useContextSelector(ChatContext, selector)

export default ChatContext
