import type { Fetcher } from 'swr'
import { del, get, getPublic, patch, post, put } from './base'
import type {
  ApiBasedExtension,
  CodeBasedExtension,
  CommonResponse,
  DataSourceNotion,
  FileUploadConfigResponse,
  ICurrentWorkspace,
  IWorkspace,
  InvitationResponse,
  LangGeniusVersionResponse,
  Member,
  QueryUserInfoByCodeType,
  SetupStatusResponse,
  SsoCommonResponse,
  UserProfileOriginResponse,
  QueryTeamList,
  QueryTeamDataType,
  QueryTeamMemberList,
} from '@/models/common'

type TeamUpdateType = {
  team_id: string
  team_name: string 
}
/* 团队相关接口--start */
// 查询团队空间
// export const getTeamList: Fetcher<QueryTeamDataType, QueryTeamList> = ({ params }) => {
//   return get<QueryTeamDataType>(`/team/list?search=${params?.size}&page=${params?.page}&size=${params?.size}`)
// }
export const getTeamList = (params: QueryTeamList) => {
  // const team_name  = params?.team_name || ''
  return get<QueryTeamDataType>(`/team/list?search=${params?.search}&page=${params?.page}&size=${params?.size}`)
}
// 编辑修改团队名称接口
export const putTeamUpdate = (body: TeamUpdateType) => {
  return put<SsoCommonResponse>('/team/update', { body })
}
// 新增团队空间
export const setTeamCreate = (body: Record<string, any>) => {
  return post<QueryTeamDataType>('/team/create', { body })
}
// 查询成员接口
export const getAccountQuery: Fetcher<QueryTeamDataType, QueryTeamList> = ({ data }) => {
  const user_id = data?.user_id
  const page = data?.page
  const size = data?.size
  const search = data?.search
  let url = `search=${search}&page=${page}&size=${size}&user_id=${user_id}`
  if (data.team_id)
    url = `search=${search}&page=${page}&size=${size}&user_id=${user_id}&team_id=${data.team_id}`
  return get<QueryTeamDataType>(`/account/query?${url}`)
}
// 解散团队接口（删除团队空间）
export const deleteTeam: Fetcher<SsoCommonResponse, string> = (body) => {
  return del<SsoCommonResponse>('/team/delete', { body })
}
// 子账号退出团队接口
export const deleteTeamExit: Fetcher<SsoCommonResponse, string> = (body) => {
  return del<SsoCommonResponse>('/team/exit', { body })
}
// 查看团队成员列表
export const getTeamMemberList = (params: QueryTeamMemberList) => {
  const QueryValue = `team_id=${params?.team_id}&role=${params?.role}&search=${params?.search}&page=${params?.page}&size=${params?.size}`
  return get<QueryTeamDataType>(`/team/member/list?${QueryValue}`)
}
// 新增团队成员接口：POST /console/api/team/member/add
export const setTeamMemberAdd = (body: Record<string, any>) => {
  return post<QueryTeamDataType>('/team/member/add', { body })
}

// 角色变更接口（修改团队成员）： PUT /console/api/team/member/update
export const putTeamMemberUpdate = (body: TeamUpdateType) => {
  return put<SsoCommonResponse>('/team/member/update', { body })
}
// 移除接口（删除团队成员）： DELETE /console/api/team/member/delete
export const deleteTeamMember = (body: QueryTeamMemberList) => {
  return del<SsoCommonResponse>('/team/member/delete', { body })
}

/* 团队相关接口--end */