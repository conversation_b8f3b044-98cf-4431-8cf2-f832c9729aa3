.long-txt-container {
  font-size: 14px;
  .buttons {
    margin:10px 12px 0 0;
    padding: 15px 25px;
    border: 1px solid #D9DCE3;
    color: #181818;
    border-radius: 4px;
    cursor: pointer;
    font-size:13px;
    outline: none;
    transition: all 0.3s;
  }
  .buttons:nth-child(2) {
    margin-right: 0;
  }

  .buttons:hover,.buttons.active {
    background-color: #f1f7ff;
    color: #3168f5;
    border-color: #007bff;
  }
}


.long-txt-containerflow {
  font-size: 14px;
  .buttons {
    margin-top: 10px;
    padding: 15px 10px;
    border: 1px solid #ccc;
    color: #181818;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    outline: none;
    margin-right:4px;
    transition: all 0.3s;
  }
  .buttons:nth-child(2) {
    margin-right: 0;
  }

  .buttons.active {
    background-color: #f1f7ff;
    color: #3168f5;
    border-color: #007bff;
  }
}

.TopicType {
  font-size: 14px;
  .FieldBefore {
    .title-14-24::before {
      content: "*";
      color: #ff4d4f;
      margin-right: 4px;
    }
  }

  .TopicButtons {
    margin: 10px 0 0 0;
    padding: 15px 25px;
    border: 1px solid #ccc;
    color: #181818;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    outline: none;
    margin-right: 16px;
    transition: all 0.3s;
  }

  .TopicButtons:hover,.TopicButtons.TopicActive {
    background-color: #f1f7ff;
    color: #3168f5;
    border-color: #007bff;
  }
  :where(.ant-picker-outlined) {
    width: 100%;
  }
}

.CheckCardList {
  .long-span-Tabs:nth-child(5n) {
    // .ant-pro-checkcard {
    //   margin-inline-end: 0 !important;
    // }
  }
  .ant-pro-checkcard-group {
    width: 100% !important;
  }
  .long-span-Tabs {
    display: inline-block;
    .ant-pro-checkcard {
      margin-inline-end: 11px !important;
    }
    .ant-pro-checkcard:nth-child(5n) {
      margin-inline-end: 0 !important;
    }

    .ant-pro-checkcard-avatar {
      position: absolute;
      top: 60%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .ant-pro-checkcard-title{
    white-space: normal;
    display: -webkit-box !important;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-all;
  }
  :where(.ant-upload-wrapper.ant-upload-picture-card-wrapper),
  :where(.ant-upload-wrapper.ant-upload-picture-circle-wrapper) {
    display: -webkit-inline-box !important;
  }
  :where(.ant-upload-wrapper.ant-upload-picture-card-wrapper .ant-upload.ant-upload-select),
  :where(.ant-upload-wrapper.ant-upload-picture-circle-wrapper) .ant-upload.ant-upload-select {
    width: 134px !important;
    height: 200px !important;
  }
  :where(.ant-upload-wrapper.ant-upload-picture-card-wrapper .ant-upload.ant-upload-select),
  :where(.ant-upload-wrapper.ant-upload-picture-circle-wrapper .ant-upload.ant-upload-select) {
    width: 134px !important;
    height: 200px !important;
  }
  :where(
      .ant-upload-wrapper.ant-upload-picture-card-wrapper
        .ant-upload-list.ant-upload-list-picture-card
        .ant-upload-list-item-container
    ),
  :where(
      .ant-upload-wrapper.ant-upload-picture-circle-wrapper
        .ant-upload-list.ant-upload-list-picture-card
        .ant-upload-list-item-container
    ),
  :where(
      .ant-upload-wrapper.ant-upload-picture-card-wrapper
        .ant-upload-list.ant-upload-list-picture-circle
        .ant-upload-list-item-container
    ),
  :where(
      .ant-upload-wrapper.ant-upload-picture-circle-wrapper
        .ant-upload-list.ant-upload-list-picture-circle
        .ant-upload-list-item-container
    ) {
    width: 134px !important;
    height: 200px !important;
  }
  :where(
      .ant-upload-wrapper.ant-upload-picture-card-wrapper
        .ant-upload-list.ant-upload-list-picture-card
        .ant-upload-list-item-container
    ),
  :where(
      .ant-upload-wrapper.ant-upload-picture-circle-wrapper
        .ant-upload-list.ant-upload-list-picture-card
        .ant-upload-list-item-container
    ),
  :where(
      .ant-upload-wrapper.ant-upload-picture-card-wrapper
        .ant-upload-list.ant-upload-list-picture-circle
        .ant-upload-list-item-container
    ),
  :where(
      .ant-upload-wrapper.ant-upload-picture-circle-wrapper
        .ant-upload-list.ant-upload-list-picture-circle
        .ant-upload-list-item-container
    ) {
    width: 134px !important;
    height: 200px !important;
  }
  :where(.ant-upload-wrapper.ant-upload-picture-card-wrapper),
  :where(.ant-upload-wrapper.ant-upload-picture-circle-wrapper) {
    display: inline-flex !important;
  }
}

.content {
  width: 100% !important;
  margin: 22px 0 15px !important;
  border-radius: 4px !important;
  .long-txt-Tabs {
    .ant-tabs-nav::before {
      border-bottom: 0px solid #000 !important;
    }
    .ant-pro-checkcard {
      margin-inline-end: 0 !important;
      margin-block-end: 5px !important;
    }
    .ant-pro-checkcard-avatar-header {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      height: 100% !important;
    }
    .ant-pro-checkcard-avatar {
      padding-inline-end: 0px !important;
    }
    .ant-pro-checkcard-content {
      padding-inline: 0px !important;
      padding-block: 0px !important;
    }
    .ant-pro-checkcard-detail {
      width: 0 !important;
    }
    :where(.ant-avatar > img) {
      object-fit: contain !important;
    }
  }
}

.custom-tooltip {
  max-width: 500px !important;
}

.docDiv{
  border:1px solid #D9DCE3;
  border-radius:4px;
  width:135px;
  height:120px;
  flex-shrink: 0;
  background: #FFF;
  padding:15px;
  margin-right:12px;
}
.docpic{
  width:32px;
  height:32px;
}
.docName{
font-size: 12px;
font-style: normal;
font-weight: 500;
line-height: 20px; /* 153.846% */
color:#181818;
margin-top:10px;
white-space: normal;
display: -webkit-box;
-webkit-line-clamp: 2;
-webkit-box-orient: vertical;
overflow: hidden;
font-weight:bold;
}
.image-uploader{
  border-radius:4px;
  width:135px;
  height:120px;
  border:1px dashed #D9DCE3;
  overflow: hidden;
}
.image-uploader .ant-upload-list .ant-upload{
  flex:1 !important;
  height:118px!important;
  background:#fff !important;
  border:none !important;
}

.upword{
  color:#5C6273;
  font-size: 12px;
}

.image-other{
  border-radius:4px;
  width:150px;
  height:150px;
  //border:1px dashed #D9DCE3;
  overflow: hidden;
}
.image-other .ant-upload-list .ant-upload{
  flex:1 !important;
  height:150px!important;
  background:#fff !important;
  //border:none !important;
  overflow: hidden;
}
.image-other .ant-upload-list-picture-card{
  width:100% !important;
}
.image-other .ant-upload-list-item-container{
  width:150px !important;
  height:150px !important;
}

.docHas{
  // border:1px solid #D9DCE3;
  border-radius:4px;
  width:150px;
  height:150px;
  flex-shrink: 0;
  padding:0px;
  margin-right:12px;
  position:relative;
  overflow: hidden;
  cursor: pointer;
}
.docHasIco{
  width:16px;
  height:16px;
}
.defaultpic{
  position:absolute;
  left:0;
  top:30px;
  width:150px;
  height:150px;
  z-index:0;
  overflow:hidden;
}
.defaultpic img{
  width:100%;
  opacity:0.7;
}
.docHasChild{
  background: linear-gradient(to bottom, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 1) 40%,rgba(255, 255, 255, 0.7) 50%, rgba(255, 255, 255, 0) 80%,rgba(255, 255, 255, 0));
  //background:red;
  width:150px;
  height:150px;
  padding:15px;
  z-index:99;
  position:relative;
}
.docMt{
  margin-top:4px !important;
}
.long-span-Tabs{
  width:150px;
  height:150px;
  flex-shrink: 0;
  margin-right: 12px;
}
.docHas .ant-pro-checkcard-content{
  padding-inline:0 !important;
  padding-block:0!important;
}
.docHas .ant-pro-checkcard-checked{
  position:relative;
  z-index:199;
}
.docHas .ant-pro-checkcard:after{
  z-index:1999;
}
.ant-pro-checkcard:after{
  z-index: 211;
}
.rollingDiv{
  max-height: calc(100vh - 480px);
  overflow-y:scroll;
  padding-right:8px;
}

.flowbtn{
  margin:10px 5px 0 0 !important;
  padding:10px!important;
}
.Fieldbtn.FieldBefore::after{
  display: none!important;
}
.long-span-Tabs-div{
  width:150px;
  height:150px;
  flex-shrink: 0;
  margin:0px 8px 8px 0px!important;
}
.zyDiv{
  border:1px solid #D9DCE3;
  border-radius:4px;
  padding:5px 8px;
  margin:10px 0 0 0;
}
.lnDiv{
  line-height:20px;
  margin-top:10px;
}
.rightDiv{
  padding:0 10px;
}
.midDiv{
  width:100%;
  height:700px;
}

.selectDiv{
  border-color:red !important;
}