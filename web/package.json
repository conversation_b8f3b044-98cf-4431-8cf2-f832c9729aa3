{"name": "agent-web", "version": "0.10.0", "private": true, "engines": {"node": ">=18.17.0"}, "scripts": {"dev": "next dev", "dev-gray": "cross-env GRAY=true next dev", "build": "cross-env NODE_OPTIONS=--max-old-space-size=4096 next build", "local-build": "cross-env LOCAL=true next build", "start": "cp -r -f .next/static .next/standalone/.next/static && cp -r -f public .next/standalone/public && cross-env PORT=$npm_config_port HOSTNAME=$npm_config_host node .next/standalone/server.js", "local-start": "xcopy .next\\static .next\\standalone\\.next\\static /E /I /Y && xcopy public .next\\standalone\\public /E /I /Y && cross-env PORT=$npm_config_port HOSTNAME=$npm_config_host node .next\\standalone\\server.js", "lint": "next lint", "fix": "next lint --fix", "eslint-fix": "eslint --fix", "prepare": "cd ../ && node -e \"if (process.env.NODE_ENV !== 'production'){process.exit(1)} \" || husky install ./web/.husky", "gen-icons": "node ./app/components/base/icons/script.js", "uglify-embed": "node ./bin/uglify-embed", "check-i18n": "node ./i18n/check-i18n.js", "find-chinese": "node ./i18n/find-chinese.js", "auto-gen-i18n": "node ./i18n/auto-gen-i18n.js", "analyze": "cross-env ANALYZE=true next build", "lint-staged": "lint-staged"}, "dependencies": {"@ant-design/icons": "^5.5.0", "@ant-design/pro-components": "^2.8.2", "@babel/runtime": "^7.22.3", "@dagrejs/dagre": "^1.1.2", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@floating-ui/react": "^0.25.2", "@formatjs/intl-localematcher": "^0.5.4", "@headlessui/react": "^1.7.13", "@heroicons/react": "^2.0.16", "@lexical/react": "^0.16.0", "@mdx-js/loader": "^2.3.0", "@mdx-js/react": "^2.3.0", "@monaco-editor/react": "^4.6.0", "@next/mdx": "^14.0.4", "@remixicon/react": "^4.2.0", "@svgdotjs/svg.js": "^3.2.4", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/typography": "^0.5.9", "agora-rtc-sdk-ng": "^4.23.1", "ahooks": "^3.7.5", "aieditor-pro": "^1.3.5", "antd": "^5.22.1", "class-variance-authority": "^0.7.0", "classnames": "^2.3.2", "compressorjs": "^1.2.1", "copy-to-clipboard": "^3.3.3", "crypto-js": "^4.2.0", "dayjs": "^1.11.7", "echarts": "^5.4.1", "echarts-for-react": "^3.0.2", "elkjs": "^0.9.3", "fast-deep-equal": "^3.1.3", "html2canvas": "^1.4.1", "i18next": "^22.4.13", "i18next-resources-to-backend": "^1.1.3", "immer": "^9.0.19", "js-audio-recorder": "^1.0.7", "js-base64": "^3.7.7", "js-cookie": "^3.0.1", "jsencrypt": "^3.3.2", "jwt-decode": "^4.0.0", "katex": "^0.16.10", "lamejs": "^1.2.1", "lexical": "^0.16.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "mermaid": "^11.4.1", "mime": "^4.0.4", "negotiator": "^0.6.3", "next": "^14.2.26", "pinyin-pro": "^3.23.0", "qrcode.react": "^3.1.0", "qs": "^6.11.1", "rc-textarea": "^1.5.2", "react": "~18.2.0", "react-18-input-autosize": "^3.0.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "~18.2.0", "react-error-boundary": "^4.0.2", "react-file-viewer": "^1.2.1", "react-headless-pagination": "^1.1.4", "react-i18next": "^12.2.0", "react-markdown": "^8.0.6", "react-multi-email": "^1.0.14", "react-papaparse": "^4.1.0", "react-perfect-scrollbar": "^1.5.8", "react-slider": "^2.0.4", "react-sortablejs": "^6.1.4", "react-syntax-highlighter": "^15.5.0", "reactflow": "^11.11.3", "recordrtc": "^5.6.2", "rehype-katex": "^6.0.2", "rehype-raw": "^7.0.0", "remark-breaks": "^3.0.2", "remark-gfm": "^3.0.1", "remark-math": "^5.1.1", "scheduler": "^0.23.0", "server-only": "^0.0.1", "slate": "^0.110.2", "slate-dom": "^0.111.0", "slate-history": "^0.110.3", "slate-react": "^0.111.0", "sortablejs": "^1.15.0", "swr": "^2.1.0", "tailwind-merge": "^2.4.0", "use-context-selector": "^1.4.1", "uuid": "^9.0.1", "video-react": "^0.16.0", "wujie-react": "^1.0.22", "zundo": "^2.1.0", "zustand": "^4.5.2"}, "devDependencies": {"@antfu/eslint-config": "^0.36.0", "@faker-js/faker": "^7.6.0", "@next/bundle-analyzer": "^15.0.2", "@rgrove/parse-xml": "^4.1.0", "@types/crypto-js": "^4.1.1", "@types/dagre": "^0.7.52", "@types/js-cookie": "^3.0.3", "@types/lodash-es": "^4.17.7", "@types/negotiator": "^0.6.1", "@types/node": "18.15.0", "@types/qs": "^6.9.7", "@types/react": "~18.2.0", "@types/react-dom": "~18.2.0", "@types/react-slider": "^1.3.1", "@types/react-syntax-highlighter": "^15.5.6", "@types/recordrtc": "^5.6.11", "@types/sortablejs": "^1.15.1", "@types/uuid": "^9.0.8", "@types/video-react": "^0.15.8", "autoprefixer": "^10.4.14", "bing-translate-api": "^4.0.2", "code-inspector-plugin": "^0.13.0", "cross-env": "^7.0.3", "eslint": "^8.36.0", "eslint-config-next": "^15.0.0", "husky": "^9.1.7", "lint-staged": "^13.2.2", "magicast": "^0.3.4", "node-forge": "^1.3.1", "postcss": "^8.4.31", "sass": "^1.61.0", "tailwindcss": "^3.4.4", "ts-node": "^10.9.2", "typescript": "4.9.5", "uglify-js": "^3.17.4"}, "resolutions": {"@types/react": "~18.2.0", "@types/react-dom": "~18.2.0", "string-width": "4.2.3"}, "lint-staged": {"**/*.js?(x)": ["eslint --fix"], "**/*.ts?(x)": ["eslint --fix"]}}