import type { FC } from 'react'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { Form, Input } from 'antd'
import { renameDocumentName } from '@/service/datasets'

// 公共组件
import Toast from '@/app/components/base/toast'
import Modal from '@/app/components/base/modal'
import Button from '@/app/components/base/button'
import { useFormDisabled } from '@/hooks/use-form'

type Props = {
  datasetId: string
  documentId: string
  name: string
  onClose: () => void
  onSaved: () => void
}

const RenameModal: FC<Props> = ({
  documentId,
  datasetId,
  name,
  onClose,
  onSaved,
}) => {
  const { t } = useTranslation()
  const [form] = Form.useForm()
  const disabled = useFormDisabled(form)
  // 重命名
  const handleSave = async () => {
    try {
      await renameDocumentName({
        datasetId,
        documentId,
        name: form.getFieldValue('name'),
      })
      Toast.notify({ type: 'success', message: t('common.actionMsg.modifiedSuccessfully') })
      onSaved()
    }
    catch (error) {
    }
  }

  return (
    <Modal
      title={t('datasetDocuments.list.table.rename')}
      isShow
      footer={
        <>
          <Button variant='secondary-accent' className='mr-4' onClick={onClose}>{t('common.operation.cancel')}</Button>
          <Button disabled={disabled} variant='primary' onClick={handleSave}>{t('common.operation.save')}</Button>
        </>
      }
      closable
      onClose={onClose}
    >
      <Form layout='vertical' form={form} initialValues={{
        name,
      }}>
        <Form.Item
          name={'name'}
          label={t('datasetDocuments.list.table.name')}
          validateFirst={true}
          rules={[
            {
              required: true,
              whitespace: true,
            },
          ]}>
          <Input placeholder={t('common.placeholder.input')!}></Input>
        </Form.Item>
      </Form>
    </Modal>
  )
}
export default React.memo(RenameModal)
