import { useCallback } from 'react'
import { useStore as useAppStore } from '@/app/components/app/store'
import { getAppPublishInfo } from '@/service/market'

export const useAppPublishInit = () => {
  const appDetail = useAppStore(state => state.appDetail)!
  const appPublishInfo = useAppStore(state => state.appPublishInfo)
  const setAppPublishInfo = useAppStore(state => state.setAppPublishInfo)
  const getAppPublishInfoData = useCallback(async () => {
    const res = await getAppPublishInfo(appDetail.id)
    setAppPublishInfo(res)
  }, [])

  return {
    getAppPublishInfoData,
    appPublishInfo,
  }
}
