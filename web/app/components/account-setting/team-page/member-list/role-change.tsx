'use client'
import { useCallback, useMemo, useState, useEffect } from 'react'
import type { FormProps } from 'antd';
import { Form, Input, Select, } from 'antd'
import { useContext } from 'use-context-selector'
import { useTranslation } from 'react-i18next'
import { ReactMultiEmail } from 'react-multi-email'
import 'react-multi-email/dist/style.css'
import { inviteMember } from '@/service/common'
import { emailRegex } from '@/config'
import { ToastContext } from '@/app/components/base/toast'
import { useProviderContext } from '@/context/provider-context'
import type { InvitationResult } from '@/models/common'
import I18n from '@/context/i18n'
import { putTeamMemberUpdate } from '@/service/team'
// 公共组件
import Modal from '@/app/components/base/modal'
import Button from '@/app/components/base/button'
import CheckSelect from '@/app/components/base/select/check-select'
import { A } from '@svgdotjs/svg.js';

type ICreateModalPropsProps = {
  onCancel?: () => void
  onSend?: (invitationResults: InvitationResult[]) => void
  title?: string
  showTeamName?: boolean
  modalData?: object
  showEditor?: boolean
  showDatasetOperator?: boolean
  userNameDisabled?: boolean
  teamId?: string
}

const CreateModal = ({
  onCancel,
  onSend,
  title = '',
  showTeamName = true,
  modalData,
  showEditor = true,
  showDatasetOperator = true,
  userNameDisabled = false,
  teamId = '',
}: ICreateModalPropsProps) => {
  const { t } = useTranslation()
  const [form] = Form.useForm()
  const values = Form.useWatch([], form)
  const [emails, setEmails] = useState<string[]>([])
  const { notify } = useContext(ToastContext)
  const { datasetOperatorEnabled } = useProviderContext()
  const { locale } = useContext(I18n)
  // 角色
  const [role, setRole] = useState<string>('normal')
  // 用户
  const [userValue, setUserValue] = useState<string>('')
  const [memberVlaue,setMemberVlaue] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    if (modalData) {
      form.setFieldValue('userName', modalData?.account_name || '') // 设置 userName 字段
      form.setFieldValue('role', modalData?.role || '') // 设置 role 字段
    }
    setUserValue(modalData?.account_name || '')
    setRole(modalData?.role || '')
  }, [modalData])
  // 保存
  const handleSend = useCallback(async () => {
    try {
      const values = await form.validateFields()
      setIsLoading(true)
      const params = {
        team_id: teamId,
        user_id: modalData?.account_id || '',
        role: values?.role || 'normal',
      }
      const response = await putTeamMemberUpdate(params)
      onSend(response)
      if (response.code === 200)
        notify({ type: 'success', message: response?.message })
    }
    catch (e) {
      // notify({ type: 'error', message: e })
    } finally {
      setIsLoading(false)
    }
      
  }, [])

  // 角色下拉列表
  const roleSelectList = [
    {
      label: t('account.role.normal'),
      desc: t('account.role.normalTipPublicCloud'),
      value: 'normal',
    },
    ...(showEditor
      ? [
        {
          label: t('account.role.editor'),
          desc: t('account.role.editorTip'),
          value: 'editor',
        },
      ]
      : []
    ),
    {
      label: t('account.role.admin'),
      desc: t('account.role.adminTipPublicCloud'),
      value: 'admin',
    },
    ...((datasetOperatorEnabled && showDatasetOperator)
    ? [
        {
          label: t('account.role.datasetOperator'),
          desc: t('account.role.datasetOperatorTip'),
          value: 'dataset_operator',
        },
      ]
      : []),
  ]
  // 检查是否禁用按钮
  const isButtonDisabled = useMemo(() => {
    if (showTeamName) {
      return !values?.userName || !values?.memberInfo || !values?.role
    }
    return !values?.memberInfo || !values?.role
  }, [values, showTeamName])

  const roleChange = useCallback((e: any) => {
    // form.setFieldValue('userName', modalData?.name || '') // 设置 userName 字段
    form.setFieldValue('role', e) // 设置 role 字段
    setRole(e)
    // checkSelect()
  }, [])
  return (
    <Modal
      closable
      isShow
      onClose={onCancel}
      className='!w-[400px] !max-w-[400px]'
      title={title}
      // description={t('common.members.inviteTeamMemberTip')}
      footer={
        <>
          <Button variant='secondary-accent' className='mr-4' onClick={onCancel}>{t('common.operation.cancel')}</Button>
          <Button 
            // disabled={isButtonDisabled} 
            variant='primary' 
            onClick={handleSend}
          >
            {t('common.operation.save')}
          </Button>
        </>
      }
    >
      <Form form={form} layout='vertical' initialValues={{
        userName: userValue,
        role,
      }}>
        {/* 用户名称 */}
        {
          showTeamName && (
            <Form.Item
              label={t('account.team.table.userName')}
              name="userName"
            >
              <Input
                disabled={userNameDisabled}
              />
            </Form.Item>
          )
        }
        {/* 成员角色 */}
        <Form.Item
          label={t('account.team.teamNameInfo.membersRole')}
          name="role"
          rules={[{ required: true }]}
        >
          <CheckSelect
            className='w-full'
            value={role}
            // onChange={e => setRole(e)}
            onChange={e => roleChange(e)}
            options={roleSelectList}
            optionRender={option => (
              <>
                <div className='text-sm leading-5'>{option.label}</div>
                <div className='text-xs leading-[18px]'>{option.data.desc}</div>
              </>
            )}
          ></CheckSelect>
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default CreateModal
