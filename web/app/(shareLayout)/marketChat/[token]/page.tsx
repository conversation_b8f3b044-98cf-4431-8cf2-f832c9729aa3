'use client'
import React from 'react'
import Chat<PERSON>ithHistory<PERSON>rap from '@/app/components/base/chat/chat-with-history'
import { ProviderContextProvider } from '@/context/provider-context'
import { AppContextProvider } from '@/context/app-context'
import { getOperToken } from '@/utils/user'

const Chat = () => {
  const token = getOperToken()
  if (token) {
    return (
      <AppContextProvider>
        <ProviderContextProvider>
          <ChatWithHistoryWrap fromMarket auth={true} />
        </ProviderContextProvider>
      </AppContextProvider>
    )
  }
  else {
    return <ChatWithHistoryWrap fromMarket></ChatWithHistoryWrap>
  }
}

export default React.memo(Chat)
