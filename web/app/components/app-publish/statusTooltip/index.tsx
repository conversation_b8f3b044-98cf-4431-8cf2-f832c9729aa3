import { useTranslation } from 'react-i18next'
import Tooltip from '@/app/components/base/tooltip'
import { ExclamationOutlined } from '../../base/icons/src/vender/line/alertsAndFeedback'
import { statusMap } from '@/app/components/app/app-publish-config/type'
import { AppPublishInfo } from '@/types/app'
import { statusEnum } from '@/app/components/app/app-publish-config/type'
type Props = {
  appPublishInfo?: AppPublishInfo
}
const StatusTooltip = ({ appPublishInfo }: Props) => {
  const { t } = useTranslation()
  if(!appPublishInfo?.status) return null
  const statusTitle = statusMap[appPublishInfo.status as keyof typeof statusMap]?.text
  const showMessage = [statusEnum.offline, statusEnum.failed].includes(appPublishInfo.status) && appPublishInfo.message
  if(!statusTitle) return null
  return (
    <Tooltip popupContent={(
        <>
          <div>{t(statusTitle)}</div>
          {showMessage && (
            <>
              <span>{ t('app.publish.messagePrefix') }</span>
              <span>{appPublishInfo.message}</span>
            </>
          )}
        </>
      )} 
    >
      <ExclamationOutlined className='shrink-0 w-4 h-4 text-gray-G3' />
    </Tooltip>
  )
}

export default StatusTooltip