'use client'
import type { FC } from 'react'
import React, { useCallback, useState } from 'react'
import { useTranslation } from 'react-i18next'
import produce from 'immer'
import { useContext } from 'use-context-selector'
import VariableTypeSelector from './variable-type-select'
import { ChatVarType } from '@/app/components/workflow/types'

// 公共能力
import DeleteButton from '@/app/components/base/button/delete-button'
import { ToastContext } from '@/app/components/base/toast'

type Props = {
  index: number
  list: any[]
  onChange: (list: any[]) => void
}

const typeList = [
  ChatVarType.String,
  ChatVarType.Number,
]

export const DEFAULT_OBJECT_VALUE = {
  key: '',
  type: ChatVarType.String,
  value: undefined,
}

const ObjectValueItem: FC<Props> = ({
  index,
  list,
  onChange,
}) => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  const [isFocus, setIsFocus] = useState(false)

  // 处理名称变更
  const handleKeyChange = useCallback((index: number) => {
    return (e: React.ChangeEvent<HTMLInputElement>) => {
      const newList = produce(list, (draft: any[]) => {
        if (!/^[a-zA-Z0-9_]+$/.test(e.target.value))
          return notify({ type: 'error', message: 'key is can only contain letters, numbers and underscores' })
        draft[index].key = e.target.value
      })
      onChange(newList)
    }
  }, [list, notify, onChange])
  // 处理类型变更
  const handleTypeChange = useCallback((index: number) => {
    return (type: ChatVarType) => {
      const newList = produce(list, (draft) => {
        draft[index].type = type
        if (type === ChatVarType.Number)
          draft[index].value = isNaN(Number(draft[index].value)) ? undefined : Number(draft[index].value)
        else
          draft[index].value = draft[index].value ? String(draft[index].value) : undefined
      })
      onChange(newList)
    }
  }, [list, onChange])
  // 处理值变更
  const handleValueChange = useCallback((index: number) => {
    return (e: React.ChangeEvent<HTMLInputElement>) => {
      const newList = produce(list, (draft: any[]) => {
        draft[index].value = draft[index].type === ChatVarType.String ? e.target.value : isNaN(Number(e.target.value)) ? undefined : Number(e.target.value)
      })
      onChange(newList)
    }
  }, [list, onChange])
  // 删除单条值
  const handleItemRemove = useCallback((index: number) => {
    return () => {
      const newList = produce(list, (draft) => {
        draft.splice(index, 1)
      })
      onChange(newList)
    }
  }, [list, onChange])
  // 新增单条
  const handleItemAdd = useCallback(() => {
    const newList = produce(list, (draft: any[]) => {
      draft.push(DEFAULT_OBJECT_VALUE)
    })
    onChange(newList)
  }, [list, onChange])

  const handleFocusChange = useCallback(() => {
    setIsFocus(true)
    if (index === list.length - 1)
      handleItemAdd()
  }, [handleItemAdd, index, list.length])

  return (
    <div className='group flex border-t border-gray-200'>
      {/* Key */}
      <div className='w-[120px] border-r border-gray-200'>
        <input
          className='block px-2 w-full h-7 text-text-secondary system-xs-regular appearance-none outline-none caret-primary-600 hover:bg-state-base-hover focus:bg-components-input-bg-active  placeholder:system-xs-regular placeholder:text-components-input-text-placeholder'
          placeholder={t('workflow.chatVariable.modal.objectKey') || ''}
          value={list[index].key}
          onChange={handleKeyChange(index)}
        />
      </div>
      {/* Type */}
      <div className='w-[96px] border-r border-gray-200'>
        <VariableTypeSelector
          inCell
          value={list[index].type}
          list={typeList}
          onSelect={handleTypeChange(index)}
          popupClassName='w-[120px]'
        />
      </div>
      {/* Value */}
      <div className='relative w-[230px]'>
        <input
          className='block px-2 w-full h-7 text-text-secondary system-xs-regular appearance-none outline-none caret-primary-600 hover:bg-state-base-hover focus:bg-components-input-bg-active  placeholder:system-xs-regular placeholder:text-components-input-text-placeholder'
          placeholder={t('workflow.chatVariable.modal.objectValue') || ''}
          value={list[index].value}
          onChange={handleValueChange(index)}
          onFocus={() => handleFocusChange()}
          onBlur={() => setIsFocus(false)}
          type={list[index].type === ChatVarType.Number ? 'number' : 'text'}
        />
        {list.length > 1 && !isFocus && (
          <DeleteButton
            className='z-10 group-hover:block hidden absolute right-1 top-1.5'
            onClick={handleItemRemove(index)}
          />
        )}
      </div>
    </div>
  )
}
export default React.memo(ObjectValueItem)
