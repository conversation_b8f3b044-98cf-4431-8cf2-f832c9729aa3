import { useTranslation } from 'react-i18next'
import { EChartsOption } from 'echarts-for-react'
import type { InputChartType, DataType } from './types'
import { StatisticalTypeEnum } from './types'
import { getPercentage } from './utils'

const useChartsBlock = () => {
  const { t } = useTranslation()

  // 图表加载错误的默认显示文字
  const errorDefaultOption = {
    title: {
      subtext: t('common.echarts.echartsError')
    }
  }
  // rich富文本样式
  const richText = {
    richTextGray1: {
      color: '#181818',
      fontSize: 14,
      lineHeight: 22
    },
    richTextGray2: {
      color: '#5C6273',
      fontSize: 14,
      lineHeight: 22
    },
    richTextGray3: {
      color: '#878EA0',
      fontSize: 14,
      lineHeight: 22
    },
    richTextRed1: {
      color: '#F56C6C',
      fontSize: 14,
      lineHeight: 22
    },
    richTextGreen1: {
      color: '#78BF13',
      fontSize: 14,
      lineHeight: 22
    }
  }
  // 图表xy轴共用配置
  const commonAixsConfig = {
    axisTick: {
      show: false
    },
    axisLabel: {
      color: '#999999'
    }
  }

  // 百分数富文本样式
  const getPercentageRichText = (decimal: number) => {
    const text = getPercentage(decimal)
    const richText = decimal > 0 ? 'richTextRed1' : 'richTextGreen1'
    return `{${richText}|${text}}`
  }

  // 处理饼图图例
  const getPieLegend = (type: keyof typeof StatisticalTypeEnum, data: DataType[]) => {
    return {
      type: 'scroll',
      pageIconSize: 8,
      orient: 'vertical',
      top: 'center',
      left: '60px',
      height: '60%',
      itemWidth: 8,
      itemHeight: 8,
      icon: 'circle',
      align: 'left',
      formatter: function (name: string) {
        const item = data.find((item: any) => item?.name === name)
        if (!item) {
          return name;
        }
        if (type in StatisticalTypeEnum) {
          if(type === 'event')
            // 事件统计类
            return `{richTextGray3|${name}}` + ' ' + `{richTextGray1|${item.value}}` + `{richTextGray3|${StatisticalTypeEnum[type]}}`;
          else if(type === 'time')
            // 时间段统计类
            return `{richTextGray3|时间段}` + ' ' + `{richTextGray1|${name}}` + `{richTextGray3|${StatisticalTypeEnum[type]}}`;
          else if(type === 'increase')
            // 增长类
            return `{richTextGray3|${name}}` + ' ' + getPercentageRichText(item.value);
        } else {
          return `{richTextGray3|${name}}` + ' ' + `{richTextGray1|${item.value}}`;
        }
      },
      textStyle: {
        rich: richText
      }
    }
  }
  // 处理饼图
  const handlePie = (pieData: EChartsOption) => {
    const isSmallPie = pieData?.layout?.size === 'small'
    // 饼图data数据处理，如果是负数需要转化为绝对值
    const data = pieData.data.map((item: any) => {
      const value = item.value
      if(typeof Number(value) === 'number' && value < 0) {
        return {
          ...item,
          value: Math.abs(Number(value)),
        }
      }

        return item
    })

    const title = {
      text: pieData.title
    }
    const tooltip = {
      trigger: 'item',
      appendToBody: true
    }
    // 图例
    const legend = getPieLegend(pieData.options.type, pieData.data)

    // 普通饼图
    const defaultPieSerie = {
      type: pieData.chartType,
      data: data,
      width: '100%',
      radius: '30%',
      left: 'center',
      itemStyle: {
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: true,
        formatter: '{richTextGray3|{b}}\n{richTextGray1|{c}}{richTextGray3|件}{richTextGray1|{d}%}',
        rich: richText,
        alignTo: 'edge',
        edgeDistance: '1%',
        bleedMargin: 10,
      }
    }
    // 小饼图
    const smallPieSerie = {
      ...defaultPieSerie,
      left: 'left',
      width: '40%',
      center: ['30%','40%'],
      radius: ['45%', '60%'],
      label: {
        show: false,
      }
    }
    if(isSmallPie) {
      // 小饼图
      return {
        title,
        tooltip,
        legend,
        series: smallPieSerie
      }
    } else {
      // 普通尺寸饼图
      return {
        title,
        tooltip,
        legend: {
          show: false
        },
        series: defaultPieSerie
      }
    }
  }

  // 处理折线图
  const handleLine = (lineData: EChartsOption) => {
    const data = lineData?.data
    // 事件总数折线图
    const isTotalEvents = lineData?.options?.count
    // 单一折线图
    const isSingleLine = data.length === 1

    const title = {
      text: lineData.title
    }
    // x轴
    const xAxis = {
      ...commonAixsConfig,
      type: 'category',
      boundaryGap: false,
      axisLine: {
        show: false
      },
      data: lineData.xAxis || []
    }
    // y轴
    const yAxis = {
      ...commonAixsConfig,
      type: 'value'
    }
    // 布局
    const grid = {
      top: '100px',
      bottom: '15%'
    }
    // 图例
    const legend = {
      type: 'scroll',
      pageIconSize: 8,
      icon: 'circle',
      top: '32px',
      data: data.map((item: any) => {
        return item.name
      })
    }
    // 系列
    const series = data.map((item: any) => {
      const serie: any = {
        name: item.name,
        type: lineData.chartType,
        symbol: 'circle',
        data: item.value,
      }
      // 事件总数 单一渐变折线图 渐变样式
      if(isTotalEvents || isSingleLine) {
        serie.lineStyle = {
          color: '#3168f5'
        }
        serie.areaStyle = {
          color: {
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {offset: 0, color: '#3168f540'}, 
              {offset: 1, color: '#3168f500'}
            ],
          }
        }
      }
      // 单一渐变折线图
      if(!isTotalEvents && isSingleLine) {
        serie.showSymbol = false
      }
      return serie
    })
    // 事件总数
    if(isTotalEvents) {
      return {
        title,
        xAxis: {
          ...xAxis,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#EBEEF5'
            }
          },
          splitLine: {
            show: true
          }
        },
        yAxis: {
          show: false
        },
        height: '20px',
        grid: {
          top: '32px',
          bottom: '32px'
        },
        series: series
      }
    } else if (isSingleLine) {
      // 单一渐变折线图
      return {
        title,
        xAxis,
        yAxis,
        grid,
        legend: {
          show: false
        },
        series: series
      }
    } else {
      return {
        title,
        xAxis,
        yAxis,
        grid,
        legend,
        series
      }
    }
  }

  // 处理条形图
  const handleBar = (barData: EChartsOption) => {
    const data = barData?.data
    // 系列
    const series = data.map((item: any) => {
      return {
        name: item.name,
        type: barData.chartType,
        barWidth: 20,
        data: item.value,
      }
    })
    return {
      title: {
        text: barData.title
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        appendToBody: true
      },
      xAxis: {
        ...commonAixsConfig,
        axisLine: {
          show: false
        },
        type: 'category',
        data: barData.xAxis || []
      },
      yAxis: {
        ...commonAixsConfig,
        type: 'value',
      },
      series: series
    }
  }

  // 把输入图表数据转换成echart的option
  const getEchartOptionList = (chartList: EChartsOption[]) => {
    let option: EChartsOption
    return chartList.map((chart) => {
      const chartType = chart?.chartType
      const size = chart?.layout?.size
      // 处理尺寸样式
      const className = size === 'small' ? 'w-1/3' : size === 'medium' ? 'w-1/2' : 'w-full'
      try {
        if (chartType === 'pie') {
          option = handlePie(chart)
        } else if (chartType === 'line') {
          option = handleLine(chart)
        } else if (chartType === 'bar') {
          option = handleBar(chart)
        }
        return {
          className: className,
          layout: chart.layout,
          option: option || errorDefaultOption,
          externalConfig: chart.options
        }
      } catch (error) {
        return {
          className: className,
          option: errorDefaultOption,
          externalConfig: chart.options
        }
      }
    })
  }

  return {
    getEchartOptionList,
  }
}

export default useChartsBlock
