const translation = {
  title: '工具',
  builtInPromptTitle: '提示词',
  howToGet: '如何获取',
  toolNameUsageTip: '工具调用名称，用于 Agent 推理和提示词',
  type: {
    all: '全部',
    builtIn: '内置',
    custom: '自定义',
    workflow: '工作流',
  },
  auth: {
    setupModalTitle: '设置授权',
  },
  addToolModal: {
    emptyTitle: '没有可用的工作流工具',
    emptyTip: '去 “工作流 -> 发布为工具” 添加',
  },
  test: {
    title: '测试',
    parametersValue: '参数和值',
    parameters: '参数',
    value: '值',
    testResult: '测试结果',
    testResultPlaceholder: '测试结果将显示在这里',
  },
  thought: {
    tool: {
      using: '正在调用',
      used: '完成调用',
      failed: '调用失败',
    },
    dataset: {
      using: '正在检索',
      used: '完成检索',
      failed: '检索失败',
    },
    requestTitle: '请求',
    responseTitle: '响应',
  },
  setBuiltInTools: {
    info: '信息',
    toolDescription: '工具描述',
    parameters: '参数',
    string: '字符串',
    number: '数字',
    required: '必填',
    infoAndSetting: '信息和设置',
  },
  createDropdown: {
    by: '通过',
    workflow: '工作流创建',
    custom: 'OpenAPI schema创建',
    createTool: '创建工具',
  },
  workflow: {
    selectWorkflow: '选择工作流',
    searchWorkflow: '搜索工作流',
    addWorkflow: '添加工作流',
    publishedWorkflow: '已发布工作流',
  },
  modal: {
    confirmTitle: '确认保存？',
    confirmTip: '发布新的工具版本可能会影响该工具已关联的应用',
    toolDetail: '工具详情',
    createCustomTool: '创建工具',
    editCustomTool: '编辑工具',
    paramConfig: '参数配置',
    toolConfig: '工具配置',
    createWorkflowTool: '创建工作流工具',
    editWorkflowTool: '编辑工作流工具',
  },
  placeholder: {
    name: '请输入工具名称',
    nameForToolCall: '用于应用配置过程中工具调用',
    description: '工具用途的简要描述，例如获取特定位置的温度。',
    schema: '在此处输入您的 OpenAPI schema',
    importFromUrl: 'https://...',
  },
  info: {
    name: '工具名称',
    nameForToolCall: '工具调用名称',
    params: '参数',
    schema: 'Schema',
    examples: '例子',
    exampleOptions: {
      json: '天气(JSON)',
      yaml: '宠物商店(YAML)',
      blankTemplate: '空白模版',
    },
    description: '工具描述',
    importFromUrl: '从 URL 中导入',
    toolInput: {
      title: '工具入参',
      name: '名称',
      required: '必须',
      method: '方式',
      methodSetting: '用户输入',
      methodSettingTip: '用户在工具配置中填写',
      methodParameter: 'LLM 填入',
      methodParameterTip: 'LLM 在推理过程中填写',
      label: '标签',
      labelPlaceholder: '选择标签(可选)',
      description: '描述',
      descriptionPlaceholder: '参数意义的描述',
    },
    availableTools: {
      title: '可用工具',
      name: '名称',
      description: '描述',
      method: '方法',
      path: '路径',
      action: '操作',
      test: '测试',
    },
    authMethod: {
      title: '鉴权方法',
      type: '鉴权类型',
      keyTooltip: 'HTTP 头部名称，如果你不知道是什么，可以将其保留为 Authorization 或设置为自定义值',
      types: {
        none: '无',
        api_key: 'API Key',
        apiKeyPlaceholder: 'HTTP 头部名称，用于传递 API Key',
        apiValuePlaceholder: '输入 API Key',
      },
      key: '键',
      value: '值',
    },
    authHeaderPrefix: {
      title: 'Auth Type',
      types: {
        basic: 'Basic',
        bearer: 'Bearer',
        custom: 'Custom',
      },
    },
  },
  action: {
    createCollection: '创建工具',
    createWorkflowTool: '创建工作流工具',
    editWorkflowTool: '编辑工作流工具',
    createCustomTool: '通过OpenAPI schema创建',
    editCustomTool: '编辑工具',
    viewSchemaSpec: '查看 OpenAPI-Swagger 规范',
    auth: '授权',
    cancelAuth: '取消授权',
    addTool: '添加工具',
  },
  notify: {
    toolRemoved: '工具已被移除',
    toolFull: '最多可创建{{num}}个工具，请删除后重试。',
    successCreateCustom: '创建自定义工具成功',
    successDeleteCustom: '删除自定义工具成功',
    updateCustomSuccess: '更新自定义工具成功',
    successCreateWorkflow: '创建工作流工具成功',
    successDeleteWorkflow: '删除工作流工具成功',
    updateWorkflowSuccess: '更新工作流工具成功',
    nameForToolCallTip: '工具调用名称仅支持数字、字母、下划线。',
    noTool: '没有可用的工具',
    urlError: '请输入有效的 URL',
    deleteToolConfirmTitle: '删除这个工具？',
    deleteToolConfirmContent: '删除工具是不可逆的。用户将无法再访问您的工具。',
    successToolAuth: '工具授权成功',
    successCancelToolAuth: '取消工具授权成功',
    addTip: '当前可添加工具上限为{{max}}',
  },
}

export default translation
