export enum DSLImportMode {
  YAML_CONTENT = 'yaml-content',
  YAML_URL = 'yaml-url',
}

export enum DSLImportStatus {
  COMPLETED = 'completed',
  COMPLETED_WITH_WARNINGS = 'completed-with-warnings',
  PENDING = 'pending',
  FAILED = 'failed',
}
export type DSLImportResponse = {
  id: string
  status: DSLImportStatus
  app_id?: string
  current_dsl_version?: string
  imported_dsl_version?: string
  error: string
}
