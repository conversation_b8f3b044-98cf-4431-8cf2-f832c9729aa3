'use client'
import React from 'react'
import { Config<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from 'antd'
import { useRouter } from 'next/navigation'
import { useContext } from 'use-context-selector'
import { useTranslation } from 'react-i18next'
import s from './styles/index.module.css'
import './styles/index.css'
import AccountDropdownPopover from './account-dropdown'
import NewWorkspaceSelector from './new-workspace-selector'
import { AppEventEmitterType } from '@/types/app'
// 公共组件
import { WorkspaceProvider } from '@/context/workspace-context'
import { useModalContext } from '@/context/modal-context'
import { useEventEmitterContextContext } from '@/context/event-emitter'
import { useProviderContext } from '@/context/provider-context'
import { useAppContext } from '@/context/app-context'
import { LayoutContext } from '@/context/layout-context'
import cn from '@/utils/classnames'
import { ToastContext } from '@/app/components/base/toast'
import But<PERSON> from '@/app/components/base/button'
import { Add } from '@/app/components/base/icons/src/vender/solid/general'
import { ArrowDown } from '@/app/components/base/icons/src/vender/line/arrows'
import Logo from '@/app/components/base/logo'
import Scrollbar from '@/app/components/base/scrollbar'

const Navbar = () => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  const { currentMenu, menus } = useContext(LayoutContext)
  const { setShowCreateAppModal } = useModalContext()
  const { onPlanInfoChanged, plan } = useProviderContext()
  const { eventEmitter } = useEventEmitterContextContext()
  const isAppsFull = plan.usage.buildApps >= plan.total.buildApps
  const { canEditApp, isVip } = useAppContext()
  const { push } = useRouter()

  // 创建应用
  const createApp = () => {
    if (isAppsFull) {
      notify({
        type: 'error',
        message: t('app.notify.appFull', { num: plan.total.buildApps }),
      })
    }
    setShowCreateAppModal({
      onClose: () => {},
      onSuccess: () => {
        eventEmitter?.emit({
          type: AppEventEmitterType.Refresh,
        } as any)
        onPlanInfoChanged()
      },
    })
  }

  // 点击菜单事件
  const onClickMenu = (key: string) => {
    push(key)
  }
  return (
    <WorkspaceProvider>
      <Logo className={s.navbarHeader}></Logo>
      <Divider className='!mt-0 !mb-3'></Divider>
      <div className='px-4 flex flex-col gap-3'>
        {/* <NewWorkspaceSelector></NewWorkspaceSelector> */}
        { isVip && <NewWorkspaceSelector></NewWorkspaceSelector>}
        { canEditApp && <Button onClick={createApp} variant={'primary'} className='mb-6 gap-1 w-full h-[40px]'>
          <Add className='w-4 h-4'></Add>
          { t('app.action.createApp') }
        </Button> }
      </div>
      <Scrollbar className={s.navbarMenu}>
        <ConfigProvider
          theme={{
            components: {
              Menu: {
                itemPaddingInline: 8,
              },
            },
          }}
        >
          <Menu
            className={'navbarMenu'}
            items={menus}
            mode='inline'
            selectedKeys={[currentMenu!]}
            onClick={item => onClickMenu(item.key)}
            expandIcon={props => <ArrowDown className={cn(props.isOpen && 'rotate-180', 'text-gray-G1 w-4 h-4')} ></ArrowDown>}
          ></Menu>
        </ConfigProvider>
      </Scrollbar>

      <div className={s.navbarUser}>
        <AccountDropdownPopover></AccountDropdownPopover>
      </div>
    </WorkspaceProvider>
  )
}

export default Navbar
