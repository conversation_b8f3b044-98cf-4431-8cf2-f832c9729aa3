const formRules = (Form: any, t: any, notify: any, appId: any, GetTemplateList: any): Promise<boolean> => {
  const { type, reportType, topicType, form, files, Upload } = Form;
  return GetTemplateList({
    app_id: appId,
    level1: type.text,
    level2: topicType.text,
    level3: reportType.text,
  }, false).then((res: any) => {

    if (type.text === t('sampleTemplate.IntelligentAgent.CreationTypeTopic')) {

      const requiredFields = res.filter((item: any) => item.required);

      const missingFields = requiredFields.filter((item: any) => !form[item.field || item.title]);

      if (missingFields.length > 0) {
        notify({ type: 'warning', message: missingFields.map((item: any) => item.title || item.field) + t('sampleTemplate.longText.isEempty') });
        return false;
      }

      return true;

    } else {
      const ArrayFormList = t('sampleTemplate.FormDataEvent.ArrayFormList', { returnObjects: true });
      const requiredFields = ArrayFormList.filter((item: any) => item.required);

      const missingFields = requiredFields.filter((item: any) => !form[item.field || item.title]);

      if (missingFields.length > 0) {
        notify({ type: 'warning', message: missingFields.map((item: any) => item.title || item.field) + t('sampleTemplate.longText.isEempty') });
        return false;
      }

      if ((!files || Object.keys(files).length === 0)) {
        notify({ type: 'warning', message: t('sampleTemplate.FormDataEvent.FileRequired') });
        return false;
      }

      return true;
    }
  });
}

export default formRules;