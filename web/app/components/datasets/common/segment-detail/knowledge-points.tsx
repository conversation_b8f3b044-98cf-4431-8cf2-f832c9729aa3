'use client'

import { type FC } from 'react'
import { useTranslation } from 'react-i18next'
import { Button, Form, Input, Space } from 'antd'
import { useEffect } from 'react'
// 公共组件
import Modal from '@/app/components/base/modal'

const { TextArea } = Input

type DetailModalProps = {
  knowPointsListObj: any
  onEditKnowleClose: () => void
  onEditKnowleConfirm: () => void
  isKnowEdit: boolean
}

const KnowledgePointsModal: FC<DetailModalProps> = ({
  knowPointsListObj = {},
  onEditKnowleClose,
  onEditKnowleConfirm,
  isKnowEdit = false,

}) => {
  const { t } = useTranslation()
  const [form] = Form.useForm()
  useEffect(() => {
    form.setFieldsValue({
      question: knowPointsListObj.question || '',
      answer: knowPointsListObj.answer || '',
      abs_knowledge_point: knowPointsListObj.abs_knowledge_point || knowPointsListObj.content || '',
    })
  }, [form, knowPointsListObj])
  // const handleDataChange = (type: string, value: string) => {
  //   setLocaleData({ ...localeData, [type]: value })
  // }
  const onEditKnowleConfirmC = () => {
    const values = form.getFieldsValue()
    if(knowPointsListObj.knowledge_point_type === 1 && !values.abs_knowledge_point) return
    if(knowPointsListObj.knowledge_point_type === 2 ) {
      if(!values.question) return
      if(!values.answer) return
    }
    console.log(values, '===保存知识切片===')
    onEditKnowleConfirm({ ...values })
  }
  const editKnowledgeContent = () => {
    return (
      <div className='px-8 mt-[15px]'>
        <div className='mt-[10px]' style={{ width: '100%' }}>
          <Form form={form} layout="vertical">
            {/* 问题 */}
            {
              knowPointsListObj.knowledge_point_type === 2 && (
                <Form.Item
                  label={t('datasetDocuments.segment.question')}
                  name={'question'}
                  validateFirst={true}
                  validateTrigger='onBlur'
                  rules={[
                    {
                      required: true,
                      whitespace: true,
                    },
                  ]}
                >
                  <TextArea
                    rows={4}
                    disabled={isKnowEdit}
                    // onChange={e => onKnowPointAreaChange('question', e.target.value)}
                  />
                </Form.Item>
              )
            }
            {/* 答案 */}
            {
              knowPointsListObj.knowledge_point_type === 2 && (
                <Form.Item
                  label={t('datasetDocuments.segment.answer')}
                  name={'answer'}
                  validateFirst={true}
                  validateTrigger='onBlur'
                  rules={[
                    {
                      required: true,
                      whitespace: true,
                    },
                  ]}
                >
                  <TextArea
                    rows={4}
                    disabled={isKnowEdit}
                    // onChange={e => onKnowPointAreaChange('answer', e.target.value)}
                  />
                </Form.Item>
              )
            }
            {/* 段落总结 */}
            {
              knowPointsListObj.knowledge_point_type === 1 && (
                <Form.Item
                  label={t('datasetCreation.stepTwo.knowledgeEnhancement.enhanceContent.paragraphSummary')}
                  name={'abs_knowledge_point'}
                  validateFirst={true}
                  validateTrigger='onBlur'
                  rules={[
                    {
                      required: true,
                      whitespace: true,
                    },
                  ]}
                >
                  <TextArea
                    rows={4}
                    disabled={isKnowEdit}
                    // onChange={e => onKnowPointAreaChange('content', e.target.value)}
                  />
                </Form.Item>
              )
            }
          </Form>

        </div>
        <div className='mt-[20px] flex items-end justify-end ' style={{ width: '100%' }}>
          <Space>
            <Button onClick={onEditKnowleClose}>{t('common.operation.cancel')}</Button>
            <Button type="primary" onClick={onEditKnowleConfirmC} disabled={isKnowEdit}>{t('common.operation.save')}</Button>
          </Space>
        </div>
      </div>
    )
  }
  return (
    <Modal
      isShow
      scrollable={false}
      closable
      onClose={onEditKnowleClose}
      // loading={isDetailLoading}
      // title={collection.label[language]}
      // description={collection.description[language] || ''}
    >
      {editKnowledgeContent()}
    </Modal>
  )
}

export default KnowledgePointsModal
