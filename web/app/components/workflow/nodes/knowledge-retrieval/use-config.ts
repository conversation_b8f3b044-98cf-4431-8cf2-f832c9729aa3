import {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import produce from 'immer'
import { isEqual } from 'lodash-es'
import type { ValueSelector, Var } from '../../types'
import { BlockEnum, TextVarFormat, VarType } from '../../types'
import {
  useIsChatMode, useNodesReadOnly,
  useWorkflow,
} from '../../hooks'
import type { KnowledgeRetrievalNodeType, MultipleRetrievalConfig } from './types'
import {
  getMultipleRetrievalConfig,
  getSelectedDatasetsMode,
} from './utils'
import { RETRIEVE_TYPE } from '@/types/datasets'
import { DATASET_DEFAULT } from '@/config'
import type { DataSet } from '@/models/datasets'
import { fetchDatasets } from '@/service/datasets'
import useNodeCrud from '@/app/components/workflow/nodes/_base/hooks/use-node-crud'
import useOneStepRun from '@/app/components/workflow/nodes/_base/hooks/use-one-step-run'
// 账户设置公共能力
import { useCurrentProviderAndModel, useModelListAndDefaultModelAndCurrentProviderAndModel } from '@/app/components/account-setting/model-provider-page/hooks'
import { ModelTypeEnum } from '@/app/components/account-setting/model-provider-page/declarations'

export const useMiniConfig = (id: string, payload: KnowledgeRetrievalNodeType) => {
  const { inputs, setInputs: doSetInputs } = useNodeCrud<KnowledgeRetrievalNodeType>(id, payload)
  const inputRef = useRef(inputs)
  const setInputs = useCallback((s: KnowledgeRetrievalNodeType) => {
    const newInputs = produce(s, (draft) => {
      if (s.retrieval_mode === RETRIEVE_TYPE.multiWay)
        delete draft.single_retrieval_config
      else
        delete draft.multiple_retrieval_config
    })
    // not work in pass to draft...
    doSetInputs(newInputs)
    inputRef.current = newInputs
  }, [doSetInputs])

  // 选中的知识库信息
  const [selectedDatasets, setSelectedDatasets] = useState<DataSet[]>([])
  // 私密的知识库信息
  const [forkDatasets, setForkDatasets] = useState<DataSet[]>([])
  // 适配已删除的知识库信息
  const allDatasets = useMemo(() => {
    return inputs.dataset_ids.map((item) => {
      const findDataset = selectedDatasets.find(dataset => [dataset.id, dataset.old_id].includes(item))
      if (findDataset)
        return { ...findDataset, lack: false }
      else
        return { id: item, lack: true }
    })
  }, [inputs.dataset_ids, selectedDatasets])

  // 获取选中的数据集
  const fetchSelectedDatasets = useCallback(async (datasetIds: string[]) => {
    let dataSetsWithDetail: DataSet[] = []
    if (datasetIds?.length > 0) {
      const result = await fetchDatasets({ url: '/datasets', params: { page: 1, ids: datasetIds } })
      dataSetsWithDetail = result.data
      if (inputs.fork)
        setForkDatasets(dataSetsWithDetail)
      else
        setSelectedDatasets(dataSetsWithDetail)
    }
    const newInputs = produce(inputs, (draft) => {
      if (inputs.dataset_ids.length === 0)
        draft.fork = false
      if (draft.fork)
        draft.dataset_ids = []
      draft.lack = dataSetsWithDetail.length !== datasetIds.length
    })
    setInputs(newInputs)
  }, [setInputs])

  // datasets
  useEffect(() => {
    fetchSelectedDatasets(inputRef.current.dataset_ids)
  }, [])
  return { forkDatasets, setForkDatasets, selectedDatasets, setSelectedDatasets, allDatasets, mutate: fetchSelectedDatasets }
}

const useConfig = (id: string, payload: KnowledgeRetrievalNodeType) => {
  const filterVar = useCallback((varPayload: Var) => {
    return varPayload.type === VarType.string
  }, [])
  const { nodesReadOnly: readOnly } = useNodesReadOnly()
  const { forkDatasets, setForkDatasets, selectedDatasets, setSelectedDatasets, allDatasets } = useMiniConfig(id, payload)
  const isChatMode = useIsChatMode()
  const { getBeforeNodesInSameBranch } = useWorkflow()
  const {
    currentProvider,
    currentModel,
  } = useModelListAndDefaultModelAndCurrentProviderAndModel(ModelTypeEnum.textGeneration)
  const {
    modelList: rerankModelList,
    defaultModel: rerankDefaultModel,
  } = useModelListAndDefaultModelAndCurrentProviderAndModel(ModelTypeEnum.rerank)
  const {
    currentModel: currentRerankModel,
  } = useCurrentProviderAndModel(
    rerankModelList,
    rerankDefaultModel
      ? {
        ...rerankDefaultModel,
        provider: rerankDefaultModel.provider.provider,
      }
      : undefined,
  )
  const { inputs, setInputs: doSetInputs } = useNodeCrud<KnowledgeRetrievalNodeType>(id, payload)
  const inputRef = useRef(inputs)
  const setInputs = useCallback((s: KnowledgeRetrievalNodeType) => {
    const newInputs = produce(s, (draft) => {
      if (s.retrieval_mode === RETRIEVE_TYPE.multiWay)
        delete draft.single_retrieval_config
      else
        delete draft.multiple_retrieval_config
    })
    // not work in pass to draft...
    doSetInputs(newInputs)
    inputRef.current = newInputs
  }, [doSetInputs])
  // single run
  const {
    isShowSingleRun,
    hideSingleRun,
    runningStatus,
    handleRun,
    handleStop,
    runInputData,
    setRunInputData,
    runResult,
  } = useOneStepRun<KnowledgeRetrievalNodeType>({
    id,
    data: inputs,
    defaultRunInputData: {
      query: '',
    },
  })
  const setQuery = useCallback((newQuery: string) => {
    setRunInputData({
      ...runInputData,
      query: newQuery,
    })
  }, [runInputData, setRunInputData])

  // rerank模型选择是否打开
  const [rerankModelOpen, setRerankModelOpen] = useState(false)
  const startNode = getBeforeNodesInSameBranch(id).find(node => node.data.type === BlockEnum.Start)
  const startNodeId = startNode?.id
  const query = runInputData.query

  // 处理查询变量变化
  const handleQueryVarChange = useCallback((newVar: ValueSelector | string) => {
    const newInputs = produce(inputs, (draft) => {
      draft.query_variable_selector = newVar as ValueSelector
    })
    setInputs(newInputs)
  }, [inputs, setInputs])
  // 处理模型变化
  const handleModelChanged = useCallback((model: { provider: string; modelId: string; mode?: string }) => {
    const newInputs = produce(inputRef.current, (draft) => {
      if (!draft.single_retrieval_config) {
        draft.single_retrieval_config = {
          model: {
            provider: '',
            name: '',
            mode: '',
            completion_params: {},
            outputs: {},
            response_format: TextVarFormat.Markdown,
          },
        }
      }
      const draftModel = draft.single_retrieval_config?.model
      draftModel.provider = model.provider
      draftModel.name = model.modelId
      draftModel.mode = model.mode!
    })
    setInputs(newInputs)
  }, [setInputs])
  // 处理模型配置参数变化
  const handleCompletionParamsChange = useCallback((newParams: Record<string, any>) => {
    // inputRef.current.single_retrieval_config?.model is old  when change the provider...
    if (isEqual(newParams, inputRef.current.single_retrieval_config?.model.completion_params))
      return

    const newInputs = produce(inputRef.current, (draft) => {
      if (!draft.single_retrieval_config) {
        draft.single_retrieval_config = {
          model: {
            provider: '',
            name: '',
            mode: '',
            completion_params: {},
            outputs: {},
            response_format: TextVarFormat.Markdown,
          },
        }
      }
      draft.single_retrieval_config.model.completion_params = newParams
    })
    setInputs(newInputs)
  }, [setInputs])
  // 变更检索模式
  const handleRetrievalModeChange = useCallback((newMode: RETRIEVE_TYPE) => {
    const newInputs = produce(inputs, (draft) => {
      draft.retrieval_mode = newMode
      if (newMode === RETRIEVE_TYPE.multiWay) {
        const multipleRetrievalConfig = draft.multiple_retrieval_config
        draft.multiple_retrieval_config = getMultipleRetrievalConfig(multipleRetrievalConfig!, selectedDatasets)
      }
      else {
        const hasSetModel = draft.single_retrieval_config?.model?.provider
        if (!hasSetModel) {
          draft.single_retrieval_config = {
            model: {
              provider: currentProvider?.provider || '',
              name: currentModel?.model || '',
              mode: currentModel?.model_properties?.mode as string,
              completion_params: {},
              outputs: {},
              response_format: TextVarFormat.Markdown,
            },
          }
        }
      }
    })
    setInputs(newInputs)
  }, [currentModel?.model, currentModel?.model_properties?.mode, currentProvider?.provider, inputs, setInputs, selectedDatasets])
  // 变更多检索配置
  const handleMultipleRetrievalConfigChange = useCallback((newConfig: MultipleRetrievalConfig) => {
    const newInputs = produce(inputs, (draft) => {
      draft.multiple_retrieval_config = getMultipleRetrievalConfig(newConfig!, selectedDatasets)
    })
    setInputs(newInputs)
  }, [inputs, setInputs, selectedDatasets])
  // 知识库添加/删除/编辑操作
  const handleOnDatasetsChange = useCallback((data: DataSet[]) => {
    let newDatasets = data
    if (newDatasets.find(item => !item.name)) { // has not loaded selected dataset
      const newSelected = produce(newDatasets, (draft: any) => {
        newDatasets.forEach((item, index) => {
          if (!item.name) { // not fetched database
            const newItem = selectedDatasets.find(i => i.id === item.id)
            if (newItem)
              draft[index] = newItem
          }
        })
      })
      setSelectedDatasets(newSelected)
      newDatasets = newSelected
    }
    else {
      setSelectedDatasets(newDatasets)
    }
    const {
      mixtureHighQualityAndEconomic,
      mixtureInternalAndExternal,
      inconsistentEmbeddingModel,
      allInternal,
      allExternal,
    } = getSelectedDatasetsMode(newDatasets)
    const newInputs = produce(inputs, (draft) => {
      draft.fork = false
      draft.dataset_ids = newDatasets.map(d => d.id)
      if (payload.retrieval_mode === RETRIEVE_TYPE.multiWay && newDatasets.length > 0) {
        const multipleRetrievalConfig = draft.multiple_retrieval_config
        draft.multiple_retrieval_config = getMultipleRetrievalConfig(multipleRetrievalConfig!, newDatasets)
      }
      draft.lack = selectedDatasets.length !== draft.dataset_ids.length
    })
    setInputs(newInputs)

    if (
      (allInternal && (mixtureHighQualityAndEconomic || inconsistentEmbeddingModel))
      || mixtureInternalAndExternal
      || (allExternal && newDatasets.length > 1)
    )
      setRerankModelOpen(true)
  }, [inputs, setInputs, selectedDatasets, payload.retrieval_mode])

  // 不更新的话inputRef.current拿到的是旧值，会覆盖掉最新值
  useEffect(() => {
    inputRef.current = inputs
  }, [inputs])
  // set defaults models
  useEffect(() => {
    const inputs = inputRef.current
    if (inputs.retrieval_mode === RETRIEVE_TYPE.multiWay && inputs.multiple_retrieval_config?.reranking_model?.provider && currentRerankModel && rerankDefaultModel)
      return

    if (inputs.retrieval_mode === RETRIEVE_TYPE.oneWay && inputs.single_retrieval_config?.model?.provider)
      return

    const newInput = produce(inputs, (draft) => {
      if (currentProvider?.provider && currentModel?.model) {
        const hasSetModel = draft.single_retrieval_config?.model?.provider
        if (!hasSetModel) {
          draft.single_retrieval_config = {
            model: {
              provider: currentProvider?.provider,
              name: currentModel?.model,
              mode: currentModel?.model_properties?.mode as string,
              completion_params: {},
              outputs: {},
              response_format: TextVarFormat.Markdown,
            },
          }
        }
      }
      const multipleRetrievalConfig = draft.multiple_retrieval_config
      draft.multiple_retrieval_config = {
        top_k: multipleRetrievalConfig?.top_k || DATASET_DEFAULT.top_k,
        score_threshold: multipleRetrievalConfig?.score_threshold,
        reranking_model: multipleRetrievalConfig?.reranking_model,
        reranking_mode: multipleRetrievalConfig?.reranking_mode,
        weights: multipleRetrievalConfig?.weights,
        reranking_enable: multipleRetrievalConfig?.reranking_enable !== undefined
          ? multipleRetrievalConfig.reranking_enable
          : Boolean(currentRerankModel && rerankDefaultModel),
      }
    })
    setInputs(newInput)
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentProvider?.provider, currentModel, rerankDefaultModel])
  // 处理变量
  useEffect(() => {
    const inputs = inputRef.current
    let query_variable_selector: ValueSelector = inputs.query_variable_selector
    if (isChatMode && inputs.query_variable_selector.length === 0 && startNodeId)
      query_variable_selector = [startNodeId, 'sys.query']

    setInputs(produce(inputs, (draft) => {
      draft.query_variable_selector = query_variable_selector
    }))
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return {
    readOnly,
    inputs,
    handleQueryVarChange,
    filterVar,
    handleRetrievalModeChange,
    handleMultipleRetrievalConfigChange,
    handleModelChanged,
    handleCompletionParamsChange,
    selectedDatasets: selectedDatasets.filter(d => d.name),
    handleOnDatasetsChange,
    forkDatasets,
    setForkDatasets,
    allDatasets,
    isShowSingleRun,
    hideSingleRun,
    runningStatus,
    handleRun,
    handleStop,
    query,
    setQuery,
    runResult,
    rerankModelOpen,
    setRerankModelOpen,
  }
}

export default useConfig
