.long-text-modal {
  top: 0;
  max-width: 100vw;
  width: 100% !important;
  height: 100vh;
  overflow: hidden;
  .ant-modal-content {
    height: 100vh;
    max-height: 100vh;
    overflow: hidden;
    .ant-modal-body {
      margin-bottom: 0 !important;
      padding: 0;
    }
    .aie-container aie-header div {
      justify-content: left;
    }
    .aie-container > div[style*="flex-grow: 1; overflow: auto; display: flex;"] {
      div[style="width: 80%; height: fit-content;"] {
        width: 100% !important;
      }
    }
    .aie-content {
      padding: 10px 24px !important;
    }
    #go {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .icon-GoBack {
    width: 20px !important;
    height: 28px !important;
    margin-right: 10px;
  }
  .icon-Img {
    width: 20px !important;
    height: 28px !important;
  }

  :where(.ant-tabs > .ant-tabs-nav) {
    padding-top: 11px;
    .ant-tabs-nav-wrap {
      justify-content: center;
      :where(.ant-tabs .ant-tabs-tab + .ant-tabs-tab) {
        margin: 0px 0 0 50px;
      }
    }
  }

  :where(.ant-tabs .ant-tabs-content-holder) {
    height: calc(100vh - 50px) !important;
    overflow-y: auto;
    background:#f0f5ff;
  }
  .aie-comment-container {
    display: none !important;
  }

  .editable-cell-value-wrap {
    padding-inline-end: 0 !important;
  }
}
.Image_model {
  padding: 0 10px;
  max-height: 680px;
  overflow-y: auto;
  .ant-pro-checkcard-group {
    column-count: 3;
    width: 100%;
    flex-wrap:wrap;
  }
  .ant-pro-checkcard {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    break-inside: avoid;
    width: 200px !important;
    height: 200px !important;
    margin-inline-end: 8px !important;
    margin-block-end: 8px !important;
    background-color: #fff;
    border:solid 1px #D9DCE3;
  }
  .ant-pro-checkcard:nth-child(3n) {
    margin-inline-end: 0px !important;
  }
  .ant-pro-checkcard-cover {
    padding-inline: 0px !important;
    padding-block: 0px !important;
  }
}
.exportbtn{
  width:92px;
  height:28px;
  line-height:28px;
  font-size:12px;
  border-radius:4px !important;
  margin-right:10px;
}
.exportbtn:hover{
  background:#4086FF !important;
  color:#fff;
}



