'use client'
import type { FC } from 'react'
import React, { useCallback } from 'react'
import produce from 'immer'
import { useTranslation } from 'react-i18next'
import Item from './video-item'
import type { Video } from '@/types/videos'
type Props = {
  list: Video[]
  showEmpty?: boolean
  onChange: (list: Video[]) => void
  readonly?: boolean
}

const VideoList: FC<Props> = ({
  list,
  showEmpty = true,
  onChange,
  readonly,
}) => {
  const { t } = useTranslation()
  // 删除视频库
  const handleRemove = useCallback((index: number) => {
    return () => {
      const newList = produce(list, (draft) => {
        draft.splice(index, 1)
      })
      onChange(newList)
    }
  }, [list, onChange])
  // 变更视频库
  const handleChange = useCallback((index: number) => {
    return (value: Video) => {
      const newList = produce(list, (draft) => {
        draft[index] = value
      })
      onChange(newList)
    }
  }, [list, onChange])
  if (!showEmpty && list.length === 0)
    return null
  return (
    <div className='space-y-1'>
      {list.length
        ? list.map((item, index) => {
          return (
            <Item
              key={index}
              payload={item}
              onRemove={handleRemove(index)}
              onChange={handleChange(index)}
              readonly={readonly}
            />
          )
        })
        : (
          <div className='p-2 text-S1 leading-H1 text-center text-gray-G2 rounded cursor-default select-none bg-gray-G7'>
            {t('workflow.nodes.video.addVideoTip')}
          </div>
        )
      }

    </div>
  )
}
export default React.memo(VideoList)
