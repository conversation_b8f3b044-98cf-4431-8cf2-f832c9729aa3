import {
  memo,
  useCallback,
  useState,
} from 'react'
import {
  useStoreApi,
} from 'reactflow'
import { useTranslation } from 'react-i18next'
import ChatVariableTip from './components/chat-variable-tip'
import VariableModal from './components/variable-modal'
import VariableItem from './components/variable-item'

import { findUsedVarNodes, updateNodeVars } from '@/app/components/workflow/nodes/_base/components/variable/utils'
import { useStore } from '@/app/components/workflow/store'
import { useNodesSyncDraft } from '@/app/components/workflow/hooks/use-nodes-sync-draft'
import { useNodesReadOnly } from '@/app/components/workflow/hooks'
import type { ConversationVariable } from '@/app/components/workflow/types'
// 公共能力
import { PlusCircle } from '@/app/components/base/icons/src/vender/line/general'
import FeatureCollapse from '@/app/components/base/features/feature-collapse'
import TextButton from '@/app/components/base/button/text-button'
import Confirm from '@/app/components/base/confirm'

const ChatVariablePanel = () => {
  const { t } = useTranslation()
  const store = useStoreApi()
  const varList = useStore(s => s.conversationVariables) as ConversationVariable[]
  const updateChatVarList = useStore(s => s.setConversationVariables)
  const { doSyncWorkflowDraft } = useNodesSyncDraft()
  const { nodesReadOnly, getNodesReadOnly } = useNodesReadOnly()

  // 编辑变量弹窗
  const [showEditModal, setShowEditModal] = useState(false)
  // 当前编辑的变量
  const [currentVar, setCurrentVar] = useState<ConversationVariable>()
  // 是否显示删除确认
  const [showRemoveVarConfirm, setShowRemoveConfirm] = useState(false)
  // 缓存等待删除的变量
  const [cacheForDelete, setCacheForDelete] = useState<ConversationVariable>()

  // 获取当前变量影响道德节点
  const getEffectedNodes = useCallback((chatVar: ConversationVariable) => {
    const { getNodes } = store.getState()
    const allNodes = getNodes()
    return findUsedVarNodes(
      ['conversation', chatVar.name],
      allNodes,
    )
  }, [store])
  // 删除变量时，删除节点中使用的变量
  const removeUsedVarInNodes = useCallback((chatVar: ConversationVariable) => {
    const { getNodes, setNodes } = store.getState()
    const effectedNodes = getEffectedNodes(chatVar)
    const newNodes = getNodes().map((node) => {
      if (effectedNodes.find(n => n.id === node.id))
        return updateNodeVars(node, ['conversation', chatVar.name], [])

      return node
    })
    setNodes(newNodes)
  }, [getEffectedNodes, store])

  // 编辑变量
  const handleEdit = (chatVar: ConversationVariable) => {
    setCurrentVar(chatVar)
    setShowEditModal(true)
  }
  // 删除变量
  const handleDelete = useCallback((chatVar: ConversationVariable) => {
    removeUsedVarInNodes(chatVar)
    updateChatVarList(varList.filter(v => v.id !== chatVar.id))
    setCacheForDelete(undefined)
    setShowRemoveConfirm(false)
    doSyncWorkflowDraft()
  }, [doSyncWorkflowDraft, removeUsedVarInNodes, updateChatVarList, varList])
  // 删除变量前的检查
  const deleteCheck = useCallback((chatVar: ConversationVariable) => {
    const effectedNodes = getEffectedNodes(chatVar)
    if (effectedNodes.length > 0) {
      setCacheForDelete(chatVar)
      setShowRemoveConfirm(true)
    }
    else {
      handleDelete(chatVar)
    }
  }, [getEffectedNodes, handleDelete])
  // 新增/编辑变量
  const handleSave = useCallback(async (chatVar: ConversationVariable) => {
    // 新增状态
    if (!currentVar) {
      const newList = [chatVar, ...varList]
      updateChatVarList(newList)
      doSyncWorkflowDraft()
      return
    }
    // 编辑状态
    const newList = varList.map(v => v.id === currentVar.id ? chatVar : v)
    updateChatVarList(newList)
    // side effects of rename env
    if (currentVar.name !== chatVar.name) {
      const { getNodes, setNodes } = store.getState()
      const effectedNodes = getEffectedNodes(currentVar)
      const newNodes = getNodes().map((node) => {
        if (effectedNodes.find(n => n.id === node.id))
          return updateNodeVars(node, ['conversation', currentVar.name], ['conversation', chatVar.name])

        return node
      })
      setNodes(newNodes)
    }
    doSyncWorkflowDraft()
  }, [currentVar, doSyncWorkflowDraft, getEffectedNodes, store, updateChatVarList, varList])

  return (
    <>
      <FeatureCollapse
        inWorkflow
        title={t('workflow.chatVariable.panelTitle')}
        useSwitch={false}
        headerRight={(
          <TextButton variant={'hover'} disabled={nodesReadOnly} onClick={() => setShowEditModal(true)}>
            <PlusCircle className='w-4 h-4' />
            <div>{t('workflow.common.addVariable')}</div>
          </TextButton>
        )}
      >
        <ChatVariableTip className='mb-2' />
        {varList.map(chatVar => (
          <VariableItem
            key={chatVar.id}
            item={chatVar}
            onEdit={handleEdit}
            onDelete={deleteCheck}
          />
        ))}
      </FeatureCollapse>
      {/* 变量添加/编辑弹框 */}
      {showEditModal
        && <VariableModal
          chatVar={currentVar}
          onSave={handleSave}
          onClose={() => {
            setShowEditModal(false)
            setCurrentVar(undefined)
          }}
        />
      }
      <Confirm
        isShow={showRemoveVarConfirm}
        title={t('workflow.common.effectVarConfirm.title')}
        content={t('workflow.common.effectVarConfirm.content')}
        onCancel={() => setShowRemoveConfirm(false)}
        onConfirm={() => cacheForDelete && handleDelete(cacheForDelete)}
      />
    </>
  )
}

export default memo(ChatVariablePanel)
