'use client'

import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { useAppContext } from '@/context/app-context'

export default function VideosLayout({ children }: { children: React.ReactNode }) {
  const { canViewVideoLib } = useAppContext()
  const router = useRouter()

  useEffect(() => {
    if (!canViewVideoLib)
      return router.replace('/square')
  }, [canViewVideoLib, router])

  return (
    <>
      {children}
    </>
  )
}
