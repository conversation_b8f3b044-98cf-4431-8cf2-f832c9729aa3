'use client'
import { useTranslation } from 'react-i18next'
import s from '@/app/components/data-statistics/detail/styles/style.module.scss'
import { useRouter, useSearchParams } from 'next/navigation';
import { LeftOutlined } from '@ant-design/icons';

type TypeDict = {
  token: string;
  model: string;
  app: string;
};

const typeDict: TypeDict = {
  token: '模型token数据消耗 ',
  model: '内置模型资源点消耗 ',
  app: '已发布应用API调用数据'

}
// 数据统计详情Nav
const DetailNav = () => {
   const searchParams = useSearchParams();
   const router = useRouter();
   const type = searchParams.get('type');
   // 返回主页面
   const goBackPage= () => {
    router.push('/data-statistics')
   }
  return <div className={s['detail-nav']}>
    <span className={s['detail-icon']} onClick={goBackPage}><LeftOutlined /></span>
    <span className={s['detail-title']}>{type && typeDict[type] }</span>
  </div>
}
export default DetailNav