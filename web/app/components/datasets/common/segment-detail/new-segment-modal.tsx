import { memo, useState } from 'react'
import type { FC } from 'react'
import { useTranslation } from 'react-i18next'
import { useContext } from 'use-context-selector'
import { useParams } from 'next/navigation'
import s from './style.module.css'

import type { SegmentUpdater } from '@/models/datasets'
import { addSegment } from '@/service/datasets'

import cn from '@/utils/classnames'
import Modal from '@/app/components/base/modal'
import Button from '@/app/components/base/button'
import AutoHeightTextarea from '@/app/components/base/auto-height-textarea/common'
import { ToastContext } from '@/app/components/base/toast'
import TagInput from '@/app/components/base/tag-input'
import { useProviderContext } from '@/context/provider-context'

type NewSegmentModalProps = {
  isShow: boolean
  category: string | number | undefined
  onCancel: () => void
  docForm: string
  onSave: () => void
}

const NewSegmentModal: FC<NewSegmentModalProps> = ({
  isShow,
  onCancel,
  docForm,
  onSave,
}) => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  // 询问内容
  const [question, setQuestion] = useState('')
  // 回答内容
  const [answer, setAnswer] = useState('')
  // 知识库、文档id
  const { datasetId, documentId } = useParams()
  // 关键词
  const [keywords, setKeywords] = useState<string[]>([])
  const { useXIYANRag } = useProviderContext()

  // 取消新增分段
  const handleCancel = () => {
    setQuestion('')
    setAnswer('')
    onCancel()
    setKeywords([])
  }
  // 保存新分段
  const handleSave = async () => {
    const params: SegmentUpdater = { content: '' }
    if (docForm === 'qa_model') {
      if (!question.trim())
        return notify({ type: 'error', message: t('datasetDocuments.segment.questionEmpty') })
      if (!answer.trim())
        return notify({ type: 'error', message: t('datasetDocuments.segment.answerEmpty') })

      params.content = question
      params.answer = answer
    }
    else {
      if (!question.trim())
        return notify({ type: 'error', message: t('datasetDocuments.segment.contentEmpty') })

      params.content = question
    }
    if (keywords?.length)
      params.keywords = keywords
    
    try {
      await addSegment({ datasetId: datasetId as string, documentId: documentId as string, body: params })
      notify({ type: 'success', message: t('common.actionMsg.modifiedSuccessfully') })
      handleCancel()
      onSave()
    }
    catch {
    }
  }

  const renderContent = () => {
    if (docForm === 'qa_model') {
      return (
        <>
          <div className='mb-1 text-xs font-semibold text-gray-500'>QUESTION</div>
          <AutoHeightTextarea
            outerClassName='mb-4'
            className='leading-6 text-md text-gray-800'
            value={question}
            placeholder={t('datasetDocuments.segment.questionPlaceholder') || ''}
            onChange={e => setQuestion(e.target.value)}
            autoFocus
          />
          <div className='mb-1 text-xs font-semibold text-gray-500'>ANSWER</div>
          <AutoHeightTextarea
            outerClassName='mb-4'
            className='leading-6 text-md text-gray-800'
            value={answer}
            placeholder={t('datasetDocuments.segment.answerPlaceholder') || ''}
            onChange={e => setAnswer(e.target.value)}
          />
        </>
      )
    }

    return (
      <AutoHeightTextarea
        outerClassName={cn(s.segModalContent, s.segModalContentActive, '!min-h-[50vh]')}
        className={s.segModal}
        value={question}
        placeholder={t('datasetDocuments.segment.contentPlaceholder') || ''}
        onChange={e => setQuestion(e.target.value)}
        disabled={false}
        autoFocus
      />
    )
  }

  return (
    <Modal
      isShow={isShow}
      closable
      onClose={handleCancel}
      title={
        docForm === 'qa_model'
          ? t('datasetDocuments.segment.newQaSegment')!
          : t('datasetDocuments.segment.newTextSegment')!
      }
      footer={
        <>
          <Button
            className='mr-4'
            variant={'secondary-accent'}
            onClick={handleCancel}>
            {t('common.operation.cancel')}
          </Button>
          <Button
            variant='primary'
            onClick={handleSave}
          >
            {t('common.operation.save')}
          </Button>
        </>
      }
    >
      { renderContent() }
      {!useXIYANRag && (
        <>
          <div className={s.segInfoItemTitle}>{t('datasetDocuments.segment.keywords')}</div>
          <TagInput items={keywords} onChange={newKeywords => setKeywords(newKeywords)} />
        </>
      )}
    </Modal>
  )
}

export default memo(NewSegmentModal)
