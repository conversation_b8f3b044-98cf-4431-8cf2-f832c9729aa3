'use client'

import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { useAppContext } from '@/context/app-context'

export default function DatasetsLayout({ children }: { children: React.ReactNode }) {
  const { canDataset } = useAppContext()
  const router = useRouter()

  useEffect(() => {
    if (!canDataset)
      return router.replace('/square')
  }, [canDataset, router])

  return (
    <>
      {children}
    </>
  )
}
