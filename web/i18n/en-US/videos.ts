const translation = {
  type: {
    img: 'Picture',
    video: 'Video',
    videosBuket: 'Video Stream',
    all: 'all',
  },
  action: {
    selectVideos: 'Select Video Library',
    addFile: 'Add files',
    editVideos: 'Edit your video library',
    createVideos: 'Create a video library',
  },
  modalTitle: {
    deleteVideoConfirmTitle: 'Video library deletion',
    detail: 'Document details',
    videoSetting: 'Video library editing',
    edit: 'Edit the file',
  },
  notify: {
    noVideo: 'There is no video library available',
    videoUsedByApp: 'Some apps are using the video library. The app will no longer be able to use the video library, and all prompt configurations and logs will be permanently deleted.',
    videoDeleted: 'The video library has been deleted.',
    noPermission: 'If you would like to use this feature, please contact the official staff',
    videoFull: 'You can create up to {{num}} video libraries, delete them and try again.',
    deleteDocTipDesc: 'If you need to resume processing later, you will continue from where you left off.',
    videoTip: 'Only the last 10 days of live video content is stored',
    deleteDocTip: 'Are you sure you want to delete it?',
    deleteVideoConfirmContent: 'After deleting the video library, tools and applications that used this video library will become invalid. The deletion operation is irreversible. Do you confirm deletion?',
  },
  placeholder: {
    namePlaceholder: 'Please enter the name of the video library.',
    descPlaceholder: 'Describe what\'s in this video library. Detailed descriptions allow AI to access the content of the video library in a timely manner. If empty, the agent platform will use the default hit policy.',
  },
  info: {
    createType: 'Create type',
    nameDescription: 'The video library name supports only Chinese, English, digits, underscores (_), hyphens (-), and English dots (.) (1~50 characters)',
    name: 'Video Library Name',
    doc: 'documentation',
    desc: 'Video gallery description',
    nameError: 'The name of the video library does not meet the requirements.',
  },
  create: {
    header: {
      update: 'Upload the file',
      creation: 'Create a video library',
    },
    uploader: {
      validation: {
        typeError: 'File types are not supported',
        size: 'The file size is too large to exceed {{size}}MB',
        filesNumber: 'Batch upload limit {{filesNumber}}.',
        count: 'Multiple files are not supported at this time.',
      },
      tip: 'Support {{supportTypes}}',
      title: 'Upload a text file',
      cancel: 'Cancel',
      change: 'Change file',
      browse: 'Select file',
      failed: 'Upload failed',
      button: 'Drag and drop the file here, or',
      tip2: 'Each file must not exceed {{size}}MB, and you can upload a maximum of {{filesNumber}} files at the same time.',
    },
    form: {
      urlInfoFormat: 'Please enter the correct URL, currently only the RTSP protocol is supported',
      desc: 'Video library description',
      urlNameMaxLength: 'The URL name can be up to 50 characters',
      urlInfoPlaceholder: 'Please enter the URL information',
      namePlaceholder: 'Please enter a name for your video library',
      urlInfoMaxLength: 'The URL information can be up to 255 characters',
      frameRate: 'Frame rate',
      name: 'The name of the video library',
      nameDescription: 'The video library name supports only Chinese, English, digits, underscores (_), hyphens (-), and English dots (.) (1~50 characters)',
      frameRatePlaceholder: 'Please select a frequency',
      urlName: 'URL name',
      urlInfo: 'URL information',
      uploader: 'Data upload',
      urlNamePlaceholder: 'Please enter the URL name',
      fileNumLimit: 'A single video library can upload up to {{max}} files, and you can also upload {{num}} files',
      descPlaceholder: 'Please enter a description of the video library',
      nameExist: 'The video library name already exists',
      urlInfoRepeat: 'The URL information already exists',
    },
  },
  doc: {
    info: {
      fileName: 'Filename',
      addTime: 'Add time',
      action: 'operate',
      deviceId: 'Device ID',
      urlSite: 'URL',
      frameRate: 'Cutting frequency',
      originFileSize: 'Original file size',
      processTime: 'Inbound time',
      locationIp: 'Location IP',
      status: 'Processing status',
      uploadTime: 'Upload time',
      lastUpdateTime: 'Last updated',
      urlName: 'URL name',
      errorInfoTip: '(Only the last five are displayed)',
      errorInfo: 'Abnormal start time:',
      siteStatus: 'The real-time status of the URL',
      originFileName: 'Original file name',
    },
    status: {
      waiting: 'Pending',
      abnormal: 'abnormal',
      paused: 'Time out',
      normal: 'normal',
      processing: 'Processing',
      failed: 'Processing failed',
      success: 'Processing complete',
    },
  },
  title: 'Video Library',
}

export default translation
