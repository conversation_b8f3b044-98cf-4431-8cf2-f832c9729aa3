import {
  memo,
  useMemo,
} from 'react'
import { useTranslation } from 'react-i18next'
import { useEdges } from 'reactflow'
import { ConfigProvider, Dropdown } from 'antd'
import type { ItemType, MenuItemGroupType, MenuItemType } from 'antd/es/menu/interface'
import style from '../styles/style.module.scss'
import ChangeBlock from './change-block'
import {
  canRunBySingle,
} from '@/app/components/workflow/utils'
import Scrollbar from '@/app/components/base/scrollbar'
import { useStore } from '@/app/components/workflow/store'
import {
  useNodeDataUpdate,
  useNodesExtraData,
  useNodesInteractions,
  useNodesReadOnly,
  useNodesSyncDraft,
} from '@/app/components/workflow/hooks'
import ShortcutsName from '@/app/components/workflow/common/shortcuts-name'
import type { Node } from '@/app/components/workflow/types'
import { BlockEnum } from '@/app/components/workflow/types'
import { useGetLanguage } from '@/context/i18n'
import { CollectionType } from '@/app/components/tools/types'
import cn from '@/utils/classnames'
import { More } from '@/app/components/base/icons/src/vender/workflow'
import TextButton from '@/app/components/base/button/text-button'

type PanelOperatorPopupProps = {
  id: string
  data: Node['data']
}
const PanelOperatorPopup = ({
  id,
  data,
}: PanelOperatorPopupProps) => {
  const { t } = useTranslation()
  const language = useGetLanguage()
  const edges = useEdges()
  const {
    handleNodeDelete,
    handleNodesDuplicate,
    handleNodeSelect,
    handleNodesCopy,
  } = useNodesInteractions()
  const { handleNodeDataUpdate } = useNodeDataUpdate()
  const { handleSyncWorkflowDraft } = useNodesSyncDraft()
  const { nodesReadOnly } = useNodesReadOnly()
  const nodesExtraData = useNodesExtraData()
  const buildInTools = useStore(s => s.buildInTools)
  const customTools = useStore(s => s.customTools)
  // const workflowTools = useStore(s => s.workflowTools)
  const edge = edges.find(edge => edge.target === id)
  const author = useMemo(() => {
    if (data.type !== BlockEnum.Tool)
      return nodesExtraData[data.type].author

    if (data.provider_type === CollectionType.builtIn)
      return buildInTools.find(toolWithProvider => toolWithProvider.id === data.provider_id)?.author

    // if (data.provider_type === CollectionType.workflow)
    //   return workflowTools.find(toolWithProvider => toolWithProvider.id === data.provider_id)?.author

    return customTools.find(toolWithProvider => toolWithProvider.id === data.provider_id)?.author
  }, [data, nodesExtraData, buildInTools, customTools])

  const about = useMemo(() => {
    if (data.type !== BlockEnum.Tool)
      return nodesExtraData[data.type].about

    if (data.provider_type === CollectionType.builtIn)
      return buildInTools.find(toolWithProvider => toolWithProvider.id === data.provider_id)?.description[language]

    // if (data.provider_type === CollectionType.workflow)
    //   return workflowTools.find(toolWithProvider => toolWithProvider.id === data.provider_id)?.description[language]

    return customTools.find(toolWithProvider => toolWithProvider.id === data.provider_id)?.description[language]
  }, [data, nodesExtraData, language, buildInTools, customTools])

  const showChangeBlock = data.type !== BlockEnum.Start && !nodesReadOnly && data.type !== BlockEnum.Iteration
  const showHelpLink = true

  const link = 'https://k36drdpyul.feishu.cn/docx/B6htdYzRuovxiyxBIA0c2PlknPN'// useNodeHelpLink(data.type)

  const items: Array<ItemType<MenuItemType> | MenuItemGroupType> = useMemo(() => {
    const list: Array<ItemType<MenuItemType> | MenuItemGroupType> = []
    if (showChangeBlock || canRunBySingle(data.type)) {
      if (canRunBySingle(data.type)) {
        list.push(
          {
            key: 'runThisStep',
            // disabled: modelItem?.status === ModelStatusEnum.disabled,
            label: (
              <div
                onClick={() => {
                  handleNodeSelect(id)
                  handleNodeDataUpdate({ id, data: { _isSingleRun: true } })
                  handleSyncWorkflowDraft(true)
                }}
              >
                {t('workflow.panel.runThisStep')}
              </div>
            ),
          },
        )
      }
      if (showChangeBlock) {
        list.push({
          key: 'changeBlock',
          label: (
            <ChangeBlock
              nodeId={id}
              nodeData={data}
              sourceHandle={edge?.sourceHandle || 'source'}
            />
          ),
        })
      }
    }
    if (data.type !== BlockEnum.Start && !nodesReadOnly) {
      list.push(
        {
          type: 'divider',
        },
        // 拷贝
        // {
        //   key: 'copy',
        //   label: (
        //     <>
        //       <div
        //         onClick={() => {
        //           handleNodesCopy()
        //         }}
        //         className='flex justify-between items-center'
        //       >
        //         {t('workflow.common.copy')}
        //         <ShortcutsName keys={['ctrl', 'c']} />
        //       </div>
        //     </>
        //   )
        // },
        // 复制
        {
          key: 'duplicate',
          label: (
            <div
              onClick={() => {
                handleNodesDuplicate(id)
              }}
              className='flex justify-between items-center'
            >
              {t('workflow.common.duplicate')}
              <ShortcutsName keys={['ctrl', 'd']} />
            </div>
          ),
        },
        {
          type: 'divider',
        },
        // 删除
        {
          key: 'delete',
          label: (
            <div
              onClick={() => handleNodeDelete(id)}
              className='flex justify-between items-center'
            >
              {t('common.operation.delete')}
              <ShortcutsName keys={['del']} />
            </div>
          ),
        },
        // {
        //   type: 'divider',
        // },
      )
    }
    if (showHelpLink) {
      list.push(
        {
          type: 'divider',
        },
        {
          key: 'help',
          label: (
            <a
              href={link}
              target='_blank'
            >
              {t('workflow.panel.helpLink')}
            </a>
          ),
        },
      )
    }
    return list
  }, [])

  const footerNode = (
    <div className='px-3 py-2 text-xs text-gray-G2'>
      <div className={cn(style.normalTextG1, 'h-9 flex items-center')}>
        {t('workflow.panel.about').toLocaleUpperCase()}
      </div>
      <div className={cn(style.normalTextG2, 'mb-1.5')}>{about}</div>
      <div className={style.normalTextG3}>
        {t('workflow.panel.createdBy')} {author}
      </div>
    </div>
  )

  return (
    <Dropdown
      overlayClassName='panelOperatorDropdown'
      trigger={['click']}
      menu={{
        items,
        className: '!p-0 !shadow-none',
        style: {
          overflow: 'unset',
        },
      }}
      dropdownRender={(menus: React.ReactNode) => (
        <Scrollbar className='max-h-[50vh]'>
          <div
            style={{
              width: '268px',
              boxShadow: '0px 4px 8px rgba(152, 170, 187, 0.12)',
              background: 'white',
              borderRadius: '4px',
              padding: '8px 0',
            }}
            onClick={() => {}}
          >
            <ConfigProvider
              theme={{
                components: {
                  Menu: {
                    boxShadow: '0 0 #0000',
                  },
                },
              }}
            >
              {menus}
            </ConfigProvider>
            {/* 底部 */}
            {/* { footerNode } */}
          </div>
        </Scrollbar>
      )}
    >
      <TextButton variant='icon-deep'>
        <More className='w-4 h-4' />
      </TextButton>
    </Dropdown>
  )
}

export default memo(PanelOperatorPopup)
