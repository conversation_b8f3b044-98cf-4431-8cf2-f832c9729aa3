'use client'
import type { HTMLProps } from 'react'
import React, { useMemo, useState } from 'react'
import {
  Cog8ToothIcon,
  DocumentTextIcon,
  RocketLaunchIcon,
} from '@heroicons/react/24/outline'
import { usePathname, useRouter } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import { Switch } from 'antd'
import SettingsModal from '../../settings'
import EmbeddedModal from '../../embedded'
import type { ConfigParams } from '../../settings'
import style from '../style.module.css'
import s from './styles/index.module.css'
import { asyncRunSafe, randomString } from '@/utils'
import cn from '@/utils/classnames'
import SecretKeyButton from '@/app/components/develop/secret-key/secret-key-button'
import type { AppDetailResponse } from '@/models/app'
import { useAppContext } from '@/context/app-context'
import type { AppSSO } from '@/types/app'
// 公共组件
import CopyInput from '@/app/components/base/input/copy-input'
import Tag from '@/app/components/base/tag'
import Confirm from '@/app/components/base/confirm'
import ShareQRCode from '@/app/components/app/common/qrcode'
import Button from '@/app/components/base/button'
import Tooltip from '@/app/components/base/tooltip'
import AppBasic from '@/app/components/app-detail/app-basic'

export type IAppCardProps = {
  className?: string
  appInfo: AppDetailResponse & Partial<AppSSO>
  cardType?: 'api' | 'webapp'
  customBgColor?: string
  onChangeStatus: (val: boolean) => Promise<void>
  onSaveSiteConfig?: (params: ConfigParams) => Promise<void>
  onGenerateCode?: () => Promise<void>
}

const EmbedIcon = ({ className = '' }: HTMLProps<HTMLDivElement>) => {
  return <div className={`${style.codeBrowserIcon} ${className}`}></div>
}

function AppCard({
  appInfo,
  cardType = 'webapp',
  customBgColor,
  onChangeStatus,
  onSaveSiteConfig,
  onGenerateCode,
  className,
}: IAppCardProps) {
  const router = useRouter()
  const pathname = usePathname()
  const { t } = useTranslation()

  const { canAdmin, canEditApp } = useAppContext()
  // 卡片类型是否为webapp
  const isApp = cardType === 'webapp'
  // 卡片是否可启用
  const toggleDisabled = isApp ? !canEditApp : !canAdmin
  // 卡片运行状态
  const runningStatus = isApp ? appInfo.enable_site : appInfo.enable_api
  const { app_base_url, access_token } = appInfo.site ?? {}
  const appMode = (appInfo.mode !== 'completion' && appInfo.mode !== 'workflow') ? 'chat' : appInfo.mode
  const appUrl = `${app_base_url}/${appMode}/${access_token}`
  const apiUrl = appInfo?.api_base_url

  // 是否显示设置弹窗
  const [showSettingsModal, setShowSettingsModal] = useState(false)
  // 是否显示嵌入弹窗
  const [showEmbedded, setShowEmbedded] = useState(false)
  // 是否显示确认删除
  const [showConfirmDelete, setShowConfirmDelete] = useState(false)

  // 操作按钮数组
  const OPERATIONS_MAP = useMemo(() => {
    const operationsMap = {
      webapp: [
        { opName: t('appOverview.overview.appInfo.preview'), opIcon: RocketLaunchIcon },
        // 隐藏定制化按钮
        // { opName: t('appOverview.overview.appInfo.customize.entry'), opIcon: PaintBrushIcon },
      ] as { opName: string; opIcon: any }[],
      api: [{ opName: t('appOverview.overview.apiInfo.doc'), opIcon: DocumentTextIcon }],
      app: [],
    }
    if (appInfo.mode !== 'completion' && appInfo.mode !== 'workflow')
      operationsMap.webapp.push({ opName: t('appOverview.overview.appInfo.embedded.entry'), opIcon: EmbedIcon })

    // if (canEditApp)
    //   operationsMap.webapp.push({ opName: t('appOverview.overview.appInfo.settings.entry'), opIcon: Cog8ToothIcon })

    return operationsMap
  }, [canEditApp, appInfo, t])

  let bgColor = 'bg-primary-50 bg-opacity-40'
  if (cardType === 'api')
    bgColor = 'bg-purple-50'

  // 根据按钮名称生成点击事件
  const genClickFuncByName = (opName: string) => {
    switch (opName) {
      case t('appOverview.overview.appInfo.preview'):
        return () => {
          window.open(appUrl, '_blank')
        }
      case t('appOverview.overview.appInfo.settings.entry'):
        return () => {
          setShowSettingsModal(true)
        }
      case t('appOverview.overview.appInfo.embedded.entry'):
        return () => {
          setShowEmbedded(true)
        }
      default:
        // jump to page develop
        return () => {
          const pathSegments = pathname.split('/')
          pathSegments.pop()
          router.push(`${pathSegments.join('/')}/develop`)
        }
    }
  }
  // 生成编码
  const onGenCode = async () => {
    if (onGenerateCode)
      await asyncRunSafe(onGenerateCode())
  }

  return (
    <div
      className={cn(s.card, className, customBgColor ?? bgColor)}
    >
      {/* 顶部操作栏 */}
      <div className="mb-4 flex items-start justify-between">
        <AppBasic
          url={appInfo.icon_url as string}
          name={appInfo.name}
          type={
            isApp
              ? t('appOverview.overview.appInfo.explanation')
              : t('appOverview.overview.apiInfo.explanation')
          }
        ></AppBasic>
        <div className="flex flex-row items-center shrink-0 h-9">
          <Tag className="mr-2" color={runningStatus ? 'green' : 'yellow'}>
            {runningStatus
              ? t('appOverview.overview.status.running')
              : t('appOverview.overview.status.disable')}
          </Tag>
          <Switch value={runningStatus} onChange={onChangeStatus} disabled={toggleDisabled} />
        </div>
      </div>
      {/* 内容部分 */}
      <div className="flex flex-col justify-center mb-4">
        <div className="mb-2 text-xs text-gray-500">
          {isApp
            ? t('appOverview.overview.appInfo.accessibleAddress')
            : t('appOverview.overview.apiInfo.accessibleAddress')}
        </div>
        <CopyInput
          className='!bg-black !bg-opacity-2 !border-black !border-opacity-5 !text-gray-700'
          value={isApp ? appUrl : apiUrl}
          options={(copyBtn: React.ReactNode) => (
            <div className='flex items-center gap-3'>
              {/* 二维码 */}
              {isApp && <ShareQRCode app={appInfo} className='!w-4 !h-4' content={isApp ? appUrl : apiUrl} />}
              {/* 拷贝按钮 */}
              { copyBtn }
              {/* 刷新 */}
              {isApp && canAdmin && (
                <Tooltip
                  popupContent={t('appOverview.overview.appInfo.regenerate') || ''}
                >
                  <div
                    className="w-4 h-4 cursor-pointer rounded"
                    onClick={() => setShowConfirmDelete(true)}
                  >
                    <div
                      className={
                        `w-full h-full ${style.refreshIcon} ${style.generateLogo}`}
                    ></div>
                  </div>
                </Tooltip>
              )}
            </div>
          )}
        ></CopyInput>
      </div>
      {/* 底部按钮 */}
      <div className={'flex items-center flex-wrap gap-2'}>
        {!isApp && <SecretKeyButton className='flex-shrink-0' appId={appInfo.id} />}
        {OPERATIONS_MAP[cardType].map((op) => {
          // 禁用状态
          const disabled
              = op.opName === t('appOverview.overview.appInfo.settings.entry')
                ? false
                : !runningStatus
          return (
            <Button
              variant={'secondary'}
              key={op.opName}
              onClick={genClickFuncByName(op.opName)}
              disabled={disabled}
            >
              <Tooltip
                popupContent={
                  t('appOverview.overview.appInfo.preUseReminder') ?? ''
                }
                disabled={!disabled}
              >
                <div className="flex flex-row items-center">
                  <op.opIcon className="h-4 w-4 mr-1 stroke-[1.8px]" />
                  <span className="text-[14px]">{op.opName}</span>
                </div>
              </Tooltip>
            </Button>
          )
        })}
      </div>
      {isApp
        ? (
          <>
            {/* 设置弹窗 */}
            {showSettingsModal && <SettingsModal
              isChat={appMode === 'chat'}
              appInfo={appInfo}
              isShow={showSettingsModal}
              onClose={() => setShowSettingsModal(false)}
              onSave={onSaveSiteConfig}
            />}
            {/* 嵌入弹窗 */}
            {showEmbedded && <EmbeddedModal
              siteInfo={appInfo.site}
              isShow={showEmbedded}
              onClose={() => setShowEmbedded(false)}
              appBaseUrl={app_base_url}
              accessToken={access_token}
            />}
          </>
        )
        : null}
      {showConfirmDelete && (
        <Confirm
          type='warning'
          title={t('appOverview.overview.appInfo.regenerate')}
          content={t('appOverview.overview.appInfo.regenerateNotice')}
          isShow={showConfirmDelete}
          onConfirm={() => {
            onGenCode()
            setShowConfirmDelete(false)
          }}
          onCancel={() => setShowConfirmDelete(false)}
        />
      )}
    </div>
  )
}

export default AppCard
