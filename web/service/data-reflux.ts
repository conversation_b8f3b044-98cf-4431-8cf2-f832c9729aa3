import type { Fetcher } from 'swr'
import { get, post } from './base'

// 创建数据集
export const createDatasetReflux: Fetcher<any, { appId: string; body: Record<string, any> }> = ({ appId, body }) => {
  return post<any>(`/${appId}/databackflow/create`, { body })
}
// 创建数据集 /rest/v1/my-dataset
export const createMyDataset: Fetcher<any, { body: Record<string, any> }> = ({ body }) => {
  return post<any>('/aiagent-maas/openApi/rest/v1/my-dataset', { body })
}

// 获取数据集列表
export const fetchDatasetList: Fetcher<any, { url: string; params?: any }> = ({ url, params }) => {
  return get<any>(url, { params })
}

// 删除数据集
export const deleteDatasetReflux = (payload: any) => {
  return post('/databackflow/delete', {
    body: {
      ...payload,
    },
  })
}

// 查询数据集删除情况
export const getDatasetRefluxStatus = ({ id }: { id: string }) => {
  return get<any>(`/databackflow/get-dataset?dataset_uuid=${id}`)
}

// 重试
export const retryDatasetReflux = (payload: any) => {
  return post('/databackflow/retry', {
    body: {
      ...payload,
    },
  })
}