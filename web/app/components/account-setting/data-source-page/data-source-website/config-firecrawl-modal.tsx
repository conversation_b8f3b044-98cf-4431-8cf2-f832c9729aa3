'use client'
import type { FC } from 'react'
import React, { useCallback, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Form, Input } from 'antd'

import { Lock01 } from '@/app/components/base/icons/src/vender/solid/security'
import Button from '@/app/components/base/button'
import type { FirecrawlConfig } from '@/models/common'
import Toast from '@/app/components/base/toast'
import { createDataSourceApiKeyBinding } from '@/service/datasets'
import { LinkExternal02 } from '@/app/components/base/icons/src/vender/line/general'
import Modal from '@/app/components/base/modal'
type Props = {
  onCancel: () => void
  onSaved: () => void
}

const I18N_PREFIX = 'account.firecrawl'

const DEFAULT_BASE_URL = 'https://api.firecrawl.dev'

const ConfigFirecrawlModal: FC<Props> = ({
  onCancel,
  onSaved,
}) => {
  const { t } = useTranslation()
  // 真噶子保存
  const [isSaving, setIsSaving] = useState(false)
  // 配置信息
  const [config, setConfig] = useState<FirecrawlConfig>({
    api_key: '',
    base_url: '',
  })
  // 变更配置
  const handleConfigChange = useCallback((key: string) => {
    return (value: string | number) => {
      setConfig(prev => ({ ...prev, [key]: value as string }))
    }
  }, [])
  // 保存网站配置
  const handleSave = useCallback(async () => {
    if (isSaving)
      return
    let errorMsg = ''
    if (config.base_url && !((config.base_url.startsWith('http://') || config.base_url.startsWith('https://'))))
      errorMsg = t('common.validate.urlError')
    if (!errorMsg) {
      if (!config.api_key) {
        errorMsg = t('common.validate.fieldRequired', {
          field: 'API Key',
        })
      }
    }
    if (errorMsg) {
      Toast.notify({
        type: 'error',
        message: errorMsg,
      })
      return
    }

    const postData = {
      category: 'website',
      provider: 'firecrawl',
      credentials: {
        auth_type: 'bearer',
        config: {
          api_key: config.api_key,
          base_url: config.base_url || DEFAULT_BASE_URL,
        },
      },
    }
    try {
      setIsSaving(true)
      await createDataSourceApiKeyBinding(postData)
      Toast.notify({
        type: 'success',
        message: t('common.actionMsg.saveSuccessfully'),
      })
    }
    finally {
      setIsSaving(false)
    }

    onSaved()
  }, [config.api_key, config.base_url, onSaved, t, isSaving])

  return (
    <Modal
      isShow
      closable
      onClose={onCancel}
      title={t(`${I18N_PREFIX}.configFirecrawl`)}
      footer={
        <div className='w-full'>
          <div className='flex justify-end mb-4'>
            <Button
              size='large'
              variant={'secondary-accent'}
              onClick={onCancel}
              className='mr-4'
            >
              {t('common.operation.cancel')}
            </Button>
            <Button
              variant='primary'
              size='large'
              onClick={handleSave}
              loading={isSaving}
            >
              {t('common.operation.save')}
            </Button>
          </div>
          <div className='border-t-[0.5px] border-t-black/5'>
            <div className='flex justify-center items-center py-3 bg-gray-50 text-xs text-gray-500'>
              <Lock01 className='mr-1 w-3 h-3 text-gray-500' />
              {t('account.modelProvider.encrypted.front')}
              <a
                className='text-primary-600 mx-1'
                target='_blank' rel='noopener noreferrer'
                href='https://pycryptodome.readthedocs.io/en/latest/src/cipher/oaep.html'
              >
                  PKCS1_OAEP
              </a>
              {t('account.modelProvider.encrypted.back')}
            </div>
          </div>
        </div>
      }
    >
      <Form layout='vertical'>
        <Form.Item
          name={'api_key'}
          label="API Key"
          validateFirst
          rules={[
            {
              required: true,
              whitespace: true,
            },
          ]}
          validateTrigger='onBlur'
        >
          <Input
            value={config.api_key}
            onChange={e => handleConfigChange('api_key')(e.target.value)}
            placeholder={t(`${I18N_PREFIX}.apiKeyPlaceholder`)!}
          ></Input>
          <a className='flex items-center space-x-1 leading-[18px] text-xs font-normal text-[#155EEF]' target='_blank' href='https://www.firecrawl.dev/account'>
            <span>{t(`${I18N_PREFIX}.getApiKeyLinkText`)}</span>
            <LinkExternal02 className='w-3 h-3' />
          </a>
        </Form.Item>
        <Form.Item label="Base URL">
          <Input
            value={config.base_url}
            onChange={e => handleConfigChange('base_url')(e.target.value)}
            placeholder={DEFAULT_BASE_URL}
          ></Input>
        </Form.Item>
      </Form>

    </Modal>
  )
}
export default React.memo(ConfigFirecrawlModal)
