'use client'

import { useTranslation } from 'react-i18next'
import type { SwitchProps } from 'antd'
import { Switch } from 'antd'
import type { ButtonProps } from '@/app/components/base/button'
import Button from '@/app/components/base/button'
import Tooltip from '@/app/components/base/tooltip'
import { useAppContext } from '@/context/app-context'
import type { TextButtonType } from '@/app/components/base/button/text-button'
import TextButton from '@/app/components/base/button/text-button'

export const VideoActionBtn = ({
  children,
  className,
  ...res
}: ButtonProps) => {
  const { t } = useTranslation()
  const { canEditVideoLib } = useAppContext()

  if (!canEditVideoLib) {
    return <Tooltip
      popupContent={t('videos.notify.noPermission')}
    >
      <Button disabled={true} className={className} {...res}>{children}</Button>
    </Tooltip>
  }

  return <Button className={className} {...res}>{children}</Button>
}

export const VideoActionTextBtn = ({
  children,
  className,
  ...res
}: TextButtonType) => {
  const { t } = useTranslation()
  const { canEditVideoLib } = useAppContext()

  if (!canEditVideoLib) {
    return <Tooltip
      popupContent={t('videos.notify.noPermission')}
    >
      <TextButton disabled={true} className={className} {...res}>{children}</TextButton>
    </Tooltip>
  }

  return <TextButton className={className} {...res}>{children}</TextButton>
}

export const VideoActionSwitch = ({
  className,
  ...res
}: SwitchProps) => {
  const { t } = useTranslation()
  const { canEditVideoLib } = useAppContext()
  if (!canEditVideoLib) {
    return <Tooltip
      popupContent={t('videos.notify.noPermission')}
    >
      <Switch disabled={true} className={className} {...res}></Switch>
    </Tooltip>
  }

  return <Switch className={className} {...res}></Switch>
}
