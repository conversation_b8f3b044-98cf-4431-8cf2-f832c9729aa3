import React, { useEffect, useState } from 'react'
import { Card, Form, Input, List, Tooltip } from 'antd'
import s from './style.module.css'
type TableFormData = {
  [key: string]: string
}

type TableData = {
  headers: string[]
  values: string[]
  detail?: any
}

const { TextArea } = Input

type TableContentProps = {
  editable?: boolean
  detail?: any
  segInfo?: any
  onDataChange?: (data: TableData) => void
}

const TableContent: React.FC<TableContentProps> = ({
  editable = false,
  detail,
  segInfo,
  onDataChange,
}) => {
  const [form] = Form.useForm<TableFormData>()
  const [formFields, setFormFields] = useState<Array<{ name: string; label: string }>>([])
  const [lastSegInfoId, setLastSegInfoId] = useState<string>('')

  // 处理表单提交
  const handleFinish = (values: TableFormData) => {
    if (!onDataChange)
      return

    // 直接计算数据，避免使用 transformFormData
    const headers: string[] = []
    const tableValues: string[] = []
    formFields.forEach((field: { name: string; label: string }) => {
      tableValues.push(field.label)
      headers.push(values[field.name] || '')
    })
    onDataChange({ headers, values: tableValues, detail })
  }

  // 处理表单值变化
  const handleValuesChange = (_: any, allValues: TableFormData) => {
    if (!editable || !onDataChange)
      return

    // 直接计算数据，避免使用 transformFormData
    const headers: string[] = []
    const values: string[] = []
    formFields.forEach((field: { name: string; label: string }) => {
      values.push(field.label)
      headers.push(allValues[field.name] || '')
    })
    onDataChange({ headers, values, detail })
  }

  // 从 detail 数据生成表单字段
  useEffect(() => {
    console.log('TableContent useEffect - segInfo:', segInfo)
    const headers = segInfo?.headers || segInfo?.columns
    console.log('TableContent useEffect - headers:', headers)
    if (!headers)
      return

    const currentSegInfoId = segInfo?.id || ''

    // 只有当segInfo发生变化时才重新初始化
    if (currentSegInfoId !== lastSegInfoId) {
      // 生成表单字段配置
      const fields = headers.map((title: string, index: number) => ({
        name: `column${index}`,
        label: title,
      }))
      setFormFields(fields)

      // 设置表单初始值
      // 如果是新增模式且segInfo没有id，则清空表单；否则使用segInfo的值
      const isNewAdd = !segInfo?.id || segInfo?.id === ''
      const initialValues = fields.reduce((acc: TableFormData, field: { name: string; label: string }) => ({
        ...acc,
        [field.name]: isNewAdd ? '' : (segInfo.values?.[fields.indexOf(field)] || ''),
      }), {})

      form.setFieldsValue(initialValues)
      setLastSegInfoId(currentSegInfoId)

      // 初始化完成后调用 onDataChange
      if (onDataChange) {
        const headers: string[] = []
        const values: string[] = []
        fields.forEach((field: { name: string; label: string }) => {
          values.push(field.label)
          headers.push(initialValues[field.name] || '')
        })
        onDataChange({ headers, values, detail })
      }
    }
  }, [segInfo?.id, segInfo?.headers, segInfo?.columns, segInfo?.values, lastSegInfoId]) // 包含 lastSegInfoId 依赖

  const renderReadOnlyContent = () => {
    return (
      <List
        className={s.listStyle}
        header={
          <div className={s.listHeader}>
            <span>字段名</span>
            <span>字段值</span>
          </div>
        }
        dataSource={formFields}
        renderItem={(field) => {
          const fieldValue = form.getFieldValue(field.name)
          const hasLongContent = (fieldValue?.match(/\n/g) || []).length > 2

          return (
            <List.Item className={s.listItem}>
              <div className={s.fieldLabel}>{field.label}</div>
              {hasLongContent
                ? (
                  <Tooltip title={fieldValue}>
                    <div className={s.fieldValue}>{fieldValue}</div>
                  </Tooltip>
                )
                : (
                  <div className={s.fieldValue}>{fieldValue}</div>
                )}
            </List.Item>
          )
        }}
      />
    )
  }

  return (
    <Card>
      {editable
        ? (
          <Form
            form={form}
            layout="horizontal"
            onFinish={handleFinish}
            disabled={!editable}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}
            style={{ minWidth: 480, maxWidth: 480 }}
            onValuesChange={handleValuesChange}
          >
            {formFields.map(field => (
              <Form.Item
                key={field.name}
                name={field.name}
                label={field.label}
              >
                <TextArea
                  placeholder={`请输入${field.label}`}
                  autoComplete="off"
                  autoSize={{ minRows: 1 }}
                />
              </Form.Item>
            ))}
          </Form>
        )
        : renderReadOnlyContent()}
    </Card>
  )
}

export default TableContent
