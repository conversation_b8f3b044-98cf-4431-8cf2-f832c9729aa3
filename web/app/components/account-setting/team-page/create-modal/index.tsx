'use client'
import { use<PERSON><PERSON>back, useMemo, useState, useEffect } from 'react'
import type { FormProps, } from 'antd';
import { Form, Input, Select, Pagination, Skeleton, Divider, Card} from 'antd'
import { useContext } from 'use-context-selector'
import { useTranslation } from 'react-i18next'
import { ReactMultiEmail } from 'react-multi-email'
import 'react-multi-email/dist/style.css'
import { inviteMember,  } from '@/service/common'
import { setTeamCreate, setTeamMemberAdd } from '@/service/team'
import { getAccountQuery } from '@/service/team'
import { emailRegex } from '@/config'
import { ToastContext } from '@/app/components/base/toast'
import { useProviderContext } from '@/context/provider-context'
import type { InvitationResult } from '@/models/common'
import I18n from '@/context/i18n'
import type { TeamCreateType } from '@/app/components/account-setting/team-page/team-type'
import s from '@/app/components/account-setting/team-page/style.module.css'
import VirtualList from 'rc-virtual-list'

// 公共组件
import Modal from '@/app/components/base/modal'
import Button from '@/app/components/base/button'
import CheckSelect from '@/app/components/base/select/check-select'
import { A } from '@svgdotjs/svg.js';
import SearchInput from '@/app/components/base/input/search-input'
import Loading from '@/app/components/base/loading'

type ICreateModalPropsProps = {
  onCancel: () => void
  onSend: (invitationResults: InvitationResult[]) => void
  title?: string
  showTeamName?: boolean
  showEditor?: boolean
  showDatasetOperator?: boolean
  teamIdValue?: string
}

const CreateModal = ({
  onCancel,
  onSend,
  title = '',
  showTeamName = true,
  showEditor = true,
  showDatasetOperator = true,
  teamIdValue = '',
}: ICreateModalPropsProps) => {
  const { t } = useTranslation()
  const [form] = Form.useForm()
  const values = Form.useWatch([], form)
  // const [emails, setEmails] = useState<string[]>([])
  const { notify } = useContext(ToastContext)
  const { datasetOperatorEnabled } = useProviderContext()
  const { locale } = useContext(I18n)
  const [role, setRole] = useState<string>('normal')
  const [teamValue, setTeamValue] = useState<string>('')
  const [memberValue, setMemberValue] = useState<string>([])
  const [isLoading, setIsLoading] = useState(false)
  // 团队成员
  const [teamMemberValue, setTeamMemberValue] = useState<any[]>([])
  const MAX_COUNT = 4
  
  const handleSend = useCallback(async () => {
    try {
      const values = await form.validateFields()
      setIsLoading(true)
      const body: TeamCreateType = {
        user_id: values?.memberInfo || [],
        role: values?.role || ''
      }
      let res: any = {}
      if (showTeamName) {
        // 创建团队
        body.team_name = values?.teamName || ''
        res = await setTeamCreate(body)
      }
      else {
        // 添加成员
        body.team_id= teamIdValue
        res = await setTeamMemberAdd(body)
      }
      if (res?.code === 200) {
        notify({
          type: 'success',
          message: res?.message,
        })
      }
      else{
        notify({
          type: 'error',
          message: res?.message,
        })
      }
      onCancel()
      onSend(res)
    }
    catch (e) {
      // notify({ type: 'error', message: t('account.team.createTeamDesc') })
    } finally {
      setIsLoading(false)
    }
    
  }, [])
  // 角色下拉列表
  const roleSelectList = [
    {
      label: t('account.role.normal'),
      desc: t('account.role.normalTipPublicCloud'),
      value: 'normal',
    },
    ...(showEditor
      ? [
        {
          label: t('account.role.editor'),
          desc: t('account.role.editorTip'),
          value: 'editor',
        },
      ]
      : []
    ),
    {
      label: t('account.role.admin'),
      desc: t('account.role.adminTipPublicCloud'),
      value: 'admin',
    },
    ...((datasetOperatorEnabled && showDatasetOperator)
      ? [
        {
          label: t('account.role.datasetOperator'),
          desc: t('account.role.datasetOperatorTip'),
          value: 'dataset_operator',
        },
      ]
      : []),
  ]
  // 成员分页
  const [currentPage, setCurrentPage] = useState(1) // 当前页码
  const [itemsPerPage, setItemsPerPage] = useState(9999) // 每页显示的条数
  const [totalItems, setTotalItems] = useState(0) // 总条数
  const [searchValue, setSearchValue] = useState('') // 搜索值
  // const [totalPages, setTotalPages] = useState(0) // 总页数
  const [isAccountLoading, setIsAccountLoading] = useState(false)
  // 团队成员 getAccountQuery
  const accountQuerys = async () => {
    setIsAccountLoading(true)

    const data = {
      user_id: '',
      page: currentPage,
      size: itemsPerPage,
      search: searchValue,
    }
    if (!showTeamName) {
      // 添加成员
      data.team_id= teamIdValue
    }
    const res = await getAccountQuery({
      data,
    })
    const list: any[] = []
    if (res?.data?.items?.length > 0) {
      res?.data?.items.map((item: any) => {
        list.push({
          label: item?.name,
          value: item?.id,
        })
      })
    }

    setTotalItems(res.data.total)
    setTeamMemberValue(list)
    setIsAccountLoading(false)
  }
  useEffect(() => {
    accountQuerys()
  },[currentPage, searchValue])

  // 检查是否禁用按钮
  const isButtonDisabled = useMemo(() => {
    if (showTeamName) {
      return !values?.teamName || !values?.memberInfo || !values?.role
    }
    return !values?.memberInfo || !values?.role
  }, [values, showTeamName])
 
  return (
    <Modal
      closable
      isShow
      onClose={onCancel}
      className='!w-[400px] !max-w-[400px]'
      title={title}
      // description={t('common.members.inviteTeamMemberTip')}
      footer={
        <>
          <Button variant='secondary-accent' className='mr-4' onClick={onCancel}>{t('common.operation.cancel')}</Button>
          <Button 
            disabled={isButtonDisabled} 
            variant='primary' 
            onClick={handleSend}
          >
            {t('common.operation.save')}
          </Button>
        </>
      }
    >
      <Form form={form} layout='vertical' initialValues={{
        role,
      }}>
        {/* 团队名称 */}
        {
          showTeamName && (
            <Form.Item
              label={t('account.team.teamName')}
              name="teamName"
              validateFirst={true}
              validateTrigger='onBlur'
              rules={[
                {
                  required: true,
                  whitespace: true,
                  pattern: /^[a-zA-Z0-9\u4E00-\u9FA5()-_.]{1,20}$/,
                },
              ]}
            >
              <Input
                // onChange={e => teamChange(e?.target?.value || '')}
                onChange={(e) => setTeamValue(e.target.value)}
              />
            </Form.Item>
          )
        }
        {/* 成员信息 */}
        <Form.Item
          label={t('account.team.teamNameInfo.membersInfo')}
          name="memberInfo"
          rules={[{ required: true }]}
        >
          <Select
            mode="multiple"
            placeholder={t('account.team.teamNameInfo.membersInfo')}
            allowClear
            showSearch
            optionFilterProp='label'
            maxCount={MAX_COUNT}
            onChange={(e) => setMemberValue(e)}
            options={teamMemberValue}
            className='select-multiple-style'
            optionRender={option => (
              <>
                <div className='text-sm leading-5' style={{ marginRight: '8px' }}>{option.label}</div>
              </>
            )}
          />
        </Form.Item>
        {/* 成员角色 */}
        <Form.Item
          label={t('account.team.teamNameInfo.membersRole')}
          name="role"
          rules={[{ required: true }]}
        >
          <CheckSelect
            className='w-full'
            value={role}
            onChange={e => setRole(e)}
            // onChange={e => roleChange(e)}
            options={roleSelectList}
            optionRender={option => (
              <>
                <div className='text-sm leading-5'>{option.label}</div>
                <div className='text-xs leading-[18px]'>{option.data.desc}</div>
              </>
            )}
          ></CheckSelect>
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default CreateModal
