import { useEffect, useMemo, useState } from 'react'
import { Form, Radio } from 'antd'
import { useTranslation } from 'react-i18next'
import type { RadioChangeEvent } from 'antd/lib'
import type { ConfigurationMethodEnum, CustomConfigurationModelFixedFields, ModelProvider } from '../declarations'

import ModelModal from '.'
import Select from '@/app/components/base/select/new-index'
import { useI18N } from '@/context/i18n'
import { getLanguage } from '@/i18n/language'

type ProviderModalProps = {
  isShow: boolean
  providerList: ModelProvider[]
  onSave: (
    provider: ModelProvider,
    CustomConfigurationModelFixedFields?: CustomConfigurationModelFixedFields,
  ) => void
  onCancel: () => void
}

const ProviderModal = ({ onSave, isShow, providerList, onCancel }: ProviderModalProps) => {
  const { t } = useTranslation()
  const { locale } = useI18N()
  // 当前provider
  const [currentProvider, setCurrentProvider] = useState<ModelProvider>()
  // 当前配置方式
  const [currentConfigType, setCurrentConfigType] = useState<ConfigurationMethodEnum>()

  // provider下拉列表
  const options = useMemo(() => {
    return providerList.map((provider) => {
      return {
        label: provider.label[getLanguage(locale)],
        value: provider.provider,
      }
    })
  }, [locale, providerList])
  // 当前provider的唯一标识
  const currentProviderId = useMemo(() => {
    return currentProvider?.provider
  }, [currentProvider?.provider])
  // 是否显示配置类型选择
  const showConfigType = useMemo(() => {
    return currentProvider ? currentProvider.configurate_methods.length > 1 : false
  }, [currentProvider])
  // 配置类型选项
  const configTypeOptions = useMemo(() => {
    return currentProvider
      ? currentProvider.configurate_methods.map((item) => {
        return {
          label: t(`account.modelProvider.configType.${item}`),
          value: item,
        }
      })
      : []
  }, [currentProvider, t])

  useEffect(() => {
    setCurrentProvider(providerList[0])
    setCurrentConfigType(providerList[0]?.configurate_methods[0])
  }, [providerList])

  // 变更provider
  const changeProvider = (providerId: string) => {
    const provider = providerList.find(item => item.provider === providerId)!
    setCurrentProvider(provider)
    setCurrentConfigType(provider?.configurate_methods[0])
  }
  if (!currentProvider || !currentConfigType)
    return null

  return (
    <>
      { isShow && <ModelModal
        provider={currentProvider}
        configType={currentConfigType}
        onCancel={onCancel}
        onSave={() => onSave(currentProvider)}
      >
        <Form.Item layout='vertical' required label={t('account.modelProvider.form.providerSelect')}>
          <Select
            className='w-full'
            options={options}
            value={currentProviderId}
            onChange={changeProvider}
          ></Select>
        </Form.Item>
        {
          showConfigType
           && <Form.Item layout='vertical' required label={t('account.modelProvider.form.configTypeSelect')} >
             <Radio.Group
               options={configTypeOptions}
               value={currentConfigType}
               onChange={({ target: { value } }: RadioChangeEvent) => setCurrentConfigType(value)}
               optionType='button'
             ></Radio.Group>
           </Form.Item>
        }

      </ModelModal> }
    </>
  )
}

export default ProviderModal
