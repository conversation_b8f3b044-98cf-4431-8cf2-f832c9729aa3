const fs = require('node:fs')
const path = require('node:path')

// 递归查找目录下所有.tsx文件
function findTSXFiles(dir) {
  const files = fs.readdirSync(dir)
  const tsxFiles = []

  files.forEach((file) => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)

    if (stat.isDirectory())
      tsxFiles.push(...findTSXFiles(filePath))

    else if (path.extname(file) === '.tsx')
      tsxFiles.push(filePath)
  })

  return tsxFiles
}

// 移除注释内容
function removeComments(code) {
  // 移除单行注释
  code = code.replace(/\/\/.*$/gm, '')
  // 删除console.log
  code = code.replace(/console\.log\(.*\)$/gm, '')
  // 移除多行注释
  code = code.replace(/\/\*[\s\S]*?\*\//g, '')
  return code
}

// 查找中文内容
function findChineseCharacters(code) {
  const chineseRegex = /[\u4E00-\u9FA5]/g
  const matches = code.match(chineseRegex)
  return matches ? matches.join('') : ''
}

// 主函数
function main() {
  const projectDir = '.' // 当前目录
  const tsxFiles = findTSXFiles(projectDir)

  tsxFiles.forEach((file) => {
    const code = fs.readFileSync(file, 'utf8')
    const codeWithoutComments = removeComments(code)
    const chineseCharacters = findChineseCharacters(codeWithoutComments)

    if (chineseCharacters) {
      // console.log(`File: ${file}`)
      // console.log(`Chinese Characters: ${chineseCharacters}`)
      // console.log('-------------------')
    }
  })
}

main()
