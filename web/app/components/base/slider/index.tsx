import React, { useCallback } from 'react'
import { Slider as ASlider, InputNumber } from 'antd'
import type { SliderRangeProps, SliderSingleProps } from 'antd/es/slider'
import s from './style.module.css'
import cn from '@/utils/classnames'

type SliderProps = SliderSingleProps | SliderRangeProps
type CustomSliderProps = {
  wrapClassName?: string
  className?: string
  showText?: boolean
  showInput?: boolean
  value: number
  defaultValue?: number
  onChange: (value: number) => void
  max?: number
  min?: number
  step?: number
}

const Slider: React.FC<SliderSingleProps & CustomSliderProps> = (props) => {
  const { wrapClassName, className, showText = false, showInput = false, step = 1, onChange, defaultValue = 0, ...rest } = props
  const { value, max, min } = props

  // 处理数字输入框blur事件
  const handleBlur = useCallback(() => {
    if (value === undefined || value === null) {
      onChange(defaultValue)
      return
    }
    if (max !== undefined && value > max) {
      onChange(max)
      return
    }
    if (min !== undefined && value < min)
      onChange(min)
  }, [defaultValue, max, min, onChange, value])
  // 处理数字输入框变化事件
  const handleChange = useCallback((value: number | string | null) => {
    if (typeof value === 'number')
      onChange(value as number)
    else
      onChange(parseFloat(value || ''))
  }, [onChange])

  return <div className={cn('flex items-center gap-3 w-full', wrapClassName)}>
    <ASlider className={cn('w-full', className)} range={false} max={max} min={min} step={step} defaultValue={defaultValue} onChange={onChange as any } {...rest}/>
    {showText && <span className={s.valueText}>{value}</span>}
    {showInput && <InputNumber
      className='!shrink-0'
      value={value}
      min={min}
      max={max}
      step={step}
      defaultValue={defaultValue}
      onChange={handleChange}
      onBlur={handleBlur}
      disabled={rest.disabled}
    />
    }
  </div>
}

export default Slider
