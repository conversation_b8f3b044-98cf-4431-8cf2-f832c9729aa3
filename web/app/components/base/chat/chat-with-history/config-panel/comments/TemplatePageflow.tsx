import { useTranslation } from 'react-i18next'
import Button from '@/app/components/base/button'
import '@/app/styles/switchLongTxt.scss'
import { upload } from '@/service/base'
import { CheckCard } from '@ant-design/pro-components'
import { PlusOutlined, EllipsisOutlined } from '@ant-design/icons'
import { useChatWithHistoryContext } from '../../context'
import { use, useEffect, useRef, useState } from 'react'
import { Dropdown, Input, message, Tooltip, Upload } from 'antd'
import type { GetProp, UploadFile, UploadProps } from 'antd'
import Field from '@/app/components/app/configuration/config-var/config-modal/field'
import { fetchGetTemplateList, GetTemplateList } from '@/service/share'
import TemplateDrawer from './templateDrawer'
import { TEMPLATE_URL } from '@/config'
import style from '@/app/components/base/chat/chat-with-history/styles/style.module.css'

interface TypeSelectorProps {
  selectedType: string,
  chatId:any,
  onTypeSelect: (type: string, item: any) => void
}

type FileType = Parameters<GetProp<UploadProps, 'beforeUpload'>>[0]

const TemplatePage = ({ selectedType, chatId, onTypeSelect }: TypeSelectorProps) => {
  const { t } = useTranslation()
  const [topicType, setopicType] = useState<string>('')
  const [ReportType, seReportType] = useState<string>('')
  const [fileList, setFileList] = useState<UploadFile[]>([])
  const [formValues, setFormValues] = useState<Record<string, string>>({})
  const [previewOpen, setPreviewOpen] = useState(false)
  const [previewImage, setPreviewImage] = useState('')
  const [open, setOpen] = useState(false)
  const [drawerData, setDrawerData] = useState<any>({})
  const UploadFiles = useRef<any>({})
  const { setChatAllTypeResult, chatAllTypeResult, appId } = useChatWithHistoryContext()
  const [templateList, setTemplateList] = useState<any[]>([])
  const [templateList1, setTemplateList1] = useState<any[]>([])
  const [templateList2, setTemplateList2] = useState<any[]>([])
  const selectedValue = useRef<any>({})
  const checkCardListRef = useRef<any>(null);


  useEffect(() => {
    handleGetTemplateList(selectedType)
  }, [selectedType])

  const handleGetTemplateList = async (e: any) => {
    const res = await GetTemplateList(
      {
        app_id: chatId,
        level1: e,
        get_type: 'all'
      },
      true
    )
    if (res) {
      setTemplateList(res as any)
    }
  }

  useEffect(() => {
    if (topicType && ReportType) {
      if (chatAllTypeResult.files) {
        setChatAllTypeResult({
          files: {}
        })
      }
      if (chatAllTypeResult.Upload) {
        setChatAllTypeResult({
          Upload: {}
        })
      }
    }
  }, [ReportType])

  useEffect(() => {
    if (templateList2) {
      selectedValue.current = templateList2[0]
      onTypeSelect('Files',templateList2[0])
    }
  }, [templateList2])

  const getBase64 = (file: FileType): Promise<string> =>
    new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = error => reject(error)
    })
  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as FileType)
    }

    setPreviewImage(file.url || (file.preview as string))
    setPreviewOpen(true)
  }

  const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    const validFileList = newFileList.filter(file => file.size !== undefined && file.size > 0);
    setFileList(validFileList);
  };

  const uploadButton = (
    <button style={{ border: 0, background: 'none' }} type="button">
      <PlusOutlined />
      <div style={{ marginTop: 10 }}>{t('sampleTemplate.longText.uploadmsg')}</div>
    </button>
  )

  const onProgress = (e: ProgressEvent, file: UploadFile) => {
    if (e.lengthComputable) {
      const percent = Math.floor((e.loaded / e.total) * 100)
      setFileList(prevFileList =>
        prevFileList.map(f => (f.uid === file.uid ? { ...f, percent } : f))
      )
    }
  }

  const customRequest = async (options: any) => {

    const file = options.file;
    if (file.size === 0) {
      message.error(t('sampleTemplate.longText.FileCannotBeEmpty')!);
      setFileList(prevFileList =>
        prevFileList.filter(f => f.uid !== options.file.uid)
      );
      return;
    }

    const formData = new FormData()
    formData.append('file', options.file)
    formData.append('source', 'web')
    upload(
      {
        xhr: new XMLHttpRequest(),
        data: formData,
        onprogress: (e: ProgressEvent) => onProgress(e, options.file)
      },
      false,
      ''
    )
      .then((res: { id: string; name: string; url: string }) => {
        if (res) {
          const files = {
            file_name: res.name,
            file_id: res.id,
            file_path: `${TEMPLATE_URL}files/${res.id}/file-preview-look`
          }
          UploadFiles.current = files
          setChatAllTypeResult({
           files: UploadFiles.current
          })
         selectedValue.current = UploadFiles.current;
        }
        setFileList(prevFileList =>
          prevFileList.map(f => (f.uid === options.file.uid ? { ...f, status: 'done' } : f))
        )
      })
      .catch(error => {
        setFileList(prevFileList =>
          prevFileList.map(f => (f.uid === options.file.uid ? { ...f, status: 'error' } : f))
        )
      })
  }

  const handleRemove = (file: UploadFile) => {
    setChatAllTypeResult({
      Upload: {}
    })
  }

  const handlePayloadChange = (key: string, value: any) => {
    const newFormValues = {
      ...formValues,
      [key]: value
    }
    setFormValues(newFormValues)
    onTypeSelect('Form',newFormValues);
  }

  const customItemRender = (
    originNode: React.ReactElement,
    file: UploadFile,
    fileList: UploadFile[],
    actions: any
  ) => {
    return (
      <div className="custom-upload-item">
        <div className="file-actions">
          <Tooltip title={file.name} key={file.uid}>
            <span className='long-span-Tabs'>
              <CheckCard
                key={file.uid}
                avatar={getAvatarForFileType(file.name)}
                title={file.name}
                style={{ width: 134, height: 200 }}
                value={UploadFiles.current}
                extra={
                  <Dropdown
                    placement="topCenter"
                    menu={{
                      onClick: e => {
                        if (e.key === '1') {
                          handleDrawer(UploadFiles.current)
                        } else if (e.key === '2') {
                          actions.remove(file)
                        }
                      },
                      items: [
                        {
                          label:t('sampleTemplate.longText.look'),
                          key: '1'
                        },
                        {
                          label:t('sampleTemplate.longText.deltxt'),
                          key: '2'
                        }
                      ]
                    }}
                  >
                    <EllipsisOutlined
                      style={{ fontSize: 22, color: 'rgba(0,0,0,0.5)' }}
                      onClick={e => e.stopPropagation()}
                    />
                  </Dropdown>
                }
              />
            </span>
          </Tooltip>
          {/* {file.status === 'done' && <Button onClick={() => handlePreview(file)}>Preview</Button>} */}
          {/* <Button onClick={() => actions.remove(file)}>Remove</Button> */}
        </div>
      </div>
    )
  }

  const fileTypeToAvatarMap: Record<string, string> = {
    '.docx': '/assets/image/word.png',
    '.doc': '/assets/image/word.png',
    '.pdf': '/assets/image/pdf.png',
    '.txt': '/assets/image/txt.png',
    '.xls': '/assets/image/excel.png',
    '.xlsx': '/assets/image/excel.png'
  }

    const defaultBj = '/assets/topremark.png'

  const getAvatarForFileType = (fileName: string): string => {
    const extension = fileName.slice(fileName.lastIndexOf('.')).toLowerCase()
    return fileTypeToAvatarMap[extension] || '/assets/image/default.png' // 默认图标
  }

  const handleDrawer = (item: any) => {
    setDrawerData(item)
    setOpen(true)
  }

  const onClose = () => {
    setOpen(false)
  }

  const clearFormValues = () => {
    setFormValues({})
    setFileList([])
    const updatedForm = {
      form: {},
      files: {}
    }
    setChatAllTypeResult(updatedForm)
  }
  const getThumbPath=(item:any)=>{
    let path = defaultBj;
    if(item.file_pic){
      if(window.location.href.includes('localhost')){
        path="http://dify3.s9537.cn/"+item.file_pic;
      }else{
        path=item.file_pic
      }
    }
    return path;
  }
  const reshtml=(item:any)=>{
    return (
     <>
      <div  className="defaultpic">
       <img src={getThumbPath(item)} alt="top"  />
      </div>
      <div className='docHasChild xq-border-box'>
        <div className='xq-flex-cbetween'>
            <img src={getAvatarForFileType(item.file_name)} className="docHasIco" alt="top"  />
            <Dropdown
                placement="top"
                menu={{
                  onClick: () => {
                    handleDrawer(item)
                  },
                  items: [
                    {
                      label:t('sampleTemplate.longText.look'),
                      key: '1'
                    }
                  ]
                }}
              >
                <EllipsisOutlined
                  style={{ fontSize: 22, color: 'rgba(0,0,0,0.5)' }}
                  onClick={e => e.stopPropagation()}
                />
              </Dropdown>
          </div>
          <p className="docName docMt">{item.file_name}</p>
        </div>
       </>
    )
  }

  return (
    <>
      <TemplateDrawer open={open} drawerData={drawerData} onClose={onClose}></TemplateDrawer>
      <div className="TopicType">
        <div className="mt-2">

          <div className={'style.title_h1'}>{t('sampleTemplate.FormDataEvent.SampleTemplate')}</div>
          {templateList.map((item: any, index: number) => (
            <Button
              key={index}
              variant="secondary-accent"
              size="small"
              className={`shrink-0 TopicButtons flowbtn ${topicType === item.text ? 'TopicActive' : ''}`}
              onClick={() => {
                seReportType('')
                setopicType(item.text)
                setTemplateList1(item.children)
                const data = {
                  text: item.text
                }
                onTypeSelect('topic', data)
              }}
            >
              {/* <PlusOutlined size={16} /> */}
              {item.text}
            </Button>
          ))}
        </div>
        <div className="mt-2">
          {topicType && (
            <>

              <div>{t('sampleTemplate.FormDataEvent.TemplateType')}</div>
              {templateList1.map((item: any, index: number) => (

                 <Button
                  key={index}
                  variant="secondary-accent"
                  size="small"
                  className={`shrink-0 TopicButtons flowbtn ${ReportType === item.text ? 'TopicActive' : ''}`}              
                  onClick={() => {
                    clearFormValues()
                    seReportType(item.text)
                    setTemplateList2(item.children)
                    const data = {
                      text: item.text
                    }
                    onTypeSelect('Report', data)
                  }}
                >
                   {item.text}
                  {/* <div className={`hhd flex  ${style.mt_text}`}> */}
                  {/* <div className={style.mt_icon}><PlusOutlined  size={16}/></div> */}
                  {/* <PlusOutlined  size={16}/> */}
                  {/* {item.text}</div> */}
                  {/* <div className={style.mt_tip}>文字文字测试</div> */}
                </Button>


              ))}
            </>
          )}
        </div>
        <div className="CheckCardList">
          <CheckCard.Group
            value={selectedValue.current}
            onChange={(value: any) => {
              if (value?.originFileObj && value?.status == 'done') {
                selectedValue.current = UploadFiles.current
                onTypeSelect('Files', UploadFiles.current)
                // setChatAllTypeResult({
                //   files: UploadFiles.current
                // })
              } else {
                selectedValue.current = value
                onTypeSelect('Files',value);
                // setChatAllTypeResult({
                //   files: value
                // })
              }
            }}
          >
            {ReportType ? (
              <>
                {(
                  t('sampleTemplate.FormDataEvent.ArrayFormList', { returnObjects: true }) as any[]
                ).map((item: any, index: number) => (
                  <Field
                    key={index}
                    className={`mt-3 Fieldbtn ${item.required ? 'FieldBefore' : ''}`}
                    title={item.title}
                  >
                    <Input
                      key={index}
                      className="mt-1"
                      type={item.type}
                      value={formValues[item.field ? item.field : item.title]}
                      onChange={e => {
                        const value = e.target.value;
                        const numericValue = item.type === 'number' ? Math.max(0, Number(value)) : value;

                        handlePayloadChange(item.field ? item.field : item.title, String(numericValue));
                      }}
                      placeholder={item.placeholder}
                    />
                  </Field>
                ))}
                <div className="mt-4 flex flex-wrap" ref={checkCardListRef.current}>
                  {templateList2.map((item: any, index: number) => (
                      <span className='long-span-Tabs-div'  key={index}>
                        <CheckCard
                          key={index}
                          avatar=""
                          title=""
                          className='docHas xq-border-box'
                          value={item}
                          extra={
                            reshtml(item)
                          }
                        />
                      </span>
                  ))}
                  {/* <Upload
                    action="#"
                    accept=".pdf,.docx,.txt"
                    customRequest={customRequest}
                    listType="picture-card"
                    className="image-other xq-border-box"
                    maxCount={1}
                    fileList={fileList}
                    onPreview={handlePreview}
                    onChange={handleChange}
                    onRemove={handleRemove}
                    itemRender={customItemRender}
                  >
                    {fileList.length >= 1 ? null : uploadButton}
                  </Upload> */}
                </div>
              </>
            ) : null}
          </CheckCard.Group>
        </div>
        {/* <div>{JSON.stringify(chatAllTypeResult)}</div> */}
      </div>
    </>
  )
}

export default TemplatePage
