'use client'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import cn from 'classnames'
import { Form, Input } from 'antd'

import EditNameModal from './edit-name-modal'
import EditPwdModal from './edit-pwd-modal'
import { IS_CE_EDITION } from '@/config'
import { useAppContext } from '@/context/app-context'
/* 公共组件 */
import Button from '@/app/components/base/button'
import Avatar from '@/app/components/base/avatar'
import { useSystemContext } from '@/context/system-context'

export default function AccountPage() {
  const { t } = useTranslation()
  const { userProfile } = useAppContext()
  const { isPrivate } = useSystemContext()
  const [form] = Form.useForm()
  // 编辑名称弹窗是否可见
  const [editNameModalVisible, setEditNameModalVisible] = useState(false)
  // 编辑密码弹窗是否可见
  const [editPasswordModalVisible, setEditPasswordModalVisible] = useState(false)

  // 编辑名称
  const handleEditName = () => {
    setEditNameModalVisible(true)
  }

  useEffect(() => {
    form.setFieldsValue({
      ...userProfile,
    })
  }, [form, userProfile])

  return (
    <>
      <Form form={form} layout='vertical' className='cardWrap px-8 py-6'>
        <Form.Item label={t('account.info.avatar')}>
          <Avatar name={userProfile.name} size={64} className='mt-2' />
        </Form.Item>
        <Form.Item label={t('account.info.name')}>
          <div className='group'>
            <Form.Item className='!mb-0' name={'name'}>
              <Input disabled={true} />
            </Form.Item>
            <Button
              className={cn('group-hover:block hidden absolute bottom-1 right-2')}
              variant={'secondary-accent'}
              size='small'
              onClick={handleEditName}
            >
              {t('common.operation.edit')}
            </Button>
          </div>
        </Form.Item>
        {
          userProfile.email && (
            <>
              <Form.Item name={'email'} label={t('account.info.email')}>
                <Input disabled={true} />
              </Form.Item>
              {IS_CE_EDITION && (
                <Form.Item label={t('account.info.passwordTitle')}>
                  <div className='mb-2 text-xs text-gray-500'>{t('account.notify.passwordTip')}</div>
                  <Button onClick={() => setEditPasswordModalVisible(true)} className='!bg-transparent'>{userProfile.is_password_set ? t('account.action.resetPassword') : t('account.action.setPassword')}</Button>
                </Form.Item>
              )}
            </>
          )
        }

      </Form>
      {/* 编辑名称模型 */}
      {editNameModalVisible && <EditNameModal
        name={userProfile.name}
        onClose={() => setEditNameModalVisible(false)}
        visible={editNameModalVisible}
      ></EditNameModal>}
      {/* 重置密码modal */}
      {editPasswordModalVisible && <EditPwdModal
        isReset={true}
        onClose={() => setEditPasswordModalVisible(false)}
        visible={editPasswordModalVisible}
      ></EditPwdModal>}
    </>
  )
}
