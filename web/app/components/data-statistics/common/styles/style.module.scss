.statistics-table-card {
  @apply px-5 py-4 mb-4;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.80);
  backdrop-filter: blur(10px);
    .statistics-table {
        .statistics-table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            min-height: 32px;
            margin-bottom: 12px;
            .statistics-table-titleExtra {
                display: flex;
                align-items: center;
                .statistics-table-extra {
                    margin-left: 8px;
                }
                .statistics-table-title {
                    color: #181818;
                    font-family: "PingFang SC";
                    font-size: 14px;
                    font-weight: 500;
                }
                .ant-btn-link {
                    padding: 0;
                }
            }
        }
    }  
}

