'use client'
import type { FC } from 'react'
import React, { useCallback, useMemo } from 'react'
import { GRAY } from '@/themes/var-define'
import { useTranslation } from 'react-i18next'
import dayjs from 'dayjs'
import quarterOfYear from 'dayjs/plugin/quarterOfYear'
import { ConfigProvider, DatePicker } from 'antd'

import type { QueryParam } from './index'
import cn from '@/utils/classnames'

// 公共组件
import SearchInput from '@/app/components/base/input/search-input'

const { RangePicker } = DatePicker
dayjs.extend(quarterOfYear)

type IFilterProps = {
  queryParams: QueryParam
  setQueryParams: (v: QueryParam) => void
}

const Filter: FC<IFilterProps> = ({ queryParams, setQueryParams }: IFilterProps) => {
  const { t } = useTranslation()

  const onRangeChange = (dates: any, dateStrings: any) => {
    if (dates) {
      setQueryParams({ ...queryParams, start: dayjs(dateStrings[0]).startOf('day').format('YYYY-MM-DD HH:mm'), end: dayjs(dateStrings[1]).endOf('day').format('YYYY-MM-DD HH:mm')})
    } else {
      const { start, end, ...props } = queryParams
      setQueryParams(props)
    }
  }
  const today = new Date()
  return (
    <div className='flex justify-between items-center mt-1 mb-4'>
      <div className='w-full flex justify-end gap-3 items-center text-gray-G1'>
        <ConfigProvider
          theme={{
            components: {
              Input: {
                colorBgContainer: 'transparent',
              },
              DatePicker: {
                colorBgContainer: 'transparent',
              }
            },
          }}
        >
          {/* 日志时间 */}
          <RangePicker 
            onChange={onRangeChange}
            maxDate={dayjs(today)}
          />
          {/* 关键词 */}
          <SearchInput
            className='w-[250px]'
            placeholder={t('common.operation.search')!}
            value={queryParams.keyword}
            onChange={(value: string) => {
              setQueryParams({ ...queryParams, keyword: value })
            }}
          ></SearchInput>
        </ConfigProvider>

      </div>
    </div>
  )
}

export default Filter
