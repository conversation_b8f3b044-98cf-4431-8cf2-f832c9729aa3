import React from 'react'
import { RiArrowDownSLine } from '@remixicon/react'

import cn from '@/utils/classnames'
import PopoverSelect from '@/app/components/base/select/popover-select'

type Props = {
  inCell?: boolean
  value?: any
  list: any
  onSelect: (value: any) => void
  popupClassName?: string
}

const VariableTypeSelector = ({
  inCell = false,
  value,
  list,
  onSelect,
}: Props) => {
  return (
    <PopoverSelect
      options={list.map((item: any) => {
        return {
          label: item,
          key: item,
          title: item,
        }
      })}
      onChange={onSelect}
      defaultValue={value}
      triggerNode={(open) => {
        return (
          <div className={cn(
            'flex items-center w-full px-2 cursor-pointer',
            inCell && 'py-0.5 hover:bg-state-base-hover',
            open && inCell && 'bg-state-base-hover hover:bg-state-base-hover',
          )}>
            <div className={cn(
              'grow p-1 system-xs-regular text-text-secondary truncate',
            )}>{value}</div>
            <RiArrowDownSLine className='ml-0.5 w-4 h-4 text-text-quaternary' />
          </div>
        )
      }}
    ></PopoverSelect>
  )
}

export default VariableTypeSelector
