import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import produce from 'immer'
import { useBoolean } from 'ahooks'
import { useStore } from '../../store'
import { type ToolNodeType, type ToolVarInputs, VarType } from './types'
import { useLanguage } from '@/app/components/account-setting/model-provider-page/hooks'
import useNodeCrud from '@/app/components/workflow/nodes/_base/hooks/use-node-crud'
import { CollectionType } from '@/app/components/tools/types'
import { updateBuiltInToolCredential } from '@/service/tools'
import { addDefaultValue, toolParametersToFormSchemas } from '@/app/components/tools/utils/to-form-schema'
import Toast from '@/app/components/base/toast'
import type { Props as FormProps } from '@/app/components/workflow/nodes/_base/components/before-run-form/form'
import { VarType as VarVarType } from '@/app/components/workflow/types'
import type { InputVar, ValueSelector, Var } from '@/app/components/workflow/types'
import useOneStepRun from '@/app/components/workflow/nodes/_base/hooks/use-one-step-run'
import {
  useFetchToolsData,
  useNodesReadOnly,
} from '@/app/components/workflow/hooks'

export const useMiniConfig = (id: string, payload: ToolNodeType) => {
  const { inputs, setInputs: doSetInputs } = useNodeCrud<ToolNodeType>(id, payload)
  const { provider_id, provider_type, tool_name } = inputs
  const isBuiltIn = provider_type === CollectionType.builtIn
  const buildInTools = useStore(s => s.buildInTools)
  const customTools = useStore(s => s.customTools)
  // const workflowTools = useStore(s => s.workflowTools)
  // 当前工具集列表
  const currentTools = (() => {
    switch (provider_type) {
      case CollectionType.builtIn:
        return buildInTools
      case CollectionType.custom:
        return customTools
      case CollectionType.workflow:
        return customTools
      default:
        return []
    }
  })()
  // 当前工具集
  const currCollection = currentTools.find(item => item.id === provider_id)
  // 当前工具
  const currTool = currCollection?.tools.find(tool => tool.name === tool_name)
  // 当前工具集 是否需要首选
  const needAuth = !!currCollection?.allow_delete
  // 是否已经授权
  const isAuthed = !!currCollection?.is_team_authorization
  // 是否显示授权按钮
  const isShowAuthBtn = isBuiltIn && needAuth && !isAuthed

  const setInputs = useCallback((value: ToolNodeType) => {
    doSetInputs(value)
  }, [doSetInputs])

  // 初始化节点数据
  useEffect(() => {
    const inputsWithDefaultValue = produce(inputs, (draft) => {
      draft.lack = !currTool
    })
    setInputs(inputsWithDefaultValue)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currTool])

  return {
    currTool,
    currCollection,
    isShowAuthBtn,
    isBuiltIn,
  }
}

const useConfig = (id: string, payload: ToolNodeType) => {
  const { nodesReadOnly: readOnly } = useNodesReadOnly()
  const { handleFetchAllTools } = useFetchToolsData()
  const { currCollection, currTool, isShowAuthBtn, isBuiltIn } = useMiniConfig(id, payload)
  const { t } = useTranslation()
  const language = useLanguage()
  const { inputs, setInputs: doSetInputs } = useNodeCrud<ToolNodeType>(id, payload)
  const { provider_type, tool_configurations } = inputs

  // 显示授权弹窗
  const [showSetAuth, {
    setTrue: showSetAuthModal,
    setFalse: hideSetAuthModal,
  }] = useBoolean(false)
  const [notSetDefaultValue, setNotSetDefaultValue] = useState(false)
  const [currVarIndex, setCurrVarIndex] = useState(-1)

  // 当前工具的表单注册像
  const formSchemas = useMemo(() => {
    return currTool ? toolParametersToFormSchemas(currTool.parameters) : []
  }, [currTool])
  const toolInputVarSchema = formSchemas.filter((item: any) => item.form === 'llm')
  // use setting
  const toolSettingSchema = formSchemas.filter((item: any) => item.form !== 'llm')
  const hasShouldTransferTypeSettingInput = toolSettingSchema.some(item => item.type === 'boolean' || item.type === 'number-input')
  const toolSettingValue = (() => {
    if (notSetDefaultValue)
      return tool_configurations

    return addDefaultValue(tool_configurations, toolSettingSchema)
  })()
  const currVarType = toolInputVarSchema[currVarIndex]?._type
  const isLoading = currTool && (isBuiltIn ? !currCollection : false)
  // single run
  const [inputVarValues, doSetInputVarValues] = useState<Record<string, any>>({})

  const setInputs = useCallback((value: ToolNodeType) => {
    if (!hasShouldTransferTypeSettingInput) {
      doSetInputs(value)
      return
    }
    const newInputs = produce(value, (draft) => {
      const newConfig = { ...draft.tool_configurations }
      Object.keys(draft.tool_configurations).forEach((key) => {
        const schema = formSchemas.find(item => item.variable === key)
        const value = newConfig[key]
        if (schema?.type === 'boolean') {
          if (typeof value === 'string')
            newConfig[key] = (value === 'true' || value === '1') ? 1 : 0

          if (typeof value === 'boolean')
            newConfig[key] = value ? 1 : 0
        }

        if (schema?.type === 'number-input') {
          if (typeof value === 'string' && value !== '')
            newConfig[key] = parseFloat(value)
        }
      })
      draft.tool_configurations = newConfig
    })
    doSetInputs(newInputs)
  }, [doSetInputs, formSchemas, hasShouldTransferTypeSettingInput])
  const setInputVarValues = (value: Record<string, any>) => {
    doSetInputVarValues(value)
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    setRunInputData(value)
  }
  // fill single run form variable with constant value first time
  const inputVarValuesWithConstantValue = () => {
    const res = produce(inputVarValues, (draft) => {
      Object.keys(inputs.tool_parameters).forEach((key: string) => {
        const { type, value } = inputs.tool_parameters[key]
        if (type === VarType.constant && (value === undefined || value === null))
          draft.tool_parameters[key].value = value
      })
    })
    return res
  }
  const setToolSettingValue = useCallback((value: Record<string, any>) => {
    setNotSetDefaultValue(true)
    setInputs({
      ...inputs,
      tool_configurations: value,
    })
  }, [inputs, setInputs])
  // setting when call
  const setInputVar = useCallback((value: ToolVarInputs) => {
    setInputs({
      ...inputs,
      tool_parameters: value,
    })
  }, [inputs, setInputs])
  const filterVar = useCallback((varPayload: Var) => {
    if (currVarType)
      return varPayload.type === currVarType

    return varPayload.type !== VarVarType.arrayFile
  }, [currVarType])
  const handleOnVarOpen = useCallback((index: number) => {
    setCurrVarIndex(index)
  }, [])
  // 保存鉴权
  const handleSaveAuth = useCallback(async (value: any) => {
    await updateBuiltInToolCredential(currCollection?.name as string, value)

    Toast.notify({
      type: 'success',
      message: t('common.actionMsg.saveSuccessfully'),
    })
    handleFetchAllTools(provider_type)
    hideSetAuthModal()
  }, [currCollection?.name, hideSetAuthModal, t, handleFetchAllTools, provider_type])
  // 初始化节点数据
  useEffect(() => {
    const inputsWithDefaultValue = produce(inputs, (draft) => {
      if (currTool) {
        if (!draft.tool_configurations || Object.keys(draft.tool_configurations).length === 0)
          draft.tool_configurations = addDefaultValue(tool_configurations, toolSettingSchema)
        if (!draft.tool_parameters)
          draft.tool_parameters = {}
      }
    })
    setInputs(inputsWithDefaultValue)
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currTool])

  const {
    isShowSingleRun,
    hideSingleRun,
    getInputVars,
    runningStatus,
    setRunInputData,
    handleRun: doHandleRun,
    handleStop,
    runResult,
  } = useOneStepRun<ToolNodeType>({
    id,
    data: inputs,
    defaultRunInputData: {},
    moreDataForCheckValid: {
      toolInputsSchema: (() => {
        const formInputs: InputVar[] = []
        toolInputVarSchema.forEach((item: any) => {
          formInputs.push({
            label: item.label[language] || item.label.en_US,
            variable: item.variable,
            type: item.type,
            required: item.required,
          })
        })
        return formInputs
      })(),
      notAuthed: isShowAuthBtn,
      toolSettingSchema,
      language,
    },
  })

  const hadVarParams = Object.keys(inputs.tool_parameters)
    .filter(key => inputs.tool_parameters[key].type !== VarType.constant)
    .map(k => inputs.tool_parameters[k])

  const varInputs = getInputVars(hadVarParams.map((p) => {
    if (p.type === VarType.variable)
      return `{{#${(p.value as ValueSelector).join('.')}#}}`

    return p.value as string
  }))

  const singleRunForms = (() => {
    const forms: FormProps[] = [{
      inputs: varInputs,
      values: inputVarValuesWithConstantValue(),
      onChange: setInputVarValues,
    }]
    return forms
  })()

  const handleRun = (submitData: Record<string, any>) => {
    const varTypeInputKeys = Object.keys(inputs.tool_parameters)
      .filter(key => inputs.tool_parameters[key].type === VarType.variable)
    const shouldAdd = varTypeInputKeys.length > 0
    if (!shouldAdd) {
      doHandleRun(submitData)
      return
    }
    const addMissedVarData = { ...submitData }
    Object.keys(submitData).forEach((key) => {
      const value = submitData[key]
      varTypeInputKeys.forEach((inputKey) => {
        const inputValue = inputs.tool_parameters[inputKey].value as ValueSelector
        if (`#${inputValue.join('.')}#` === key)
          addMissedVarData[inputKey] = value
      })
    })
    doHandleRun(addMissedVarData)
  }

  return {
    readOnly,
    inputs,
    currTool,
    toolSettingSchema,
    toolSettingValue,
    setToolSettingValue,
    toolInputVarSchema,
    setInputVar,
    handleOnVarOpen,
    filterVar,
    currCollection,
    isShowAuthBtn,
    showSetAuth,
    showSetAuthModal,
    hideSetAuthModal,
    handleSaveAuth,
    isLoading,
    isShowSingleRun,
    hideSingleRun,
    inputVarValues,
    varInputs,
    setInputVarValues,
    singleRunForms,
    runningStatus,
    handleRun,
    handleStop,
    runResult,
  }
}

export default useConfig
