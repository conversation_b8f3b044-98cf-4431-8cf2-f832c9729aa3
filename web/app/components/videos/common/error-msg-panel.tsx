import { useTranslation } from 'react-i18next'
import type { ErrorMsg } from '@/types/videos'
import Tooltip from '@/app/components/base/tooltip'

const ErrorMsgPanel = ({ msg }: {
  msg: Array<ErrorMsg>
}) => {
  const { t } = useTranslation()
  return <Tooltip
    popupContent={
      <>
        <div className='text-S1 leading-H1 text-gray-G2'>{t('videos.doc.info.errorInfo')}</div>
        { msg.length > 5 && <div className='text-S1 leading-H1 text-gray-G2'>{ t('videos.doc.info.errorInfoTip') }</div> }
        {
          msg.slice(0, 5).map((item: ErrorMsg, index: number) => {
            return <div className='text-S1 leading-H1 text-gray-G2' key={index}>{item.time}</div>
          })
        }
      </>
    }
    triggerClassName='ml-2 w-4 h-4 shrink-0'
  ></Tooltip>
}

export default ErrorMsgPanel
