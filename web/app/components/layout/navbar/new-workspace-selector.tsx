import React, { useMemo } from 'react'
import type { MenuItemGroupType } from 'antd/es/menu/interface'
import { useContext } from 'use-context-selector'
import { useTranslation } from 'react-i18next'
import { switchWorkspace } from '@/service/common'
import type { IWorkspace } from '@/models/common'
// 公共组件
import { useWorkspacesContext } from '@/context/workspace-context'
import { ToastContext } from '@/app/components/base/toast'
import { Person, Team } from '@/app/components/base/icons/src/public/user'
import { ArrowDown } from '@/app/components/base/icons/src/vender/line/arrows'
import PopoverSelect from '@/app/components/base/select/popover-select'
import cn from '@/utils/classnames'

const NewWorkspaceSelection = () => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  const { workspaces } = useWorkspacesContext()
  const currentWorkspace = workspaces.find(item => item.current)

  // 渲染菜单选项
  const labelRender = (item: IWorkspace, showCurrent = true) => {
    return (
      <div className='flex items-center gap-2 h-full' style={{ width: 'calc(100% - 16px)' }}>
        {
          item?.plan === 'basic'
            ? (
              <Person className='w-6 h-6'></Person>
            )
            : (
              <Team className='w-6 h-6'></Team>
            )
        }
        {/* 工作空间名称 */}
        <div className='w-full text-gray-G1 text-S3 leading-H3 grow truncate'>{ item.name }</div>
      </div>
    )
  }

  // 处理工作空间数据
  const menuItemList = useMemo(() => {
    // 初始化团队数据
    const teamList: { key: string; label: React.JSX.Element; title: string }[] = []
    // 初始化个人空间
    const personalList: { key: string; label: React.JSX.Element; title: string }[] = []
    workspaces.forEach((item) => {
      if (item.plan === 'basic') {
        personalList.push({
          key: item.id,
          label: labelRender(item),
          title: item.name,
        })
      }
      else {
        teamList.push({
          key: item.id,
          label: labelRender(item),
          title: item.name,
        })
      }
    })
    // 生成下拉选项了
    return [
      {
        key: 'person',
        type: 'group',
        label: t('app.action.person'), // '个人',
        children: personalList,
      },
      ...(teamList.length > 0
        ? [
          {
            key: 'team',
            type: 'group',
            label: t('app.action.team'),
            children: teamList,
          },
        ]
        : []),
    ]
  }, [t, workspaces])

  /* 切换工作空间 */
  const onChange = async (value: string) => {
    const tenant_id = value
    if (currentWorkspace?.id === tenant_id)
      return
    await switchWorkspace({ url: '/workspaces/switch', body: { tenant_id } })
    notify({ type: 'success', message: t('common.actionMsg.modifiedSuccessfully') })
    location.assign(`${location.origin}`)
  }

  if (workspaces.length === 1)
    return null

  return (
    <PopoverSelect
      options={menuItemList as Array<MenuItemGroupType>}
      defaultValue={currentWorkspace?.id}
      onChange={onChange}
      search={true}
      showCheck={true}
      triggerNode={(open) => {
        return (
          <div className={cn('!h-10 px-2 flex justify-between items-center hover:bg-gray-G6 rounded cursor-pointer', open && 'bg-gray-G6')}>
            { labelRender(currentWorkspace!, false) }
            {/* 箭头 */}
            <ArrowDown className={cn('w-4 h-4 text-gray-G2', open && 'rotate-180')} />
          </div>
        )
      }}
    >
    </PopoverSelect>
  )
}

export default React.memo(NewWorkspaceSelection)
