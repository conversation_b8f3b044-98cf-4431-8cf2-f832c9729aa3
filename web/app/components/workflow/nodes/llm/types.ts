import type { CommonNodeType, Memory, ModelConfig, PromptItem, ValueSelector, Variable, VisionSetting } from '@/app/components/workflow/types'
import type { SpecialDatasetConfigs } from '@/models/debug'
import type { ToolItem } from '@/types/tools'

export type LLMNodeType = CommonNodeType & {
  model: ModelConfig
  prompt_template: PromptItem[] | PromptItem
  prompt_config?: {
    jinja2_variables?: Variable[]
  }
  memory?: Memory
  context: {
    enabled: boolean
    variable_selector: ValueSelector
  }
  // 视图配置
  vision: {
    enabled: boolean
    configs?: VisionSetting
  }
  // 工具列表
  tools: Array<ToolItem>
  // 工具缺失
  tool_lack?: boolean
  // 知识库配置
  dataset_configs: SpecialDatasetConfigs
}
