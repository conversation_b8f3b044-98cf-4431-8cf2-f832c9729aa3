import {
  useCallback,
  useMemo,
  useState,
} from 'react'
import { useTranslation } from 'react-i18next'
import produce from 'immer'
import { Divider } from 'antd'
import type { VarType as NumberVarType } from '../../../tool/types'
import type {
  Condition,
  HandleAddSubVariableCondition,
  HandleRemoveCondition,
  HandleToggleSubVariableConditionLogicalOperator,
  HandleUpdateCondition,
  HandleUpdateSubVariableCondition,
  handleRemoveSubVariableCondition,
} from '../../types'
import {
  ComparisonOperator,
} from '../../types'
import { comparisonOperatorNotRequireValue, getOperators } from '../../utils'
import { FILE_TYPE_OPTIONS, SUB_VARIABLES, TRANSFER_METHOD } from '../../default'
import ConditionWrap from '../condition-wrap'
import ConditionNumberInput from './condition-number-input'
import ConditionOperator from './condition-operator'
import ConditionInput from './condition-input'
import VariableTag from './variable-tag'
import type {
  Node,
  NodeOutPutVar,
  Var,
} from '@/app/components/workflow/types'
import { VarType } from '@/app/components/workflow/types'
import cn from '@/utils/classnames'
import PopoverSelect from '@/app/components/base/select/popover-select'
import { Variable02 } from '@/app/components/base/icons/src/vender/solid/development'
import Select from '@/app/components/base/select/new-index'
import Tag from '@/app/components/base/tag'
import { Delete } from '@/app/components/base/icons/src/vender/line/general'
const optionNameI18NPrefix = 'workflow.nodes.ifElse.optionName'

type ConditionItemProps = {
  className?: string
  disabled?: boolean
  caseId: string
  conditionId: string // in isSubVariableKey it's the value of the parent condition's id
  condition: Condition // condition may the condition of case or condition of sub variable
  file?: { key: string }
  isSubVariableKey?: boolean
  isValueFieldShort?: boolean
  onRemoveCondition?: HandleRemoveCondition
  onUpdateCondition?: HandleUpdateCondition
  onAddSubVariableCondition?: HandleAddSubVariableCondition
  onRemoveSubVariableCondition?: handleRemoveSubVariableCondition
  onUpdateSubVariableCondition?: HandleUpdateSubVariableCondition
  onToggleSubVariableConditionLogicalOperator?: HandleToggleSubVariableConditionLogicalOperator
  nodeId: string
  nodesOutputVars: NodeOutPutVar[]
  availableNodes: Node[]
  numberVariables: NodeOutPutVar[]
  filterVar: (varPayload: Var) => boolean
}
const ConditionItem = ({
  className,
  disabled,
  caseId,
  conditionId,
  condition,
  file,
  isSubVariableKey,
  isValueFieldShort,
  onRemoveCondition,
  onUpdateCondition,
  onAddSubVariableCondition,
  onRemoveSubVariableCondition,
  onUpdateSubVariableCondition,
  onToggleSubVariableConditionLogicalOperator,
  nodeId,
  nodesOutputVars,
  availableNodes,
  numberVariables,
  filterVar,
}: ConditionItemProps) => {
  const { t } = useTranslation()

  const [isHovered, setIsHovered] = useState(false)

  const doUpdateCondition = useCallback((newCondition: Condition) => {
    if (isSubVariableKey)
      onUpdateSubVariableCondition?.(caseId, conditionId, condition.id, newCondition)
    else
      onUpdateCondition?.(caseId, condition.id, newCondition)
  }, [caseId, condition, conditionId, isSubVariableKey, onUpdateCondition, onUpdateSubVariableCondition])

  const canChooseOperator = useMemo(() => {
    if (disabled)
      return false

    if (isSubVariableKey)
      return !!condition.key

    return true
  }, [condition.key, disabled, isSubVariableKey])
  const handleUpdateConditionOperator = useCallback((value: ComparisonOperator) => {
    const newCondition = {
      ...condition,
      comparison_operator: value,
    }
    doUpdateCondition(newCondition)
  }, [condition, doUpdateCondition])

  const handleUpdateConditionNumberVarType = useCallback((numberVarType: NumberVarType) => {
    const newCondition = {
      ...condition,
      numberVarType,
      value: '',
    }
    doUpdateCondition(newCondition)
  }, [condition, doUpdateCondition])

  const isSubVariable = condition.varType === VarType.arrayFile && [ComparisonOperator.contains, ComparisonOperator.notContains, ComparisonOperator.allOf].includes(condition.comparison_operator!)
  const fileAttr = useMemo(() => {
    if (file)
      return file
    if (isSubVariableKey) {
      return {
        key: condition.key!,
      }
    }
    return undefined
  }, [condition.key, file, isSubVariableKey])

  const isArrayValue = fileAttr?.key === 'transfer_method' || fileAttr?.key === 'type'

  const handleUpdateConditionValue = useCallback((value: string) => {
    if (value === condition.value || (isArrayValue && value === condition.value?.[0]))
      return
    const newCondition = {
      ...condition,
      value: isArrayValue ? [value] : value,
    }
    doUpdateCondition(newCondition)
  }, [condition, doUpdateCondition, fileAttr])

  const isSelect = condition.comparison_operator && [ComparisonOperator.in, ComparisonOperator.notIn].includes(condition.comparison_operator)
  const selectOptions = useMemo(() => {
    if (isSelect) {
      if (fileAttr?.key === 'type' || condition.comparison_operator === ComparisonOperator.allOf) {
        return FILE_TYPE_OPTIONS.map(item => ({
          label: t(`${optionNameI18NPrefix}.${item.i18nKey}`),
          value: item.value,
        }))
      }
      if (fileAttr?.key === 'transfer_method') {
        return TRANSFER_METHOD.map(item => ({
          label: t(`${optionNameI18NPrefix}.${item.i18nKey}`),
          value: item.value,
        }))
      }
      return []
    }
    return []
  }, [condition.comparison_operator, fileAttr?.key, isSelect, t])

  const isNotInput = isSelect || isSubVariable

  const isSubVarSelect = isSubVariableKey
  // 次级变量选择
  const subVarOptions = SUB_VARIABLES.map(item => ({
    label: item,
    key: item,
  }))

  const handleSubVarKeyChange = useCallback((key: string) => {
    const newCondition = produce(condition, (draft) => {
      draft.key = key
      if (key === 'size')
        draft.varType = VarType.number
      else
        draft.varType = VarType.string

      draft.value = ''
      draft.comparison_operator = getOperators(undefined, { key })[0]
    })

    onUpdateSubVariableCondition?.(caseId, conditionId, condition.id, newCondition)
  }, [caseId, condition, conditionId, onUpdateSubVariableCondition])

  const doRemoveCondition = useCallback(() => {
    if (isSubVariableKey)
      onRemoveSubVariableCondition?.(caseId, conditionId, condition.id)
    else
      onRemoveCondition?.(caseId, condition.id)
  }, [caseId, condition, conditionId, isSubVariableKey, onRemoveCondition, onRemoveSubVariableCondition])

  return (
    <div className={cn('p-2 rounded border broder-gray-G6 bg-white', className)}>
      <div className='flex items-center mb-1'>
        <div className='grow w-0'>
          {isSubVarSelect
            ? (
              <PopoverSelect
                defaultValue={condition.key}
                options={subVarOptions}
                onChange={item => handleSubVarKeyChange(item.value as string)}
                triggerNode={(open, value) => (
                  <div className='w-full cursor-pointer'><Tag color='blue' size='small' className='w-full'>
                    <Variable02 className='shrink-0 w-3.5 h-3.5' />
                    <div className='ml-0.5 truncate'>{value}</div>
                  </Tag></div>
                )}
                showCheck={false}
              />
            )
            : (
              <VariableTag
                valueSelector={condition.variable_selector || []}
                varType={condition.varType}
                availableNodes={availableNodes}
                isShort
              />
            )}
        </div>
        <Divider type='vertical' className='!mx-2'></Divider>
        {/* 操作选择 */}
        <ConditionOperator
          className='!px-0 !border-none'
          disabled={!canChooseOperator}
          varType={condition.varType}
          value={condition.comparison_operator}
          onSelect={handleUpdateConditionOperator}
          file={fileAttr}
        />
        <Divider type='vertical' className='!mx-2'></Divider>
        {/* 删除按钮 */}
        <div
          className='shrink-0 flex items-center justify-center w-6 h-6 rounded cursor-pointer hover:bg-state-destructive-hover text-text-tertiary hover:text-text-destructive'
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          onClick={doRemoveCondition}
        >
          <Delete className='w-4 h-4' />
        </div>
      </div>
      {/* 茶凉输入 */}
      {
        !comparisonOperatorNotRequireValue(condition.comparison_operator) && !isNotInput && condition.varType !== VarType.number && (
          <div className='max-h-[100px] text-S1 text-gray-G2 leading-H1 overflow-y-auto'>
            <ConditionInput
              disabled={disabled}
              value={condition.value as string}
              onChange={handleUpdateConditionValue}
              nodesOutputVars={nodesOutputVars}
              availableNodes={availableNodes}
            />
          </div>
        )
      }
      {
        !comparisonOperatorNotRequireValue(condition.comparison_operator) && !isNotInput && condition.varType === VarType.number && (
          <ConditionNumberInput
            numberVarType={condition.numberVarType}
            onNumberVarTypeChange={handleUpdateConditionNumberVarType}
            value={condition.value as string}
            onValueChange={handleUpdateConditionValue}
            variables={numberVariables}
            isShort={isValueFieldShort}
            unit={fileAttr?.key === 'size' ? 'Byte' : undefined}
          />
        )
      }
      {
        !comparisonOperatorNotRequireValue(condition.comparison_operator) && isSelect && (
          <Select
            className='w-full'
            defaultValue={isArrayValue ? (condition.value as string[])?.[0] : (condition.value as string)}
            options={selectOptions}
            onSelect={item => handleUpdateConditionValue(item as string)}
          />
        )
      }
      {
        !comparisonOperatorNotRequireValue(condition.comparison_operator) && isSubVariable && (
          <ConditionWrap
            isSubVariable
            caseId={caseId}
            conditionId={conditionId}
            readOnly={!!disabled}
            cases={condition.sub_variable_condition ? [condition.sub_variable_condition] : []}
            handleAddSubVariableCondition={onAddSubVariableCondition}
            handleRemoveSubVariableCondition={onRemoveSubVariableCondition}
            handleUpdateSubVariableCondition={onUpdateSubVariableCondition}
            handleToggleSubVariableConditionLogicalOperator={onToggleSubVariableConditionLogicalOperator}
            nodeId={nodeId}
            nodesOutputVars={nodesOutputVars}
            availableNodes={availableNodes}
            filterVar={filterVar}
          />
        )
      }
    </div>
  )
}

export default ConditionItem
