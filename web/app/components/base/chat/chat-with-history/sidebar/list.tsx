import type { FC } from 'react'
import Item from './item'
import type { ConversationItem } from '@/types/share'
import Scrollbar from '@/app/components/base/scrollbar'

type ListProps = {
  isPin?: boolean
  title?: string
  list: ConversationItem[]
  onOperate: (type: string, item: ConversationItem) => void
  onChangeConversation: (conversationId: string) => void
  renameItem?: ConversationItem | null
  setNewName: (name: string) => void
  currentConversationId: string
}
const List: FC<ListProps> = ({
  isPin,
  title,
  list,
  onOperate,
  onChangeConversation,
  renameItem,
  setNewName,
  currentConversationId,
}) => {
  return (
    <>
      {
        title && (
          <div className='px-4 py-1 text-gray-G1 text-S1 leading-H1'>
            {title}
          </div>
        )
      }
      <Scrollbar className='h-0 grow px-2'>
        {
          list.map((item, index) => (
            <Item
              key={item.id}
              index={index}
              isPin={isPin}
              item={item}
              onOperate={onOperate}
              onChangeConversation={onChangeConversation}
              renameItem={renameItem}
              setNewName={setNewName}
              currentConversationId={currentConversationId}
            />
          ))
        }
      </Scrollbar>

    </>
  )
}

export default List
