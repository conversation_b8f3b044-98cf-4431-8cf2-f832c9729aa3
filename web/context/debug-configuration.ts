import { createContext, useContext } from 'use-context-selector'
import { PromptMode } from '@/models/debug'
import type {
  BlockStatus,
  ChatPromptConfig,
  CompletionPromptConfig,
  ConversationHistoriesRole,
  DatasetConfigs,
  Inputs,
  ModelConfig,
  PromptConfig,
  PromptItem,
} from '@/models/debug'
import type { ExternalDataTool } from '@/models/common'
import type { DataSet } from '@/models/datasets'
import { RETRIEVE_TYPE } from '@/types/datasets'
import { ModelModeType } from '@/types/model'
import { DEFAULT_AGENT_SETTING, DEFAULT_CHAT_PROMPT_CONFIG, DEFAULT_COMPLETION_PROMPT_CONFIG } from '@/config'
import type { FormValue } from '@/app/components/account-setting/model-provider-page/declarations'
import type { Collection } from '@/app/components/tools/types'
import type { NewVoiceConfigType } from '@/models/app'

type IDebugConfiguration = {
  appId: string
  mode: string
  modelModeType: ModelModeType
  promptMode: PromptMode
  setPromptMode: (promptMode: PromptMode) => void
  isAdvancedMode: boolean
  isAgent: boolean
  isFunctionCall: boolean
  collectionList: Collection[]
  voicesConfigData: NewVoiceConfigType[]
  chatPromptConfig: ChatPromptConfig
  completionPromptConfig: CompletionPromptConfig
  currentAdvancedPrompt: PromptItem | PromptItem[]
  setCurrentAdvancedPrompt: (prompt: PromptItem | PromptItem[], isUserChanged?: boolean) => void
  showHistoryModal: () => void
  conversationHistoriesRole: ConversationHistoriesRole
  setConversationHistoriesRole: (conversationHistoriesRole: ConversationHistoriesRole) => void
  hasSetBlockStatus: BlockStatus
  prevPromptConfig: PromptConfig
  setPrevPromptConfig: (prevPromptConfig: PromptConfig) => void
  externalDataToolsConfig: ExternalDataTool[]
  setExternalDataToolsConfig: (externalDataTools: ExternalDataTool[]) => void
  formattingChanged: boolean
  setFormattingChanged: (formattingChanged: boolean) => void
  inputs: Inputs
  setInputs: (inputs: Inputs) => void
  // Belows are draft infos
  completionParams: FormValue
  setCompletionParams: (completionParams: FormValue) => void
  // model_config
  modelConfig: ModelConfig
  setModelConfig: (modelConfig: ModelConfig) => void
  dataSets: DataSet[]
  setDataSets: (dataSet: DataSet[]) => void
  showSelectDataSet: () => void
  // dataset config
  datasetConfigs: DatasetConfigs
  setDatasetConfigs: (config: DatasetConfigs) => void
  forkDatasets: DataSet[]
  setForkDatasets: (forkDatasets: DataSet[]) => void
  forkTools: any[]
  setForkTools: (forkTools: any[]) => void
  hasSetContextVar: boolean
  isVoiceCalling: boolean
  setIsVoiceCalling: (isVoiceCalling: boolean) => void
}

const DebugConfigurationContext = createContext<IDebugConfiguration>({
  appId: '',
  mode: '',
  modelModeType: ModelModeType.chat,
  voicesConfigData: [],
  promptMode: PromptMode.simple,
  setPromptMode: () => { },
  isAdvancedMode: false,
  isAgent: false,
  isFunctionCall: false,
  collectionList: [],
  chatPromptConfig: DEFAULT_CHAT_PROMPT_CONFIG,
  completionPromptConfig: DEFAULT_COMPLETION_PROMPT_CONFIG,
  currentAdvancedPrompt: [],
  showHistoryModal: () => { },
  conversationHistoriesRole: {
    user_prefix: 'user',
    assistant_prefix: 'assistant',
  },
  setConversationHistoriesRole: () => { },
  setCurrentAdvancedPrompt: () => { },
  hasSetBlockStatus: {
    context: false,
    history: false,
    query: false,
  },
  prevPromptConfig: {
    prompt_template: '',
    prompt_variables: [],
  },
  setPrevPromptConfig: () => { },
  externalDataToolsConfig: [],
  setExternalDataToolsConfig: () => { },
  formattingChanged: false,
  setFormattingChanged: () => { },
  inputs: {},
  setInputs: () => { },
  completionParams: {
    max_tokens: 16,
    temperature: 1, // 0-2
    top_p: 1,
    presence_penalty: 1, // -2-2
    frequency_penalty: 1, // -2-2
  },
  setCompletionParams: () => { },
  modelConfig: {
    provider: 'OPENAI', // 'OPENAI'
    model_id: 'gpt-3.5-turbo', // 'gpt-3.5-turbo'
    mode: ModelModeType.unset,
    configs: {
      prompt_template: '',
      prompt_variables: [],
    },
    more_like_this: null,
    opening_statement: '',
    suggested_questions: [],
    sensitive_word_avoidance: null,
    speech_to_text: null,
    text_to_speech: null,
    file_upload: null,
    suggested_questions_after_answer: null,
    retriever_resource: null,
    annotation_reply: null,
    dataSets: [],
    agentConfig: DEFAULT_AGENT_SETTING,
  },
  setModelConfig: () => { },
  dataSets: [],
  showSelectDataSet: () => { },
  setDataSets: () => { },
  datasetConfigs: {
    retrieval_model: RETRIEVE_TYPE.multiWay,
    reranking_model: {
      reranking_provider_name: '',
      reranking_model_name: '',
    },
    top_k: 2,
    score_threshold_enabled: true,
    score_threshold: 0.7,
    datasets: {
      datasets: [],
    },
  },
  setDatasetConfigs: () => { },
  forkDatasets: [],
  setForkDatasets: () => { },
  forkTools: [],
  setForkTools: () => { },
  hasSetContextVar: false,
  isVoiceCalling: false,
  setIsVoiceCalling: () => { },
})

export const useDebugConfigurationContext = () => useContext(DebugConfigurationContext)

export default DebugConfigurationContext
