'use client'
import type { FC } from 'react'
import React, { useCallback } from 'react'
import type { Video } from '@/types/videos'
// 公共组件
import { Delete } from '@/app/components/base/icons/src/vender/line/general'
import TextButton from '@/app/components/base/button/text-button'
import Avatar from '@/app/components/base/app-info/avatar'

type Props = {
  payload: Video
  onRemove: () => void
  onChange: (dataSet: Video) => void
  readonly?: boolean
}

const DatasetItem: FC<Props> = ({
  payload,
  onRemove,
  readonly,
}) => {
  const handleRemove = useCallback((e: React.MouseEvent) => {
    e.stopPropagation()
    onRemove()
  }, [onRemove])

  return (
    <div className={'flex items-center h-10 justify-between rounded px-3 border border-gray-G5 cursor-pointer group/dataset-item'}>
      <div className='w-0 grow flex items-center space-x-1.5'>
        <Avatar size={24} appMode={payload.type}></Avatar>
        <div className='w-0 grow text-text-secondary system-sm-medium truncate'>{payload.name}</div>
      </div>
      {!readonly && (
        <div className='hidden group-hover/dataset-item:flex shrink-0 ml-2 items-center space-x-2'>
          <TextButton variant='text' onClick={handleRemove}>
            <Delete className='w-4 h-4'></Delete>
          </TextButton>
        </div>
      )}

    </div>
  )
}
export default React.memo(DatasetItem)
