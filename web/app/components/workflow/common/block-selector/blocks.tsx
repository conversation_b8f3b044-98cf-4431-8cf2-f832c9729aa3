import {
  memo,
  useCallback,
  useMemo,
} from 'react'
import { useTranslation } from 'react-i18next'
import { groupBy } from 'lodash-es'
import { BlockEnum } from '../../types'
import {
  useIsChatMode,
  useNodesExtraData,
} from '../../hooks'
import type { ToolDefaultValue } from '../../nodes/tool/types'
import style from './styles/style.module.css'
import { BLOCKS, BLOCK_CLASSIFICATIONS } from '@/app/components/workflow/constants/block-data'
import BlockIcon from '@/app/components/workflow/common/block-icon'
// 公共组件
import Tooltip from '@/app/components/base/tooltip'
import Scrollbar from '@/app/components/base/scrollbar'

type BlocksProps = {
  searchText: string
  onSelect: (type: BlockEnum, tool?: ToolDefaultValue) => void
  availableBlocksTypes?: BlockEnum[]
}
const Blocks = ({
  searchText,
  onSelect,
  availableBlocksTypes = [],
}: BlocksProps) => {
  const { t } = useTranslation()
  const isChatMode = useIsChatMode()
  const nodesExtraData = useNodesExtraData()

  // 节点分组
  const groups = useMemo(() => {
    return BLOCK_CLASSIFICATIONS.reduce((acc, classification) => {
      const list = groupBy(BLOCKS, 'classification')[classification].filter((block) => {
        if (block.type === BlockEnum.Answer && !isChatMode)
          return false

        return (block.title.toLowerCase().includes(searchText.toLowerCase()) || nodesExtraData[block.type].title?.includes(searchText.toLowerCase())) && availableBlocksTypes.includes(block.type)
      })

      return {
        ...acc,
        [classification]: list,
      }
    }, {} as Record<string, typeof BLOCKS>)
  }, [isChatMode, searchText, nodesExtraData, availableBlocksTypes])
  // 渲染分组
  const renderGroup = useCallback((classification: string) => {
    const list = groups[classification]
    return (
      <div key={classification}>
        {
          !!list.length && (
            <div className={style.blockGroupTitle}>
              {t(`workflow.tabs.${classification}`)}
            </div>
          )
        }
        {
          list.map(block => (
            <Tooltip
              key={block.type}
              position='right'
              popupContent={(
                <div className={style.tipText}>{nodesExtraData[block.type].about}</div>
              )}
            >
              <div
                key={block.type}
                className={style.blockItem}
                onClick={() => onSelect(block.type)}
              >
                <BlockIcon
                  title={nodesExtraData[block.type].title}
                  type={block.type}
                />
              </div>
            </Tooltip>
          ))
        }
      </div>
    )
  }, [groups, nodesExtraData, onSelect, t])

  return (
    <Scrollbar className='max-w-[320px] max-h-[33vh] min-h-[430px]' emptyText={t('workflow.tabs.noResult')}>
      {BLOCK_CLASSIFICATIONS.map(renderGroup)}
    </Scrollbar>
  )
}

export default memo(Blocks)
