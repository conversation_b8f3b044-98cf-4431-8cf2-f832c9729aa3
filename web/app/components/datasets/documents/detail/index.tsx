'use client'
import type { FC } from 'react'
import React, { useEffect, useRef, useState } from 'react'
import useS<PERSON> from 'swr'
import { useAsyncEffect } from 'ahooks'
import { createContext, useContext } from 'use-context-selector'
import { useTranslation } from 'react-i18next'
import { useRouter } from 'next/navigation'
import { omit } from 'lodash-es'
import { Di<PERSON>r, Drawer, Popover } from 'antd'
import OperationAction from '../operation'
import Completed from './completed'
import Embedding from './embedding'
import Metadata from './metadata'
import BatchModal, { ProcessStatus } from './batch-modal'
import type { MetadataType } from '@/service/datasets'
import {
  addSegment,
  checkSegmentBatchImportProgress,
  fetchDocumentDetail,
  fetchDocumentsIndexingStatus,
  segmentBatchImport,
} from '@/service/datasets'
import { DatesetFileTypeCode } from '@/models/datasets'
import type {
  DocForm,
  SegmentDetailModel, SegmentUpdater,
} from '@/models/datasets'
import { useDatasetDetailContext } from '@/context/dataset-detail'
import DropdownItem from '@/app/components/base/dropdown/dowdown-item'

import datsetStyle from '@/app/components/datasets/styles/style.module.css'
import {
  judgeIsEmbedding,
  judgeIsIndexing,
} from '@/app/components/datasets/utils'
import DocumentTitle from '@/app/components/datasets/common/document-title'
import NewSegmentModal from '@/app/components/datasets/common/segment-detail/new-segment-modal'
import { ArrowLeftCircle } from '@/app/components/base/icons/src/vender/line/arrows'
import Loading from '@/app/components/base/loading'
import { ToastContext } from '@/app/components/base/toast'
import TextButton from '@/app/components/base/button/text-button'
import { Settings01 } from '@/app/components/base/icons/src/vender/line/general'
import { FilePlus02 } from '@/app/components/base/icons/src/vender/line/files'
import { useProviderContext } from '@/context/provider-context'
import { fetchFileInfo } from '@/service/common'
import SegmentDetailStructured from '@/app/components/datasets/common/segment-detail/segment-detail-structured'

export const DocumentContext = createContext<{
  datasetId?: string
  documentId?: string
  docForm: string
  category?: DatesetFileTypeCode
  metadatas?: any[]
}>({ docForm: '', metadatas: [] })

type Props = {
  datasetId: string
  documentId: string
}

const DocumentDetail: FC<Props> = ({ datasetId, documentId }) => {
  const router = useRouter()
  const { t } = useTranslation()
  const { useXIYANRag } = useProviderContext()
  const { notify } = useContext(ToastContext)
  const { dataset } = useDatasetDetailContext()
  const embeddingAvailable = !!dataset?.embedding_available

  // 是否显示主数据
  // const [showMetadata, setShowMetadata] = useState(true)
  // 是否显示新增分段弹窗
  const [newSegmentModalVisible, setNewSegmentModalVisible] = useState(false)
  // 批量添加弹窗是否可见
  const [batchModalVisible, setBatchModalVisible] = useState(false)
  // 导入状态
  const [importStatus, setImportStatus] = useState<ProcessStatus | string>()
  // 是否为正在索引中的文档
  const [isIndexing, setIsIndexing] = useState(false)
  // 是否展示文档信息
  const [showInfoDrawer, setShowInfoDrawer] = useState(false)
  const controlsTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const completedRef = useRef<{
    reset: () => void
  }>()
  const [metadatas, setMetadatas] = useState<any[]>([])
  // 当前分段信息
  const [currSegment, setCurrSegment] = useState<{
    segInfo?: SegmentDetailModel
    showModal: boolean
    showModalStructured: boolean
  }>({ showModal: false, showModalStructured: false })

  // 获取文档详情
  const {
    data: documentDetail,
    error,
    mutate: detailMutate,
  } = useSWR(
    {
      action: 'fetchDocumentDetail',
      datasetId,
      documentId,
      params: { metadata: 'without' as MetadataType },
    },
    apiParams => fetchDocumentDetail(omit(apiParams, 'action')),
  )
  // 获取文档主数据
  const {
    data: documentMetadata,
    error: metadataErr,
    mutate: metadataMutate,
  } = useSWR(
    {
      action: 'fetchDocumentDetail',
      datasetId,
      documentId,
      params: { metadata: 'only' as MetadataType },
    },
    apiParams => fetchDocumentDetail(omit(apiParams, 'action')),
  )

  // 详情是否正在加载
  const isDetailLoading = !documentDetail && !error
  // 主数据是否正在加载
  const isMetadataLoading = !documentMetadata && !metadataErr

  const showNewSegmentModal = () => setNewSegmentModalVisible(true)
  const showBatchModal = () => setBatchModalVisible(true)
  const hideBatchModal = () => setBatchModalVisible(false)
  const resetProcessStatus = () => setImportStatus('')
  const backToPrev = () => {
    router.push(`/datasets/${datasetId}/documents`)
  }
  useEffect(() => {
    return () => {
      if (controlsTimeoutRef.current)
        clearTimeout(controlsTimeoutRef.current)
    }
  }, [])
  // 确认批量导入进度
  const checkProcess = async (jobID: string) => {
    try {
      const res = await checkSegmentBatchImportProgress({ jobID })
      setImportStatus(res.job_status)
      if (
        res.job_status === ProcessStatus.WAITING
        || res.job_status === ProcessStatus.PROCESSING
      ) {
        if (controlsTimeoutRef.current)
          clearTimeout(controlsTimeoutRef.current)
        controlsTimeoutRef.current = setTimeout(
          () => checkProcess(res.job_id),
          2500,
        )
      }
      if (res.job_status === ProcessStatus.ERROR) {
        notify({
          type: 'error',
          message: `${t('datasetDocuments.list.batchModal.runError')}`,
        })
      }
    }
    catch (e: any) {
      notify({
        type: 'error',
        message: `${t('datasetDocuments.list.batchModal.runError')}${
          'message' in e ? `: ${e.message}` : ''
        }`,
      })
    }
  }
  // 执行批量导入
  const runBatch = async (csv: File, type?: string) => {
    const formData = new FormData()
    formData.append('file', csv)
    if (type)
      formData.append('type', type)
    try {
      // 在执行新的批量导入前重置状态
      setImportStatus(undefined)

      const res = await segmentBatchImport({
        url: `/datasets/${datasetId}/documents/${documentId}/segments/batch_import`,
        body: formData,
      })
      setImportStatus(res.job_status)
      // 如果导出失败
      if (res.job_status === ProcessStatus.FAILED) {
        notify({
          type: 'error',
          message: `${t('datasetDocuments.list.batchModal.runError')}`,
        })
      }
      checkProcess(res.job_id)
    }
    catch (e: any) {
      notify({
        type: 'error',
        message: `${t('datasetDocuments.list.batchModal.runError')}${
          'message' in e ? `: ${e.message}` : ''
        }`,
      })
    }
  }
  // 操作函数
  const handleOperate = (operateName?: string) => {
    if (operateName === 'delete')
      backToPrev()
    else detailMutate()
  }

  useEffect(() => {
    console.log(documentDetail, 'documentDetail')
    documentDetail && setIsIndexing(judgeIsIndexing(documentDetail))
  }, [documentDetail])
  useEffect(() => {
    if (isIndexing) {
      const intervalController = setInterval(async () => {
        const res = await fetchDocumentsIndexingStatus({
          datasetId,
          document_id_list: [documentDetail!.id],
        })
        console.log('Document indexing status:', res)
        const newStatus = judgeIsIndexing(res.document_status[documentDetail!.id])
        console.log('New indexing status:', newStatus)
        setIsIndexing(newStatus)
        if (newStatus !== isIndexing)
          detailMutate()
      }, 1000)
      return () => clearInterval(intervalController)
    }
  }, [datasetId, documentDetail, isIndexing])

  // 根据文件id初始化文件配置
  useAsyncEffect(async () => {
    if (documentDetail?.file_id) {
      try {
        const { metadatas } = await fetchFileInfo(
          [documentDetail.file_id],
        )
        setMetadatas(metadatas[0]?.metadata)
      }
      catch (err) {
        // setFileDataConfigList([])
      }
    }
    else {
      // setFileDataConfigList([])
    }
  }, [documentDetail?.file_id])

  // 关闭调整分段卡片弹窗
  const onCloseModal = () => {
    setCurrSegment({ ...currSegment, showModal: false, showModalStructured: false })
  }

  // 更新分段卡片
  const handleUpdateSegment = async (
    segmentId: string,
    question: string,
    answer: string,
    keywords: string[],
    headers?: string[],
    values?: string[],
  ) => {
    const params: SegmentUpdater = { content: '' }
    console.log(documentDetail, 'documentDetail')

    if (documentDetail?.doc_form === 'qa_model' && documentDetail?.category === DatesetFileTypeCode.Unstructured) {
      if (!question.trim()) {
        return notify({
          type: 'error',
          message: t('datasetDocuments.segment.questionEmpty'),
        })
      }
      if (!answer.trim()) {
        return notify({
          type: 'error',
          message: t('datasetDocuments.segment.answerEmpty'),
        })
      }

      params.content = question
      params.answer = answer
    }
    else {
      if (documentDetail?.category === DatesetFileTypeCode.Unstructured && !question.trim()) {
        return notify({
          type: 'error',
          message: t('datasetDocuments.segment.contentEmpty'),
        })
      }

      params.content = question
    }
    if (keywords.length)
      params.keywords = keywords
    if (documentDetail?.category !== DatesetFileTypeCode.Unstructured) {
      delete params.content
      params.headers = headers
      params.values = values
    }
    try {
      await addSegment({ datasetId: datasetId as string, documentId: documentId as string, body: params })
      notify({
        type: 'success',
        message: t('common.actionMsg.modifiedSuccessfully'),
      })
      completedRef.current?.reset()
      onCloseModal()
    }
    catch {}
  }

  return (
    <DocumentContext.Provider
      value={{
        datasetId,
        documentId,
        docForm: documentDetail?.doc_form || '',
        category: documentDetail?.category,
        metadatas,
      }}
    >
      {/* 头部——文档信息及操作部分 */}
      <div className={datsetStyle.header}>
        {/* 返回按钮 */}
        <TextButton variant="text" onClick={backToPrev}>
          <ArrowLeftCircle className="h-8 w-8" />
        </TextButton>
        {/* 文档标题 */}
        <DocumentTitle
          wrapperCls="mx-3"
          extension={documentDetail?.data_source_info?.upload_file?.extension}
          name={documentDetail?.name}
        />
        {/* 操作部分 */}
        <div className="flex items-center">
          {/* 添加分段 */}
          {embeddingAvailable
            && documentDetail
            && !documentDetail.archived
            && !judgeIsEmbedding(documentDetail)
            && (
              <>
                <Popover
                  arrow={false}
                  trigger="click"
                  placement="bottom"
                  content={
                    <div className="w-full py-1">
                      <DropdownItem
                        onClick={() => {
                          if (documentDetail?.category === DatesetFileTypeCode.Unstructured)
                            showNewSegmentModal()
                          else
                          // 非结构化分段
                            setCurrSegment({ showModal: false, showModalStructured: true })
                        }}
                        title={t('datasetDocuments.list.action.newAdd')}
                      />
                      <DropdownItem
                        onClick={showBatchModal}
                        title={t('datasetDocuments.list.action.batchAdd')}
                      />
                    </div>
                  }
                >
                  <TextButton variant={'text'}>
                    <FilePlus02 className="w-4 h-4" />
                    {t('datasetDocuments.list.action.add')}
                  </TextButton>
                </Popover>
                <Divider className="mx-3" type="vertical"></Divider>
              </>
            )}
          {/* 分段设置 */}
          {embeddingAvailable && !documentDetail?.archived && !useXIYANRag && (
            <>
              <TextButton
                variant="text"
                onClick={() =>
                  router.push(
                    `/datasets/${datasetId}/doc-config/${documentId}/settings`,
                  )
                }
              >
                <Settings01 className="w-4 h-4" />
                {t('datasetDocuments.list.action.settings')}
              </TextButton>
              <Divider className="mx-3" type="vertical"></Divider>
            </>
          )}
          {/* 状态+启用按钮 */}
          {!useXIYANRag && <OperationAction
            embeddingAvailable={embeddingAvailable}
            scene="detail"
            detail={documentDetail}
            datasetId={datasetId}
            onUpdate={handleOperate}
          />}
          {/* <button
                className={cn(style.layoutRightIcon, showMetadata ? style.iconShow : style.iconClose)}
                onClick={() => setShowMetadata(!showMetadata)}
              /> */}
        </div>
      </div>
      {/* 内容部分 */}
      <div className={datsetStyle.wrap} style={{ height: 'calc(100% - 68px)' }}>
        <div className={datsetStyle['left-part']}>
          {isDetailLoading
            ? (
              <Loading type="app" />
            )
            : judgeIsEmbedding(documentDetail)
              ? (
                <Embedding
                  detail={documentDetail}
                  onSeeDocumentInfo={() => setShowInfoDrawer(true)}
                  detailUpdate={detailMutate}
                />
              )
              : (
                <Completed
                  ref={completedRef}
                  embeddingAvailable={embeddingAvailable}
                  importStatus={importStatus}
                  archived={documentDetail?.archived}
                  onClearImportStatus={resetProcessStatus}
                  onSeeDocumentInfo={() => setShowInfoDrawer(true)}
                  category={documentDetail?.category}
                />
              )}
        </div>
        {/* 主数据部分 */}
        {/* <div className={cn(datsetStyle['right-part'], showMetadata ? '!flex' : '!hidden', '!min-w-[400px]')}>
            <Metadata
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-expect-error
              docDetail={{ ...documentDetail, ...documentMetadata, doc_type: documentMetadata?.doc_type === 'others' ? '' : documentMetadata?.doc_type } as any}
              loading={isMetadataLoading}
              onUpdate={metadataMutate}
            />
          </div> */}
      </div>
      {/* 批量导入分段弹窗 */}
      {batchModalVisible && (
        <BatchModal
          metadatas={metadatas}
          isShow={batchModalVisible}
          onCancel={hideBatchModal}
          onConfirm={file => runBatch(file, documentDetail?.category === DatesetFileTypeCode.Unstructured ? 'unstructured' : 'structured')}
          docForm={documentDetail?.doc_form as DocForm}
          category={documentDetail?.category}
        />
      )}
      {/* 新增分段弹窗 */}
      <NewSegmentModal
        isShow={newSegmentModalVisible}
        docForm={documentDetail?.doc_form || ''}
        onCancel={() => setNewSegmentModalVisible(false)}
        onSave={() => completedRef.current?.reset()}
        category={documentDetail?.category}
      />
      <SegmentDetailStructured
        canEdit={true}
        isAdd={true}
        isShow={currSegment.showModalStructured}
        segInfo={currSegment.showModalStructured
          ? {
            id: '',
            headers: metadatas[0]?.columns || [],
          }
          : { id: '' }}
        onUpdate={handleUpdateSegment}
        onCancel={() => setCurrSegment({ ...currSegment, showModalStructured: false })}
      />
      {/* 文档信息抽屉 */}
      <Drawer
        title={t('datasetDocuments.list.dialog.data')}
        placement={'bottom'}
        height={194}
        closable={true}
        onClose={() => setShowInfoDrawer(false)}
        open={showInfoDrawer}
      >
        <Metadata
          docDetail={
            {
              ...documentDetail,
              ...documentMetadata,
              doc_type:
                // @ts-expect-error 期望的类型检擦
                documentMetadata?.doc_type === 'others'
                  ? ''
                  : documentMetadata?.doc_type,
            } as any
          }
          loading={isMetadataLoading}
          onUpdate={metadataMutate}
          onlyInfo={true}
        />
      </Drawer>
    </DocumentContext.Provider>
  )
}

export default DocumentDetail
