import type { FC } from 'react'
import { memo, useEffect, useMemo, useState } from 'react'
import dynamic from 'next/dynamic'
import { useTranslation } from 'react-i18next'
import type { ChatConfig, ChatItem } from '../../types'
import LoadingAnim from '../loading-anim'
import Citation from '../citation'
import styles from '../styles/style.module.css'
import Operation from './operation'
import AgentContent from './agent-content'
import AnswerIcon from './answer-icon'
import More from './more'
import WorkflowProcess from './workflow-process'
import style from './styles/index.module.css'
import { EditTitle } from '@/app/components/app/annotation/edit-annotation-modal/edit-item'
import type { AppData } from '@/types/share'
// 公共组件
import { FileList } from '@/app/components/base/file-uploader'
import Avatar from '@/app/components/base/avatar'
import cn from '@/utils/classnames'


const BasicContent = dynamic(() => import('./basic-content'), { ssr: false })
const BasicContentflow = dynamic(() => import('./basic-contentflow'), { ssr: false })

type AnswerProps = {
  item: ChatItem
  question: string
  index: number
  onSend: (message: any) => void
  config?: ChatConfig
  answerIcon?: string
  responding?: boolean
  chatAnswerContainerInner?: string
  hideProcessDetail?: boolean
  appData?: AppData
  noChatInput?: boolean
  islongModel?: boolean
  onShowModel: (item: any) => void
  isBook?: boolean
  inputsForm?: any[]
  onAc: (item: any) => void
  parentItem?: any
  onPlayVoice?: (content: string, id?: string) => void
  onCloseVoice?: () => void
}
const Answer: FC<AnswerProps> = ({
  item,
  question,
  index,
  onSend,
  config,
  answerIcon,
  responding,
  chatAnswerContainerInner,
  hideProcessDetail,
  appData,
  noChatInput,
  islongModel = false,
  onShowModel,
  isBook = false,
  inputsForm = [],
  onAc = () => {},
  parentItem = '',
  onPlayVoice,
  onCloseVoice,
}) => {
  const { t } = useTranslation()
  const {
    content,
    citation,
    agent_thoughts,
    more,
    annotation,
    workflowProcess,
    allFiles,
    message_files,
  } = item

  // 应用头像
  const AppAvatarIcon = useMemo(() => {
    if (!answerIcon)
      return null
    return <Avatar avatar={answerIcon} size={32} className="!rounded-full"></Avatar>
  }, [answerIcon])
  // 对话中止后，agent_thoughts内的回复可能是空的，这时候日志还是显示外层的content回复内容
  const hasAgentThoughts = () => {
    if (!agent_thoughts || agent_thoughts.length === 0)
      return false
    return (
      agent_thoughts.filter((thought) => {
        return thought.thought
      }).length > 0
    )
  }
  const [workarr, setworkarr] = useState<any[]>([])
  const [isC, setIsc] = useState(false)// 是否显示文章流程
  const [workstatus, setWorkstatus] = useState<any[]>([0, 0, 0, 0, 0])// 工作流的状态
  const [wflow, setwflow] = useState<any>('')
  const [isDg, setisDg] = useState<any>('0')// 是否为大纲
  useEffect(() => {
    if (workflowProcess && workflowProcess.tracing)
      setpress(workflowProcess.tracing)
  }, [workflowProcess])
  useEffect(() => {
    if (item && item.content) {
      if (item.content.includes(t('sampleTemplate.BasicModal.generatingLongtxt')) && item.workflowProcess && item.workflowProcess.tracing)
        setpress(item.workflowProcess.tracing)

      if (item.content.includes(t('sampleTemplate.BasicModal.generatingOutline')))
        setisDg(1)
    }
  }, [item])

  const setpress = (proess: any[]) => {
    const ar: any[] = []
    const arstatus = []
    proess.forEach((item: any) => {
      ar.push(item.title)
      arstatus.push(item.status)
    })
    if (ar.length > 0) {
      setworkarr(ar)
      const title1 = t('sampleTemplate.longText.flow1')
      const title2 = t('sampleTemplate.longText.flow2')
      const title3 = t('sampleTemplate.longText.flow3')
      const title4 = t('sampleTemplate.longText.flow4')
      const title5 = t('sampleTemplate.longText.flow5')
      if (ar.includes(title1) && ar.includes(title2)) {
        setIsc(true)
        let status1 = 0; let status2 = 0; let status3 = 0; let status4 = 0; let status5 = 0
        proess.forEach((item: any, index: any) => {
          if (item.title == title1 && item.status == 'succeeded')
            status1 = 1

          else if (item.title == title2 && item.status == 'succeeded')
            status2 = 1
          else if (item.title == title3 && item.status == 'succeeded')
            status3 = 1
          else if (item.title == title4 && item.status == 'succeeded')
            status4 = 1
          else if (item.title == title5 && item.status == 'succeeded')
            status5 = 1
        })
        const status: any[] = [status1, status2, status3, status4, status5]
        setWorkstatus(status)
      }
    }
  }

  const sendmsg = (item: any) => {
    onAc({ type: 1, data: item })
  }
  // 切换页面关闭语音播放
  useEffect(() => {
    return () => {
      onCloseVoice?.()
    }
  }, [])

  return (
    <div className="p-2-24 flex gap-x mt25">
      <div className={styles.chatIcon}>
        {/* 机器人回复头像 */}
        {AppAvatarIcon || <AnswerIcon />}
        {/* 加载动画 */}
        {/* {responding && (
          <div className="absolute -top-[3px] -left-[3px] pl-[6px] flex items-center w-4 h-4 bg-white rounded-full shadow-xs border-[0.5px] border-gray-50">
            <LoadingAnim type="avatar" />
          </div>
        )} */}
      </div>
      <div className="w-0 chat-answer-container group grow">
        <div className={cn('group relative', chatAnswerContainerInner)}>
          <div className={cn(style.answerPanel, `${workflowProcess ? 'w-full' : 'w-full'}`)}>
            {isC && !isBook
            && <div className={`${style.lcDIV} xq-border-box`}>
              <div className={`${style.lctitle}`}>
                <img src='/assets/mask.svg' className={`${style.lcpic}`} />
                <div className={`${style.titleword}`}>{t('sampleTemplate.longText.flow52')}</div>
              </div>
              <div className={`${style.prolist} xq-border-box`}>
                {workstatus[0] == 0 ? <div className={`${style.proch1} xq-border-box`}>{t('sampleTemplate.longText.flow1')}</div> : <div className={`${style.proch1} ${style.proch2} xq-border-box`}>{t('sampleTemplate.longText.flow1')}</div>}
                {workstatus[1] == 0 ? <div className={`${style.proch1} xq-border-box`}>{t('sampleTemplate.longText.flow2')}</div> : <div className={`${style.proch1} ${style.proch2} xq-border-box`}>{t('sampleTemplate.longText.flow2')}</div>}
                {workstatus[2] == 0 ? <div className={`${style.proch1} xq-border-box`}>{t('sampleTemplate.longText.flow32')}</div> : <div className={`${style.proch1} ${style.proch2} xq-border-box`}>{t('sampleTemplate.longText.flow32')}</div>}
                {workstatus[3] == 0 ? <div className={`${style.proch1} xq-border-box`}>{t('sampleTemplate.longText.flow42')}</div> : <div className={`${style.proch1} ${style.proch2} xq-border-box`}>{t('sampleTemplate.longText.flow42')}</div>}
                {workstatus[4] == 0 ? <div className={`${style.proch1} xq-border-box`}>{t('sampleTemplate.longText.flow52')}...</div> : <div className={`${style.proch1} ${style.proch2} xq-border-box`}>{t('sampleTemplate.longText.flow52')}</div>}
              </div>
            </div>
            }
            <div className={cn(style.answerPanel, `${workflowProcess && 'w-full'}`)}>
              {/** Render the normal steps */}
              {workflowProcess && !hideProcessDetail && (
                <WorkflowProcess
                  data={workflowProcess}
                  item={item}
                  hideProcessDetail={hideProcessDetail}
                />
              )}
              {/** Hide workflow steps by it's settings in siteInfo */}
              {workflowProcess
              && hideProcessDetail
              && appData
              && appData.site.show_workflow_steps && (
                <WorkflowProcess
                  data={workflowProcess}
                  item={item}
                  hideProcessDetail={hideProcessDetail}
                />
              )}
              {/* 加载动画 */}
              {responding && !content && !hasAgentThoughts() && (
                <div className="flex items-center justify-center w-6 h-5">
                  <LoadingAnim type="text" />
                </div>
              )}
              {/* 返回信息流不带有agentThoughts数据的话 */}
              {content && !isBook && !hasAgentThoughts() && <BasicContent item={item} onSend={onSend} islongModel={islongModel} onShowModel={onShowModel} onAc={onAc} parentItem={parentItem} />}
              {content && isBook && !hasAgentThoughts() && <BasicContentflow item={item} onSend={onSend} islongModel={islongModel} onShowModel={onShowModel} inputForm={inputsForm}  />}
              {/* 返回信息流带有agentThoughts数据的话 */}
              {hasAgentThoughts() && <AgentContent item={item} responding={responding} />}
              {!!allFiles?.length && (
                <FileList
                  className="my-1"
                  files={allFiles}
                  showDeleteAction={false}
                  showDownloadAction
                  canPreview
                />
              )}
              {!!message_files?.length && (
                <FileList
                  className="my-1"
                  files={message_files}
                  showDeleteAction={false}
                  showDownloadAction
                  canPreview
                />
              )}
              {annotation?.id && annotation.authorName && (
                <EditTitle
                  className="mt-1"
                  title={t('appAnnotation.editBy', { author: annotation.authorName })}
                />
              )}
              {!!citation?.length && !responding && !isLongWeb && (
                <Citation data={citation} showHitInfo={config?.supportCitationHitInfo} />
              )}
              {/* 操作按钮 */}
              {!responding && (
                <Operation
                  item={item}
                  question={question}
                  index={index}
                />
              )}
            </div>
            {
              isDg == '1' && islongModel
            && <div className='resultDivGray xq-nowrap xq-flex-vcenter' onClick={() => sendmsg(t('sampleTemplate.BasicModal.regenerateoutline'))}>
              <img src='/assets/undo.png' className="undo" />
              <span>{t('sampleTemplate.BasicModal.regenerate')}</span>
            </div>
            }
          </div>
          {more && <More more={more} />}
        </div>
      </div>
    </div>
  )
}

export default memo(Answer)
