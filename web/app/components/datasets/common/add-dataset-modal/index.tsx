'use client'
import type { FC } from 'react'
import React, { useEffect, useState } from 'react'
import Link from 'next/link'
import { useGetState } from 'ahooks'
import { useTranslation } from 'react-i18next'
import style from './styles/index.module.css'
import cn from '@/utils/classnames'
import type { DataSet } from '@/models/datasets'
import { fetchDatasets } from '@/service/datasets'
import { useKnowledge } from '@/hooks/use-knowledge'
import useTimestamp from '@/hooks/use-timestamp'
import AppAvatar from '@/app/components/app/avatar'

// 公共组件
import Badge from '@/app/components/base/badge'
import Button from '@/app/components/base/button'
import TextButton from '@/app/components/base/button/text-button'
import CardList from '@/app/components/base/card-list'
import Card from '@/app/components/base/card'
import Modal from '@/app/components/base/modal'
import { AddCircle } from '@/app/components/base/icons/src/vender/line/general'

export type ISelectDataSetProps = {
  isShow: boolean
  onClose: () => void
  value: DataSet[]
  onSelect: (dataSet: DataSet[]) => void
}

const SelectDataSet: FC<ISelectDataSetProps> = ({
  isShow,
  onClose,
  value,
  onSelect,
}) => {
  const { t } = useTranslation()
  const { formatTime } = useTimestamp()
  const { formatIndexingTechniqueAndMethod } = useKnowledge()

  const canSelectMulti = true

  // 选中的知识库信息
  const [selected, setSelected] = React.useState<DataSet[]>(value)
  // 页码
  const [page, setPage, getPage] = useGetState(1)
  // 是否还有未加载的
  const [hasMore, setHasMore] = useState(true)
  // 知识库是否正在加载
  const [loaded, setLoaded] = React.useState(false)
  // 知识库列表
  const [datasets, setDataSets] = React.useState<DataSet[]>([])

  // 获取知识库列表
  const getDatasetList = async () => {
    if (hasMore) {
      setLoaded(true)
      const { data, has_more } = await fetchDatasets({ url: '/datasets', params: { page } })
      setHasMore(has_more)
      const newList = [...(datasets || []), ...data.filter(item => (item.indexing_technique || item.provider === 'external') && item.embedding_available)]
      setDataSets(newList)
      setPage(getPage() + 1)
      setLoaded(false)
    }
  }
  // 是否选中
  const isSelectd = (dataSet: DataSet) => {
    return selected.some(i => i.id === dataSet.id)
  }
  // 勾选知识库
  const toggleSelect = (dataSet: DataSet) => {
    const isSelected = isSelectd(dataSet)
    if (isSelected) {
      onSelect(selected.filter(item => item.id !== dataSet.id))
      setSelected(selected.filter(item => item.id !== dataSet.id))
    }
    else {
      if (canSelectMulti) {
        onSelect([...selected, dataSet])
        setSelected([...selected, dataSet])
      }
      else {
        setSelected([dataSet])
        onSelect([dataSet])
      }
    }
  }

  useEffect(() => {
    getDatasetList()
  }, [])

  return (
    <Modal
      isShow={isShow}
      onClose={onClose}
      closable={true}
      scrollable={false}
      className='w-[720px]'
      title={t('appDebug.feature.dataSet.selectTitle')}
      headerExtra={(
        <TextButton size='middle' onClick={() => window.open('/datasets/create', '_blank', 'noreferrer')}>
          <AddCircle className='w-4 h-4'></AddCircle>
          {t('dataset.action.createDataset')}
        </TextButton>
      )}
    >
      <CardList
        type='scroll'
        loading={loaded}
        layout='line'
        className='px-8'
        loadFunc={getDatasetList}
        emptyText={(
          <div>
            <span className='text-gray-500'>{t('appDebug.feature.dataSet.noDataSet1')}</span>
            <Link href="/datasets/create" target="_blank" className='font-normal text-primary-P1'>{t('appDebug.feature.dataSet.toCreate')}</Link>
            <span className='text-gray-500'>{t('appDebug.feature.dataSet.noDataSet2')}</span>
          </div>
        )}
        emptyWrapClassName='my-10'
      >
        {
          datasets.map(item => (
            <Card
              key={item.id}
              replaceClassName={style.card}
              generateHead={() => <AppAvatar appMode='datasets' size={44}></AppAvatar>}
              title={() => (
                <>
                  <div className={style.cardInfoHeader}>
                    {/* 标题 */}
                    <div className='flex flex-col'>
                      <div title={item.name} className={cn('max-w-[250px] title-16-26 truncate', !item.embedding_available && 'opacity-50 !max-w-[120px]')}>{item.name}</div>
                      <div className='flex items-center'>
                        {/* 标签 */}
                        {
                          item.indexing_technique && (
                            <Badge
                              text={formatIndexingTechniqueAndMethod(item.indexing_technique, item.retrieval_model_dict?.search_method)}
                            />
                          )
                        }
                        {
                          item.provider === 'external' && (
                            <Badge text={t('dataset.externalTag')!} />
                          )
                        }
                      </div>
                    </div>
                    {/* 操作按钮 */}
                    {
                      isSelectd(item)
                        ? <div className='group'>
                          <Button
                            className='group-hover:hidden !border-none !w-[72px] !text-primary-P1 !bg-primary-P4 !shrink-0'
                            size={'small'}
                            variant='secondary'
                          >{t('common.status.added')}</Button>
                          <Button
                            className='group-hover:flex hidden !w-[72px] !shrink-0'
                            size={'small'}
                            variant={'warning'}
                            onClick={() => toggleSelect(item)}
                          >{t('common.operation.remove')}</Button>
                        </div>
                        : <Button
                          className='!w-[72px] !shrink-0'
                          size={'small'}
                          variant='secondary-accent'
                          onClick={() => toggleSelect(item)}
                        >{t('common.operation.add')}</Button>
                    }

                  </div>
                </>
              )}
              description={item.description || '-'}
            >
              <div className='text-[12px] leading-[18px] text-gray-G3 !visible'>
                {item.created_at ? formatTime(item.created_at, t('common.dateFormat.dateTime') as string) : '-'}
              </div>
            </Card>
          ))
        }
      </CardList>
    </Modal>
  )
}
export default React.memo(SelectDataSet)
