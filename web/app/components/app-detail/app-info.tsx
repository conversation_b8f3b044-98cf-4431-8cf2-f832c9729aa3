import { useTranslation } from 'react-i18next'
import { useRouter } from 'next/navigation'
import React, { useCallback, useState } from 'react'
import { Divider, Popover } from 'antd'
import { OperationsContent } from '../app/operations'
import StatusTooltip from '../app-publish/statusTooltip'
import s from './style.module.css'
import AppAvatar from '@/app/components/app/avatar'
import { useStore as useAppStore } from '@/app/components/app/store'
import { useAppContext } from '@/context/app-context'

import cn from '@/utils/classnames'
import { ArrowDown } from '@/app/components/base/icons/src/vender/line/arrows'

export type IAppInfoProps = {
  expand: boolean
}

const selectItemWrap = 'h-9 py-2 px-3 w-full flex items-center hover:bg-primary-P4 rounded cursor-pointer'
const selectItemText = 'text-gray-G1 text-sm leading-[24px]'

const AppInfo = ({ expand }: IAppInfoProps) => {
  const { t } = useTranslation()
  const appDetail = useAppStore(state => state.appDetail)
  const appPublishInfo = useAppStore(state => state.appPublishInfo)
  const { canEditApp } = useAppContext()
  const router = useRouter()

  // 是否展开
  const [open, setOpen] = useState(false)

  // 删除应用 返回首页
  const onDelete = useCallback(() => {
    if (appDetail?.mode === 'agent-chat')
      router.push('/apps?category=agent-chat')

    else if (appDetail?.mode === 'advanced-chat')
      router.push('/apps?category=chat')

    else if (appDetail?.mode === 'workflow')
      router.push('/tools')

    else
      router.push('/apps')
  }, [])

  if (!appDetail)
    return null

  return (
    <Popover
      open={open}
      arrow={false}
      trigger={'click'}
      onOpenChange={setOpen}
      placement="bottomLeft"
      content={
        <div className="relative w-[320px] bg-white rounded shadow-xl">
          {/* header */}
          <div className={cn('flex pl-4 pt-3 pr-3', !appDetail.description && 'pb-2')}>
            <div className="relative mr-2 shrink-0">
              <AppAvatar url={appDetail?.icon_url || undefined} size={36} className="!rounded" />
            </div>
            <div className='w-0 grow'>
              <div title={appDetail.name} className='flex items-center justify-between text-sm font-semibold leading-5 truncate text-gray-G1'>{appDetail.name}</div>
              <div className='flex items-center text-[10px] leading-[18px] font-semibold text-gray-G2 gap-1'>
                {appDetail.mode === 'advanced-chat' && (
                  <>
                    <div className={s.appTag}>{t('app.types.chatbot').toUpperCase()}</div>
                    <div title={t('app.newApp.advanced') || ''} className={s.appTag}>
                      {t('app.newApp.advanced').toUpperCase()}
                    </div>
                  </>
                )}
                {appDetail.mode === 'agent-chat' && (
                  <div className={s.appTag}>{t('app.types.agent').toUpperCase()}</div>
                )}
                {appDetail.mode === 'chat' && (
                  <>
                    <div className={s.appTag}>{t('app.types.chatbot').toUpperCase()}</div>
                    <div title={t('app.newApp.basic') || ''} className={s.appTag}>
                      {t('app.newApp.basic').toUpperCase()}
                    </div>
                  </>
                )}
                {appDetail.mode === 'completion' && (
                  <>
                    <div className={s.appTag}>{t('app.types.completion').toUpperCase()}</div>
                    <div title={t('app.newApp.basic') || ''} className={s.appTag}>
                      {t('app.newApp.basic').toUpperCase()}
                    </div>
                  </>
                )}
                {appDetail.mode === 'workflow' && (
                  <div className={s.appTag}>{t('app.types.workflow').toUpperCase()}</div>
                )}
              </div>
            </div>
          </div>
          {/* desscription */}
          {appDetail.description && (
            <div className="px-4 py-2 text-gray-G2 text-xs leading-[18px]">
              {appDetail.description}
            </div>
          )}
          {/* operations */}
          {
            canEditApp && <>
              <Divider className="!m-0" />
              <OperationsContent
                itemTitleClass={selectItemText}
                itemWrapClass={selectItemWrap}
                app={appDetail}
                beforeAction={() => setOpen(false)}
                onDelete={onDelete}
              ></OperationsContent>
            </>
          }
        </div>
      }
    >

      <div className={cn('flex cursor-pointer')} style={{ width: 'calc(100% - 36px)' }}>
        {appDetail.mode !== 'agent-chat' && (
          <div className="relative mr-2 shrink-0">
            <AppAvatar url={appDetail?.icon_url || undefined} size={36} className="!rounded" />
          </div>
        )}
        {expand && (
          <div className="flex flex-col justify-between gap-0.5" style={{ width: 'calc(100% - 44px)' }}>
            <div className='flex gap-1 items-center text-S1 leading-[16px] font-semibold'>
              <div className='truncate' title={appDetail.name}>{appDetail.name}</div>
              <ArrowDown className='w-4 h-4 shrink-0 text-gray-G2' />
              <StatusTooltip appPublishInfo={appPublishInfo}/>
            </div>
            <div className="flex items-center text-[10px] leading-[12px] text-gray-G2 gap-1">
              {appDetail.mode === 'advanced-chat' && (
                <div title={t('app.newApp.advanced') || ''} className={s.appTag}>
                  {t('app.newApp.advanced').toUpperCase()}
                </div>
              )}
              {appDetail.mode === 'agent-chat' && (
                <div className={s.appTag}>{t('app.types.agent').toUpperCase()}</div>
              )}
              {appDetail.mode === 'chat' && (
                <>
                  <div className={s.appTag}>{t('app.types.chatbot').toUpperCase()}</div>
                  <div title={t('app.newApp.basic') || ''} className={s.appTag}>
                    {t('app.newApp.basic').toUpperCase()}
                  </div>
                </>
              )}
              {appDetail.mode === 'completion' && (
                <>
                  <div className={s.appTag}>{t('app.types.completion').toUpperCase()}</div>
                  <div title={t('app.newApp.basic') || ''} className={s.appTag}>
                    {t('app.newApp.basic').toUpperCase()}
                  </div>
                </>
              )}
              {appDetail.mode === 'workflow' && (
                <div className={s.appTag}>{t('app.types.workflow').toUpperCase()}</div>
              )}
            </div>
          </div>
        )}
      </div>
    </Popover>
  )
}

export default React.memo(AppInfo)
