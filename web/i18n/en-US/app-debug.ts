const translation = {
  featureCollapseGroup: {
    title: {
      dataSet: 'knowledge',
      conversation: 'dialogue',
      toolbox: 'toolbox',
      tools: 'tool',
      voice: 'Voice',
    },
  },
  pageTitle: {
    appConfig: 'App configuration',
    line2: 'Choreography',
    line1: 'Role Directives',
    feature: 'Capacity development',
    basicInfo: 'Basic Information',
  },
  promptMode: {
    operation: {
      addMessage: 'Add a message',
    },
    contextMissing: 'Contextual content blocks are missing, and the effectiveness of character directives may not be good.',
  },
  operation: {
    disagreeContent: {
      reason: 'Choose at least one!',
      feedback: 'feedback',
      textAreaPlaceholder: 'We would like to know why you are not satisfied with this answer, what do you think is a better answer?',
    },
    debugConfig: 'debugging',
    automatic: 'generate',
    disagree: 'Tap to step on',
    agree: 'Thumbs up',
    addFeature: 'Add features',
    dislikeRadio: {
      isFake: 'False information',
      formatProblem: 'Formatting issues',
      wrongAnswer: 'sidestep the question',
      noHelp: 'Didn\'t help',
      logicProblem: 'Logical problem',
      unsafe: 'Harmful/unsafe',
    },
  },
  notSetAPIKey: {
    settingBtn: 'Go to Settings',
    trailFinished: 'The trial has ended',
    title: 'The LLM provider\'s key is not set',
    description: 'You need to set the LLM provider\'s key before debugging.',
  },
  trailUseGPT4Info: {
    title: 'GPT-4 is not currently supported',
    description: 'With gpt-4, set the API Key',
  },
  feature: {
    groupChat: {
      description: 'Adding pre-conversation settings to your chat app can improve the user experience.',
      title: 'Chat enhancements',
    },
    groupExperience: {
      title: 'Experience enhancements',
    },
    conversationOpener: {
      title: 'Opening',
      description: 'The guidance information that is automatically displayed after the user enters the agent application is used to help the user understand the capabilities and uses of the agent.',
    },
    suggestedQuestionsAfterAnswer: {
      title: 'Ask',
      tryToAsk: 'Try asking',
      description: 'After the agent replies, it automatically provides 3 suggestions for the user\'s questions based on the content of the conversation.',
      resDes: 'At the end of your answer, you\'ll be given 3 suggestions',
    },
    moreLikeThis: {
      title: 'More similar',
      generateNumTip: 'Number of each generation',
      description: 'Generate multiple pieces of text at once, which can be edited and continued on top of that, and using this feature will consume additional tokens',
    },
    speechToText: {
      description: 'You can use voice input.',
      resDes: 'Voice input is enabled',
      title: 'Speech-to-text',
    },
    textToSpeech: {
      resDes: 'Text-to-audio is enabled',
      description: 'Text can be converted to speech.',
      title: 'Text-to-speech',
    },
    newVoice: {
      voiceInput: {
        title: 'Voice input',
        description: 'After it is enabled, it supports voice input and AI reply playback.',
      },
      voiceConversation: {
        title: 'Voice conversations',
        description: 'When enabled, voice conversations and AI replies are supported.',
      },
      voiceSettings: {
        autoPlayEnabled: 'Open',
        autoPlayDisabled: 'Shut down',
        autoPlay: 'Autoplay feature',
        language: 'Play the dialect type',
        timbre: 'Play the sound type',
      },
      title: 'Voice',
    },
    citation: {
      title: 'References and attributions',
      resDes: 'References and attribution are enabled',
      description: 'Displays the source document information referenced in the reply.',
    },
    voiceConversation: {
      description: 'If the voice conversation feature is enabled, users can have conversations through voice.',
      title: 'Voice conversations',
      rightTooltipDesc: 'When enabled, voice dialogue and reading are supported',
      resDes: 'Voice conversations are enabled',
    },
    voiceInput: {
      resDes: 'Voice input is enabled',
      rightTooltipDesc: 'When enabled, voice input and reading are supported',
      title: 'Voice input',
      description: 'After it is enabled, it supports voice input and AI reply playback.',
    },
    playVoiceType: {
      playDialectType: 'Play the dialect type',
      playTimbreType: 'Play the sound type',
    },
    annotation: {
      scoreThreshold: {
        accurateMatch: 'Precise matching',
        description: 'Lets you set the matching similarity threshold for callout responses.',
        title: 'Score threshold',
        easyMatch: 'Easy to match',
      },
      matchVariable: {
        title: 'Match variables',
        choosePlaceholder: 'Please select a variable',
      },
      resDes: 'Callout responses are enabled',
      title: 'Annotate responses',
      cached: 'Labeled',
      add: 'Add labels',
      description: 'Configure answers to certain questions, and the agent replies with fixed answers to those answers.',
      cacheManagement: 'Annotation management',
      edit: 'Edit the callout',
      removeConfirm: 'Delete this callout?',
      remove: 'Remove',
    },
    dataSet: {
      queryVariable: {
        choosePlaceholder: 'Please select a variable',
        noVar: 'There are no variables',
        unableToQueryDataSetTip: 'Unable to successfully query the knowledge base, select a context query variable in the Context section.',
        tip: 'The variable will be used as a query input for context retrieval, obtaining contextual information related to the input of that variable.',
        deleteContextVarTitle: 'Delete the variable "{{varName}}"?',
        title: 'Query variables',
        contextVarNotEmpty: 'Context query variables can\'t be empty',
        unableToQueryDataSet: 'The knowledge base could not be queried',
        ok: 'Good',
        noVarTip: 'Please create a variable',
        deleteContextVarTip: 'This variable has been set as a context query variable, and deleting it will affect the normal use of the knowledge base. If you still need to delete it, reselect it in the context section.',
      },
      selectTitle: 'Select Knowledge Base',
      textBlocks: 'Blocks of text',
      toCreate: 'Go to the Knowledge Base',
      notSupportSelectMulti: 'Currently, only one knowledge base can be referenced',
      noData: 'Once the knowledge base is added, the large model will provide more accurate answers based on the uploaded documents',
      noDataSet2: 'to create',
      words: 'word',
      title: 'knowledge base',
      noDataSet1: 'No knowledge base currently.',
    },
    tools: {
      modal: {
        toolType: {
          placeholder: 'Please select a tool type',
          title: 'The type of tool',
        },
        name: {
          title: 'name',
          placeholder: 'Please fill in the name',
        },
        variableName: {
          title: 'The name of the variable',
          placeholder: 'Please enter the variable name',
        },
        title: 'tool',
      },
      toolsInUse: '{{count}} tool in use',
      tips: 'Tools provide a standard way to call the API with user input or variables as request parameters for the API and external data as context for querying for external data.',
    },
    conversationHistory: {
      editModal: {
        title: 'Edit the conversation role name',
        assistantPrefix: 'Assistant prefix',
        userPrefix: 'User prefix',
      },
      title: 'Conversation History',
      description: 'Set the prefix name of the conversation role',
      learnMore: 'Learn more',
      tip: 'Conversation history is not enabled, please add it in the prompt above<histories>.',
    },
    toolbox: {
      title: 'toolbox',
    },
    moderation: {
      modal: {
        provider: {
          openaiTip: {
            prefix: 'OpenAI Moderation is required in the ',
            suffix: 'in Configure the OpenAI API key.',
          },
          title: 'category',
          openai: 'OpenAI moderation',
          keywords: 'keyword',
        },
        keywords: {
          tip: 'One for each line, separated by a line break. Up to 100 characters per line.',
          placeholder: 'One for each line, separated by a line break',
          line: 'Yes',
        },
        content: {
          output: 'Review the output',
          fromApi: 'Canned responses are returned via the API',
          placeholder: 'Here is the preset content of the reply',
          errorMessage: 'Canned replies cannot be empty',
          condition: 'At least one item is enabled for reviewing input content and reviewing output content',
          supportMarkdown: 'Markdown is supported',
          input: 'Review the input',
          preset: 'Canned responses',
        },
        openaiNotConfig: {
          before: 'OpenAI content censorship is required in the ',
          after: 'in Configure the OpenAI API key.',
        },
        title: 'Content moderation settings',
      },
      inputEnabled: 'Input',
      description: 'You can call the moderation API or maintain a sensitive thesaurus to make the model output more secure.',
      allEnabled: 'Input and output content',
      title: 'Content Moderation',
      contentEnableLabel: 'Enable moderation of content',
      outputEnabled: 'Output content',
    },
    fileUpload: {
      supportedTypes: 'Supported file types',
      numberLimit: 'Maximum number of uploads',
      title: 'File upload',
      description: 'The chat input box supports uploading files. Types include images, documents, and others',
      modalTitle: 'File upload settings',
    },
    imageUpload: {
      title: 'Image upload',
      supportedTypes: 'Supported file types',
      numberLimit: 'Maximum number of uploads',
      description: 'You can upload images',
      modalTitle: 'Image upload settings',
    },
    bar: {
      manage: 'manage',
      empty: 'Turn on features to enhance the WebApp user experience',
      enableText: 'The feature is turned on',
    },
    chatBg: {
      delete: 'Delete a session background?',
      description: 'Add a background image to your conversation to make your chat-style background look greater. After publishing, it can be displayed externally in the application square or front-end page.',
      title: 'Session background image',
      deleteDescription: 'Are you sure you want to delete the session background image? After deletion, there is no background style in the session area.',
    },
  },
  generate: {
    template: {
      pythonDebugger: {
        name: 'Python code helper',
        instruction: 'A bot that helps you write and correct bugs',
      },
      translation: {
        name: 'Translation bots',
        instruction: 'A translator that can translate multiple languages',
      },
      professionalAnalyst: {
        instruction: 'Extract insights, identify risks, and distill key information from long-form reports',
        name: 'Professional Analyst',
      },
      excelFormulaExpert: {
        name: 'Excel Formula Expert',
        instruction: 'A conversational bot that allows novice users to understand, use, and create Excel formulas',
      },
      travelPlanning: {
        name: 'Travel planning assistant',
        instruction: 'Trip Planner Assistant is a smart tool designed to help users plan their trips easily',
      },
      SQLSorcerer: {
        instruction: 'Convert natural language into SQL queries',
        name: 'SQL generation',
      },
      GitGud: {
        name: 'Git Guru',
        instruction: 'Generate appropriate Git commands from user-raised versioning needs',
      },
      meetingTakeaways: {
        instruction: 'Summarize the content of the meeting, including discussion topics, key takeaways, and to-do items',
        name: 'Summary of meeting minutes',
      },
      writingsPolisher: {
        name: 'Polishing the article',
        instruction: 'Improve my article with authentic editing skills',
      },
    },
    generate: 'generate',
    noDataLine2: 'A preview of the orchestration will be displayed here.',
    instruction: 'directives',
    title: 'Character Instruction Generator',
    apply: 'apply',
    description: 'The Character Instruction Generator uses the configured model to optimize the Character Instruction for higher quality and better structure. Please write a clear and detailed description.',
    noData: 'Describe your use case on the left, and an orchestration preview will be displayed here.',
    resTitle: 'Generated character directives',
    tryIt: 'Get your feet wet',
    loading: 'Orchestrate your app in...',
    overwriteMessage: 'Applying this prompt will overwrite the existing configuration.',
    overwriteTitle: 'Overwrite an existing configuration?',
    instructionPlaceHolder: 'Write clear, specific instructions.',
    noDataLine1: 'Describe your use case on the left,',
  },
  resetConfig: {
    title: 'Confirm the reset?',
    message: 'The reset will lose all changes to the current page and revert to the configuration it was last published',
  },
  errorMessage: {
    nameOfKeyRequired: 'The name of the variable {{key}} is mandatory',
    waitForBatchResponse: 'Wait for the batch task to complete',
    valueOfVarRequired: 'Variables are required',
    waitForResponse: 'Please wait for the response to the previous message to complete',
    waitForImgUpload: 'Please wait for the image to be uploaded',
    notSelectModel: 'Please select a model',
    queryRequired: 'Primary text is required',
    notSelectBestModel: 'If the optimal model is not selected, it can be returned only after it is selected',
    waitForFileUpload: 'Please wait for the file upload to complete',
  },
  variableTable: {
    name: 'The name of the field',
    key: 'Variable Key',
    typeString: 'text',
    action: 'operate',
    typeSelect: 'Drop-down options',
    optional: 'Optional',
    type: 'type',
  },
  varKeyError: {
    notStartWithNumber: '{{key}} cannot start with a number',
    tooLong: '{{key}} is too long. It cannot exceed 30 characters',
    keyAlreadyExists: '{{key}} already exists',
    notValid: '{{key}} is illegal. It can only contain English characters, digits, and underscores',
    canNoBeEmpty: '{{key}} required',
  },
  otherError: {
    queryNoBeEmpty: 'The query content must be set in the role directive',
    promptNoBeEmpty: 'Role directives cannot be empty',
    historyNoBeEmpty: 'Conversation history must be set in the character directive',
  },
  variableConfig: {
    'file': {
      image: {
        name: 'Image',
      },
      audio: {
        name: 'audio',
      },
      document: {
        name: 'documentation',
      },
      video: {
        name: 'Video',
      },
      custom: {
        description: 'Specify a different file type',
        name: 'Other file types',
        createPlaceholder: '  File extension, such as .doc',
      },
      supportFileTypes: 'Supported file types',
    },
    'errorMsg': {
      labelNameRequired: 'Display name is required',
      optionRepeat: 'The option cannot be repeated',
      atLeastOneOption: 'At least one option is required',
      varNameCanBeRepeat: 'Variable names cannot be repeated',
    },
    'description': 'Set variable {{varName}}',
    'select': 'Drop-down options',
    'options': 'Options',
    'text-input': 'text',
    'longTxt': 'Template templates',
    'editModalTitle': 'Edit variables',
    'maxLength': 'Maximum length',
    'required': 'Required',
    'varName': 'The name of the variable',
    'single-file': 'Single file',
    'number': 'digit',
    'addOption': 'Add options',
    'paragraph': 'paragraph',
    'apiBasedVar': 'API-based variables',
    'uploadFileTypes': 'Upload file type',
    'notSet': 'If not, enter {{input}} in the Prompt to try',
    'inputPlaceholder': 'Please enter',
    'both': 'both',
    'content': 'content',
    'addModalTitle': 'Add variables',
    'fieldType': 'The type of field',
    'maxNumberOfUploads': 'Maximum number of uploads',
    'stringTitle': 'Text box settings',
    'maxNumberTip': 'The maximum upload file size is {{size}}',
    'string': 'text',
    'labelName': 'Display name',
    'localUpload': 'Local upload',
    'multi-files': 'List of files',
  },
  vision: {
    visionSettings: {
      uploadMethod: 'Upload method',
      resolution: 'resolution',
      low: 'low',
      localUpload: 'Local upload',
      url: 'URL',
      uploadLimit: 'Upload limit',
      resolutionTooltip: 'Low-resolution mode will cause the model to receive a low-resolution version of the image, measuring 512 x 512, with 65 tokens to represent the image. This allows the API to return a response faster and consumes less input in use cases that don\'t require high detail.\nThe high-resolution mode will first allow the model to view the low-resolution image and then create a detailed cropped image of 512 pixels based on the size of the input image. Each detailed cropped image uses twice the budget for a total of 129 Tokens.',
      both: 'both',
      high: 'high',
      title: 'Visual settings',
    },
    onlySupportVisionModelTip: 'Only the vision model is configured with vision capabilities',
    description: 'Turning on the visual feature will allow the model to input an image and answer the user\'s questions based on the understanding of the image\'s content',
    name: 'vision',
    settings: 'Set up',
  },
  voice: {
    voiceSettings: {
      voice: 'timbre',
      autoPlayEnabled: 'Open',
      resolutionTooltip: 'Text-to-speech voices support languages.',
      autoPlayDisabled: 'Shut down',
      title: 'Tone settings',
      language: 'language',
      autoPlay: 'Autoplay',
    },
    settings: 'Set up',
    defaultDisplay: 'Default sound',
    description: 'Text-to-speech voice settings',
    name: 'timbre',
  },
  openingStatement: {
    tooShort: 'Pre-dialogue character instructions must be at least 20 words to generate an opening line',
    notIncludeKey: 'The variable {{key}} is not included in the prefix role directive. Please add this variable to the prefix role directive',
    add: 'Add an opening line',
    title: 'Opening',
    noDataPlaceHolder: 'In conversational applications, having AI actively say the first sentence can bring the user closer together.',
    placeholder: 'Write your opening statement here.',
    writeOpener: 'Write an opening statement',
    placeholder2: 'Enter a preset question',
    label1: 'Copywriting for opening remarks',
    label2: 'Preset questions',
    varTip: 'You can use variables, try typing {{variable}}',
  },
  inputs: {
    title: 'Preview & Debug',
    completionVarTip: 'Fill in the value of the variable, which will be automatically replaced in the character directive each time you submit an issue',
    userInputField: 'User input',
  },
  datasetConfig: {
    retrieveChangeTip: 'Modifying the indexing and retrieval patterns may affect the applications associated with that knowledge base.',
    knowledgeTip: 'Click the " button to add a knowledge base',
    top_k: 'Maximum number of recalled fragments',
    score_threshold: 'Similarity match value',
    score_thresholdTip: 'A quantitative measure of the degree of relevance or match between search results and search terms. The higher the score, the more relevant or matched the result is, and the data value is between 0 and 1.',
    top_kTip: 'Find the top K results that are most relevant or important to the search term in a large amount of data, and fill in the value for the user.',
  },
  assistantType: {
    chatAssistant: {
      name: 'Basic Assistant',
      description: 'Build a chat-like assistant based on LLMs',
    },
    agentAssistant: {
      description: 'Build a smart assistant that can choose the tools you set to complete the tasks you set',
      name: 'Smart assistants',
    },
    name: 'The type of assistant',
  },
  agent: {
    agentModeType: {
      functionCall: 'Function Calling',
      ReACT: 'ReAct',
    },
    setting: {
      maximumIterations: {
        name: 'Maximum number of iterations',
        description: 'Limit the number of iterations that an agent assistant can perform',
      },
      description: 'The smart assistant settings allow you to set up advanced features such as proxy mode and built-in prompts, which are only available in the proxy type.',
      name: 'Agent settings',
    },
    tools: {
      description: 'The use of plugins can extend the capabilities of the agent, such as searching the Internet or scientific computing',
      name: 'tool',
      tip: 'The capabilities of large models can be extended using tools, such as searching the internet or doing scientific calculations',
      enabled: 'enable',
    },
    promptPlaceholder: 'Write down your character instructions here',
    agentMode: 'Agent Mode',
    buildInPrompt: 'Built-in role directives',
    firstPrompt: 'First Character Directive',
    agentModeDes: 'Set the agent\'s inference mode type',
    nextIteration: 'The next iteration',
  },
  formattingChangedTitle: 'The app has been adjusted',
  autoAddVar: 'Are undefined variables referenced in role directives automatically added to the user input form?',
  chatSubTitle: 'Role Directives',
  variableTip: 'The variables will be filled in by the user in the form of a form before the conversation, and the content of the form filled in by the user will automatically replace the variables in the role instructions.',
  notSetVar: 'Variables can be used to introduce character instructions or opening lines into the user input form, you can try typing {{input}} in the character command',
  promptPlaceholder: 'Please enter the character command, and the agent will reply to the user\'s question according to the character settings, tasks, and reply logic. The clearer and more unambiguous the instructions are written, the more the agent\'s response will be as expected.',
  promptTip: 'Set the identity, task, and response logic for the agent, and the large model will understand and reply according to the role identity setting and task.',
  completionSubTitle: 'Prefix role directives',
  variableTitle: 'variable',
  formattingChangedText: 'Click "Start over" to update the configuration and open a new dialog for debugging.',
  bestModel: 'Optimal model',
  debugAsSingleModel: 'Single model for commissioning',
  noResult: 'The output is shown here',
  debugAsMultipleModel: 'Multi-model comparison',
  returnWithBestModel: 'Returns the optimal model',
  publishAs: 'Published as',
  removeModel: 'Remove the model',
}

export default translation
