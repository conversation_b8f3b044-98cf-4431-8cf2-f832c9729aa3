import { BlockEnum } from '../../types'
import type { NodeDefault } from '../../types'
import type { VideoNodeType } from './types'
import { ALL_CHAT_AVAILABLE_BLOCKS, ALL_COMPLETION_AVAILABLE_BLOCKS } from '@/app/components/workflow/constants'
const i18nPrefix = 'workflow'

const nodeDefault: NodeDefault<VideoNodeType> = {
  defaultValue: {
    type: BlockEnum.Video,
    library_ids: [],
    fork: false,
  },
  getAvailablePrevNodes(isChatMode: boolean) {
    const nodes = isChatMode
      ? ALL_CHAT_AVAILABLE_BLOCKS
      : ALL_COMPLETION_AVAILABLE_BLOCKS.filter(type => type !== BlockEnum.End)
    return nodes
  },
  getAvailableNextNodes(isChatMode: boolean) {
    const nodes = isChatMode ? ALL_CHAT_AVAILABLE_BLOCKS : ALL_COMPLETION_AVA<PERSON><PERSON>LE_BLOCKS
    return nodes
  },
  checkValid(payload: VideoNodeType, t: any) {
    let errorMessages = ''
    if (!errorMessages && !(
      payload.start_time_variable_selector
      || payload.end_time_variable_selector
      || payload.event_name_variable_selector
      || payload.location_variable_selector
      || payload.device_variable_selector))
      errorMessages = t(`${i18nPrefix}.errorMsg.inputVarRequired`)

    if (!errorMessages && (!payload.library_ids || payload.library_ids.length === 0))
      errorMessages = t(`${i18nPrefix}.errorMsg.fieldRequired`, { field: t(`${i18nPrefix}.nodes.video.name`) })

    return {
      isValid: !errorMessages,
      errorMessage: errorMessages,
    }
  },
}

export default nodeDefault
