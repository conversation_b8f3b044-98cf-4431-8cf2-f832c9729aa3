import type { ReactElement } from 'react'
import RadioGroupContext from '../../context'
import s from '../../style.module.css'
import cn from '@/utils/classnames'

export type TRadioGroupProps = {
  children?: ReactElement | ReactElement[]
  value?: string | number
  className?: string
  onChange?: (value: any) => void
  type?: 'default' | 'info'
}

export default function Group({ children, value, onChange, type = 'default', className = '' }: TRadioGroupProps): JSX.Element {
  const onRadioChange = (value: any) => {
    onChange?.(value)
  }
  return (
    <div className={cn(
      'flex flex-wrap items-center',
      s[`radio-group-${type}`],
      className,
    )}>
      <RadioGroupContext.Provider value={{ value, onChange: onRadioChange, type }}>
        {children}
      </RadioGroupContext.Provider>
    </div>
  )
}
