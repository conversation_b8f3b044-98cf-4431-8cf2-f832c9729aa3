import { useCallback, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useChatWithHistoryContext } from '../context'
import List from './list'
import style from './styles/style.module.css'

import type { ConversationItem } from '@/types/share'
import Confirm from '@/app/components/base/confirm'
import cn from '@/utils/classnames'
import Button from '@/app/components/base/button'
import { Add } from '@/app/components/base/icons/src/vender/solid/general'

const Sidebar = () => {
  const { t } = useTranslation()
  const {
    pinnedConversationList,
    conversationList,
    handleNewConversation,
    currentConversationId,
    handleChangeConversation,
    handlePinConversation,
    handleUnpinConversation,
    handleRenameConversation,
    handleDeleteConversation,
    isMobile,
  } = useChatWithHistoryContext()
  const [showConfirm, setShowConfirm] = useState<ConversationItem | null>(null)
  const [renameItem, setRenameItem] = useState<ConversationItem | null>(null)

  // 对话项单项
  const handleOperate = useCallback(
    (type: string, item: ConversationItem) => {
      if (type === 'pin')
        handlePinConversation(item.id)
      if (type === 'delete')
        setShowConfirm(item)
      if (type === 'rename')
        setRenameItem(item)
    }, [handlePinConversation, handleUnpinConversation])
  // 取消删除
  const handleCancelConfirm = useCallback(() => {
    setShowConfirm(null)
  }, [])
  // 点击删除按钮
  const handleDelete = useCallback(() => {
    if (showConfirm)
      handleDeleteConversation(showConfirm.id, { onSuccess: handleCancelConfirm })
  }, [showConfirm, handleDeleteConversation, handleCancelConfirm])
  // 取消重命名
  const handleCancelRename = useCallback(() => {
    setRenameItem(null)
  }, [])
  // 重命名对话
  const setNewName = useCallback((newName: string) => {
    if (renameItem?.id && newName)
      handleRenameConversation(renameItem.id, newName, { onSuccess: handleCancelRename })
    else
      handleCancelRename()
  }, [renameItem, handleRenameConversation, handleCancelRename])

  return (
    <div className={cn(style.siderbar, isMobile && '!w-[300px]')}>
      <Button
        variant='secondary'
        className={style.newChatBtn}
        onClick={handleNewConversation}
      >
        <Add className='w-4 h-4' />
        {t('share.chat.newChat')}
      </Button>
      <div className='flex flex-col -mx-2 font-normal grow text-S1 leading-H1'>
        {/* 固定对话列表 */}
        {
          !!pinnedConversationList.length && (
            <List
              isPin
              title={t('share.chat.pinnedTitle') || ''}
              list={pinnedConversationList}
              onChangeConversation={handleChangeConversation}
              onOperate={handleOperate}
              currentConversationId={currentConversationId}
              renameItem={renameItem}
              setNewName={setNewName}
            />
          )
        }
        {/* 对话列表 */}
        {
          !!conversationList.length && (
            <List
              title={t('share.chat.chatListHistory')!}
              list={conversationList}
              onChangeConversation={handleChangeConversation}
              onOperate={handleOperate}
              currentConversationId={currentConversationId}
              renameItem={renameItem}
              setNewName={setNewName}
            />
          )
        }
      </div>
      {!!showConfirm && (
        <Confirm
          title={t('share.chat.deleteConversation.title')}
          content={t('share.chat.deleteConversation.content') || ''}
          isShow
          onCancel={handleCancelConfirm}
          onConfirm={handleDelete}
        />
      )}
    </div>
  )
}

export default Sidebar
