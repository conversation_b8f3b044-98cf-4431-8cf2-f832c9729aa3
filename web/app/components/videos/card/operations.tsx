import { useCallback, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useContext } from 'use-context-selector'
import EditVideoModal from '../create-video-modal'
import { VideoActionTextBtn } from '../common/video-action-btn'
import type { Video } from '@/types/videos'
import { deleteVideo } from '@/service/videos'
/* 公共组件 */
import Confirm from '@/app/components/base/confirm'
import { ToastContext } from '@/app/components/base/toast'
import { useProviderContext } from '@/context/provider-context'

type OperationsProps = {
  video: Video
  onSuccess?: () => void
}

const Operations = ({ video, onSuccess }: OperationsProps) => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  const { onPlanInfoChanged } = useProviderContext()

  // 显示重命名弹窗
  const [showEditModal, setShowEditModal] = useState(false)
  // 显示确认删除窗口
  const [showConfirmDelete, setShowConfirmDelete] = useState(false)

  // 确认删除
  const onConfirmDelete = useCallback(async () => {
    await deleteVideo(video.id)
    notify({ type: 'success', message: t('videos.notify.videoDeleted') })
    onPlanInfoChanged()
    if (onSuccess)
      onSuccess()
    setShowConfirmDelete(false)
  }, [video.id, notify, t, onPlanInfoChanged, onSuccess])

  // 基础函数
  const baseFunc = (e: React.MouseEvent) => {
    e.stopPropagation()
    e.preventDefault()
  }
  // 重命名
  const onClickRename = async (e: React.MouseEvent<HTMLButtonElement>) => {
    baseFunc(e)
    setShowEditModal(true)
  }
  // 删除
  const onClickDelete = async (e: React.MouseEvent<HTMLDivElement>) => {
    baseFunc(e)
    setShowConfirmDelete(true)
  }

  return (
    <>
      <div className='flex items-center gap-6'>
        <VideoActionTextBtn size='small' onClick={onClickRename}>
          {t('common.operation.edit')}
        </VideoActionTextBtn>
        <VideoActionTextBtn
          size='small'
          onClick={onClickDelete}
        >
          {t('common.operation.delete')}
        </VideoActionTextBtn>
      </div>
      <div className='absolute' onClick={e => e.stopPropagation()}>
        {showEditModal && (
          <EditVideoModal
            video={video}
            onClose={() => setShowEditModal(false)}
            onSuccess={onSuccess}
          />
        )}
        {showConfirmDelete && (
          <Confirm
            title={t('videos.modalTitle.deleteVideoConfirmTitle')}
            content={t('videos.notify.deleteVideoConfirmContent')}
            isShow={showConfirmDelete}
            onConfirm={onConfirmDelete}
            onCancel={() => setShowConfirmDelete(false)}
          />
        )}
      </div>
    </>
  )
}

export default Operations
