'use client'
import { useState, useEffect } from 'react';
import { useAppContext } from '@/context/app-context'

interface AccessConfig {
  allowPaid?: boolean;
  allowBasic?: boolean;
  allowAdmin?: boolean;
  allowSub?: boolean;
  allowPersonal?: boolean;
  allowTeam?: boolean;
  allowPrivatePersonal?: boolean,
  allowPrivateTeam?: boolean,
}

/**
 * 检查当前用户是否有权限访问特定功能
 * @param config 权限配置
 * @returns boolean
 */
const useFeatureAccess = (config: AccessConfig): boolean => {
  const { userProfile, isPrivate, currentWorkspace } = useAppContext()
  const { is_parent, is_vip } = userProfile // is_parent是否主账号，is_vip是否付费版
  const { plan } = currentWorkspace // plan: 'basic'个人版 |'team'团队版
  
  if (isPrivate) 
    return (plan === 'basic' && config.allowPrivatePersonal) || (plan === 'team' && config.allowPrivateTeam) || false;

  // 基础版：直接返回 allowBasic 的配置
  if (!is_vip) {
    return config.allowBasic ?? false;
  }

  // 付费版：检查角色和空间权限
  const isPaidAllowed = config.allowPaid ?? false;
  const isRoleAllowed = 
    (is_parent && config.allowAdmin) || 
    (!is_parent && config.allowSub);
  const isSpaceAllowed = 
    (plan === 'basic' && config.allowPersonal) || 
    (plan === 'team' && config.allowTeam);
  return Boolean(isPaidAllowed && isRoleAllowed && isSpaceAllowed); 
};

export default useFeatureAccess;