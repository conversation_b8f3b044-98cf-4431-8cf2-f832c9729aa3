'use client'
import React, { Fragment, useCallback, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { Select, Switch } from 'antd'
import s from './style.module.css'
import type { NewLanguageType, NewVoiceConfigType } from '@/models/app'

const labelStyle = 'text-[12px] leading-H1 text-gray-G1 mb-2'
type LanguageParamConfigProps = {
  languageValue: string
  timbreValue: string
  autoPlayValue?: boolean
  onLanguageChange: (value: string) => void
  onTimbreChange: (value: string) => void
  isAutoPlay?: boolean
  onAutoPlayChange?: (value: boolean) => void
  voicesConfigData?: NewVoiceConfigType[]
  disabled?: boolean
}
const LanguageParamConfig = ({
  disabled,
  languageValue,
  timbreValue,
  autoPlayValue,
  onLanguageChange,
  onTimbreChange,
  onAutoPlayChange,
  isAutoPlay,
  voicesConfigData,
}: LanguageParamConfigProps) => {
  const { t } = useTranslation()

  // const [languageList, setLanguageList] = React.useState<NewLanguageType[]>([])
  const [timbreList, setTimbreList] = React.useState<NewLanguageType[]>([])
  // const [languageVal, setLanguageValue] = React.useState('')
  // const [timbreVal, setTimbreValue] = React.useState('')

  const getTimbreLists = useCallback(() => {
    if (!voicesConfigData)
      return
    const list = voicesConfigData?.find((item) => {
      return item?.value === languageValue
    })
    setTimbreList(list?.children || [])
  }, [languageValue, voicesConfigData])
  useEffect(() => {
    getTimbreLists()
  }, [getTimbreLists])

  return (
    <>
      {/* <div className='mb-3'>
        <div className={s.voice_configuration_div_style} >
          <div className={s.voice_configuration_title_style} >
            {t('appDebug.feature.newVoice.voiceSettings.language')}
          </div>
          <Select
            placeholder="请选择"
            showSearch={ false }
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            options= { getLanguageList }
            value={ languageValue }
            // style={{ width: 'calc(100% - 107px)', display: 'inline-block', borderRadius: '0px 4px 4px 0px' }}
            className={s.voice_configuration_select_style}
            onChange={(value) => {
              onLanguageChange(value)
              getTimbreLists()
              onTimbreChange(timbreDefaulValue)
            }}
          />
        </div>
      </div> */}
      <div className='mb-3'>
        <div className={s.voice_configuration_div_style} >
          <div className={s.voice_configuration_title_style} >
            {t('appDebug.feature.newVoice.voiceSettings.timbre')}
          </div>
          <Select
            disabled={disabled}
            placeholder={t('common.placeholder.select')}
            showSearch={ false }
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            options= { timbreList }
            value={ timbreValue }
            className={s.voice_configuration_select_style}
            onChange={(value) => {
              onTimbreChange(value)
            }}
          />
        </div>
      </div>
      {/* <div className='mb-3'>
      <div>
        <div className={s.voice_title_style} >
          {t('appDebug.feature.newVoice.voiceSettings.language')}
        </div>
        <Select
          placeholder="请选择"
          showSearch={ false }
          filterOption={(input, option) =>
            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
          }
          options= { getLanguageList }
          value={ languageValue }
          // style={{ width: 'calc(100% - 107px)', display: 'inline-block', borderRadius: '0px 4px 4px 0px' }}
          className={s.voice_select_style}
          onChange={(value) => {
            onLanguageChange(value)
            getTimbreLists()
            onTimbreChange(timbreDefaulValue)
          }}
        />
      </div>
    </div> */}
      {/* <div className='mb-3'>
        <div className={labelStyle}>{t('appDebug.feature.newVoice.voiceSettings.language')}</div>
        <div className='flex items-center gap-1'>
          <Select
            placeholder="请选择"
            showSearch={ false }
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            options= { getLanguageList }
            value={ languageValue }
            style={{ width: '100%' }}
            onChange={(value) => {
              onLanguageChange(value)
              getTimbreLists()
              onTimbreChange(timbreDefaulValue)
            }}
          />
        </div>
      </div> */}
      {/* <div className='mb-3'>
        <div className={labelStyle}>{t('appDebug.feature.newVoice.voiceSettings.timbre')}</div>
        <div className='flex items-center gap-1'>
          <Select
            placeholder="请选择"
            showSearch={ false }
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            options= { timbreList }
            value={ timbreValue }
            style={{ width: '100%' }}
            onChange={(value) => {
              onTimbreChange(value)
              console.log(value, '=====音色')
            }}
          />
        </div>
      </div> */}
      {/* 自动播放功能 */}
      {
        isAutoPlay && (
          <div className='mb-3'>
            <div className='flex justify-between items-center'>
              <div className='text-[12px] leading-H1 text-gray-G1 mb-2'>
                {t('appDebug.feature.newVoice.voiceSettings.autoPlay')}
              </div>
              <div className='flex gap-3 items-center'>
                <Switch
                  size='small'
                  defaultValue={ autoPlayValue }
                  onChange={(value) => {
                    onAutoPlayChange?.(value)
                  }}
                />
              </div>
            </div>

          </div>
        )
      }
    </>
  )
}

export default React.memo(LanguageParamConfig)
