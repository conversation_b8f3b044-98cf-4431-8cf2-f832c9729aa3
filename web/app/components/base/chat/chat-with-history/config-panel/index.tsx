import { useTranslation } from 'react-i18next'
import { useChatWithHistoryContext } from '../context'
import Form from './form'
import AppAvatar from '@/app/components/app/avatar'
import style from '@/app/components/base/chat/chat-with-history/styles/style.module.css'

const ConfigPanel = () => {
  const { t } = useTranslation()
  const {
    appData,
    inputsForms,
    showConfigPanelBeforeChat,
  } = useChatWithHistoryContext()

  // 是否是长文档应用
  const longTxtForm = inputsForms.find(form => form.type === 'longTxt')

  return (
    <>
      {longTxtForm
        ? (
          <div
            className={`
            grow  rounded border-gray-G5'
          `}
          >
            <div className={`xq-flex-between xq-flex-vbottom ${style.wDiv}`}>
              <div>
                <div className='xq-nowrap'>
                  <div className={`xq-nowrap xq-flex-vcenter ${style.titleDiv}`}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                      <path d="M8.89276 2.84766H3.70454C3.13636 2.84766 2 3.16518 2 4.50199C2 5.8388 3.13636 6.17547 3.70454 6.17547H8.6654C9.23356 6.17547 10.3333 6.50213 10.3333 7.83895C10.3333 9.17574 9.23356 9.50942 8.6654 9.50942H2.98366" stroke="#3168F5" stroke-linecap="square" stroke-linejoin="round"/>
                      <path d="M2.84444 8.67578L2.01562 9.52297L2.84444 10.344" stroke="#3168F5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M9.09375 2L9.92256 2.84719L9.09375 3.6682" stroke="#3168F5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <div className={`xq11 ${style.titleWord}`}>
                      {t('sampleTemplate.longText.workflow')}
                    </div>
                  </div>
                </div>
                <div className={`xq-nowrap ${style.bigtel}`}>
                  {t('sampleTemplate.longText.longtitle1')}<br/>{t('sampleTemplate.longText.longtitle2')}
                </div>
                <div className={`xq-nowrap ${style.hDiv}`}></div>
              </div>
              <img src='/assets/topremark.png' className={`${style.topremarkPic}`}></img>
            </div>
            {/* <div className={style.logo}>
              <AppAvatar
                appMode={'chat'}
                url={appData?.site?.icon_url}
                size={68}
                className={style.logo_img}
              />
              <div className={style.name}>长文档创作智能体</div>
            </div> */}
            {showConfigPanelBeforeChat && (
              <div className="p-2 flex">
                <AppAvatar
                  appMode={'chat'}
                  url={appData?.site?.icon_url}
                  size={32}
                  className="mt-4 mr-2 rounded-full"
                />
                <div
                  className={'grow bg-white border border-gray-G5 p-4 rounded'}
                >
                  <Form />
                </div>
              </div>
            )}
          </div>
        )
        : (
          showConfigPanelBeforeChat && <div
            className={`
            grow overflow-y-auto px-6 py-4 bg-white rounded border border-gray-G5'
          `}
          >
            <div className="grow title-14-24 mb-4">{t('share.chat.privatePromptConfigTitle')}</div>
            <Form />
          </div>
        )}

    </>
  )
}

export default ConfigPanel
