'use client'
import type { FC } from 'react'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import CSVUploader from './csv-uploader'
import CSVDownloader from './csv-downloader'
import Button from '@/app/components/base/button'
import Modal from '@/app/components/base/modal'
import type { DocForm } from '@/models/datasets'

export enum ProcessStatus {
  WAITING = 'waiting',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  ERROR = 'error',
  FAILED = 'failed'
}

export type IBatchModalProps = {
  isShow: boolean
  docForm: DocForm
  onCancel: () => void
  onConfirm: (file: File) => void
  metadatas?: any[]
  category?: string | number
}

const BatchModal: FC<IBatchModalProps> = ({
  isShow,
  docForm,
  onCancel,
  onConfirm,
  metadatas = [],
  category,
}) => {
  const { t } = useTranslation()
  const [currentCSV, setCurrentCSV] = useState<File>()
  const handleFile = (file?: File) => setCurrentCSV(file)

  const handleSend = () => {
    if (!currentCSV)
      return
    onCancel()
    onConfirm(currentCSV)
  }

  useEffect(() => {
    if (!isShow)
      setCurrentCSV(undefined)
  }, [isShow])

  return (
    <Modal
      isShow={isShow}
      onClose={onCancel}
      closable
      title={t('datasetDocuments.list.batchModal.title')}
      footer={
        <>
          <Button className='mr-4' variant={'secondary-accent'} onClick={onCancel}>
            {t('datasetDocuments.list.batchModal.cancel')}
          </Button>
          <Button variant="primary" onClick={handleSend} disabled={!currentCSV}>
            {t('datasetDocuments.list.batchModal.run')}
          </Button>
        </>
      }
    >
      <CSVUploader
        file={currentCSV}
        updateFile={handleFile}
      />
      <CSVDownloader
        docForm={docForm}
        metadatas={metadatas}
        category={category}
      />
    </Modal>
  )
}
export default React.memo(BatchModal)
