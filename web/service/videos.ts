import qs from 'qs'
import { del, get, post, put } from './base'
import type {
  DocHandleStatusResponse,
  Video,
  VideoDocListResponse,
  VideoListResponse,
} from '@/types/videos'
import {
  MediaType,
} from '@/types/videos'
import type { CommonResponse } from '@/models/common'

// 获取视频库列表
export const fetchVideoList = (params: {
  page: number
  size?: number
  keyword?: string
  ids?: string[]
}) => {
  const urlParams = qs.stringify(params, { indices: false })
  return get<CommonResponse<VideoListResponse>>(`/video-libraries?${urlParams}`)
}
// 创建视频库
export const createVideo = (body: {
  name: string
  description: string
  type: MediaType
}) => {
  return post<CommonResponse<Video>>('/video-libraries', { body })
}
// 删除视频库
export const deleteVideo = (id: string) => {
  return del<CommonResponse>(`/video-libraries/${id}`)
}
// 修改视频库
export const updateVideo = (id: string, body: {
  name: string
  description: string
}) => {
  return put<CommonResponse<Video>>(`/video-libraries/${id}`, { body })
}
// 获取视频库文档
export const fetchVideoDocuments = (videoId: string, params: {
  page: number
  size: number
  keyword: string
}) => {
  const urlParams = qs.stringify(params, { indices: false })
  return get<CommonResponse<VideoDocListResponse>>(
    `/video-libraries/${videoId}?${urlParams}`,
  )
}
// 获取视频库文档更新状态
export const fetchVideoDocumentsHandleStatus = (videoId: string, body: {
  ids: Array<string>
}) => {
  return post<CommonResponse<DocHandleStatusResponse>>(
    `/video-libraries/${videoId}/status`, body,
  )
}
// 删除视频库文件
export const deleteVideoDocument = (body: {
  videoId: string
  documentId: string
}) => {
  return del<CommonResponse>(`/video-libraries/${body.videoId}/files/${body.documentId}`)
}
// 编辑视频库文件
export const editVideoDocument = (videoId: string, docId: string, type: MediaType, body: {
  name: string
  frame_interval: number
}) => {
  const fixed = type === MediaType.VideoStream ? 'streams' : (type === MediaType.Video ? 'videos' : 'image')
  return put<CommonResponse>(`/video-libraries/${videoId}/${fixed}/${docId}/config`, { body })
}
// 启用视频库文档
export const enableVideoDocument = (videoId: string, docId: string, enabled: boolean) => {
  return post<CommonResponse>(`/video-libraries/${videoId}/files/${docId}/${enabled ? 'enable' : 'disable'}`)
}
// 重新处理视频库文档
export const reprocessVideoDocument = (videoId: string, docId: string) => {
  return post<CommonResponse>(`/video-libraries/${videoId}/files/${docId}/restart`)
}
// 获取视频库详情
export const fetchVideoDetail = (videoId: string) => {
  return get<CommonResponse<Video>>(
    `/video-libraries/${videoId}/info`,
  )
}
// 增加视频流
export const addVideoStream = (videoId: string, body: {
  urls: Array<{
    url: string
    name: string
    frame_interval: string
  }>
}) => {
  return post<CommonResponse>(`/video-libraries/${videoId}/streams`, { body })
}
// 增加视频库文件
export const addFiles = (videoId: string, body: {
  file_ids: Array<{
    id: string
    frame_interval?: number
  }>
  file_type: MediaType
}) => {
  return post<CommonResponse>(`/video-libraries/${videoId}`, { body })
}
// 检查视频流url
export const checkVideoStreamURL = (videoId: string, url: string) => {
  return post<CommonResponse>(`/video-libraries/${videoId}/streams/check-url`, {
    body: {
      url,
    },
  })
}
