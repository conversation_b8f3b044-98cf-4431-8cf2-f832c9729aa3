import type { ModelConfig } from './model'
import type { Tag } from '@/types/public/tag'
import type { statusEnum } from '@/app/components/app/app-publish-config/type'
import type { SiteConfig } from '@/types/share'

// 应用类型
export enum AppType {
  'chat' = 'chat',
  'completion' = 'completion',
}
// 应用模式
export const AppModes = ['advanced-chat', 'agent-chat', 'chat', 'completion', 'workflow', 'LongTextWorkflow'] as const
export type AppMode = typeof AppModes[number]

export type CompletionParams = {
  /** Maximum number of tokens in the answer message returned by Completion */
  max_tokens: number
  /**
   * A number between 0 and 2.
   * The larger the number, the more random the result;
   * otherwise, the more deterministic.
   * When in use, choose either `temperature` or `top_p`.
   * Default is 1.
   */
  temperature: number
  /**
   * Represents the proportion of probability mass samples to take,
   * e.g., 0.1 means taking the top 10% probability mass samples.
   * The determinism between the samples is basically consistent.
   * Among these results, the `top_p` probability mass results are taken.
   * When in use, choose either `temperature` or `top_p`.
   * Default is 1.
   */
  top_p: number
  /** When enabled, the Completion Text will concatenate the Prompt content together and return it. */
  echo: boolean
  /**
   * Specify up to 4 to automatically stop generating before the text specified in `stop`.
   * Suitable for use in chat mode.
   * For example, specify "Q" and "A",
   * and provide some Q&A examples as context,
   * and the model will give out in Q&A format and stop generating before Q&A.
   */
  stop: string[]
  /**
   * A number between -2.0 and 2.0.
   * The larger the value, the less the model will repeat topics and the more it will provide new topics.
   */
  presence_penalty: number
  /**
   * A number between -2.0 and 2.0.
   * A lower setting will make the model appear less cultured,
   * always repeating expressions.
   * The difference between `frequency_penalty` and `presence_penalty`
   * is that `frequency_penalty` penalizes a word based on its frequency in the training data,
   * while `presence_penalty` penalizes a word based on its occurrence in the input text.
   */
  frequency_penalty: number
}

export type AppIconType = 'image' | 'emoji'

// 应用
export type App = {
  /** App ID */
  id: string
  /** Name */
  name: string
  /** Description */
  description: string

  /**
   * Icon Type
   * @default 'emoji'
  */
  icon_type: AppIconType | null
  /** Icon, stores file ID if icon_type is 'image' */
  icon: string
  /** Icon Background, only available when icon_type is null or 'emoji' */
  // icon_background: string | null
  /** Icon URL, only available when icon_type is 'image' */
  icon_url: string | null
  icon_background: string | null
  /** Whether to use app icon as answer icon */
  use_icon_as_answer_icon: boolean

  is_publish: boolean
  /** Mode */
  mode: AppMode
  /** Enable web app */
  enable_site: boolean
  /** Enable web API */
  enable_api: boolean
  api_enable_site: boolean
  api_enable_api: boolean
  web_enable_site: boolean
  web_enable_api: boolean
  embed_enable_site: boolean
  embed_enable_api: boolean
  /** API requests per minute, default is 60 */
  api_rpm: number
  /** API requests per hour, default is 3600 */
  api_rph: number
  /** Whether it's a demo app */
  is_demo: boolean
  /** Model configuration */
  model_config: ModelConfig
  app_model_config: ModelConfig
  /** Timestamp of creation */
  created_at: number
  /** Web Application Configuration */
  site: SiteConfig
  web_site: SiteConfig
  api_site: SiteConfig
  embed_site: SiteConfig
  market_site: SiteConfig
  /** api site url */
  api_base_url: string
  tags: Tag[]
  is_longtxt_type: boolean
  deleted_tools: Array<any>
}

export type AppSSO = {
  enable_sso: boolean
}

// 应用事件总线枚举
export enum AppEventEmitterType {
  // 刷新
  Refresh = 'refresh',
}

export type AppPublishInfo = {
  status: statusEnum
  message: string
}
// 应用基础信息
export type AppBasicInfo = {
  id: string
  mode: AppMode
  icon_type: AppIconType | null
  icon: string
  icon_background: string
  icon_url: string
  name: string
  description: string
  use_icon_as_answer_icon: boolean
}

export type InstalledApp = {
  app: AppBasicInfo
  id: string
  uninstallable: boolean
  is_pinned: boolean
}
