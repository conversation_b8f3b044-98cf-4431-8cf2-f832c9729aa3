const translation = {
  common: {
    undo: '撤销',
    redo: '重做',
    // paramConfig: '参数配置',
    toolConfig: '工具配置',
    updateSubmit: '更新提交',
    // editing: '编辑中',
    autoSaved: '自动保存',
    unpublished: '未发布',
    published: '已发布',
    publish: '发布',
    update: '更新',
    run: '运行',
    running: '运行中',
    // inRunMode: '在运行模式中',
    // inPreview: '预览中',
    // inPreviewMode: '预览中',
    preview: '预览',
    viewRunHistory: '查看运行历史',
    runHistory: '运行历史',
    goBackToEdit: '返回编辑模式',
    // conversationLog: '对话记录',
    features: '功能',
    addVariable: '添加变量',
    // featuresDescription: '增强 web app 用户体验',
    ImageUploadLegacyTip: '现在可以在 start 表单中创建文件类型变量。未来我们将不继续支持图片上传功能。',
    fileUploadTip: '图片上传功能已扩展为文件上传。',
    featuresDocLink: '了解更多',
    debugAndPreview: '预览',
    restart: '重新开始',
    currentDraft: '当前草稿',
    currentDraftUnpublished: '当前草稿未发布',
    latestPublished: '最新发布',
    publishedAt: '发布于',
    restore: '恢复',
    runApp: '网页运行',
    batchRunApp: '批量运行',
    runDisabled: {
      notPublished: '当前应用未发布',
      notPublishedAfterCopied: '复制应用/创作同款应用需要发布后才能运行',
    },
    appUseType: '应用使用方式',
    // accessAPIReference: '访问 API',
    embedIntoSite: '嵌入页面',
    addTitle: '添加标题...',
    addDescription: '添加描述...',
    noVar: '没有变量',
    variableNamePlaceholder: '变量名',
    searchVar: '搜索变量',
    setVarValuePlaceholder: '设置变量值',
    needConnectTip: '此节点尚未连接到其他节点',
    maxTreeDepth: '每个分支最大限制 {{depth}} 个节点',
    needEndNode: '必须添加结束节点',
    needAnswerNode: '必须添加直接回复节点',
    workflowProcess: '工作流',
    notRunning: '尚未运行',
    previewPlaceholder: '在下面的框中输入内容开始调试聊天机器人',
    effectVarConfirm: {
      title: '移除变量',
      content: '该变量在其他节点中使用。您是否仍要删除它？',
    },
    insertVarTip: '按 \'/\' 键快速插入',
    processData: '数据处理',
    input: '输入',
    output: '输出',
    jinjaEditorPlaceholder: '输入 “/” 或 “{” 插入变量',
    viewOnly: '只读',
    showRunHistory: '显示运行历史',
    enableJinja: '开启支持 Jinja 模板',
    learnMore: '了解更多',
    copy: '拷贝',
    duplicate: '复制',
    addBlock: '添加节点',
    pasteHere: '粘贴到这里',
    pointerMode: '指针模式',
    handMode: '手模式',
    model: '模型',
    workflowAsTool: '工具配置',
    configureRequired: '需要进行配置',
    configure: '配置',
    manageInTools: '访问工具页',
    workflowAsToolTip: '工作流更新后需要重新配置工具参数',
    asToolInitTip: '工作流创建后需要先进行发布再配置工具参数',
    viewDetailInTracingPanel: '查看详细信息',
    syncingData: '同步数据中，只需几秒钟。',
    backupCurrentDraft: '备份当前草稿',
    importFailure: '导入失败',
    importSuccess: '导入成功',
    parallelRun: '并行运行',
    parallelTip: {
      click: {
        title: '点击 ',
        desc: '添加节点，',
      },
      drag: {
        title: '拖拽 ',
        desc: '连接节点',
      },
      limit: '并行分支限制为 {{num}} 个',
      depthLimit: '并行嵌套层数限制 {{num}} 层',
    },
    disconnect: '断开连接',
    jumpToNode: '跳转到节点',
    addParallelNode: '添加并行节点',
    parallel: '并行',
    branch: '分支',
  },
  env: {
    envPanelTitle: '环境变量',
    envDescription: '环境变量是一种存储敏感信息的方法，如 API 密钥、数据库密码等。它们被存储在工作流程中，而不是代码中，以便在不同环境中共享。',
    envPanelButton: '添加环境变量',
    modal: {
      title: '添加环境变量',
      editTitle: '编辑环境变量',
      type: '类型',
      name: '名称',
      namePlaceholder: '变量名',
      value: '值',
      valuePlaceholder: '变量值',
      secretTip: '存储密钥或其他敏感信息，防止明文存储导致信息泄露。',
      nameExist: '名称已存在',
      valueEmpty: '变量值不能为空',
    },
    type: {
      string: 'String',
      number: 'Number',
      secret: '密钥',
    },
  },
  chatVariable: {
    panelTitle: '会话变量',
    panelDescription: '会话变量用于存储 LLM 需要的上下文信息，如用户偏好、对话历史等。它是可读写的。',
    docLink: '查看文档了解更多。',
    button: '添加变量',
    modal: {
      title: '添加会话变量',
      editTitle: '编辑会话变量',
      name: '名称',
      namePlaceholder: '变量名',
      type: '类型',
      value: '默认值',
      valuePlaceholder: '默认值，可以为空',
      description: '描述',
      descriptionPlaceholder: '变量的描述',
      editInJSON: '在 JSON 中编辑',
      oneByOne: '逐个添加',
      editInForm: '在表单中编辑',
      arrayValue: '值',
      addArrayValue: '添加值',
      objectKey: '属性',
      objectType: '类型',
      objectValue: '默认值',
      nameExist: '名称已存在',
      objectKeyEmpty: '对象的属性不能为空',
    },
    storedContent: '存储内容',
    updatedAt: '更新时间 ',
    variableName: '变量名',
    variableType: '变量类型',
  },
  dsl: {
    title: 'DSL管理',
    import: '导入',
    export: '导出',
    checkbox: '是否允许导出该敏感信息',
    exportFailed: '导出DSL失败',
    importFailure: '导入失败',
    importSuccess: '导入成功',
    importWarning: '导入异常',
    importDSL: '导入 DSL',
    importDSLTip: '导入提示',
    chooseDSL: '选择 DSL(yml) 文件',
    overwriteAndImport: '覆盖并导入',
    importDSLTipContent: '导入DSL后，将覆盖当前画布及配置信息，是否继续导入?',
  },
  changeHistory: {
    title: '变更历史',
    placeholder: '尚未更改任何内容',
    clearHistory: '清除历史记录',
    hint: '提示',
    hintText: '您的编辑操作将被跟踪并存储在您的设备上，直到您离开编辑器。此历史记录将在您离开编辑器时被清除。',
    stepBackward_one: '{{count}} 步后退',
    stepBackward_other: '{{count}} 步后退',
    stepForward_one: '{{count}} 步前进',
    stepForward_other: '{{count}} 步前进',
    sessionStart: '会话开始',
    currentState: '当前状态',
    nodeTitleChange: '块标题已更改',
    nodeDescriptionChange: '块描述已更改',
    nodeDragStop: '块已移动',
    nodeChange: '块已更改',
    nodeConnect: '块已连接',
    nodePaste: '块已粘贴',
    nodeDelete: '块已删除',
    nodeAdd: '块已添加',
    nodeResize: '块已调整大小',
    noteAdd: '注释已添加',
    noteChange: '注释已更改',
    noteDelete: '注释已删除',
    edgeDelete: '块已断开连接',
  },
  errorMsg: {
    fieldRequired: '{{field}} 不能为空',
    rerankModelRequired: '开启 Rerank 模型前，请务必确认模型已在设置中成功配置。',
    authRequired: '请先授权',
    invalidJson: '{{field}} 是非法的 JSON',
    fields: {
      variable: '变量名',
      variableValue: '变量值',
      code: '代码',
      model: '模型',
      rerankModel: 'Rerank 模型',
      visionVariable: '视觉变量',
      sceneDesc: '使用场景说明',
      outputVar: '输入变量',
    },
    invalidVariable: '无效的变量',
    inputVarRequired: '请至少填入一个输入变量',
    lackDataset: '部分知识库已被删除',
    lackTool: '部分工具已被删除',
    toolNotExist: '工具不存在',
  },
  singleRun: {
    testRun: '测试运行 ',
    startRun: '开始运行',
    running: '运行中',
    testRunIteration: '测试运行迭代',
    back: '返回',
    iteration: '迭代',
  },
  tabs: {
    'searchBlock': '搜索节点',
    'blocks': '节点',
    'searchTool': '搜索工具',
    'tools': '工具',
    'allTool': '全部',
    'builtInTool': '内置',
    'customTool': '自定义',
    'workflowTool': '工作流',
    'default': '基础能力',
    'logic': '逻辑处理',
    'knowledge-and-memory': '知识与记忆',
    'information-processing': '信息处理',
    'development': '进阶开发',
    'noResult': '未找到匹配项',
  },
  blocks: {
    'start': '开始',
    'end': '结束',
    'answer': '回复',
    'llm': '大模型',
    'knowledge-retrieval': '知识库',
    'question-classifier': '问题分类',
    'if-else': '条件',
    'code': '代码',
    'template-transform': '模板转换',
    'http-request': 'HTTP',
    'variable-assigner': '变量聚合',
    'variable-aggregator': '变量聚合',
    'assigner': '变量赋值',
    'iteration-start': '迭代开始',
    'iteration': '迭代',
    'parameter-extractor': '参数提取',
    'document-extractor': '文档提取',
    'list-operator': '列表操作',
    'agent': '智能体',
    'video-library': '视频库',
  },
  blocksAbout: {
    'start': '定义一个 workflow 流程启动的初始参数',
    'end': '流程结束',
    'answer': '设定聊天对话中的回应内容',
    'llm': '调用大型语言模型来解答问题或处理自然语言',
    'knowledge-retrieval': '从选定的知识库中，根据输入变量提取最匹配的信息',
    'question-classifier': '定义用户问题的分类规则，大模型根据这些规则确定对话的流程',
    'if-else': '使用if/else将工作流分成两个分支',
    'code': '编写代码实现自定义逻辑',
    'template-transform': '采用 Jinja 模板将数据转换成字符串',
    'http-request': '允许向外部服务发送HTTP请求',
    'variable-assigner': '将不同分支的变量合并成一个变量，以便下游节点能够统一配置',
    'assigner': '对变量进行赋值',
    'variable-aggregator': '将不同分支的变量合并成一个变量，以便下游节点能够统一配置',
    'iteration': '持续地对列表对象应用步骤，直到所有结果展示完毕',
    'parameter-extractor': '使用大模型从文本中提取出结构化参数，用于后续的工具调用或HTTP请求',
    'document-extractor': '用于将用户上传的文档解析为 LLM 便于理解的文本内容',
    'list-operator': '用于过滤或排序数组内容',
    'agent': '选择一个已有的智能体应用/创建一个新的智能体应用在画布内使用',
    'video-library': '引入视频、视频流、图片适用于图片切割场景',
  },
  operator: {
    zoomIn: '放大',
    zoomOut: '缩小',
    zoomTo50: '缩放到 50%',
    zoomTo100: '放大到 100%',
    zoomToFit: '自适应',
  },
  panel: {
    userInputField: '用户输入字段',
    changeBlock: '更改节点',
    helpLink: '帮助链接',
    about: '关于',
    createdBy: '作者',
    nextStep: '下一步',
    addNextStep: '添加此工作流程中的下一个节点',
    selectNextStep: '选择下一个节点',
    runThisStep: '运行此步骤',
    checklist: '发布前检查清单',
    checklistTip: '发布前确保所有问题均已解决',
    checklistResolved: '所有问题均已解决',
    organizeBlocks: '整理节点',
    change: '更改',
    optional: '（选填）',
  },
  nodes: {
    common: {
      outputVars: '输出变量',
      insertVarTip: '插入变量',
      memory: {
        memory: '记忆',
        memoryTip: '聊天记忆设置',
        memoryType: '记忆方式',
        type: {
          auto: '动态平衡',
          custom: '自定义',
        },
        windowSize: '记忆窗口',
        conversationRoleName: '对话角色名',
        user: '用户前缀',
        assistant: '助手前缀',
      },
      tool: {
        tool: '工具',
      },
      memories: {
        title: '记忆',
        tip: '聊天记忆',
        builtIn: '内置',
      },
      errorRetry: {
        errorRetry: '错误重试',
        maxRetry: '最大重试次数',
        retryInterval: '最大重试间隔(ms)',
      },
      dataset: {
        knowledge: '知识库',
      },
    },
    start: {
      required: '必填',
      inputField: '输入变量',
      systemField: '系统输入变量',
      builtInVar: '内置变量',
      outputVars: {
        query: '用户输入',
        memories: {
          des: '会话历史',
          type: '消息类型',
          content: '消息内容',
        },
        files: '文件列表',
      },
      noVarTip: '设置的输入可在工作流程中使用',
    },
    end: {
      outputs: '输出',
      output: {
        type: '输出类型',
        variable: '输出变量',
      },
      type: {
        'none': '无',
        'plain-text': '纯文本',
        'structured': '结构化',
      },
    },
    answer: {
      answer: '回复',
      answerContent: '回复内容',
      outputVars: '输出变量',
      outputAnswer: '输出回复',
      placeholder: '请填写回复消息内容，输入/插入变量',
      output: {
        type: '输出类型',
        variable: '输出变量',
      },
    },
    llm: {
      model: '模型',
      expansion: '能力拓展',
      variables: '变量',
      context: '上下文',
      abilityDevelopment: '能力拓展',
      contextTooltip: '添加知识库后大模型将基于上传的文档提供更加精准的回答',
      notSetContextInPromptTip: '要启用上下文功能，请在提示中填写上下文变量。',
      prompt: '提示词',
      systemPrompt: '系统提示词',
      userPrompt: '用户提示词',
      addMessage: '添加消息',
      rolePlaceholder: {
        system: '为智能体提供系统级指导，如设定人设和回复逻辑与限制。',
        user: '为智能体提供指令、查询或任何基于文本的输入',
        assistant: '基于用户消息的模型回复',
      },
      roleDescription: {
        system: '为智能体提供系统级指导，如设定人设和回复逻辑与限制。',
        user: '为智能体提供指令、查询或任何基于文本的输入',
        assistant: '基于用户消息的模型回复',
      },
      vision: '视觉',
      files: '文件',
      resolution: {
        name: '分辨率',
        high: '高',
        low: '低',
      },
      outputVars: {
        output: '生成内容',
        usage: '模型用量信息',
        type: '输出类型',
        outputPlaceholderName: '变量名',
        outputPlaceholderValue: '{x}设置变量值',
      },
      singleRun: {
        variable: '变量',
      },
      sysQueryInUser: 'user message 中必须包含 sys.query',
    },
    agent: {
      model: '模型',
      context: '上下文',
      variables: '变量',
      abilityDevelopment: '能力拓展',
      contextTooltip: '添加知识库后大模型将基于上传的文档提供更加精准的回答',
      notSetContextInPromptTip: '要启用上下文功能，请在提示中填写上下文变量。',
      prompt: '提示词',
      systemPrompt: '系统提示词',
      userPrompt: '用户提示词',
      addMessage: '添加消息',
      rolePlaceholder: {
        system: '为智能体提供系统级指导，如设定人设和回复逻辑与限制。',
        user: '为智能体提供指令、查询或任何基于文本的输入',
        assistant: '基于用户消息的模型回复',
      },
      roleDescription: {
        system: '为智能体提供系统级指导，如设定人设和回复逻辑与限制。',
        user: '为智能体提供指令、查询或任何基于文本的输入',
        assistant: '基于用户消息的模型回复',
      },
      vision: '视觉',
      files: '文件',
      resolution: {
        name: '分辨率',
        high: '高',
        low: '低',
      },
      outputVars: {
        output: '生成内容',
        usage: '模型用量信息',
        type: '输出类型',
      },
      singleRun: {
        variable: '变量',
      },
      sysQueryInUser: 'user message 中必须包含 sys.query',
      createModeTitle: '创建模式',
      createAgent: '创建智能体',
      selectAgent: '选择智能体',
      advanceSetting: '高级设置',
      desc: '智能体描述',
      sceneDesc: '使用场景说明',
      advance: {
        iterationNum: '最大迭代次数',
      },
      query: '输入变量',
    },
    knowledgeRetrieval: {
      queryVariable: '输入变量',
      knowledge: '知识库',
      outputVars: {
        output: '召回的分段',
        content: '分段内容',
        title: '分段标题',
        icon: '分段图标',
        url: '分段链接',
        metadata: '其他元数据',
      },
    },
    http: {
      inputVars: '输入变量',
      api: 'API',
      apiPlaceholder: '输入 URL，输入变量时请键入‘/’',
      notStartWithHttp: 'API 应该以 http:// 或 https:// 开头',
      key: '键',
      type: '类型',
      value: '值',
      bulkEdit: '批量编辑',
      keyValueEdit: '键值编辑',
      headers: 'Headers',
      params: 'Params',
      body: 'Body',
      binaryFileVariable: 'Binary 文件变量',
      outputVars: {
        body: '响应内容',
        statusCode: '响应状态码',
        headers: '响应头列表 JSON',
        files: '文件列表',
      },
      authorization: {
        'authorization': '鉴权',
        'authorizationType': '鉴权类型',
        'no-auth': '无',
        'api-key': 'API-Key',
        'auth-type': 'API 鉴权类型',
        'basic': '基础',
        'bearer': 'Bearer',
        'custom': '自定义',
        'api-key-title': 'API Key',
        'header': 'Header',
      },
      insertVarPlaceholder: '键入 \'/\' 键快速插入变量',
      timeout: {
        title: '超时设置',
        connectLabel: '连接超时',
        connectPlaceholder: '输入连接超时（以秒为单位）',
        readLabel: '读取超时',
        readPlaceholder: '输入读取超时（以秒为单位）',
        writeLabel: '写入超时',
        writePlaceholder: '输入写入超时（以秒为单位）',
      },
      advancedSetting: '高级设置',
    },
    code: {
      inputVars: '输入变量',
      outputVars: '输出变量',
      advancedDependencies: '高级依赖',
      advancedDependenciesTip: '在这里添加一些预加载需要消耗较多时间或非默认内置的依赖包',
      searchDependencies: '搜索依赖',
      advancedSetting: '高级设置',
    },
    templateTransform: {
      inputVars: '输入变量',
      code: '代码',
      codeSupportTip: '只支持 Jinja2',
      outputVars: {
        output: '转换后内容',
      },
    },
    ifElse: {
      if: 'If',
      else: 'Else',
      elseDescription: '用于定义当 if 条件不满足时应执行的逻辑。',
      and: 'and',
      or: 'or',
      operator: '操作符',
      notSetVariable: '请先设置变量',
      comparisonOperator: {
        'contains': '包含',
        'not contains': '不包含',
        'start with': '开始是',
        'end with': '结束是',
        'is': '是',
        'is not': '不是',
        'empty': '为空',
        'not empty': '不为空',
        'null': '空',
        'not null': '不为空',
        'in': '是',
        'not in': '不是',
        'all of': '全部是',
        'exists': '存在',
        'not exists': '不存在',
      },
      optionName: {
        image: '图片',
        doc: '文档',
        audio: '音频',
        video: '视频',
        localUpload: '本地上传',
        url: 'URL',
      },
      enterValue: '输入值',
      addCondition: '添加条件',
      conditionNotSetup: '条件未设置',
      selectVariable: '选择变量',
      addSubVariable: '添加子变量',
      conditionalBranch: '条件分支',
      addBranch: '新建分支',
      select: '选择',
    },
    variableAssigner: {
      title: '变量赋值',
      outputType: '输出类型',
      varNotSet: '未设置变量',
      noVarTip: '添加需要赋值的变量',
      type: {
        string: 'String',
        number: 'Number',
        object: 'Object',
        array: 'Array',
      },
      aggregationGroup: '聚合分组',
      aggregationGroupTip: '开启该功能后，变量聚合器内可以同时聚合多组变量',
      addGroup: '添加分组',
      outputVars: {
        varDescribe: '{{groupName}}的输出变量',
      },
      setAssignVariable: '设置赋值变量',
    },
    assigner: {
      'assignedVariable': '赋值的变量',
      'writeMode': '写入模式',
      'writeModeTip': '使用追加模式时，赋值的变量必须是数组类型。',
      'over-write': '覆盖',
      'append': '追加',
      'plus': '加',
      'clear': '清空',
      'setVariable': '设置变量',
      'variable': '变量',
    },
    tool: {
      toAuthorize: '授权',
      inputVars: '输入变量',
      outputVars: {
        text: '工具生成的内容',
        files: {
          title: '工具生成的文件',
          type: '支持类型。现在只支持图片',
          transfer_method: '传输方式。值为 remote_url 或 local_file',
          url: '图片链接',
          upload_file_id: '上传文件ID',
        },
        json: '工具生成的json',
      },
    },
    questionClassifiers: {
      model: '模型',
      inputVars: '输入变量',
      outputVars: {
        className: '分类名称',
      },
      class: '分类',
      classNamePlaceholder: '输入你的分类名称',
      advancedSetting: '高级设置',
      topicName: '主题内容',
      topicPlaceholder: '在这里输入你的主题内容',
      addClass: '添加分类',
      instruction: '指令',
      instructionTip: '你可以输入额外的附加指令，帮助问题分类器更好的理解如何分类',
      instructionPlaceholder: '在这里输入你的指令',
    },
    parameterExtractor: {
      inputVar: '输入变量',
      extractParameters: '提取参数',
      importFromTool: '从工具导入',
      addExtractParameter: '添加提取参数',
      addExtractParameterContent: {
        name: '名称',
        namePlaceholder: '提取参数名称',
        type: '类型',
        typePlaceholder: '提取参数类型',
        description: '描述',
        descriptionPlaceholder: '提取参数描述',
        required: '必填',
        requiredContent: '必填仅作为模型推理的参考，不用于参数输出的强制验证。',
      },
      extractParametersNotSet: '提取参数未设置',
      instruction: '指令',
      instructionTip: '你可以输入额外的附加指令，帮助参数提取器理解如何提取参数',
      advancedSetting: '高级设置',
      reasoningMode: '推理模式',
      reasoningModeTip: '你可以根据模型对于 Function calling 或 Prompt 的指令响应能力选择合适的推理模式',
      isSuccess: '是否成功。成功时值为 1，失败时值为 0。',
      errorReason: '错误原因',
    },
    iteration: {
      deleteTitle: '删除迭代节点？',
      deleteDesc: '删除迭代节点将删除所有子节点',
      input: '输入',
      output: '输出变量',
      iteration_one: '{{count}}个迭代',
      iteration_other: '{{count}}个迭代',
      currentIteration: '当前迭代',
    },
    note: {
      addNote: '添加注释',
      editor: {
        placeholder: '输入注释...',
        small: '小',
        medium: '中',
        large: '大',
        bold: '加粗',
        italic: '斜体',
        strikethrough: '删除线',
        link: '链接',
        openLink: '打开',
        unlink: '取消链接',
        enterUrl: '输入链接...',
        invalidUrl: '无效的链接',
        bulletList: '列表',
        showAuthor: '显示作者',
      },
    },
    docExtractor: {
      inputVar: '输入变量',
      outputVars: {
        text: '提取的文本',
      },
      supportFileTypes: '支持的文件类型: {{types}}。',
      learnMore: '了解更多',
    },
    listFilter: {
      inputVar: '输入变量',
      filterCondition: '过滤条件',
      filterConditionKey: '过滤条件的 Key',
      filterConditionComparisonOperator: '过滤条件比较操作符',
      filterConditionComparisonValue: '过滤条件比较值',
      selectVariableKeyPlaceholder: '选择子变量的 Key',
      limit: '取前 N 项',
      orderBy: '排序',
      asc: '升序',
      desc: '降序',
      outputVars: {
        result: '过滤结果',
        first_record: '第一条记录',
        last_record: '最后一条记录',
      },
    },
    video: {
      name: '视频库',
      startTime: '开始时间',
      endTime: '结束时间',
      event: '事件',
      location: '地点',
      device: '设备',
      queryVariable: '输入变量',
      addVideoTip: '点击 “+” 按钮添加视频库',
      outputVars: {
        output: '输出变量',
        timestamp: '时间戳',
        url: '图片地址信息',
        detected_events: '分段链接',
        device_id: '设备ID',
        address_ip: '地址信息',
      },
    },
  },
  tracing: {
    stopBy: '由{{user}}终止',
  },
}

export default translation
