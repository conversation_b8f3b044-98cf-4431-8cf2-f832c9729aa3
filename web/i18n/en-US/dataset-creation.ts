const translation = {
  steps: {
    header: {
        creation: 'Create Knowledge Base',
        update: 'Upload File'
    }
  },
  // Step two
  stepTwo: {
    configSelect: 'Configuration Selection',
    segmentation: 'Segmentation Processing',
    auto: 'Automatic Segmentation and Cleaning',
    previewContent: 'Segmentation Preview Display Area',
    autoDescription: 'Segment the uploaded file according to the default method without having to care about the segmentation rules',
    custom: 'Customize',
    customDescription: 'Customize parameters such as segmentation rules, segmentation length, and preprocessing rules',
    separator: 'Segmentation Identifier',
    separatorPlaceholder: '\\n\\n is used for segmentation; \\n is used for line breaks',
    maxLength: 'Maximum Segmentation Length',
    overlap: 'Segmentation Overlap Length',
    overlapTip: 'By reasonably setting the overlap length, the semantic relationship between segments can be preserved, the information coherence can be improved, and the processing effect can be optimized.',
    overlapCheck: 'The segmentation overlap length cannot be greater than the maximum segmentation length',
    rules: 'Text Preprocessing Rules',
    removeExtraSpaces: 'Replace consecutive spaces, line breaks, and tab characters',
    removeUrlEmails: 'Delete all URLs and email addresses',
    removeStopwords: 'Remove stop words, such as "a", "an", "the", etc.',
    preview: 'Confirm and Preview',
    estimateSegment: 'Estimated Number of Segments',
    calculating: 'Calculating...',
    fileSource: 'Preprocessed Document',
    previewTitle: 'Segmentation Preview',
    indexSettingTip: 'To change the indexing method and embedding model, go to',
    datasetSettingLink: 'Knowledge Base Settings.',
    viewAll: 'View All',
    repackUp: 'Collapse All',
    maxLengthTip: 'The byte calculation rule is two bytes for one Chinese character and one byte for one English letter',
    form: {
      sheet: 'Data Sheet',
      canIndex: 'Whether or not to participate in the search',
      header: 'Header',
      col: 'Listing',
      beginLine: 'Start line'
    },
    table: 'Table structure',
    setting: 'Knowledge retrieval settings',
    dataSelect: 'Data selection',
    structuredDataCheck: 'Please have at least one column to participate in the search'
  },
  // Step one
  stepOne: {
    filePreview: 'File Preview',
    form: {
      name: 'Knowledge Base Name',
      namePlaceholder: 'Please enter the knowledge base name',
      nameDescription: 'The knowledge base name only supports Chinese, English, numbers, underscore (_), hyphen (-), and English dot (.) (1~50 characters)',
      desc: 'Knowledge Base Description',
      descPlaceholder: 'Please enter the knowledge base description',
      uploader: 'Data Upload',
      dataType: {
        title: 'data type',
        unstructured: 'Unstructured data',
        unstructuredDesc: 'The main content of the document is text and graphics, such as articles, reports, books, etc., and supports TXT, MARKDOWN, PDF, and DOCX formats',
        structuredDesc: 'The main content of the document is structured text, which needs to have clear field constraints, such as Q&A summary, policy terms, data collection, etc., and supports csv, xlsx, and xls formats',
        fileTypeError: 'This data type supports only {{supportType}} files',
        structured: 'Structured data',
        structuredTip: 'Support CSV, XLSX, XLS formats, each file is no more than 15mb, up to 50 files can be uploaded at the same time, no more than 100,000 rows, 20 columns, no more than 150,000 words per row, and up to one sheet in the project file (content that is out of scope will be automatically ignored)'
      }
    }
  },
  error: {
      unavailable: 'This knowledge base is unavailable'
  },
  stepThree: {
    creationTitle: '🎉 Knowledge Base Created',
    label: 'Knowledge Base Name',
    additionTitle: '🎉 Document Uploaded',
    additionP1: 'The document has been uploaded to the knowledge base:',
    additionP2: ', and you can find it in the document list of the knowledge base.',
    stop: 'Stop Processing',
    resume: 'Resume Processing',
    navTo: 'Go to Document',
    toApps: 'Develop Applications',
    sideTipTitle: 'What to Do Next',
    sideTipContent: 'When the document indexing process is completed, the knowledge base can be integrated into the application as context, and you can find the context settings on the prompt orchestration page. You can also create it into an independently usable ChatGPT indexing plugin and publish it.',
    modelTitle: 'Are you sure you want to stop the indexing process?',
    modelContent: 'If you need to resume processing later, it will continue from where it stopped.',
    modelButtonConfirm: 'Confirm Stop',
    modelButtonCancel: 'Cancel',
    parsing: 'Processing',
    completed: 'Processing Completed',
    viewAll: 'View All',
    repackUp: 'Repack',
    retry: 'Retry'
  }
}

export default translation
