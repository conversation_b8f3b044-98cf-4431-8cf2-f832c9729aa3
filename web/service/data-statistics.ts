import { get, post } from './base'
import { UsageResponse } from '@/app/components/data-statistics/type'

// 查询当前主账号及其子账号的知识库使用情况（知识库创建数量、文件上传数量、空间数量）
export const getDatasetUsage = () => {
  return get<UsageResponse>(`/stats/knowledgebase-usage`)
}

// 查询当前主账号及其子账号的应用使用情况（自主规划、工作流编排、长文档等应用创建数量）
export const getAppUsage = () => {
  return get<UsageResponse>(`/stats/app-usage`)
}

// 查询当前主账号及其子账号的工具使用情况（工作流工具、openschema 工具创建数量）
export const getToolUsage = () => {
  return get<UsageResponse>(`/stats/tool-usage`)
}

// 查询免费资源点的总消耗情况（已消耗点数、可用点数）
export const getFreeResourcePoints = () => {
  return get<any>(`/stats/free-resource-points`)
}

// 查询内置模型的价格清单（输入/输出千 token 消耗点数）
export const getModelPricing = () => {
  return get<any>(`/stats/model-pricing`)
}

// 查询内置模型的 token 消耗情况（输入/输出 token 数量及总数）
export const getModelTokenConsumption = () => {
  return get<any>(`/stats/model-token-consumption`)
}

// 按日期查询内置模型的 token 消耗详情
export const getModelTokenDaily = () => {
  return get<any>(`/stats/model-token-daily`)
}

// 查询内置模型的点数消耗情况（输入/输出点数及总点数）
export const getModelPointsConsumption = () => {
  return get<any>(`/stats/model-points-consumption`)
}

// 按日期查询内置模型的点数消耗详情
export const getModelPointsDaily = () => {
  return get<any>(`/stats/model-points-daily`)
}

// 查询已创建的应用数量及相关统计数据
export const getAppCount = () => {
  return get<any>(`/stats/app-count`)
}

// 查询已发布应用的 API 调用次数
export const getAppApiCalls = () => {
  return get<any>(`/stats/app-api-calls`)
}