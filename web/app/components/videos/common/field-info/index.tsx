import { useTranslation } from 'react-i18next'
import type { FC } from 'react'
import { Input } from 'antd'
import s from './styles/index.module.css'
import type { inputType } from '@/hooks/use-metadata'

import cn from '@/utils/classnames'
import { getTextWidthWithCanvas } from '@/utils'
import Select from '@/app/components/base/select/new-index'
import AutoHeightTextarea from '@/app/components/base/auto-height-textarea'

type IFieldInfoProps = {
  label: string
  labelClassName?: string
  value?: string
  displayedValue?: string
  defaultValue?: string
  showEdit?: boolean
  inputType?: inputType
  selectOptions?: Array<{ value: string; label: string }>
  onUpdate?: (v: any) => void
}
export const FieldInfo: FC<IFieldInfoProps> = ({
  label,
  labelClassName,
  value = '',
  displayedValue = '',
  defaultValue,
  showEdit = false,
  inputType = 'input',
  selectOptions = [],
  onUpdate,
}) => {
  const { t } = useTranslation()
  const textNeedWrap = getTextWidthWithCanvas(displayedValue) > 190
  const editAlignTop = showEdit && inputType === 'textarea'
  const readAlignTop = !showEdit && textNeedWrap

  return (
    <div className={cn(s.fieldInfo, editAlignTop && '!items-start', readAlignTop && '!items-start pt-1')}>
      <div className={cn(s.label, labelClassName, editAlignTop && 'pt-1')}>{label}：</div>
      <div className={s.value}>
        {!showEdit
          ? displayedValue
          : inputType === 'select'
            ? <Select
              className='w-full'
              onChange={value => onUpdate && onUpdate(value as string)}
              options={selectOptions}
              defaultValue={value}
              placeholder={`${t('common.placeholder.select')}${label}`}
            />
            : inputType === 'textarea'
              ? <AutoHeightTextarea
                onChange={e => onUpdate && onUpdate(e.target.value)}
                value={value}
                className={s.textArea}
                placeholder={`${t('common.placeholder.input', { label })}`}
              />
              : <Input
                onChange={e => onUpdate?.(e.target.value)}
                value={value}
                defaultValue={defaultValue}
                placeholder={`${t('common.placeholder.input', { label })}`}
              />
        }
      </div>
    </div>
  )
}
export default FieldInfo
