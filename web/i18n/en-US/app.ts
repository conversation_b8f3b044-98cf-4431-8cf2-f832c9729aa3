const translation = {
  title: 'Applications',
  newApp: {
    basic: 'Autonomous Planning',
    advanced: 'Workflow Orchestration',
    longDoc: 'Template template',
    Sampletemplate: 'Template templates',
  },
  // Application types
  types: {
    all: 'All',
    chatbot: 'Workflow',
    agent: 'Autonomous Planning',
    workflow: 'Workflow',
    completion: 'Text Generation',
    emptyApp: 'Application',
  },
  // Actions
  action: {
    publish: 'Publish',
    updatePublish: 'Update and Publish',
    createApp: 'Create Application',
    export: 'Export DSL',
    startByType: 'Create {{type}}',
    experience: 'Experience Now',
    reUpload: 'Re-upload',
    configure: 'Configure',
    previewAndDebugger: 'Preview & Debug',
    publishMarket: 'Publish to App Square',
    unPublish: 'End-of-line applications',
    appDetail: 'Application details',
    experienceApp: 'Experience the app',
    detail: 'detail',
    reject: 'Rejection of the application',
    publishApp: 'Go live with your app',
    downQRcode: 'Download the QR code',
    createTeam: 'Create a team',
    personalSpace: 'Personal Space',
    team: 'Team',
    person: 'Individual',
  },
  // Tracing
  tracing: {
    title: 'Track Application Performance',
    description: 'Configure a third-party LLMOps provider and track the application performance.',
    config: 'Configure',
    view: 'View',
    collapse: 'Collapse',
    expand: 'Expand',
    tracing: 'Tracing',
    disabled: 'Disabled',
    disabledTip: 'Please configure the provider first',
    enabled: 'Enabled',
    tracingDescription: 'Capture the full context of the application execution, including LLM calls, context, prompts, HTTP requests, etc., and send it to a third-party tracking platform.',
    configProviderTitle: {
      configured: 'Configured',
      notConfigured: 'Configure the provider to enable tracing',
      moreProvider: 'More Providers',
    },
    langsmith: {
      title: 'LangSmith',
      description: 'A comprehensive developer platform for every step of the LLM-driven application lifecycle.',
    },
    langfuse: {
      title: 'Langfuse',
      description: 'Track, evaluate, manage prompts, and view metrics to debug and improve your LLM applications.',
    },
    inUse: 'In Use',
    configProvider: {
      title: 'Configure ',
      placeholder: 'Enter your {{key}}',
      project: 'Project',
      publicKey: 'Public Key',
      secretKey: 'Secret Key',
      viewDocsLink: 'View the documentation of {{key}}',
      removeConfirmTitle: 'Delete the {{key}} configuration?',
      removeConfirmContent: 'The current configuration is in use. Deleting it will turn off the tracing function.',
    },
  },
  // Status
  status: {
    published: 'Published',
    unPublish: 'Unpublished',
    appUnknownError: 'The app isn\'t available',
  },
  // Input placeholders
  placeholder: {
    defaultSelect: 'Please select',
    defaultInput: 'Please enter',
    appName: 'Please enter the application name',
    appDescription: 'Please enter a description of the application. This description has no impact on the agent effect.',
    chatbotDescription: 'Orchestrate agent applications using workflows, providing more customization capabilities. It is suitable for handling complex scenarios and experienced users.',
    agentDescription: 'Orchestrate agent applications through interface configuration, quickly and conveniently building applications. It is suitable for scenarios with simple logic.',
    templateDescription: 'Based on long text creation, you can quickly create an application and complete long text generation.',
    appCategoriesPlaceholder: 'Please select the Scene tab',
    failMessagePlaceholder: 'Please enter a reason',
  },
  // Application permissions
  appPermission: {
    copy: 'Public Configuration',
    view: 'Private Configuration',
  },
  // Modal titles
  modalTitle: {
    publish: 'Publish',
    publishMarket: 'Publish to the App Marketplace',
    editAppTitle: 'Application Information Editing',
    duplicateTitle: 'Duplicate Application',
    deleteAppConfirmTitle: 'Do you want to delete the application?',
    publishDetail: 'Release details',
  },
  // Information
  info: {
    appName: 'Application Name',
    appHead: 'Application Avatar',
    appPermission: 'Application Permission',
    appCategories: 'Application Tags',
    appDescription: 'Application Description',
    appCreateType: 'Application Creation Method',
    appMarket: 'App Marketplace',
    publicConfig: 'Public',
    acl: 'Whether the app can support the creation of the same paragraph',
    publishAt: 'Released',
    description: 'Application Description:',
    category: 'type',
    createAt: 'Creation time',
    updateAt: 'Updated',
    name: 'The name of the app',
    action: 'operate',
    status: 'state',
  },
  // Notifications, announcements
  notify: {
    appCreated: 'The application has been created',
    appCreateFailed: 'Application creation failed',
    successPublishApp: 'The application has been successfully published',
    errorPublishApp: 'Application publishing failed',
    unPublishSuccess: 'Canceling the application publication was successful',
    unPublishError: 'Canceling the application publication failed',
    editDone: 'The application information has been updated',
    editFailed: 'Updating the application information failed',
    appDeleted: 'The application has been deleted',
    appDeleteFailed: 'Application deletion failed',
    exportFailed: 'Exporting DSL failed',
    // Announcements
    noApp: 'No available applications',
    nameNotEmpty: 'The name cannot be empty',
    deleteAppConfirmContent: 'After deletion, if you need to use it again, please recreate it',
    appFull: 'You can create at most {{num}} applications. Please delete some and try again.',
    multipleModelPublishTip: 'Currently in the multi-model comparison state, publishing is not allowed. You can publish after selecting the optimal model.',
    shareTip: 'I\'ve built an agent in {{name}}, welcome to scan the QR code to experience it!',
  },
  // Sorting
  sort: {
    hot: 'Most Popular',
    new: 'Newly Published',
  },
  // Cropper
  cropper: {
    title: 'Conversation Background Image',
    upload: 'Upload Image',
    uploadTip: 'Click to upload an image or drag the image here',
    typeTip: 'Supports uploading an image with a height of at least 640px. The image types are png, jpg, jpeg',
    reUploadTip: 'Supports zooming in and out of the image with the mouse scroll or dragging to adjust the position',
    widePreview: 'Wide-screen Preview',
    narrowPreview: 'Narrow-screen Preview',
    fontColor: 'Font Color',
    fontColorBlack: 'Dark',
    fontColorWhite: 'Light',
    typeErrorMessage: 'The file type should be png, jpg, or jpeg',
    heightErrorMessage: 'Please upload a background image with a height of at least 640px',
    sizeErrorMessage: 'The memory size of the image should be less than 10mb',
  },
  mermaid: {
    handDrawn: 'Hand-drawn',
    classic: 'Classic',
  },
  appMenus: {
    overview: 'monitoring',
    logAndAnn: 'analyse',
    workflowConfig: 'Workflow configuration',
    logs: 'log',
    publish: 'publish',
    promptEng: 'Choreography',
    apiAccess: 'Access the API',
  },
  appMarket: 'Application Square',
  publish: {
    methodOption: {
      web: 'Front-end pages',
      embed: 'Embedding',
      api: 'Backend APIs',
      market: 'Square',
    },
    copy: {
      copyDataset: 'Private knowledge base synchronization',
      tool: 'Tool synchronization',
      dataset: 'Knowledge base synchronization',
      noTool: 'The tool is out of sync',
      noDataset: 'The knowledge base is out of sync',
      copyTool: 'Customize tool synchronization',
      forkTag: 'Imported when creating the same paragraph',
    },
    embed: {
      scripts: 'Embedded on the right',
      iframe: 'Centering embedded',
      embeddingMethod: 'Method selection',
    },
    status: {
      online: 'Available',
      failed: 'Failed',
      offline: 'Removed from shelves',
      review: 'Under review',
      on: 'Yes',
      off: 'not',
      pending: 'To be published',
    },
    tip: {
      marketTip: 'Publishing to App Square is not configured',
      publishMethodTip: 'At least one publishing channel needs to be selected',
      embeddingTip: 'The embedding form is not selected',
      unPublishTip: 'After you select to remove an app, the apps that have been created in the app section will not be affected. Only apps that have been uploaded to App Square will be removed?',
      marketConfigTip: 'When you allow users to create the same model and support users to copy the custom tools and private knowledge bases you have built, the files you upload (including slices) and the tools you build will be used by users.',
    },
    reConfirm: {
      title: 'Update release',
      content: 'The latest version of the channel is subject to the first release.\nThe App Square update release overwrites the original version and can\'t be restored, but apps imported into the App tile will still display the original version.\nDo you want to update the release?',
    },
    history: 'Release history',
    messagePrefix: 'Cause:',
    config: 'Publish the configuration',
    method: 'Publishing channels',
  },
}

export default translation
