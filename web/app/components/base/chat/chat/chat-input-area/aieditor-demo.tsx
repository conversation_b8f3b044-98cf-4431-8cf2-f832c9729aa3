import { useEffect, useRef } from 'react'
import { AiEditor } from 'aieditor'
import 'aieditor/dist/style.css'
import { t } from 'i18next'

function RichTextEditor() {
  //定义 ref
  const divRef = useRef(null)

  //初始化 AiEditor
  useEffect(() => {
    if (divRef.current) {
      const aiEditor = new AiEditor({
        element: divRef.current,
        placeholder:t('sampleTemplate.longText.placeholder'),
        content: `AiEditor ${t('sampleTemplate.longText.editemsg')} “{content}”`,
        textSelectionBubbleMenu: {
          enable: false
        }
      })

      // 获取属性示例
      const attributeName = 'yourAttributeName' // 替换为你想获取的属性名
      const attributeValue = aiEditor.getAttributes(attributeName)

      return () => {
        aiEditor.destroy()
      }
    }
  }, [])

  return (
    <>
      <div ref={divRef}>
        <div className="aie-container">
          <div className="aie-container-header" style={{ display: 'none' }}></div>
          <div className="aie-container-main"></div>
          <div className="aie-container-footer" style={{ display: 'none' }}></div>
        </div>
      </div>
    </>
  )
}

export default RichTextEditor
