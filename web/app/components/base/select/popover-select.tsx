'use client'

import type { DropdownProps } from 'antd'
import { ConfigProvider, Dropdown, Spin, Tooltip } from 'antd'
import React, { useCallback, useMemo, useState } from 'react'
import type { ItemType, MenuItemGroupType, MenuItemType } from 'antd/es/menu/interface'
import { useTranslation } from 'react-i18next'
import { usePathname } from 'next/navigation'
import { Check } from '../icons/src/vender/line/general'
import SearchInput from '../input/search-input'
import Scrollbar from '../scrollbar'
import { ArrowDown } from '../icons/src/vender/line/arrows'

// 公共组件
import cn from '@/utils/classnames'
type PopoverSelectProps = {
  loading?: boolean
  options: Array<ItemType<MenuItemType> | MenuItemGroupType>
  readonly?: boolean
  defaultValue?: string
  onChange: (value: any) => void
  search?: boolean
  placeholder?: string
  headerNode?: React.ReactNode
  triggerNode?: (open: boolean, value: any) => React.ReactNode
  footerNode?: React.ReactNode
  showCheck?: boolean
  isButton?: boolean
  buttonText?: string
  onAddButtonClick?: () => void
} & DropdownProps

// 默认触发器
const defaultTrigger = (open: boolean, value: string, placeholder?: string, className?: string) => {
  return (
    <div className={cn(
      'flex items-center px-[11px] h-[36px] rounded cursor-pointer border border-gray-G5 hover:border-primary-P1 bg-white',
      open && 'border-primary-P1',
      className,
    )}>
      <input className='cursor-pointer grow w-[calc(100%-20px)] text-gray-G1' value={value} placeholder={placeholder}></input>
      <ArrowDown className={cn('w-4 h-4 text-gray-G2', open && 'rotate-180')} />
    </div>
  )
}

const PopoverSelect = ({
  loading = false,
  options,
  readonly,
  defaultValue,
  onChange,
  search,
  placeholder,
  headerNode,
  triggerNode,
  footerNode,
  showCheck = true,
  onOpenChange,
  placement,
  className,
  children,
  overlayClassName,
}: PopoverSelectProps) => {
  const { t } = useTranslation()
  const pathname = usePathname()

  // 下拉列表选项
  const [value, setValue] = useState(defaultValue || '')
  // 关键词
  const [keyword, setKeyword] = useState('')
  // 是否打开
  const [open, setOpen] = useState(false)
  // 适配下拉列表
  const wrapSelectList = useCallback((list: Array<ItemType<MenuItemType> | MenuItemGroupType>) => {
    const selectList: Array<ItemType<MenuItemType> | MenuItemGroupType> = []
    list.forEach((item) => {
      if (item?.type === 'group' && item.children) {
        selectList.push({
          ...item,
          children: wrapSelectList(item.children) as Array<ItemType<MenuItemType>>,
        })
      }
      else {
        selectList.push({
          ...item,
          label: (
            <div className='flex justify-between items-center w-full'>
              {(item as MenuItemType).label}
              { (item?.key?.toString().endsWith(value) && value && showCheck) ? <Check className='shrink-0 w-4 h-4 text-primary-600 ml-4' /> : '' }
            </div>
          ),
        } as ItemType<MenuItemType>)
      }
    })
    return selectList
  }, [showCheck, value])
  const getFilterOption = useCallback((options: any, keyword?: string) => {
    const result = options.map((option: any) => {
      if (option?.type === 'group' && option.children && option.children.length > 0) {
        const filterChildren = getFilterOption(option.children, keyword)
        if (filterChildren.length > 0) {
          return {
            ...option,
            children: filterChildren,
          }
        }
        else {
          return null
        }
      }
      else {
        return option.title?.includes(keyword) ? option : null
      }
    }).filter((item: any) => item !== null)
    return result
  }, [])
  // 下拉列表数据
  const items = useMemo(() => {
    if (!search && !showCheck)
      return options
    // 根据关键词过滤后的列表
    try {
      const filterOption = getFilterOption(options, keyword)
      return wrapSelectList(filterOption)
    }
    catch (error) {
      return options
    }
  }, [getFilterOption, keyword, options, search, showCheck, wrapSelectList])
  // 选中项名称
  const selectName = useMemo(() => {
    return (options.find(item => item?.key === value) as MenuItemType)?.title || ''
  }, [options, value])

  // 变更下拉选项事件
  const changeSelect = (key: any) => {
    if (key !== defaultValue) {
      onChange(key)
      setValue(key)
    }
  }
  // 打开下拉选项
  const changeOpen = (open: boolean, info: {
    source: 'trigger' | 'menu'
  }) => {
    setOpen(open)
    onOpenChange && onOpenChange(open, info)
  }

  const triggerContent = triggerNode ? triggerNode(open, value) : (children || defaultTrigger(open, selectName, placeholder, className))

  return (
    <Dropdown
      open={!readonly && open}
      dropdownRender={(menus: React.ReactNode) => (
        <div
          style={{
            boxShadow: 'rgba(0, 0, 0, 0.08) 0px 6px 16px 0px, rgba(0, 0, 0, 0.12) 0px 3px 6px -4px, rgba(0, 0, 0, 0.05) 0px 9px 28px 8px',
            background: 'white',
            borderRadius: '4px',
            padding: search ? '12px 0 12px 0' : '4px 0',
            zIndex: 9999,
          }}
          onClick={() => {}}
        >
          {/* 顶部 */}
          { headerNode }
          {/* 搜索部分 */}
          { search
            && <div className='mx-4 mb-3'>
              <SearchInput
                value={keyword}
                onChange={setKeyword}
              ></SearchInput>
            </div>}
          <ConfigProvider
            theme={{
              components: {
                Menu: {
                  boxShadow: '0 0 #0000',
                },
              },
            }}
          >
            <Spin spinning={loading}>
              <Scrollbar className='max-h-[33vh]' options={{
                suppressScrollX: true,
              }}>
                {menus}
              </Scrollbar>
            </Spin>
          </ConfigProvider>
          {/* 底部 */}
          { footerNode }
        </div>
      )}
      menu={{
        items,
        className: '!p-0 !shadow-none',
        style: {
          overflow: 'unset',
        },
        selectedKeys: [value],
        onClick: ({ key }) => { changeSelect(key) },
      }}
      trigger={
        ['click']
      }
      placement={placement}
      onOpenChange={changeOpen}
      overlayClassName={overlayClassName}
    >
      {readonly
        ? (
          <Tooltip title={pathname.includes('/datasets/create') ? t('dataset.info.createDatasetUnselectableTip') : t('dataset.info.unselectable')}>
            {triggerContent}
          </Tooltip>
        )
        : triggerContent}
    </Dropdown>
  )
}

export default PopoverSelect
