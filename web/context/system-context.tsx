'use client'

import { useCallback, useEffect, useMemo, useState } from 'react'
import useSWR from 'swr'
import { createContext, useContext, useContextSelector } from 'use-context-selector'
import type { FC, ReactNode } from 'react'
import { getSystemFeatures } from '@/service/common'
import { Theme } from '@/types/system'
import type {
  LangGeniusVersionResponse,
} from '@/models/common'
import type { SystemFeatures } from '@/types/system'
// 公共能力
import Loading from '@/app/components/base/loading'
import { DEFAULT_SYSTEM_FEATURE } from '@/config/system'
import useBreakpoints, { MediaType } from '@/hooks/use-breakpoints'

export type SystemContextValue = {
  systemFeatures: SystemFeatures
  isPrivate: boolean
  canI18n: boolean
  langeniusVersionInfo: LangGeniusVersionResponse
  useSelector: typeof useSelector
  isSso?: boolean
  isMobile: boolean
}

const initialLangeniusVersionInfo = {
  current_env: '',
  current_version: '',
  latest_version: '',
  release_date: '',
  release_notes: '',
  version: '',
  can_auto_update: false,
}

const SystemContext = createContext<SystemContextValue>({
  systemFeatures: DEFAULT_SYSTEM_FEATURE,
  canI18n: false,
  isPrivate: false,
  langeniusVersionInfo: initialLangeniusVersionInfo,
  useSelector,
  isSso: false,
  isMobile: false,
})

export function useSelector<T>(selector: (value: SystemContextValue) => T): T {
  return useContextSelector(SystemContext, selector)
}

export type SystemContextProviderProps = {
  children: ReactNode
}

export const SystemContextProvider: FC<SystemContextProviderProps> = ({ children }) => {
  const media = useBreakpoints()
  const isMobile = media === MediaType.mobile
  // 系统特征
  const { data: systemFeatures, isLoading } = useSWR({ url: '/console/system-features' }, getSystemFeatures, {
    fallbackData: DEFAULT_SYSTEM_FEATURE,
    revalidateOnFocus: false,
  })
  // 风格
  const [theme, setTheme] = useState<Theme>(Theme.light)
  // langenius版本信息
  const [langeniusVersionInfo, setLangeniusVersionInfo] = useState<LangGeniusVersionResponse>(
    initialLangeniusVersionInfo,
  )
  // 是否为私有化
  const isPrivate = useMemo(() => systemFeatures.enable_private_register, [systemFeatures])
  // 是否支持国际化
  const canI18n = useMemo(() => systemFeatures.i18n_enabled, [systemFeatures])
  // 是否为sso登录方式
  const isSso = useMemo(() => systemFeatures.enable_sso_login, [systemFeatures])
  
  // 变更风格
  const handleSetTheme = useCallback((theme: Theme) => {
    setTheme(theme)
    globalThis.document.documentElement.setAttribute('data-theme', theme)
  }, [])

  useEffect(() => {
    globalThis.document.documentElement.setAttribute('data-theme', theme)
  }, [theme])

  if (!systemFeatures || isLoading)
    return <Loading type='app' />

  return (
    <SystemContext.Provider value={{
      systemFeatures,
      langeniusVersionInfo,
      useSelector,
      isPrivate,
      canI18n,
      isSso,
      isMobile,
    }}>
      {children}
    </SystemContext.Provider>
  )
}

export const useSystemContext = () => useContext(SystemContext)

export default SystemContext
