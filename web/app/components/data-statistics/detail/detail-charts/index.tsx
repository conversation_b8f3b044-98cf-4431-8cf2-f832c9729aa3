import React, { useEffect, useState, useRef } from 'react';
import s from '@/app/components/data-statistics/detail/styles/style.module.scss'
import * as echarts from 'echarts';
import { Button, Tabs, Spin, Statistic, Radio } from 'antd';
import type { TabsProps } from 'antd';
import { Bar, Line } from '@/app/components/base/icons/src/vender/statisticsData'

// 类型定义
interface ISeriesItem {
  label: string;
  value: string;
  data: number[];
  color?: string;
  type?: 'line' | 'bar';

}

interface IChartData {
  xAxis: string[];
  series: ISeriesItem[];
  statistics: {
    total: number;
    input: number;
    output: number;
  };
}

interface IProps {
  showTabs?: boolean;
  defaultActiveKey?: string;
  fetchData: (params: any, tabKey: string) => Promise<IChartData>;
  params?: any;
  activeSeriesKeys?: string[];  // 当前选中的series
  allSeries?: ISeriesItem[];    // 所有可选的series配置
  defaultChartType?: 'line' | 'bar';
}

// 默认颜色方案
const DEFAULT_COLORS = [
  '#3168F5', '#FF9C28', '#15B881', 
  '#9C63DB', '#8b5cf6', '#ec4899'
];

// echarts折线图或柱状图
const DetailCharts: React.FC<IProps> = ({
  showTabs = true,
  defaultActiveKey = 'total',
  fetchData,
  params = {},
  activeSeriesKeys,
  allSeries, 
  defaultChartType = 'line'
}) => {
  const [activeKey, setActiveKey] = useState(defaultActiveKey);
  const [chartType, setChartType] = useState(defaultChartType);
  const [loading, setLoading] = useState(false);
  const [chartData, setChartData] = useState<IChartData>({
    xAxis: [],
    series: [],
    statistics: { total: 0, input: 0, output: 0 }
  });

  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  // Tab配置
  const tabItems: TabsProps['items'] = [
    {
      key: 'total',
      label: <TabLabel text="总计" value={chartData.statistics.total} />
    },
    {
      key: 'input',
      label: <TabLabel text="输入" value={chartData.statistics.input} />
    },
    {
      key: 'output',
      label: <TabLabel text="输出" value={chartData.statistics.output} />
    }
  ];

  // 初始化图表
  const initChart = () => {
    if (chartRef.current && !chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current);
      window.addEventListener('resize', resizeChart);
    }
  };

  // 渲染图表
  const renderChart = () => {
    if (!chartInstance.current || chartData.series.length === 0) return;

    const option = {
      tooltip: {
        trigger: 'axis',
        formatter: (params: any[]) => {
          let result = params[0].axisValue + '<br/>';
          params.forEach((item: any) => {
            result += `${item.marker} ${item.seriesName}: ${item.value}<br/>`;
          });
          return result;
        }
      },
      legend: {
        data: chartData.series.map(s => s.label),
        type: 'scroll' // 支持滚动
      },
      grid: {
        left: '3%',
        right: '4%',
        containLabel: true,
        bottom: '3%'
      },
      xAxis: {
        type: 'category',
        data: chartData.xAxis,
        axisLabel: {
          interval: 0,
          rotate: chartData.xAxis.length > 7 ? 45 : 0
        }
      },
      yAxis: { type: 'value', name: params.yAxisName || '数值' },
      series: chartData.series.map((item, index) => ({
        name: item.label,
        data: item.data,
        type: item.type || chartType,
        barWidth: '12',
        smooth: false,
        itemStyle: { color: item.color || DEFAULT_COLORS[index % DEFAULT_COLORS.length] },
      }))
    };

    chartInstance.current.setOption(option, true);
  };

  // 获取数据
  const getData = async () => {
    try {
      setLoading(true);
      const data = await fetchData(params, activeKey);

      const filteredSeries = allSeries
        ? allSeries
            .filter(item => !activeSeriesKeys || activeSeriesKeys.includes(item.value))
            .map(item => ({
              ...item,
              data: data.series.find(s => s.label === item.label)?.data || Array(data.xAxis.length).fill(0)
            }))
        : data.series;

      setChartData({
        ...data,
        series: filteredSeries
      });
    } finally {
      setLoading(false);
    }
  };

    // 切换图表类型
    const toggleChartType = () => {
      setChartType(prev => (prev === 'line' ? 'bar' : 'line'));
    };

  // 窗口resize处理
  const resizeChart = () => chartInstance.current?.resize();

  useEffect(() => {
    initChart();
    return () => {
      window.removeEventListener('resize', resizeChart);
      chartInstance.current?.dispose();
    };
  }, []);

  useEffect(() => { getData(); }, [activeKey, params, activeSeriesKeys, allSeries]);
  useEffect(() => { renderChart(); }, [chartData, chartType]);

  return (
    <div className={s['detail-charts']}>
      <div className={s['chart-header']}>
        {showTabs && <Tabs activeKey={activeKey} items={tabItems} onChange={setActiveKey} style={{ flex: 1 }} />}
      </div>
      <div className={s['chart-content']}>
        <Radio.Group value={chartType} className={s['chart-btn']} onChange={toggleChartType}>
            <Radio.Button value="line"><Line  /></Radio.Button>
            <Radio.Button value="bar"><Bar /></Radio.Button>
          </Radio.Group>
          <div 
            ref={chartRef} 
            className={s['detail-chart']}
        />
      </div>
      {loading && (
        <div style={loadingStyle}>
          <Spin tip="加载中..." />
        </div>
      )}
    </div>
  );
};

// 切换tab组件
const TabLabel: React.FC<{ text: string; value: number }> = ({ text, value }) => (
  <div>
    {text}
    <Statistic 
      value={value}
    />
  </div>
);

// 样式
const loadingStyle: React.CSSProperties = {
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  background: 'rgba(255,255,255,0.7)',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  zIndex: 10
};

export default DetailCharts;
