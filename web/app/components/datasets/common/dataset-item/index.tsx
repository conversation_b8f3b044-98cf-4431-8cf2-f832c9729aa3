import { memo } from 'react'
import { useTranslation } from 'react-i18next'
import { AlertTriangle } from '@/app/components/base/icons/src/vender/line/alertsAndFeedback'
import { Folder } from '@/app/components/base/icons/src/vender/solid/files'
import Tooltip from '@/app/components/base/tooltip'
import cn from '@/utils/classnames'
import { GRAY } from '@/themes/var-define'

type DatasetItemProps = {
  name: string
  id: string
  lack?: boolean
  operations?: React.ReactElement
  border?: boolean
  className?: string
  textClassName?: string
}

const DatasetItem = ({
  name,
  id,
  operations,
  lack = false,
  border = false,
  className,
  textClassName,
}: DatasetItemProps) => {
  const { t } = useTranslation()
  /* 知识库默认图标 */
  const fileIcon = () => {
    return (
      <div className='shrink-0 flex items-center justify-center w-6 h-6 bg-[#F5F8FF] rounded-md border-[0.5px] border-[#E0EAFF]'>
        <Folder className='w-4 h-4 text-[#444CE7]' />
      </div>
    )
  }
  /* 被删除后图标 */
  const deletedIcon = () => {
    return (
      <Tooltip popupContent={t('common.status.deleted')}>
        <div className='shrink-0 flex items-center justify-center w-6 h-6 bg-[#F5F8FF] rounded-md border-[0.5px] border-[#E0EAFF]'>
          <AlertTriangle className='w-4 h-4 text-[#F79009]' />
        </div>
      </Tooltip>
    )
  }

  return (
    <div
      className={cn(
        'group relative flex justify-between items-center last-of-type:mb-0 h-[40px] py-2 w-full rounded',
        className,
        border ? 'border border-gray-G5 px-3' : '',
      )}
    >
      <div className='flex grow w-0 items-center'>
        {/* 图标 */}
        { !lack ? fileIcon() : deletedIcon()}
        {/* 名称 */}
        <div title={name || id} className={cn('text-S3 leading-H3  ml-2 grow w-0 truncate', textClassName)} style={{
          color: GRAY.G1,
        }}>{ name || id }</div>
      </div>
      {/* 操作栏 */}
      <div className='shrink-0 hidden group-hover:flex gap-2 items-center justify-end'>
        { operations }
      </div>
    </div>
  )
}

export default memo(DatasetItem)
