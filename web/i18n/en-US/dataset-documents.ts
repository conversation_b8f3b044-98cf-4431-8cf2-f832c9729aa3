const translation = {
  list: {
    title: 'Documents',
    addFile: 'Add File',
    table: {
      header: {
        fileName: 'File Name',
        words: 'Number of Characters',
        hitCount: 'Hit Count',
        uploadTime: 'Upload Time',
        status: 'Status',
        action: 'Action',
      },
      rename: '<PERSON><PERSON>',
      name: 'Name',
    },
    action: {
      uploadFile: 'Upload New File',
      settings: 'Segmentation Settings',
      addButton: 'Add Segment',
      add: 'Add New Segment',
      batchAdd: 'Batch Add',
      archive: 'Archive',
      unarchive: 'Unarchive',
      enableWarning: 'Archived files cannot be enabled',
      data: 'File Data',
      newAdd: 'Add a new segment',
    },
    dialog: {
      detail: 'File Details',
      data: 'File Data',
    },
    notify: {
      archiveSuccessfully: 'Archived Successfully',
      unarchiveSuccessfully: 'Unarchived Successfully',
      syncSuccessfully: 'Synced Successfully',
    },
    index: {
      enableTip: 'This file can be indexed',
      disableTip: 'This file cannot be indexed',
    },
    status: {
      queuing: 'Queuing',
      indexing: 'Indexing',
      paused: 'Paused',
      error: 'Error',
      available: 'Enabled',
      enabled: 'Enabled',
      disabled: 'Disabled',
      archived: 'Archived',
    },
    empty: {
      title: 'No Documents Yet',
      upload: {
        tip: 'You can upload files, sync from websites, or sync from web applications (such as Notion, GitHub, etc.).',
      },
      sync: {
        tip: 'The Agent Platform will regularly download files from your Notion and complete the processing.',
      },
    },
    delete: {
      title: 'Are you sure you want to delete it?',
      content: 'If you need to resume processing later, you will continue from where you left off',
    },
    batchModal: {
      title: 'Batch Add Segments',
      csvUploadTitle: 'Drag and drop your CSV file here, or',
      browse: 'Select File',
      tip: 'The CSV file must conform to the following structure:',
      question: 'Question',
      answer: 'Answer',
      contentTitle: 'Segment Content',
      content: 'Content',
      template: 'Download Template',
      cancel: 'Cancel',
      run: 'Import',
      runError: 'Batch Import Failed',
      processing: 'Batch Processing',
      completed: 'Import Completed',
      error: 'Import Error',
      ok: 'OK',
    },
    info: {
      originFileName: 'Original File Name',
      originFileSize: 'Original File Size',
      uploadTime: 'Upload Time',
      lastUpdateTime: 'Last Update Time',
    },
  },
  metadata: {
    title: 'Metadata',
    desc: 'Tagging the metadata of documents allows the AI to access them in a timely manner and disclose the reference sources to users.',
    dateTimeFormat: 'YYYY-MM-DD HH:mm',
    docTypeSelectTitle: 'Please select a document type',
    docTypeChangeTitle: 'Change Document Type',
    docTypeSelectWarning: 'If you change the document type, the metadata filled in now will no longer be retained',
    firstMetaAction: 'Start',
    placeholder: {
      add: 'Enter',
      select: 'Select',
    },
    source: {
      upload_file: 'File Upload',
      notion: 'Documents synced from Notion',
      github: 'Code synced from Github',
    },
    type: {
      book: 'Book',
      webPage: 'Web Page',
      paper: 'Paper',
      socialMediaPost: 'Social Media Post',
      personalDocument: 'Personal Document',
      businessDocument: 'Business Document',
      IMChat: 'IM Chat Record',
      wikipediaEntry: 'Wikipedia Entry',
      notion: 'Documents synced from Notion',
      github: 'Code synced from Github',
      technicalParameters: 'Technical Parameters',
    },
    field: {
      processRule: {
        processDoc: 'Preprocess Document',
        segmentRule: 'Segmentation Rule',
        segmentLength: 'Segment Length',
        processClean: 'Text Preprocessing and Cleaning',
      },
      book: {
        title: 'Title',
        language: 'Language',
        author: 'Author',
        publisher: 'Publisher',
        publicationDate: 'Publication Date',
        ISBN: 'ISBN',
        category: 'Category',
      },
      webPage: {
        title: 'Title',
        url: 'URL',
        language: 'Language',
        authorPublisher: 'Author/Publisher',
        publishDate: 'Publication Date',
        topicsKeywords: 'Topics/Keywords',
        description: 'Description',
      },
      paper: {
        title: 'Title',
        language: 'Language',
        author: 'Author',
        publishDate: 'Publication Date',
        journalConferenceName: 'Journal/Conference Name',
        volumeIssuePage: 'Volume/Issue/Page Number',
        DOI: 'DOI',
        topicsKeywords: 'Topics/Keywords',
        abstract: 'Abstract',
      },
      socialMediaPost: {
        platform: 'Platform',
        authorUsername: 'Author/Username',
        publishDate: 'Publication Date',
        postURL: 'Post URL',
        topicsTags: 'Topics/Tags',
      },
      personalDocument: {
        title: 'Title',
        author: 'Author',
        creationDate: 'Creation Date',
        lastModifiedDate: 'Last Modified Date',
        documentType: 'Document Type',
        tagsCategory: 'Tags/Category',
      },
      businessDocument: {
        title: 'Title',
        author: 'Author',
        creationDate: 'Creation Date',
        lastModifiedDate: 'Last Modified Date',
        documentType: 'Document Type',
        departmentTeam: 'Department/Team',
      },
      IMChat: {
        chatPlatform: 'Chat Platform',
        chatPartiesGroupName: 'Chat Participants/Group Name',
        participants: 'Participants',
        startDate: 'Start Date',
        endDate: 'End Date',
        topicsKeywords: 'Topics/Keywords',
        fileType: 'File Type',
      },
      wikipediaEntry: {
        title: 'Title',
        language: 'Language',
        webpageURL: 'Webpage URL',
        editorContributor: 'Editor/Contributor',
        lastEditDate: 'Last Edit Date',
        summaryIntroduction: 'Summary/Introduction',
      },
      notion: {
        title: 'Title',
        language: 'Language',
        author: 'Author',
        createdTime: 'Creation Time',
        lastModifiedTime: 'Last Modified Time',
        url: 'URL',
        tag: 'Tag',
        description: 'Description',
      },
      github: {
        repoName: 'Repository Name',
        repoDesc: 'Repository Description',
        repoOwner: 'Repository Owner',
        fileName: 'File Name',
        filePath: 'File Path',
        programmingLang: 'Programming Language',
        url: 'URL',
        license: 'License',
        lastCommitTime: 'Last Commit Time',
        lastCommitAuthor: 'Last Commit Author',
      },
      originInfo: {
        originalFilename: 'Original File Name',
        originalFileSize: 'Original File Size',
        uploadDate: 'Upload Date',
        lastUpdateDate: 'Last Update Date',
        source: 'Source',
      },
      technicalParameters: {
        segmentSpecification: 'Segmentation Rule',
        segmentLength: 'Paragraph Length',
        avgParagraphLength: 'Average Paragraph Length',
        paragraphs: 'Number of Paragraphs',
        hitCount: 'Hit Count',
        embeddingTime: 'Embedding Time',
        embeddedSpend: 'Tokens Consumed',
      },
    },
    languageMap: {
      zh: 'Chinese',
      en: 'English',
      es: 'Spanish',
      fr: 'French',
      de: 'German',
      ja: 'Japanese',
      ko: 'Korean',
      ru: 'Russian',
      ar: 'Arabic',
      pt: 'Portuguese',
      it: 'Italian',
      nl: 'Dutch',
      pl: 'Polish',
      sv: 'Swedish',
      tr: 'Turkish',
      he: 'Hebrew',
      hi: 'Hindi',
      da: 'Danish',
      fi: 'Finnish',
      no: 'Norwegian',
      hu: 'Hungarian',
      el: 'Greek',
      cs: 'Czech',
      th: 'Thai',
      id: 'Indonesian',
    },
    categoryMap: {
      book: {
        fiction: 'Fiction',
        biography: 'Biography',
        history: 'History',
        science: 'Science',
        technology: 'Technology',
        education: 'Education',
        philosophy: 'Philosophy',
        religion: 'Religion',
        socialSciences: 'Social Sciences',
        art: 'Art',
        travel: 'Travel',
        health: 'Health',
        selfHelp: 'Self-help',
        businessEconomics: 'Business/Economics',
        cooking: 'Cooking',
        childrenYoungAdults: 'Children/Young Adults',
        comicsGraphicNovels: 'Comics/Graphic Novels',
        poetry: 'Poetry',
        drama: 'Drama',
        other: 'Other',
      },
      personalDoc: {
        notes: 'Notes',
        blogDraft: 'Blog Draft',
        diary: 'Diary',
        researchReport: 'Research Report',
        bookExcerpt: 'Book Excerpt',
        schedule: 'Schedule',
        list: 'List',
        projectOverview: 'Project Overview',
        photoCollection: 'Photo Collection',
        creativeWriting: 'Creative Writing',
        codeSnippet: 'Code Snippet',
        designDraft: 'Design Draft',
        personalResume: 'Personal Resume',
        other: 'Other',
      },
      businessDoc: {
        meetingMinutes: 'Meeting Minutes',
        researchReport: 'Research Report',
        proposal: 'Proposal',
        employeeHandbook: 'Employee Handbook',
        trainingMaterials: 'Training Materials',
        requirementsDocument: 'Requirements Document',
        designDocument: 'Design Document',
        productSpecification: 'Product Specification',
        financialReport: 'Financial Report',
        marketAnalysis: 'Market Analysis',
        projectPlan: 'Project Plan',
        teamStructure: 'Team Structure',
        policiesProcedures: 'Policies and Procedures',
        contractsAgreements: 'Contracts and Agreements',
        emailCorrespondence: 'Email Correspondence',
        other: 'Other',
      },
    },
  },
  embedding: {
    processing: 'Embedding Processing...',
    waiting: 'Embedding Queuing...',
    paused: 'Embedding Stopped',
    completed: 'Embedding Completed',
    error: 'Embedding Error',
    docName: 'File to be Processed',
    mode: 'Segmentation Processing',
    segments: 'Segments',
    embeddingModel: 'Embedding Model',
    highQuality: 'High Quality Mode',
    economy: 'Economy Mode',
    estimate: 'Estimated Consumption',
    stop: 'Stop Processing',
    resume: 'Resume Processing',
    automatic: 'Automatic Segmentation and Cleaning',
    auto: 'Automatic',
    custom: 'Customize',
    previewTip: 'Segments are visible only after successful loading',
    files: 'File data',
  },
  segment: {
    paragraphs: 'There are a total of {{number}} segments',
    keywords: 'Keywords',
    addKeyWord: 'Add Keyword',
    keywordError: 'The maximum length of a keyword is 20',
    hitCount: 'Hit Count',
    vectorHash: 'Vector Hash:',
    questionPlaceholder: 'Add a question here',
    questionEmpty: 'The question cannot be empty',
    answerPlaceholder: 'Add an answer here',
    answerEmpty: 'The answer cannot be empty',
    contentPlaceholder: 'Add content here',
    contentEmpty: 'The content cannot be empty',
    newTextSegment: 'New Text Segment',
    newQaSegment: 'New Q&A Segment',
    delete: 'Segment Deletion',
    deleteTip: 'Are you sure you want to delete this segment? This operation is irreversible.',
    structed: {},
  },
}

export default translation
