import { isString } from 'lodash-es'

// 邮箱验证
export function validateEmail(email: string) {
  const emailRegex = /^[\w.!#$%&'*+\-/=?^{|}~]+@([\w-]+\.)+[\w-]{2,}$/m
  return emailRegex.test(email)
}

// 验证用户密码
export function validatePassword(password: string) {
  const passwordRegex = /^(?=.*[a-zA-Z])(?=.*\d).{8,20}$/
  return passwordRegex.test(password)
}

// 验证手机号
export function validatePhone(phone: string) {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}
// 验证固定号码
export function validateFixedPhone(phone: string) {
  // 正则表达式，匹配中国的固定电话格式
  // 区号：3-4位数字，电话号码：7-8位数字，可选的分机号：1-4位数字，以"-"或空格分隔
  const regex = /^(0\d{2,3})?[-]?([2-9]\d{6,7})$/
  return regex.test(phone)
}
// 验证是否有内容
export function validateEmpty(input: any): boolean {
  if (isString(input)) {
    const regex = /[\s\n\r]+/g
    return !input?.replace(regex, '')
  }
  return true
}
// 验证url
export function validateUrl(url: string, protocol?: string) {
  const pattern = new RegExp(`^(http|https|${protocol}):\\/\\/([a-zA-Z0-9\\-\\.]+)(:[0-9]{1,5})?(\\/?[a-zA-Z0-9\\-._~:/?#[\\]@!$&'()*+,;=]*)?$`)
  return pattern.test(url)
}
