import type { FC } from 'react'
import { memo } from 'react'
import { useTranslation } from 'react-i18next'
import { Divider } from 'antd'
import VarReferencePicker from '../_base/components/variable/var-reference-picker'
import useConfig from './use-config'
import AddVideo from './components/add-video'
import DatasetList from './components/video-list'
import type { VideoNodeType } from './types'
import Field from '@/app/components/workflow/nodes/_base/components/field'
import OutputVars from '@/app/components/workflow/nodes/_base/components/variable/output-vars'
import { InputVarType, type NodePanelProps } from '@/app/components/workflow/types'
import BeforeRunForm from '@/app/components/workflow/nodes/_base/components/before-run-form'
import ResultPanel from '@/app/components/workflow/run/result-panel'
import ForkTip from '@/app/components/base/forkTip'

const i18nPrefix = 'workflow.nodes.video'

const Panel: FC<NodePanelProps<VideoNodeType>> = ({
  id,
  data,
}) => {
  const { t } = useTranslation()

  const {
    readOnly,
    inputs,
    filterVar,
    filterNumberVar,
    selectedVideos,
    forkVideos,
    isShowSingleRun,
    runningStatus,
    runInputData,
    setQuery,
    runResult,
    hideSingleRun,
    handleRun,
    handleStop,
    handleStartTimeChange,
    handleEndTimeChange,
    handleDeviceChange,
    handleLocationChange,
    handleEventChange,
    handleOnVideosChange,
  } = useConfig(id, data)

  return (
    <>
      <div className='flex flex-col gap-4'>
        {/* 输入变量 */}
        <Field title={t(`${i18nPrefix}.queryVariable`)}>
          <>
            {/* 开始时间 */}
            <VarReferencePicker
              className='mb-2'
              nodeId={id}
              readonly={readOnly}
              isShowNodeName
              value={inputs.start_time_variable_selector}
              label={t(`${i18nPrefix}.startTime`)!}
              onChange={handleStartTimeChange}
              filterVar={filterNumberVar}
            />
            {/* 结束时间 */}
            <VarReferencePicker
              className='mb-2'
              nodeId={id}
              readonly={readOnly}
              isShowNodeName
              value={inputs.end_time_variable_selector}
              label={t(`${i18nPrefix}.endTime`)!}
              onChange={handleEndTimeChange}
              filterVar={filterNumberVar}
            />
            {/* 事件 */}
            <VarReferencePicker
              className='mb-2'
              nodeId={id}
              readonly={readOnly}
              isShowNodeName
              value={inputs.event_name_variable_selector}
              label={t(`${i18nPrefix}.event`)!}
              onChange={handleEventChange}
              filterVar={filterVar}
            />
            {/* 地点 */}
            <VarReferencePicker
              className='mb-2'
              nodeId={id}
              readonly={readOnly}
              isShowNodeName
              value={inputs.location_variable_selector}
              label={t(`${i18nPrefix}.location`)!}
              onChange={handleLocationChange}
              filterVar={filterVar}
            />
            {/* 设备 */}
            <VarReferencePicker
              nodeId={id}
              readonly={readOnly}
              isShowNodeName
              value={inputs.device_variable_selector}
              label={t(`${i18nPrefix}.device`)!}
              onChange={handleDeviceChange}
              filterVar={filterVar}
            />
          </>
        </Field>
        {/* 添加视频库 */}
        <Field
          title={t(`${i18nPrefix}.name`)}
          operations={
            !readOnly
              ? (
                <AddVideo
                  selected={selectedVideos}
                  onChange={handleOnVideosChange}
                />
              )
              : <></>
          }
        >
          <>
            {forkVideos.length > 0 && data.fork && (
              <ForkTip
                className='forkTip mx-4 mb-2'
                message={t('common.fork.videoTip')}
                onClose={() => handleOnVideosChange([])}
              >
                <div>
                  {forkVideos.map(({ id, name }) => (
                    <div key={id} className=''>
                      <div className=''>
                        · {name}
                      </div>
                    </div>
                  ))}
                </div>
              </ForkTip>
            )}
            <DatasetList
              list={selectedVideos}
              onChange={handleOnVideosChange}
              readonly={readOnly}
            />
          </>
        </Field>
      </div>
      <Divider />
      {/* 输出变量 */}
      <OutputVars
        vars={[{
          name: 'result',
          type: 'Array[Object]',
          description: t(`${i18nPrefix}.outputVars.output`),
          subItems: [
            {
              name: 'url',
              type: 'string',
              description: t(`${i18nPrefix}.outputVars.url`),
            },
            {
              name: 'timestamp',
              type: 'int',
              description: t(`${i18nPrefix}.outputVars.timestamp`),
            },
            {
              name: 'detected_events',
              type: 'Array[Object]',
              description: t(`${i18nPrefix}.outputVars.detected_events`),
            },
            {
              name: 'device_id',
              type: 'string/int',
              description: t(`${i18nPrefix}.outputVars.device_id`),
            },
            {
              name: 'address_ip',
              type: 'string',
              description: t(`${i18nPrefix}.outputVars.address_ip`),
            },
          ],
        }]}
      >

      </OutputVars>
      {isShowSingleRun && (
        <BeforeRunForm
          nodeName={inputs.title}
          onHide={hideSingleRun}
          forms={[
            {
              inputs: [{
                label: t(`${i18nPrefix}.startTime`)!,
                variable: 'start_time',
                type: InputVarType.number,
                required: false,
              }],
              values: { start_time: runInputData?.start_time },
              onChange: keyValue => setQuery((keyValue as any).start_time, 'start_time'),
            },
            {
              inputs: [{
                label: t(`${i18nPrefix}.endTime`)!,
                variable: 'end_time',
                type: InputVarType.number,
                required: false,
              }],
              values: { end_time: runInputData?.end_time },
              onChange: keyValue => setQuery((keyValue as any).end_time, 'end_time'),
            },
            {
              inputs: [{
                label: t(`${i18nPrefix}.event`)!,
                variable: 'event_name',
                type: InputVarType.textInput,
                required: false,
              }],
              values: { event_name: runInputData?.event_name },
              onChange: keyValue => setQuery((keyValue as any).event_name, 'event_name'),
            },
            {
              inputs: [{
                label: t(`${i18nPrefix}.location`)!,
                variable: 'location',
                type: InputVarType.textInput,
                required: false,
              }],
              values: { location: runInputData?.location },
              onChange: keyValue => setQuery((keyValue as any).location, 'location'),
            },
            {
              inputs: [{
                label: t(`${i18nPrefix}.device`)!,
                variable: 'device',
                type: InputVarType.textInput,
                required: false,
              }],
              values: { device: runInputData?.device },
              onChange: keyValue => setQuery((keyValue as any).device, 'device'),
            },

          ]}
          runningStatus={runningStatus}
          onRun={handleRun}
          onStop={handleStop}
          result={<ResultPanel {...runResult} showSteps={false} />}
        />
      )}
    </>
  )
}

export default memo(Panel)
