'use client'

import { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import TextButton from '../button/text-button'
import Toast from '../toast'
import SsoVerify, { CaptchaType } from '../sso-verify'

type SmsVerifyProps = {
  // 时间间隔
  interval: number
  // 是否启用disabled
  disabled: boolean
  // 发送函数
  sendFunc: (value?: string) => void
  // 是否需要额外验证
  extraVerify?: boolean
}

const SmsVerify = ({
  interval,
  disabled: defaultDisabled,
  sendFunc,
  extraVerify = false,
}: SmsVerifyProps) => {
  const { t } = useTranslation()
  // 正在发送
  const [isSending, setIsSending] = useState(false)
  // 剩余时间
  const [timeRemaing, setTimeRemaing] = useState(interval)
  // 是否展示图形验证码弹窗
  const [showSsoBox, setShowSsoBox] = useState(false)
  // 剩余时间文本
  const remaingTimeText = useMemo(() => {
    return t('common.codeVerify.remaingTimeText', { time: timeRemaing })
  }, [t, timeRemaing])

  // 获取验证码
  const getSmsVerify = async (value?: string) => {
    try {
      await sendFunc(value)
      setIsSending(true)
      setShowSsoBox(false)
      Toast.notify({
        type: 'success',
        message: t('common.codeVerify.waitSms'),
      })
    }
    catch (error) {
      setShowSsoBox(false)

      Toast.notify({
        type: 'error',
        message: `${error}`.split('Error:')[1] as string,
      })
    }
  }
  // 处理点击事件
  const handleClick = () => {
    if (extraVerify)
      setShowSsoBox(true)
    else
      getSmsVerify()
  }

  useEffect(() => {
    if (isSending) {
      if (timeRemaing > 0) {
        const timeout = setTimeout(() => {
          setTimeRemaing(timeRemaing - 1)
        }, 1000)
        return () => clearTimeout(timeout)
      }
      else {
        setIsSending(false)
        setTimeRemaing(interval)
      }
    }
  }, [interval, isSending, timeRemaing])

  return (
    <>
      {isSending ? <div className='text-S3 leading-H3 text-gray-G4'>{remaingTimeText}</div> : <TextButton size='middle' variant='primary' onClick={handleClick} disabled={defaultDisabled}>{t('common.codeVerify.getSms')}</TextButton>}
      <SsoVerify
        captchaType={CaptchaType.BlockPuzzle}
        onSuccess={getSmsVerify}
        showBox={showSsoBox}
        onClose={() => setShowSsoBox(false)}
      ></SsoVerify>
    </>
  )
}

export default SmsVerify
