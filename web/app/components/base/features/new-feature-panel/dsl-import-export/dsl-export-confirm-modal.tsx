'use client'
import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { RiLock2Line } from '@remixicon/react'
import { Checkbox, Table, type TableColumnsType } from 'antd'
import type { EnvironmentVariable } from '@/app/components/workflow/types'
/* 公共组件 */
import cn from '@/utils/classnames'
import { Env } from '@/app/components/base/icons/src/vender/line/others'
import Modal from '@/app/components/base/modal'
import Button from '@/app/components/base/button'

export type DSLExportConfirmModalProps = {
  envList: EnvironmentVariable[]
  onConfirm: (state: boolean) => void
  onClose: () => void
}

const DSLExportConfirmModal = ({
  envList = [],
  onConfirm,
  onClose,
}: DSLExportConfirmModalProps) => {
  const { t } = useTranslation()
  // 是否导出私密环境变量
  const [exportSecrets, setExportSecrets] = useState<boolean>(false)
  const columns: TableColumnsType = [
    {
      title: 'NAME',
      key: 'name',
      dataIndex: 'name',
      width: 200,
      render: text => (
        <div className='flex gap-1 items-center'>
          <Env className='shrink-0 w-4 h-4 text-util-colors-violet-violet-600' />
          <div className='text-text-primary truncate'>{text}</div>
          <div className='shrink-0 text-text-tertiary'>Secret</div>
          <RiLock2Line className='shrink-0 w-3 h-3 text-text-tertiary' />
        </div>
      ),
    },
    {
      title: 'VALUE',
      key: 'value',
      dataIndex: 'value',
    },
  ]

  // 确认导出
  const submit = async () => {
    await onConfirm(exportSecrets)
    onClose()
  }

  return (
    <Modal
      isShow={true}
      closable
      onClose={onClose}
      title={t('workflow.dsl.title')}
      className={cn('max-w-[480px] w-[480px] relative')}
      footer={
        <>
          <Button variant='secondary-accent' onClick={onClose}>{t('common.operation.cancel')}</Button>
          <Button className='ml-4' variant='primary' onClick={submit}>{t('common.operation.confirm')}</Button>
        </>
      }
    >
      <Table
        size='middle'
        pagination={false}
        className='border-gray-G6 rounded border mb-6'
        columns={columns}
        dataSource={envList}>
      </Table>
      <Checkbox checked={exportSecrets} onChange={e => setExportSecrets(e.target.checked)}>{t('workflow.dsl.checkbox')}</Checkbox>
    </Modal>
  )
}

export default DSLExportConfirmModal
