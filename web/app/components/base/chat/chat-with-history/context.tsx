'use client'

import type { RefObject } from 'react'
import { createContext, useContext } from 'use-context-selector'
import type { Callback, ChatConfig, ChatItem, Feedback } from '../types'
import type { ThemeBuilder } from '../embedded-chatbot/theme/theme-context'
import type { AppConversationData, AppData, AppMeta, ConversationItem } from '@/types/share'
import type { AppMarket } from '@/types/app-market'

export type ChatWithHistoryContextValue = {
  appInfoError?: any
  appInfoLoading?: boolean
  appMeta?: AppMeta
  appData?: AppData
  appMarketData?: AppMarket
  appParams?: ChatConfig
  appChatListDataLoading?: boolean
  currentConversationId: string
  currentConversationItem?: ConversationItem
  appPrevChatList: ChatItem[]
  pinnedConversationList: AppConversationData['data']
  conversationList: AppConversationData['data']
  showConfigPanelBeforeChat: boolean
  newConversationInputs: Record<string, any>
  newConversationInputsRef: RefObject<Record<string, any>>
  handleNewConversationInputsChange: (v: Record<string, any>) => void
  setChatAllTypeResult: (v: Record<string, any>) => void
  inputsForms: any[]
  handleNewConversation: () => void
  handleChangeConversation: (conversationId: string, isBasicLongChatModel?: boolean) => void
  handlePinConversation: (conversationId: string) => void
  handleUnpinConversation: (conversationId: string) => void
  handleDeleteConversation: (conversationId: string, callback: Callback) => void
  conversationRenaming: boolean
  handleAutoRename: (conversationId: string) => void
  handleRenameConversation: (conversationId: string, newName: string, callback: Callback) => void
  setCurrentChatConversationId: (conversationId: string) => void
  handleNewConversationCompleted: (newConversationId: string) => void
  chatShouldReloadKey: string
  isMobile: boolean
  isInstalledApp: boolean
  appId?: string
  handleFeedback: (messageId: string, feedback: Feedback) => void
  currentChatInstanceRef: RefObject<{ handleStop: () => void; handleRestart: () => void }>
  themeBuilder?: ThemeBuilder
  fromMarket?: boolean
  chatAllTypeResult: Record<string, any>
  isBasicLongChatModel: boolean
  setIsBasicLongChatModel: (v: boolean) => void
  handleChatList: (conversationId: string) => void
  chatReady: boolean
}

export const ChatWithHistoryContext = createContext<ChatWithHistoryContextValue>({
  currentConversationId: '',
  appPrevChatList: [],
  pinnedConversationList: [],
  conversationList: [],
  showConfigPanelBeforeChat: false,
  isBasicLongChatModel: false,
  setIsBasicLongChatModel: () => {},
  newConversationInputs: {},
  newConversationInputsRef: { current: {} },
  handleNewConversationInputsChange: () => {},
  setChatAllTypeResult: () => {},
  inputsForms: [],
  handleNewConversation: () => {},
  handleChangeConversation: () => {},
  handlePinConversation: () => {},
  handleUnpinConversation: () => {},
  handleDeleteConversation: () => {},
  conversationRenaming: false,
  handleAutoRename: () => {},
  handleRenameConversation: () => {},
  setCurrentChatConversationId: () => {},
  handleNewConversationCompleted: () => {},
  chatShouldReloadKey: '',
  isMobile: false,
  isInstalledApp: false,
  handleFeedback: () => {},
  currentChatInstanceRef: { current: { handleStop: () => {}, handleRestart: () => {} } },
  fromMarket: false,
  chatAllTypeResult: {},
  handleChatList: () => {},
  chatReady: false,
})
export const useChatWithHistoryContext = () => useContext(ChatWithHistoryContext)
