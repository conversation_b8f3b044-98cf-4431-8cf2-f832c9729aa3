'use client'
import { useRouter } from 'next/navigation'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useContext } from 'use-context-selector'
import type { TableColumnsType, TableProps } from 'antd'
import useTimestamp from '@/hooks/use-timestamp'
import { OperationLogResponse, OperationTypeEnum, OperationSourceEnum, OperationContent } from './type'
import type { QueryParam } from './index'

// 组件
import { Table } from 'antd'
import { ToastContext } from '@/app/components/base/toast'

import s from './style.module.css'
import cn from '@/utils/classnames'

const i18nPrefix = 'account.operationLog'

type props = {
  queryParams: QueryParam
  setQueryParams: (v: QueryParam) => void
  operationLogData: OperationLogResponse,
  loading: boolean,
  onRefresh: () => void
}
// 对话列表
const List = ({ queryParams, setQueryParams, operationLogData, loading, onRefresh }: props) => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  const { formatTime } = useTimestamp()
  const defaultValue = '-'

  // 表格列
  const columns: TableColumnsType<any> = [
    // 序号
    {
      title: t(`${i18nPrefix}.table.header.index`),
      key: 'index',
      render: (_: any, logItem, index) => {
        return ((operationLogData.page - 1) * operationLogData.limit) + index + 1
      },
      width: 70,
      ellipsis: true,
    },
    // 用户id
    {
      title: t(`${i18nPrefix}.table.header.userId`),
      key: 'userId',
      render: (_: any, logItem) => {
        return logItem.user_id || defaultValue
      },
      width: 100,
      ellipsis: true,
    },
    // 用户昵称
    {
      title: t(`${i18nPrefix}.table.header.userName`),
      key: 'userName',
      render: (_: any, logItem) => {
        return logItem.user_name || defaultValue
      },
      width: 100,
      ellipsis: true,
    },
    // 用户账户
    {
      title: t(`${i18nPrefix}.table.header.account`),
      key: 'account',
      render: (_: any, logItem) => {
        return logItem.account || '-'
      },
      width: 100,
      ellipsis: true,
    },
    // 操作时间
    {
      title: t(`${i18nPrefix}.table.header.time`),
      key: 'time',
      render: (_: any, logItem) => {
        return formatTime(logItem.created_at, t('common.dateFormat.dateTime') as string)
      },
      width: 150,
      ellipsis: true
    },
    // 团队空间
    {
      title: t(`${i18nPrefix}.table.header.workspace`),
      render: (_: any, logItem) => {
        return logItem.workspace || '-'
      },
      key: 'workspace',
      align: 'left',
      width: 100,
    },
    // 操作动作
    {
      title: t(`${i18nPrefix}.table.header.operation`),
      render: (_: any, logItem) => {
        return getOperationLabel(logItem.operation) || '-'
      },
      key: 'operation',
      align: 'left',
      width: 100,
    },
    // 具体细节
    {
      title: t(`${i18nPrefix}.table.header.content`),
      render: (_: any, logItem) => {
        return getContentLabel(logItem.content) || '-'
      },
      key: 'content',
      align: 'left',
      width: 150,
    },
  ]
  // 操作动作
  const getOperationLabel = (operation: OperationTypeEnum) => {
    if(operation in OperationTypeEnum)
      return t(`${i18nPrefix}.operation.${operation}`)
    return operation || ''
  }
  const getTypeLabel = (type: OperationSourceEnum) => {
    if(type in OperationSourceEnum)
      return t(`${i18nPrefix}.source.${type}`)
    return type || ''
  }
  // 具体细节
  const getContentLabel = (content: OperationContent) => {
    if(!content) return defaultValue
    const { source, type, target } = content
    const operationLabel = getOperationLabel(type)
    const typeLabel = getTypeLabel(source)
    return t(`${i18nPrefix}.contentLabel`, {target: target, typeLabel: typeLabel, operationLabel: operationLabel})
  }

  // 排序
  const handleTableChange = useCallback((pagination: any, filters: any, sorter: any) => {
    setQueryParams({
      ...queryParams,
      sort_by: sorter.order === 'ascend' ? '-created_at' : 'created_at',
    })
  }, [])

  return (
    <>
      <Table
        size='middle'
        loading={loading}
        columns={columns}
        pagination={false}
        scroll={{ x: 'calc(100% - 200px)',y: 'calc(100vh - 260px)' }}
        rowKey='id'
        dataSource={operationLogData?.data || []}
        className='border-gray-G6 rounded border'
        rowClassName='cursor-pointer'
        onChange={handleTableChange}
      ></Table>
    </>
  )
}

export default List
