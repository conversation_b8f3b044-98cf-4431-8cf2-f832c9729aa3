'use client'

import { type FC, memo, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { RiEditLine } from '@remixicon/react'
import { Card, Divider, Input, Space } from 'antd'
import { useParams } from 'next/navigation'
import { formatPosition } from '../segment-index'
import s from './style.module.css'
import type { KnowledgePointsData, SegmentDetailModel } from '@/models/datasets'
import style from '@/app/components/datasets/create/knowledge-enhancement-setting/styles/style.module.scss'
import KnowledgePointsModal from '@/app/components/datasets/common/segment-detail/knowledge-points'

import cn from '@/utils/classnames'
import { formatNumber } from '@/utils/format'
import Modal from '@/app/components/base/modal'
import AutoHeightTextarea from '@/app/components/base/auto-height-textarea/common'
import Button from '@/app/components/base/button'
import TagInput from '@/app/components/base/tag-input'
import { TypeSquare } from '@/app/components/base/icons/src/vender/line/editor'
import { Target04 } from '@/app/components/base/icons/src/vender/line/general'
import TextButton from '@/app/components/base/button/text-button'
import { useProviderContext } from '@/context/provider-context'
import { DatesetFileTypeCode } from '@/models/datasets'

import { knowledgePointsMarket, setknowledgePointsMarket } from '@/service/datasets'
import Tooltip from '@/app/components/base/tooltip'

const { TextArea } = Input
type ISegmentDetailProps = {
  canEdit?: boolean
  segInfo?: Partial<SegmentDetailModel> & { id: string }
  onUpdate?: (segmentId: string, q: string, a: string, k: string[]) => void
  onCancel: () => void
  category?: string | number
}
// 分段详情-单个分段组件
const SegmentDetailComponent: FC<ISegmentDetailProps> = ({
  segInfo,
  canEdit = true,
  onUpdate,
  onCancel,
  category,
}) => {
  const { t } = useTranslation()
  // 是否是私有知识库
  const { useXIYANRag } = useProviderContext()
  // 是否正在编辑
  const [isEditing, setIsEditing] = useState(false)
  // 问题内容
  const [question, setQuestion] = useState(segInfo?.content || '')
  // 回答文本
  const [answer, setAnswer] = useState(segInfo?.answer || '')
  // 关键字
  const [keywords, setKeywords] = useState<string[]>(segInfo?.keywords || [])
  // 是否正在索引
  const isIndexing = segInfo?.index_status === 'indexing'

  // 是否知识增强
  const [isEnhancedEnable, setIsEnhancedEnable] = useState(false)

  // 当前知识点切片可否编辑
  const [isKnowEdit, setIsKnowEdit] = useState(false)
  console.log('==查看===', segInfo)
  const datasetParams = useParams<{ datasetId: string; documentId: string }>()

  // 知识点列表
  const [knowPointsList, setKnowPointsList] = useState<KnowledgePointsData[]>([])
  const [knowPointsNum, setKnowPointsNum] = useState(0)
  // 当前知识点
  const [knowPointsListObj, setKnowPointsListObj] = useState({})
  // 知识切片弹窗
  const [showKnowModal, setShowKnowModal] = useState(false)

  // web\app\(commonLayout)\datasets\(datasetDetailLayout)\[datasetId]\doc-config\[documentId]\settings\page.tsx
  const resetState = () => {
    setIsEditing(false)
    setQuestion(segInfo?.content || '')
    setAnswer(segInfo?.answer || '')
    setKeywords(segInfo?.keywords || [])
    setIsEnhancedEnable(segInfo?.enhanced_enable === '1' || false)
  }
  const handleCancel = () => {
    resetState()
  }
  const handleSave = async () => {
    await onUpdate!(segInfo?.id || '', question, answer, keywords)
  }

  useEffect(() => {
    resetState()
    console.log(isEnhancedEnable, '==segInfo===', segInfo)
  }, [segInfo])

  // 测试数据
  const kpData = () => {
    const list = []
    for (let i = 0; i < 3; i++) {
      list.push({
        id: `z_${i}`,
        content: `${i}亚龙湾国家旅游度假区亚龙湾位于中国最南端的热带滨海旅游城市--三亚市东`,
        enhanced_enable: '1',
        enhanced_feature: i > 0 ? ['3'] : ['3', '2'],

      })
    }
    setKnowPointsList(list)
    setKnowPointsNum(list.length || 0)
  }
  const knowPoints = async () => {
    try {
      const params = {
        dataset_id: datasetParams.datasetId,
        segment_id: segInfo?.id || '',
      }
      const res = await knowledgePointsMarket(params)
      setKnowPointsList(res?.data || [])
      setKnowPointsNum(res?.data?.length || 0)
      // console.log('==知识点列表res===', res)
      // kpData() // 测试数据
    }
    catch {
      // 测试demo
      // kpData()
    }
  }
  useEffect(() => {
    knowPoints()
  }, [])
  const renderContent = () => {
    if (segInfo?.answer) {
      return (
        <>
          <div className="mb-1 text-xs font-semibold text-gray-500">
            QUESTION
          </div>
          <AutoHeightTextarea
            outerClassName="mb-4"
            className="leading-6 text-md text-gray-800"
            value={question}
            placeholder={
              t('datasetDocuments.segment.questionPlaceholder') || ''
            }
            onChange={e => setQuestion(e.target.value)}
            disabled={!isEditing}
          />
          <div className="mb-1 text-xs font-semibold text-gray-500">ANSWER</div>
          <AutoHeightTextarea
            outerClassName="mb-4"
            className="leading-6 text-md text-gray-800"
            value={answer}
            placeholder={t('datasetDocuments.segment.answerPlaceholder') || ''}
            onChange={e => setAnswer(e.target.value)}
            disabled={!isEditing}
            autoFocus
          />
        </>
      )
    }

    return (
      <AutoHeightTextarea
        outerClassName={cn(
          s.segModalContent,
          isEditing && s.segModalContentActive, 'min-h-[50vh]',
        )}
        className="leading-H3 text-S3 text-gray-G2 !bg-transparent"
        value={question}
        placeholder={t('datasetDocuments.segment.contentPlaceholder') || ''}
        onChange={e => setQuestion(e.target.value)}
        disabled={!isEditing}
        autoFocus
      />
    )
  }
  const showSaveBut = () => {
    return (
      <>
        <Button
          size={'small'}
          className="!rounded !w-[72px] !mr-4"
          variant={'secondary-accent'}
          onClick={handleCancel}
        >
          {t('common.operation.cancel')}
        </Button>
        <Button
          size={'small'}
          variant="primary"
          className="!rounded !w-[72px]"
          onClick={handleSave}
        >
          {t('common.operation.save')}
        </Button>
      </>
    )
  }
  const segInfoContent = () => {
    return (
      <div className={s.segInfoWrapper}>
        {/* 字符数 */}
        <div className={cn(s.segInfoItem, 'mb-2')}>
          <TypeSquare className={s.commonIcon} />
          <span>{formatNumber(segInfo?.word_count as number)}</span>
          <span>{t('common.unit.characters')}</span>
        </div>
        {/* 命中次数 */}
        <div className={cn(s.segInfoItem, 'mb-4')}>
          <Target04 className={s.commonIcon} />
          <span>{formatNumber(segInfo?.hit_count as number)} </span>
          <span>{t('datasetDocuments.segment.hitCount')}</span>
        </div>
        {/* 哈希码 */}
        {!useXIYANRag && (
          <>
            <div className={s.segInfoItemTitle}>
              {t('datasetDocuments.segment.vectorHash')}
            </div>
            <div className={s.hashValue}>{segInfo?.index_node_hash}</div>
            <Divider type="horizontal" className="!my-4"></Divider>
          </>
        )}
        {/* 标签 */}
        {!useXIYANRag && (
          <>
            <div className={s.segInfoItemTitle}>
              {t('datasetDocuments.segment.keywords')}
            </div>
            <div className={s.keywordWrapper}>
              <TagInput
                className="!min-w-[160px]"
                items={keywords}
                onChange={newKeywords => setKeywords(newKeywords)}
                disableAdd={!isEditing}
                canRemove={isEditing && keywords.length !== 1}
              />
            </div>
          </>
        )}
        { 
          // 未开启知识增强，且不为西研环境
          (!isEnhancedEnable && !useXIYANRag) && isEditing && (
          // isEditing && (
          <div className="flex items-center justify-end">
            {/* <Button
              size={'small'}
              className="!rounded !w-[72px] !mr-4"
              variant={'secondary-accent'}
              onClick={handleCancel}
            >
              {t('common.operation.cancel')}
            </Button>
            <Button
              size={'small'}
              variant="primary"
              className="!rounded !w-[72px]"
              onClick={handleSave}
            >
              {t('common.operation.save')}
            </Button> */}
            {showSaveBut()}
          </div>
        )}
      </div>
    )
  }

  const onEditKnowleConfirm = async (data) => {
    console.log('点击编辑知识点', knowPointsListObj)
    const params = {
      dataset_id: datasetParams.datasetId,
      segment_id: segInfo?.id || '',
      knowledge_point_id: knowPointsListObj.id || '',
    }
    const body = { ...knowPointsListObj }
    switch (knowPointsListObj.knowledge_point_type) {
      case 1:
        // 兼容
        body.abs_knowledge_point = data.abs_knowledge_point
        body.content = data.abs_knowledge_point
        break
      case 2:
        body.question = data.question
        body.answer = data.answer
        break
    }
    console.log(body, '===更新知识点===')
    await setknowledgePointsMarket(params, body)
    knowPoints() // 刷新知识点列表
    onEditKnowleClose()
  }
  // 关闭编辑知识切片弹窗
  const onEditKnowleClose = (type?: string) => {
    setShowKnowModal(false)
    // 关闭后清空
    setKnowPointsListObj({})
  }

  const enhancFeature = (item) => {
    if (item.knowledge_point_type === 2)
      return <span className='mr-[10px]'>{t('datasetCreation.stepTwo.knowledgeEnhancement.enhanceContent.problemGeneration')}</span>

    else
      return <span className='mr-[10px]'>{t('datasetCreation.stepTwo.knowledgeEnhancement.enhanceContent.paragraphSummary')}</span>
  }
  const KnowledgeSlicing = () => {
    return (
      <Card
        className='w-[56%] knowledCardSty min-w-[300px]'
      >
        <div className={`${s.segInfoItemTitle} mb-[10px]`}>
          {t('datasetDocuments.segment.knowledgePoints')}
          { knowPointsNum }
          {t('datasetDocuments.segment.paragraph')}
        </div>
        {/*  className='hover:text-components-button-destructive-ghost-text' */}
        <div>
          {
            knowPointsList.map((item) => {
              return (
                <Card
                  className={`bg-[#f8f9fa] text-[#5c6273] mb-[10px] ${cn(
                    style['Kno-card-sty'],
                    // !disabled && s['abled-radio-card'],
                    // isChosen && s['radio-card-active'],
                    // style['Kno-card-active']
                  )}`}
                  key={item.id}
                >
                  <div className={s.KnowledgeCardConSty}>
                    {
                      item.knowledge_point_type === 1 && (item.abs_knowledge_point || item.content)
                    }
                    {
                      item.knowledge_point_type === 2 && item.question
                    }
                  </div>
                  <div className='flex justify-between'>
                    <div className={s.sourceKnowSty}>
                      {t('datasetDocuments.segment.sourceOfKnowledge')}
                      { enhancFeature(item) }
                    </div>
                    <div>
                      <TextButton variant="text" onClick={() => {
                        setKnowPointsListObj(item)
                        console.log('点击编辑onEditKnowledClick', knowPointsListObj)
                        // const isEditC = !isEditing && !isIndexing && canEdit && segInfo?.enabled && (!useXIYANRag || (useXIYANRag && category === DatesetFileTypeCode.Unstructured))
                        // setIsKnowEdit(isEditing)
                        setShowKnowModal(true)
                      }}>
                        <RiEditLine className="w-4 h-4 text-gray-G1" />
                      </TextButton>
                    </div>
                  </div>
                </Card>
              )
            })
          }
        </div>

        {/* <AutoHeightTextarea
          outerClassName="mb-4"
          className="leading-6 text-md text-gray-800"
          value={question}
          placeholder={
            t('datasetDocuments.segment.questionPlaceholder') || ''
          }
          onChange={e => setQuestion(e.target.value)}
          disabled={!isEditing}
        /> */}
      </Card>
    )
  }
  const showEdit = !isEditing
  && !isIndexing
  && canEdit
  && segInfo?.enabled
  && (!useXIYANRag || (useXIYANRag && category === DatesetFileTypeCode.Unstructured)) 
  console.log(isEditing,isEditing , '======')
  return (
    <>
      <Modal
        isShow
        onClose={onCancel}
        closable
        className="!w-[732px] !min-h-[60vh]"
        title={useXIYANRag ? <span>&nbsp;</span> : `#${formatPosition(segInfo?.position || '')}`}
        // wrapperClassName="flex !flex-row"
        headerExtra={
          // !isEditing
          // && !isIndexing
          // && canEdit
          // && segInfo?.enabled
          // && (!useXIYANRag || (useXIYANRag && category === DatesetFileTypeCode.Unstructured)) && (
          //   <TextButton variant="text" onClick={() => setIsEditing(true)}>
          //     <RiEditLine className="w-4 h-4 text-gray-G1" />
          //   </TextButton>
          // )
          showEdit && (
            <Tooltip popupContent={t('datasetDocuments.segment.editButTip')}>
              <TextButton variant="text" onClick={() => setIsEditing(true)}>
                <RiEditLine className="w-4 h-4 text-gray-G1" />
              </TextButton>
            </Tooltip>
          )
        }
      >
        <div className="flex !flex-row">
          {renderContent()}
          {
            isEnhancedEnable && useXIYANRag
              ? (
                <>
                  <Divider className="mx-4 h-full" type="vertical"></Divider>
                  {KnowledgeSlicing()}
                </>
              )
              : (
                <>
                  <Divider className="mx-4 h-full" type="vertical"></Divider>
                  {segInfoContent()}
                </>
              )
          }
        </div>
        {
          isEnhancedEnable && (
            <>
              <div className=' mt-[20px]'>
                {(isEnhancedEnable && useXIYANRag) && isEditing && (
                  <div className="flex items-center justify-start">
                    {showSaveBut()}
                  </div>
                )}

              </div>
              <div className="flex !flex-row mt-[20px]">
                <Space>
                  {/* 字符数 */}
                  <div className={cn(s.segInfoItem)}>
                    <TypeSquare className={s.commonIcon} />
                    <span>{formatNumber(segInfo?.word_count as number)}</span>
                    <span>{t('common.unit.characters')}</span>
                  </div>
                  {/* 命中次数 */}
                  <div className={cn(s.segInfoItem)}>
                    <Target04 className={s.commonIcon} />
                    <span>{formatNumber(segInfo?.hit_count as number)} </span>
                    <span>{t('datasetDocuments.segment.hitCount')}</span>
                  </div>

                </Space>
              </div>
            </>
          )
        }
      </Modal>
      {
        showKnowModal && (
          <KnowledgePointsModal
            knowPointsListObj = {knowPointsListObj}
            onEditKnowleClose = {onEditKnowleClose}
            onEditKnowleConfirm={onEditKnowleConfirm}
            isKnowEdit = {!showEdit}
            // isKnowEdit ={segInfo?.enabled}
          />
        )
      }
    </>
  )
}
export default memo(SegmentDetailComponent)
