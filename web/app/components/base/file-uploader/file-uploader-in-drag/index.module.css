.uploader {
    @apply relative flex flex-col gap-[2px] justify-center items-center;
    @apply p-3 w-full min-h-[80px];
    @apply border border-gray-G5 bg-gray-G7 rounded text-S3 leading-H3;
  }
  .uploadIcon {
    @apply w-6 h-6 text-gray-G3;
  }
  .uploadTitle {
    @apply flex justify-center items-center text-gray-G2 text-S3 leading-H4 font-semibold;
  }
  .uploadTip {
    @apply text-S1 leading-H3 text-gray-G3;
  }
  .uploader .browse {
    @apply pl-1 cursor-pointer text-primary-P1;
  }
  .uploader.dragging {
    background: #F5F8FF;
    border: 1px dashed #B2CCFF;
  }
  .uploader .draggingCover {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
  
  .fileList {
    @apply w-full max-h-[300px] lg:!max-h-[calc(100vh-584px)];
  }
  .file {
    @apply h-9 rounded cursor-pointer px-4 w-full mt-2;
    @apply relative flex items-center justify-between;
    @apply text-S3 leading-H3 text-gray-G1;
  }
  .file.uploading {
    @apply bg-primary-P4;
  }
  .file.success {
    @apply bg-green-G2;
  }
  .file.error {
    @apply bg-red-R3;
  }
  .fileInfo {
    @apply grow flex items-center;
    z-index: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .resultIcon {
    @apply w-4 h-4 mr-3;
  }
  .fileIcon {
    @apply shrink-0 w-6 h-6 mr-1;
  }
  .percent {
    @apply !text-gray-G2 font-semibold;
  }