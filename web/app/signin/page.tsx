'use client'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { ConfigProvider, Tabs } from 'antd'
import { useRouter } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import Link from 'next/link'
import style from './page.module.css'
import SsoPasswordLoginForm from './sso-password-login-form'
import SsoCodeLoginForm from './sso-code-login-form'
import CodeLoginForm from './code-login-form'
import PasswordLoginForm from './password-login-form'
import { GRAY } from '@/themes/var-define'

import Loading from '@/app/components/base/loading'
import Logo from '@/app/components/base/logo'
import { languages } from '@/i18n/language'
import { useI18N } from '@/context/i18n'
import PopoverSelect from '@/app/components/base/select/popover-select'
import TextButton from '@/app/components/base/button/text-button'
import { ArrowDown } from '@/app/components/base/icons/src/vender/line/arrows'
import { Globe01 } from '@/app/components/base/icons/src/vender/line/mapsAndTravel'
import cn from '@/utils/classnames'
import { useSystemContext } from '@/context/system-context'

enum LoginType {
  code = 'Code',
  password = 'Password',
}

const SignIn = () => {
  const { t } = useTranslation()
  const { systemFeatures, isPrivate, canI18n, isSso } = useSystemContext()
  const { locale, setLocaleOnClient } = useI18N()
  const router = useRouter()
  // 登录方式
  const [loginType, setLoginType] = useState<LoginType>(LoginType.code)
  // 登录方式选项
  const loginOptions = useMemo(() => {
    // 是否支持账号登录
    const canPasswordLogin = systemFeatures.enable_email_password_login
    // 是否支持验证码登录
    const canCodeLogin = systemFeatures.enable_email_code_login
    const options = []
    canCodeLogin && options.push({
      key: LoginType.code,
      label: t('login.loginType.codeLogin'),
    })
    canPasswordLogin && options.push({
      key: LoginType.password,
      label: t('login.loginType.passwordLogin'),
    })
    return options
  }, [systemFeatures.enable_email_code_login, systemFeatures.enable_email_password_login, t])

  // 语言切换选项
  const languageOptions = useMemo(() => {
    return languages.map((item) => {
      return {
        key: item.value,
        label: item.name,
        title: item.name,
      }
    })
  }, [])

  const handleLogin = useCallback(() => {
  }, [])

  useEffect(() => {
    setLoginType(loginOptions[0]?.key as LoginType)
  }, [loginOptions])
  useEffect(() => {
    if (!isPrivate)
      router.replace('/')
  }, [isPrivate, router])

  if (!isPrivate)
    return <Loading type='app'></Loading>

  return (
    <div className={style.background}>
      <div className={`${style.wrap}`}>
        {/* 头部图标 */}
        <Logo className={style.header}></Logo>
        {/* 登录选项 */}
        {
          loginOptions.length > 1
                && <Tabs
                  className='signinTab'
                  activeKey={loginType}
                  onChange={ key => setLoginType(key as LoginType)}
                  items={loginOptions}
                ></Tabs>
        }
        <ConfigProvider theme={{
          components: {
            Form: {
              labelColor: GRAY.G2,
            },
          },
        }}>
          {/* 登录表单 */}
          {
            isSso
              ? <>
                { loginType === LoginType.password && <SsoPasswordLoginForm onLogin={handleLogin}></SsoPasswordLoginForm> }
                { loginType === LoginType.code && <SsoCodeLoginForm onLogin={handleLogin}></SsoCodeLoginForm> }
              </>
              : <>
                { loginType === LoginType.password && <PasswordLoginForm onLogin={handleLogin}/>}
                { loginType === LoginType.code && <CodeLoginForm onLogin={handleLogin}/>}
              </>
          }
        </ConfigProvider>
        <div className="flex justify-center items-center gap-3 w-hull h-6 mt-4 text-sm">
          <Link
            href='/register'
            target='_self' rel='noopener noreferrer'
            className='text-primary-P1'
          >{ t('login.action.register')}</Link>
        </div>
      </div>
      {/* 语言切换能力 */}
      {canI18n && <PopoverSelect
        options={languageOptions}
        defaultValue={locale}
        onChange={setLocaleOnClient}
        triggerNode={(open, value) => {
          return (
            <TextButton variant='hover-deep' className='flex items-center gap-1 absolute top-6 right-6' style={{ color: GRAY.G1 }} size='middle'>
              <Globe01 className='w-4 h-4'></Globe01>
              {languageOptions.find(item => item.key === value)?.label}
              <ArrowDown className={cn('w-4 h-4', open && 'rotate-180')} />
            </TextButton>
          )
        }}
      >
      </PopoverSelect>
      }
    </div>
  )
}

export default SignIn
