import { useTranslation } from 'react-i18next'
import { useContext } from 'use-context-selector'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Form, Input } from 'antd'
import { debounce } from 'lodash-es'
import { MD5 } from 'crypto-js'
import { logout, updateUserProfile } from '@/service/common'
/* 公共组件 */
import Modal from '@/app/components/base/modal'
import Button from '@/app/components/base/button'
import { ToastContext } from '@/app/components/base/toast'
import { validatePassword } from '@/utils/validate'
import { useAppContext } from '@/context/app-context'
import { ssoEditPassword } from '@/service/sso'
import { removeAuth } from '@/utils/user'
import { useSystemContext } from '@/context/system-context'

type EditPwdModalProps = {
  visible: boolean
  isReset: boolean
  onClose: () => void
}

const EditPwdModal = ({ visible, isReset, onClose }: EditPwdModalProps) => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  const { isMicroApp } = useAppContext()
  const { systemFeatures } = useSystemContext()
  const router = useRouter()

  const [form] = Form.useForm()
  const values = Form.useWatch([], form)
  // 是否可提交
  const [disabled, setDisabled] = useState(true)

  // 重置表单节点
  const resetPasswordForm = () => {
    form.resetFields()
  }
  // 保存密码
  const handleSavePassowrd = async () => {
    const { password, new_password } = form.getFieldsValue()
    try {
      if (systemFeatures.enable_sso_login) {
        const res = await ssoEditPassword({
          oldPassword: MD5(password).toString(),
          newPassword: MD5(new_password).toString(),
        })
        if (res.code !== '********')
          throw new Error(res.message)
      }
      else {
        await updateUserProfile({
          url: 'account/password',
          body: {
            ...form.getFieldsValue(),
          },
        })
      }

      notify({ type: 'success', message: t('common.actionMsg.modifiedSuccessfully') })
      onClose()
      resetPasswordForm()
      removeAuth()

      const res = await logout({
        url: '/logout',
        params: {},
      })
      if (res.code === '3002')
        window.location.href = res.data
      else
        window.location.reload()
    }
    catch (e) {
      notify({ type: 'error', message: (e as Error).message })
    }
  }

  useEffect(debounce(() => {
    form.validateFields({ validateOnly: true }).then(() => {
      setDisabled(false)
    },
    ).catch(() => {
      setDisabled(true)
    })
  }, 100), [form, values])

  return (
    <Modal
      title = {isReset ? t('account.action.resetPassword') : t('account.action.setPassword')}
      isShow={visible}
      closable
      onClose={onClose}
      className='!max-w-[400px] !w-[400px]'
      footer={
        <>
          <Button variant='secondary-accent' className='mr-4' onClick={() => {
            onClose()
            resetPasswordForm()
          }}>{t('common.operation.cancel')}</Button>
          <Button
            disabled={disabled}
            variant='primary'
            onClick={handleSavePassowrd}
          >
            {isReset ? t('common.operation.reset') : t('common.operation.save')}
          </Button>
        </>
      }
    >
      <Form form={form} layout='vertical'>
        {isReset && (
          <Form.Item
            label={t('account.info.currentPassword')}
            name={'password'}
            validateFirst={true}
            validateTrigger='onBlur'
            rules={[
              {
                required: true,
                whitespace: true,
              },
            ]}
          >
            <Input.Password placeholder={t('common.placeholder.input', { label: t('account.info.currentPassword') })!}/>
          </Form.Item>
        )}
        <Form.Item
          label={isReset ? t('account.info.newPassword') : t('account.info.passwordTitle')}
          validateTrigger='onBlur'
          name={'new_password'}
          required
          validateFirst={true}
          rules={[() => ({
            required: true,
            whitespace: true,
            validator: (_, value: string) => {
              return (validatePassword(value)) ? Promise.resolve() : Promise.reject(t('login.error.passwordInvalid'))
            },
          })]}
        >
          <Input.Password placeholder={t('common.placeholder.input', { label: isReset ? t('account.info.newPassword') : t('account.info.passwordTitle') })!} />
        </Form.Item>
        <Form.Item
          label={t('account.info.confirmPassword')}
          validateTrigger='onBlur'
          required
          name={'repeat_new_password'}
          validateFirst={true}
          rules={[({ getFieldValue }) => ({
            required: true,
            whitespace: true,
            validator: (_, value: string) => {
              return ((value === getFieldValue('new_password')) ? Promise.resolve() : Promise.reject(t('login.error.passwordSameInValid')))
            },
          })]}
        >
          <Input.Password placeholder={t('account.placeholder.confirmPwdPlaceholder')!} />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default EditPwdModal
