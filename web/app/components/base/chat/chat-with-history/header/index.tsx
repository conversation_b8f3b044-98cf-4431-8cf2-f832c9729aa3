import { memo, useCallback, useState } from 'react'
import { Alert } from 'antd'
import { useTranslation } from 'react-i18next'
import { useRouter, useSearchParams } from 'next/navigation'
import { useChatWithHistoryContext } from '../context'
import Sidebar from '../sidebar'
import s from './styles/index.module.css'
import type { forkAppData } from '@/service/market'
import { forkApp } from '@/service/market'
import type { AppMarket } from '@/types/app-market'
import { AppMarketPower } from '@/types/app-market'
// 公共能力
import { Fold } from '@/app/components/base/icons/src/vender/line/action'
import Logo from '@/app/components/base/logo'
import Button from '@/app/components/base/button'
import { useProviderContext } from '@/context/provider-context'
import Toast from '@/app/components/base/toast'
import { useAppContext } from '@/context/app-context'
import useBreakpoints, { MediaType } from '@/hooks/use-breakpoints'

// 移动端头部
const HeaderInMobile = () => {
  const {
    appParams,
  } = useChatWithHistoryContext()
  // 是否显示侧边栏
  const [showSidebar, setShowSidebar] = useState(false)
  return (
    <>
      <div className={s['chat-history-header']}
        style={{
          backgroundColor: appParams?.background_configs?.fontColor === 'white' ? 'rgba(24, 24, 24, 0.70)' : 'rgba(251, 251, 253, 0.80)',
          backdropFilter: 'blur(2px)',
        }}
      >
        <div
          className='shrink-0 flex items-center justify-center w-8 h-8 rounded-lg'
          onClick={() => setShowSidebar(true)}
        >
          <Fold className='text-gray-G3'></Fold>
        </div>
        {/* logo 图标 */}
        <Logo></Logo>
        {/* 用于对称 */}
        <div></div>
      </div>
      {
        showSidebar && (
          <div className='fixed inset-0 z-50'
            style={{ backgroundColor: 'rgba(35, 56, 118, 0.2)', zIndex: 999 }}
            onClick={() => setShowSidebar(false)}
          >
            <div className='inline-block h-full bg-white' onClick={e => e.stopPropagation()}>
              <Sidebar />
            </div>
          </div>
        )
      }
    </>
  )
}
// 桌面端头部
const HeaderInDesktop = () => {
  const { t } = useTranslation()
  const router = useRouter()
  const searchParams = useSearchParams()
  const {
    appData,
    fromMarket,
    appMarketData: marketAppData,
  } = useChatWithHistoryContext()
  const { onPlanInfoChanged, plan } = useProviderContext()
  const { canEditApp: canCreateApp } = useAppContext()
  // 应用id
  const appId = appData?.app_id
  // 是否可以创建应用
  const isAppsFull = plan.usage.buildApps >= plan.total.buildApps
  // 获取应用市场提示
  const getMarketAppForkTip = useCallback(() => {
    const forkTool = marketAppData?.tool_dataset_acl?.includes('tool')
    const forkDataset = marketAppData?.tool_dataset_acl?.includes('dataset')
    if (forkTool && forkDataset)
      return t('common.chat.market.datasetTooltip')

    else if (forkTool && !forkDataset)
      return t('common.chat.market.toolTip')

    else if (forkDataset && !forkTool)
      return t('common.chat.market.datasetTip')

    return t('common.chat.market.noDatasetTooltip')
  }, [marketAppData?.tool_dataset_acl, t])
  // 创建同款
  const createApp = async () => {
    if (!appId)
      return
    if (isAppsFull) {
      Toast.notify({
        type: 'error',
        message: t('app.notify.appFull', { num: plan.total.buildApps }),
      })
      return
    }
    try {
      const { name, description, icon_type, icon, icon_background } = marketAppData as AppMarket
      const forkAppData = (await forkApp(appId, {
        app_id: appId,
        name,
        description,
        icon_type,
        icon,
        icon_background,
      })) as forkAppData
      Toast.notify({
        type: 'success',
        message: t('common.chat.market.createSuccessfully'),
      })
      onPlanInfoChanged()

      const appURL = `/app/${forkAppData.id}/configuration`
      router.push(appURL)
    }
    catch (err) {
      Toast.notify({
        type: 'error',
        message: t('common.chat.market.createFailed'),
      })
    }
  }

  return (
    <div className={s['chat-history-header-normal']}>
      <Logo></Logo>
      {/* 如果从应用广场进入 */}
      {fromMarket && canCreateApp && marketAppData?.acl !== AppMarketPower.View && (
        <div className='flex items-center h-full gap-3'>
          <Alert message={getMarketAppForkTip()} type={'warning'} showIcon></Alert>
          <Button onClick={createApp} variant={'primary'}>{t('common.chat.market.createAppBtn') }</Button>
        </div>
      )}
    </div>
  )
}

const Header = () => {
  const media = useBreakpoints()
  const isMobile = media === MediaType.mobile
  return (
    <>
      {
        isMobile && <HeaderInMobile />
      }
      {
        !isMobile && <HeaderInDesktop />
      }
    </>
  )
}

export default memo(Header)
