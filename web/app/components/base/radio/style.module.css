.label {
    position: relative;
    margin-right: 3px;
}

.label:last-child {
    margin-right: 0;
}

.radio-group-default {   
}
.radio-group-info {
  @apply bg-gray-50;
  padding: 4px;
  border-radius: 4px;
}
.radio-button-default {
  @apply h-[36px] flex items-center justify-center rounded border border-gray-G4 text-gray-G1 bg-white hover:border-primary-P1 hover:text-primary-P1;
  position: relative;
  padding: 0px 8px;
  cursor: pointer;
}
.radio-button-default.active {
  @apply !border-primary-P1 !text-primary-P1;
}
.radio-button-default.disabled {
  @apply !border-gray-G5 !text-gray-G4 !cursor-not-allowed;
}
.radio-button-info {
  @apply flex items-center py-[6px] relative px-7 cursor-pointer hover:bg-white rounded text-S1 leading-H1 text-gray-G2 font-normal;
}
.radio-button-info.active {
  @apply bg-white !text-gray-G1 !font-semibold;
}
.radio-button-info.disabled {
  @apply !cursor-not-allowed;
}