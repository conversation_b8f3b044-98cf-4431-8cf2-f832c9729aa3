'use client'
import React, { useCallback, useRef, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Divider } from 'antd'
import StepOne from './step-one'
import StepTwo from './step-two'
import type { FileItem, VideoStreamConfig } from '@/types/videos'

import style from '@/app/components/videos/styles/style.module.css'
import { useVideoCreateMode } from '@/app/components/videos/hook'
// 公共组件
import Scrollbar from '@/app/components/base/scrollbar'
import { useVideoDetailContext } from '@/context/video-detail'
type VideosUpdateFormProps = {
  videoId?: string
}

export type VideoInfoType = {
  name: string
  description?: string
  voice_stream_config?: Array<VideoStreamConfig>
  files: FileItem[]
}

const VideosUpdateForm = ({ videoId }: VideosUpdateFormProps) => {
  const router = useRouter()
  const { video } = useVideoDetailContext()
  // 当前视频库类型
  const { isImageMode } = useVideoCreateMode(video!.type)
  // 第一步表单节点
  const stepOneRef = useRef<{
    getFormInfo: () => Promise<VideoInfoType | undefined>
  }>()

  // 视频库创建是否准备完成
  const [ready, setReady] = useState(false)

  // 返回上一步
  const navBackHandle = useCallback(() => {
    if (!videoId)
      router.replace('/videos')
    else
      router.replace(`/videos/${videoId}/documents`)
  }, [router, videoId])
  return (
    <Scrollbar className={style['middle-wrap']}>
      <StepOne
        id={video?.id}
        type={video!.type}
        ref={stepOneRef}
        updateReady={setReady}
      />
      {!isImageMode && <Divider type='horizontal' className='!mb-5 !mt-0'></Divider>}
      <StepTwo
        type={video!.type}
        ready={ready}
        onSave={navBackHandle}
        getFormInfo={stepOneRef.current?.getFormInfo}
      />
    </Scrollbar>
  )
}

export default VideosUpdateForm
