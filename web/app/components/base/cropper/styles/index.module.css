.cropper-container {
  @apply relative;
}
.cropper-floating {
  @apply w-full h-full top-0 left-0 absolute z-[200] pointer-events-none;
}
.cropper-vague {
  @apply w-full h-full top-0 left-0 absolute z-[100] pointer-events-none;
}
.cropper-vague-item {
  @apply h-full absolute top-0 left-0;
}
.cropper-loading {
  @apply flex items-center justify-center left-1/2 -translate-x-1/2 text-white absolute;
  gap: 2px;
  width: 68px;
  height: 24px;
  border-radius: 100px;
  background: rgba(92, 98, 115, 0.50);
  backdrop-filter: blur(8px);
  top: 16px;
}

.cropper-wrapper {
  @apply relative h-full overflow-hidden;
}
.cropper-wrapper-container {
  @apply w-full h-full absolute top-0 left-0 z-[200] flex flex-col;
  background: rgba(0,0,0,.12);
}
.cropper-wrapper-content {
  @apply h-0 grow relative flex flex-col;
}
.cropper-wrapper-header {
  @apply w-full;
}
.cropper-top-vague {
  @apply absolute pointer-events-none w-full;
  background: linear-gradient(180deg,rgba(0,0,0,.32), transparent);
  height: 60px;
  top: 0px;
}