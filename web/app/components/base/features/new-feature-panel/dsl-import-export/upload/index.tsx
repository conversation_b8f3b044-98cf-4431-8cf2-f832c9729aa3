'use client'
import type { FC } from 'react'
import React, { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useContext } from 'use-context-selector'
import { Divider } from 'antd'
import s from './style.module.css'
// 公共能力
import cn from '@/utils/classnames'
import { ToastContext } from '@/app/components/base/toast'
import { Delete, Upload01 } from '@/app/components/base/icons/src/vender/line/general'
import FileIcon from '@/app/components/base/file-icon'
import TextButton from '@/app/components/base/button/text-button'

export type Props = {
  file: File | undefined
  updateFile: (file?: File) => void
  className?: string
}

const Uploader: FC<Props> = ({
  file,
  updateFile,
}) => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  // 是否正在拖动
  const [dragging, setDragging] = useState(false)
  const dropRef = useRef<HTMLDivElement>(null)
  const dragRef = useRef<HTMLDivElement>(null)
  const fileUploader = useRef<HTMLInputElement>(null)

  const handleDragEnter = (e: DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    e.target !== dragRef.current && setDragging(true)
  }
  const handleDragOver = (e: DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }
  const handleDragLeave = (e: DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    e.target === dragRef.current && setDragging(false)
  }
  const handleDrop = (e: DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragging(false)
    if (!e.dataTransfer)
      return
    const files = [...e.dataTransfer.files]
    if (files.length > 1) {
      notify({ type: 'error', message: t('common.fileUploader.validation.count') })
      return
    }
    updateFile(files[0])
  }
  const selectHandle = () => {
    if (fileUploader.current)
      fileUploader.current.click()
  }
  const removeFile = () => {
    if (fileUploader.current)
      fileUploader.current.value = ''
    updateFile()
  }
  const fileChangeHandle = (e: React.ChangeEvent<HTMLInputElement>) => {
    const currentFile = e.target.files?.[0]
    updateFile(currentFile)
  }

  useEffect(() => {
    dropRef.current?.addEventListener('dragenter', handleDragEnter)
    dropRef.current?.addEventListener('dragover', handleDragOver)
    dropRef.current?.addEventListener('dragleave', handleDragLeave)
    dropRef.current?.addEventListener('drop', handleDrop)
    return () => {
      dropRef.current?.removeEventListener('dragenter', handleDragEnter)
      dropRef.current?.removeEventListener('dragover', handleDragOver)
      dropRef.current?.removeEventListener('dragleave', handleDragLeave)
      dropRef.current?.removeEventListener('drop', handleDrop)
    }
  }, [])

  return (
    <>
      <input
        ref={fileUploader}
        style={{ display: 'none' }}
        type="file"
        id="fileUploader"
        accept='.yml'
        onChange={fileChangeHandle}
      />
      <div ref={dropRef} className={cn(s.uploader, dragging && s.dragging)}>
        {!file && (
          <>
            <Upload01 className={s.uploadIcon}></Upload01>
            <div className={s.uploadTitle}>
              {t('common.fileUploader.button')}
              <label className={s.browse} onClick={selectHandle}>{t('common.fileUploader.browse')}</label>
            </div>
            {dragging && <div ref={dragRef} className={s.draggingCover} />}
          </>
        )}
        {/* 存在文件的情况下 */}
        {file && (
          <div className={cn('flex items-center h-20 w-full px-6 rounded bg-gray-50 border border-gray-200 text-sm font-normal group', 'hover:bg-[#F5F8FF] hover:border-[#B2CCFF]')}>
            {/* 文件图标 */}
            <FileIcon type='yaml'></FileIcon>
            {/* 文件名 */}
            <div className='flex ml-2 w-0 grow'>
              <span className='max-w-[calc(100%_-_30px)] text-ellipsis whitespace-nowrap overflow-hidden text-gray-G2'>{file.name.replace(/(.yaml|.yml)$/, '')}</span>
              <span className='shrink-0 text-gray-G2'>.yml</span>
            </div>
            {/* 操作栏 */}
            <div className='hidden group-hover:flex items-center'>
              <TextButton
                variant='primary'
                onClick={(e) => {
                  e.stopPropagation()
                  selectHandle()
                }}
              >
                {t('common.operation.reUpload')}
              </TextButton>
              <Divider type='vertical' className='!mx-2' />
              <TextButton variant='text' onClick={removeFile}>
                <Delete className='w-4 h-4' />
              </TextButton>
            </div>
          </div>
        )}
      </div>
    </>
  )
}

export default React.memo(Uploader)
