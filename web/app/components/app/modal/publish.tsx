'use client'

import { useTranslation } from 'react-i18next'
import { Form, Input, Radio } from 'antd'
import { useState } from 'react'
import { useMount } from 'ahooks'
import { getRandomAppHead } from '../utils'
import { fetchMarketCategories } from '@/service/market'
import type { App } from '@/types/app'
import { AppMarketPower } from '@/types/app-market'
import { MAX_APP_DESC_LENGTH, MAX_APP_NAME_LENGTH } from '@/config'
// 公共组件
import Button from '@/app/components/base/button'
import Modal from '@/app/components/base/modal'
import Avatar from '@/app/components/base/avatar'
import Select from '@/app/components/base/select/new-index'
import { useFormDisabled } from '@/hooks/use-form'

type PublishAppModalProps = {
  app: App
  onCancel: () => void
  onSave: (params: any) => void
}

const PublishAppModal = ({
  onCancel,
  onSave,
}: PublishAppModalProps) => {
  const { t } = useTranslation()
  const [form] = Form.useForm()
  const disabled = useFormDisabled(form)
  // 应用头像
  const [appAvatar, setAppAvatar] = useState(getRandomAppHead())
  // 应用类别列表
  const [appCategoriesList, setAppCategoriesList] = useState<Array<any>>([])
  // 应用权限列表
  const appPermissionList = [{
    label: t('app.appPermission.copy'),
    value: AppMarketPower.Copy,
  }, {
    label: t('app.appPermission.view'),
    value: AppMarketPower.View,
  }]

  // 发布应用
  const publishApp = async () => {
    const params = {
      ...form.getFieldsValue(),
      icon: appAvatar,
    }
    await onSave(params)
  }
  // 获取应用种类列表
  const getAppCategoriesList = async () => {
    await fetchMarketCategories().then((res) => {
      setAppCategoriesList(
        res.map(item => ({
          value: item.id,
          label: item.name,
        })),
      )
    })
  }

  useMount(() => {
    getAppCategoriesList()
  })

  return (
    <Modal
      isShow
      className="max-w-[480px]"
      closable
      onClose={onCancel}
      title={t('app.modalTitle.publishMarket')}
      footer={
        <>
          <Button onClick={onCancel} className="mr-4" variant={'secondary-accent'}>{ t('common.operation.cancel') }</Button>
          <Button disabled={disabled} onClick={publishApp} variant={'primary'}>{ t('common.operation.confirm') }</Button>
        </>
      }
    >
      <Form form={form} layout='vertical' initialValues={{ acl: AppMarketPower.Copy }}>
        <Form.Item
          required
          label={t('app.info.appName')}
        >
          <div className='flex items-center gap-2'>
            <Avatar
              avatar={appAvatar}
              size={36}
              showUpload
              onChange={setAppAvatar}
              className='!rounded'
            ></Avatar>
            <Form.Item
              name="name"
              label={t('app.info.appName')}
              rules={[
                {
                  required: true,
                  whitespace: true,
                  max: MAX_APP_NAME_LENGTH,
                },
              ]}
              validateTrigger='onBlur'
              noStyle
            >
              <Input placeholder={t('app.placeholder.appName') as string} maxLength={MAX_APP_NAME_LENGTH}></Input>
            </Form.Item>
          </div>
        </Form.Item>
        <Form.Item
          name="description"
          label={t('app.info.appDescription')}
          rules={[
            {
              required: true,
              whitespace: true,
              max: MAX_APP_DESC_LENGTH,
            },
          ]}
        >
          <Input.TextArea
            placeholder={t('app.placeholder.appDescription') as string}
            maxLength={MAX_APP_DESC_LENGTH}
          ></Input.TextArea>
        </Form.Item>
        <Form.Item
          name="category_id"
          label={t('app.info.appCategories')}
          validateFirst={true}
          rules={[{ required: true }]}
          validateTrigger='onChange'
        >
          <Select options={appCategoriesList}></Select>
        </Form.Item>
        <Form.Item
          name="acl"
          label={t('app.info.appPermission')}
          required
        >
          <Radio.Group options={appPermissionList}></Radio.Group>
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default PublishAppModal
