import React, { useCallback, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import produce from 'immer'
import FeatureCollapse from '@/app/components/base/features/feature-collapse'
import SettingModal from '@/app/components/base/features/new-feature-panel/file-upload/setting-modal'
import { useFeatures, useFeaturesStore } from '@/app/components/base/features/hooks'
import type { OnFeaturesChange } from '@/app/components/base/features/types'
import { FeatureEnum } from '@/app/components/base/features/types'
import { Setting } from '@/app/components/base/icons/src/vender/line/general'
import TextButton from '@/app/components/base/button/text-button'

type Props = {
  disabled: boolean
  onChange?: OnFeaturesChange
}

const FileUpload = ({
  disabled,
  onChange,
}: Props) => {
  const { t } = useTranslation()
  const file = useFeatures(s => s.features.file)
  const inWorkflow = useFeatures(s => s.inWorkflow)
  const featuresStore = useFeaturesStore()
  const [modalOpen, setModalOpen] = useState(false)
  const [isHovering, setIsHovering] = useState(false)

  const supportedTypes = useMemo(() => {
    return file?.allowed_file_types?.join(',') || '-'
  }, [file?.allowed_file_types])

  const handleChange = useCallback((type: FeatureEnum, enabled: boolean) => {
    const {
      features,
      setFeatures,
    } = featuresStore!.getState()

    const newFeatures = produce(features, (draft) => {
      draft[type] = {
        ...draft[type],
        enabled,
      }
    })
    setFeatures(newFeatures)
    if (onChange)
      onChange()
  }, [featuresStore, onChange])

  return (
    <FeatureCollapse
      inWorkflow={inWorkflow}
      title={t('appDebug.feature.fileUpload.title')}
      tooltip={t('appDebug.feature.fileUpload.description')}
      value={file?.enabled}
      onChange={state => handleChange(FeatureEnum.file, state)}
      // onMouseEnter={() => setIsHovering(true)}
      // onMouseLeave={() => setIsHovering(false)}
      disabled={disabled}
      headerRight={(
        <div className='flex gap-4'>
          <SettingModal
            open={modalOpen && !disabled}
            onOpen={(v) => {
              setModalOpen(v)
              setIsHovering(v)
            }}
            onChange={onChange}
          >
            <TextButton variant={'text'} type={'text'} disabled={!file?.enabled || disabled}>
              <Setting/>
              {t('common.operation.settings')}
            </TextButton>
          </SettingModal>
        </div>
      )}
    >
      {/* <>
        {file?.enabled && (
          <>
            {!isHovering && !modalOpen && (
              <div className='pt-0.5 flex items-center gap-4'>
                <div className=''>
                  <div className='mb-0.5 text-text-tertiary system-2xs-medium-uppercase'>{t('appDebug.feature.fileUpload.supportedTypes')}</div>
                  <div className='text-text-secondary system-xs-regular'>{supportedTypes}</div>
                </div>
                <div className='w-px h-[27px] bg-divider-subtle rotate-12'></div>
                <div className=''>
                  <div className='mb-0.5 text-text-tertiary system-2xs-medium-uppercase'>{t('appDebug.feature.fileUpload.numberLimit')}</div>
                  <div className='text-text-secondary system-xs-regular'>{file?.number_limits}</div>
                </div>
              </div>
            )}
            <SettingModal
              open={modalOpen && !disabled}
              onOpen={(v) => {
                setModalOpen(v)
                setIsHovering(v)
              }}
              onChange={onChange}
            >
              <Button className='w-full' disabled={disabled}>
                <RiEqualizer2Line className='mr-1 w-4 h-4' />
                {t('common.operation.settings')}
              </Button>
            </SettingModal>
          </>
        )}
      </> */}
    </FeatureCollapse>
  )
}

export default FileUpload
