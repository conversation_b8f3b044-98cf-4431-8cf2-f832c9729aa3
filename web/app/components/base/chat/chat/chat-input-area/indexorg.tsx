/* eslint-disable multiline-ternary */
import { useCallback, useRef, useState } from 'react'
import Textarea from 'rc-textarea'
import { useTranslation } from 'react-i18next'
import Recorder from 'js-audio-recorder'
import { MessageOutlined } from '@ant-design/icons'
import type { OnSend } from '../../types'
import type { Theme } from '../../embedded-chatbot/theme/theme-context'
import type { InputForm } from '../type'
import { useChatWithHistoryContext } from '../../chat-with-history/context'
import { XCircle } from '../../../icons/src/vender/solid/general'
import VoiceInput from '../../../voice-input'
import NewChat from './new-chat'
import { useCheckInputsForms, useTextAreaHeight } from './hooks'
import Operation from './operation'
import formRules from './formRules'
import style from './styles/style.module.css'
import ResearchReportInput from './ReportInput'
import useBreakpoints, { MediaType } from '@/hooks/use-breakpoints'
import cn from '@/utils/classnames'
import { FileListInChatInput, FileUploaderInChatInputMobile } from '@/app/components/base/file-uploader'
import { useFile } from '@/app/components/base/file-uploader/hooks'
import { FileContextProvider, useFileStore } from '@/app/components/base/file-uploader/store'
// import { Icon } from '@ant-design/icons';
// import VoiceInput from '@/app/components/base/voice-input'
import { useToastContext } from '@/app/components/base/toast'
// import FeatureBar from '@/app/components/base/features/new-feature-panel/feature-bar'
import type { FileUpload } from '@/app/components/base/features/types'
import { TransferMethod } from '@/types/app'
import { GetTemplateList } from '@/service/share'

type ChatInputAreaProps = {
  // 展示特性栏
  showFeatureBar?: boolean
  // 展示文件上床
  showFileUpload?: boolean
  // 特性栏disabled
  featureBarDisabled?: boolean
  // 特性栏点击
  onFeatureBarClick?: (state: boolean) => void
  // 文件上传配置
  visionConfig?: FileUpload
  // speechToTextConfig?: EnableType
  // 是否正在响应
  isResponding?: boolean

  //hq 判断是否以生成长文 
  isLongTxt?:boolean

  inputs?: Record<string, any>
  inputsForm?: InputForm[]
  theme?: Theme | null
  // 显示输入框底部提示
  showInputBottomTip?: boolean
  // 发送函数
  onSend?: OnSend
  // 停止响应
  onStopResponding?: () => void
  // 开启新对话
  handleNewConversation?: () => void
  // 移动端显示底部上传卡片
  handleClickShowFiles?: () => void
}

const ChatInputArea = ({
  // showFeatureBar,
  // showFileUpload,
  // featureBarDisabled,
  // onFeatureBarClick,
  visionConfig,
  // speechToTextConfig = { enabled: true },
  isResponding = false,
  isLongTxt=false,
  onSend,
  onStopResponding,
  inputs = {},
  inputsForm = [],
  theme,
  showInputBottomTip = true,
  handleNewConversation,
}: ChatInputAreaProps) => {
  const media = useBreakpoints()
  const isMobile = media === MediaType.mobile
  const { t } = useTranslation()
  const filesStore = useFileStore()
  const { notify } = useToastContext()
  const {
    wrapperRef,
    textareaRef,
    textValueRef,
    holdSpaceRef,
    handleTextareaResize,
    isMultipleLine,
  } = useTextAreaHeight()
  const longTxtForm = inputsForm.find(form => form.type === 'longTxt')

  const {
    handleDragFileEnter,
    handleDragFileLeave,
    handleDragFileOver,
    handleDropFile,
    handleClipboardPasteFile,
    isDragActive,
  } = useFile(visionConfig!)

  // 提问信息
  const [query, setQuery] = useState('')
  // 是否正在组合输入
  const isUseInputMethod = useRef(false)
  // 是否展示语音输入
  const [showVoiceInput, setShowVoiceInput] = useState(false)

  // 移动端文件卡片是否显示
  const [showFiles, setShowFiles] = useState(false)

  const { checkInputsForm } = useCheckInputsForms()
  const reportInputRef = useRef<{ getContent: () => string }>(null)

  const { chatAllTypeResult, setChatAllTypeResult, appId } = useChatWithHistoryContext()

  // 发送提问
  const handleSend = () => {
    if (onSend) {
      const inputContent = reportInputRef.current?.getContent() || ''
      if (longTxtForm || isLongTxt) {
        if ((!inputContent || !inputContent.trim()) && !isLongTxt) {
          notify({ type: 'warning', message: t('sampleTemplate.warning.FormRequired') })
          return
        }
        const { files, setFiles } = filesStore.getState()
        if (
          files.find(item => item.transferMethod === TransferMethod.local_file && !item.uploadedId)
        ) {
          notify({ type: 'info', message: t('appDebug.errorMessage.waitForFileUpload') })
          return
        }

        //hq 判断是否以生成长文,生成长文后发送指令
        if(isLongTxt){
          onSend(query,files,null)
          setQuery('')
          setFiles([])
          return;
        }

        formRules(chatAllTypeResult, t, notify, appId, GetTemplateList).then((result: boolean) => {
          if (result && checkInputsForm(inputs, inputsForm)) {
            onSend(inputContent, files)
            setQuery('')
            setFiles([])
            setChatAllTypeResult({})
          }
        })
      }
      else {
        const { files, setFiles } = filesStore.getState()
        if (
          files.find(item => item.transferMethod === TransferMethod.local_file && !item.uploadedId)
        ) {
          notify({ type: 'info', message: t('appDebug.errorMessage.waitForFileUpload') })
          return
        }
        if ((!query || !query.trim()) && !files.length) {
          notify({ type: 'info', message: t('appAnnotation.errorMessage.queryRequired') })
          return
        }
        if (checkInputsForm(inputs, inputsForm)) {
          onSend(query, files)
          setQuery('')
          setFiles([])
        }
      }
    }
  }
  // enter按键按下处理
  const handleKeyUp = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.code === 'Enter') {
      e.preventDefault()
      // prevent send message when using input method enter
      if (!e.shiftKey && !isUseInputMethod.current)
        handleSend()
    }
  }
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // 是否正在组合输入
    isUseInputMethod.current = e.nativeEvent.isComposing
    if (e.code === 'Enter' && !e.shiftKey) {
      setQuery(query.replace(/\n$/, ''))
      e.preventDefault()
    }
  }
  // 展示音频输入
  const handleShowVoiceInput = useCallback(() => {
    (Recorder as any).getPermission().then(
      () => {
        setShowVoiceInput(true)
      },
      () => {
        notify({ type: 'error', message: t('common.voiceInput.notAllow') })
      },
    )
  }, [t, notify])

  const handleClickShowFiles = () => {
    setShowFiles(!showFiles)
  }

  const operation = (
    <Operation
      ref={holdSpaceRef}
      query={query}
      fileConfig={visionConfig}
      // speechToTextConfig={speechToTextConfig}
      isResponding={isResponding}
      onSend={handleSend}
      onStopResponding={onStopResponding}
      handleClickShowFiles={handleClickShowFiles}
    />
  )

  return longTxtForm ? (
    Object.keys(chatAllTypeResult.form || {}).length > 0 ? (
      <div className="flex  items-center justify-center w-full gap-2">
        <div className='xq-no-scale messageDiv flex  items-center justify-center'><MessageOutlined /></div>
        <div className="flex items-center justify-end w-full gap-3">
          {handleNewConversation && <NewChat onClick={handleNewConversation} />}
          <div
            className={cn(
              style.chatInput,
              'rounded',
              !handleNewConversation && '!w-full',
              isDragActive
                    && 'border border-dashed border-components-option-card-option-selected-border',
            )}
          >
            <div className={cn('relative')}>
              <div className="flex flex-col items-start max-h-[150px] bg-white border border-gray-G5 rounded overflow-y-auto">
                <FileListInChatInput fileConfig={visionConfig!} />
                <div ref={wrapperRef} className="flex items-center justify-between w-full">
                  <div className="relative flex items-center w-full grow">
                    <ResearchReportInput ref={reportInputRef} />
                  </div>
                  <div
                    className={cn(
                      'absolute top-0 bottom-0 h-full flex items-center',
                      'right-3',
                    )}
                  >
                    {query
                      ? (
                        <div
                          className="flex items-center justify-center w-8 h-8 ml-2 cursor-pointer"
                          onClick={() => setQuery('')}
                        >
                          <XCircle className="w-4 h-4 text-gray-G5 hover:text-gray-G4" />
                        </div>
                      )
                      : null}

                    {!isMultipleLine && operation}
                    {showVoiceInput && (
                      <VoiceInput
                        onCancel={() => setShowVoiceInput(false)}
                        onConverted={text => setQuery(text)}
                      />
                    )}
                    {isMultipleLine && <div className="px-[9px]">{operation}</div>}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {visionConfig?.enabled && isMobile && showFiles && (
          <>
            <FileUploaderInChatInputMobile fileConfig={visionConfig!} />
          </>
        )}
        {showInputBottomTip && (
          <div className="text-[12px] leading-H1 text-gray-G4 text-center mt-2 pb-3">
            {t('common.chat.tip')}
          </div>
        )}
        {/* {showFeatureBar && (
        <FeatureBar
          showFileUpload={showFileUpload}
          disabled={featureBarDisabled}
          onFeatureBarClick={onFeatureBarClick}
        />
      )} */}
      </div>
    ) : 
    (
      isLongTxt?
      <div>
       <div className='xq-flex-around xq-flex-vcenter'>
          
        </div> 
       <div className="flex  items-center justify-center w-full gap-2">
        <div className='xq-no-scale messageDiv flex  items-center justify-center'><MessageOutlined /></div>
        <div className="w-full gap-3">
          <div className='xq-nowrap xq-flex-vcenter'>
            <div className='xq-nowrap xq-flex-vcenter inputbtn' onClick={() => setQuery('全文润色:')}>
              <svg  className="xqicon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6820" width="200" height="200"><path d="M768.093013 73.386667L736.235236 14.791111c-11.377778-19.911111-39.822222-19.911111-51.2 0l-31.857778 58.595556c-2.844444 4.551111-6.826667 8.533333-11.377778 11.377777l-58.595556 32.426667c-20.48 10.808889-20.48 39.822222 0 50.631111l58.595556 31.857778c4.551111 2.844444 8.533333 6.826667 11.377778 11.946667l31.857778 58.595555c11.377778 19.911111 39.822222 19.911111 51.2 0l31.857777-58.595555c2.844444-5.12 6.826667-9.102222 11.377778-11.946667l58.595556-31.857778c19.911111-10.808889 19.911111-39.822222 0-50.631111l-58.595556-32.426667a36.181333 36.181333 0 0 1-11.377778-11.377777z m81.351111 923.306666l-123.448888 24.462223c-46.08 9.102222-92.728889-8.533333-120.035556-46.08L30.244124 226.986667C-22.093653 157.013333 17.15968 57.457778 103.061902 39.822222L227.07968 15.36c45.511111-9.102222 92.16 9.102222 120.035556 46.08L922.830791 809.528889c51.768889 69.973333 12.515556 170.097778-73.386667 187.164444z m-17.066666-85.333333c5.12-1.137778 9.102222-2.844444 13.084444-5.12 1.137778-1.706667 5.12-4.551111 6.257778-6.257778 1.706667-2.275556 3.413333-5.688889 5.12-8.533333 1.706667-5.688889 2.844444-10.808889 1.706667-15.928889a27.932444 27.932444 0 0 0-5.688889-14.222222L466.013013 358.4 285.106347 416.426667l389.688889 505.742222a32.995556 32.995556 0 0 0 23.893333 14.222222q1.706667 0.568889 3.413333 0.568889 3.413333 0 6.257778-1.137778l124.017778-24.462222zM228.786347 343.04l180.906666-58.026667-131.413333-170.666666a36.295111 36.295111 0 0 0-14.791111-11.946667 29.923556 29.923556 0 0 0-9.671111-2.275556c-3.413333-0.568889-6.257778 0-9.671111 0.568889l-124.017778 24.462223c-4.551111 1.137778-9.102222 2.844444-13.084445 5.688888l-5.688888 5.688889c-2.275556 2.844444-3.982222 5.688889-5.688889 9.102223a30.72 30.72 0 0 0 4.551111 30.151111L228.786347 343.04z m742.968889-2.275556l-18.773334-34.702222a11.776 11.776 0 0 0-20.48 0l-18.773333 34.702222c-1.137778 1.706667-2.844444 3.413333-5.12 4.551112l-34.702222 18.773333c-7.964444 4.551111-7.964444 15.928889 0 20.48l34.702222 19.342222c2.275556 0.568889 3.982222 2.275556 5.12 4.551111l18.773333 34.702222c4.551111 7.964444 15.928889 7.964444 20.48 0l18.773334-34.702222c1.137778-2.275556 2.844444-3.982222 4.551111-4.551111l34.702222-19.342222c7.964444-4.551111 7.964444-15.928889 0-20.48l-34.702222-18.773333a17.294222 17.294222 0 0 1-4.551111-4.551112z" p-id="6821"></path></svg>
              <div className='xqword'>全文润色</div>
            </div>
            <div className='xq-nowrap xq-flex-vcenter inputbtn' onClick={() => setQuery('全文扩写:')}>
              <svg  className="xqicon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6820" width="200" height="200"><path d="M768.093013 73.386667L736.235236 14.791111c-11.377778-19.911111-39.822222-19.911111-51.2 0l-31.857778 58.595556c-2.844444 4.551111-6.826667 8.533333-11.377778 11.377777l-58.595556 32.426667c-20.48 10.808889-20.48 39.822222 0 50.631111l58.595556 31.857778c4.551111 2.844444 8.533333 6.826667 11.377778 11.946667l31.857778 58.595555c11.377778 19.911111 39.822222 19.911111 51.2 0l31.857777-58.595555c2.844444-5.12 6.826667-9.102222 11.377778-11.946667l58.595556-31.857778c19.911111-10.808889 19.911111-39.822222 0-50.631111l-58.595556-32.426667a36.181333 36.181333 0 0 1-11.377778-11.377777z m81.351111 923.306666l-123.448888 24.462223c-46.08 9.102222-92.728889-8.533333-120.035556-46.08L30.244124 226.986667C-22.093653 157.013333 17.15968 57.457778 103.061902 39.822222L227.07968 15.36c45.511111-9.102222 92.16 9.102222 120.035556 46.08L922.830791 809.528889c51.768889 69.973333 12.515556 170.097778-73.386667 187.164444z m-17.066666-85.333333c5.12-1.137778 9.102222-2.844444 13.084444-5.12 1.137778-1.706667 5.12-4.551111 6.257778-6.257778 1.706667-2.275556 3.413333-5.688889 5.12-8.533333 1.706667-5.688889 2.844444-10.808889 1.706667-15.928889a27.932444 27.932444 0 0 0-5.688889-14.222222L466.013013 358.4 285.106347 416.426667l389.688889 505.742222a32.995556 32.995556 0 0 0 23.893333 14.222222q1.706667 0.568889 3.413333 0.568889 3.413333 0 6.257778-1.137778l124.017778-24.462222zM228.786347 343.04l180.906666-58.026667-131.413333-170.666666a36.295111 36.295111 0 0 0-14.791111-11.946667 29.923556 29.923556 0 0 0-9.671111-2.275556c-3.413333-0.568889-6.257778 0-9.671111 0.568889l-124.017778 24.462223c-4.551111 1.137778-9.102222 2.844444-13.084445 5.688888l-5.688888 5.688889c-2.275556 2.844444-3.982222 5.688889-5.688889 9.102223a30.72 30.72 0 0 0 4.551111 30.151111L228.786347 343.04z m742.968889-2.275556l-18.773334-34.702222a11.776 11.776 0 0 0-20.48 0l-18.773333 34.702222c-1.137778 1.706667-2.844444 3.413333-5.12 4.551112l-34.702222 18.773333c-7.964444 4.551111-7.964444 15.928889 0 20.48l34.702222 19.342222c2.275556 0.568889 3.982222 2.275556 5.12 4.551111l18.773333 34.702222c4.551111 7.964444 15.928889 7.964444 20.48 0l18.773334-34.702222c1.137778-2.275556 2.844444-3.982222 4.551111-4.551111l34.702222-19.342222c7.964444-4.551111 7.964444-15.928889 0-20.48l-34.702222-18.773333a17.294222 17.294222 0 0 1-4.551111-4.551112z" p-id="6821"></path></svg>
              <div className='xqword'>全文扩写</div>
            </div>
            <div className='xq-nowrap xq-flex-vcenter inputbtn' onClick={() => setQuery('全文缩写:')}>
              <svg  className="xqicon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6820" width="200" height="200"><path d="M768.093013 73.386667L736.235236 14.791111c-11.377778-19.911111-39.822222-19.911111-51.2 0l-31.857778 58.595556c-2.844444 4.551111-6.826667 8.533333-11.377778 11.377777l-58.595556 32.426667c-20.48 10.808889-20.48 39.822222 0 50.631111l58.595556 31.857778c4.551111 2.844444 8.533333 6.826667 11.377778 11.946667l31.857778 58.595555c11.377778 19.911111 39.822222 19.911111 51.2 0l31.857777-58.595555c2.844444-5.12 6.826667-9.102222 11.377778-11.946667l58.595556-31.857778c19.911111-10.808889 19.911111-39.822222 0-50.631111l-58.595556-32.426667a36.181333 36.181333 0 0 1-11.377778-11.377777z m81.351111 923.306666l-123.448888 24.462223c-46.08 9.102222-92.728889-8.533333-120.035556-46.08L30.244124 226.986667C-22.093653 157.013333 17.15968 57.457778 103.061902 39.822222L227.07968 15.36c45.511111-9.102222 92.16 9.102222 120.035556 46.08L922.830791 809.528889c51.768889 69.973333 12.515556 170.097778-73.386667 187.164444z m-17.066666-85.333333c5.12-1.137778 9.102222-2.844444 13.084444-5.12 1.137778-1.706667 5.12-4.551111 6.257778-6.257778 1.706667-2.275556 3.413333-5.688889 5.12-8.533333 1.706667-5.688889 2.844444-10.808889 1.706667-15.928889a27.932444 27.932444 0 0 0-5.688889-14.222222L466.013013 358.4 285.106347 416.426667l389.688889 505.742222a32.995556 32.995556 0 0 0 23.893333 14.222222q1.706667 0.568889 3.413333 0.568889 3.413333 0 6.257778-1.137778l124.017778-24.462222zM228.786347 343.04l180.906666-58.026667-131.413333-170.666666a36.295111 36.295111 0 0 0-14.791111-11.946667 29.923556 29.923556 0 0 0-9.671111-2.275556c-3.413333-0.568889-6.257778 0-9.671111 0.568889l-124.017778 24.462223c-4.551111 1.137778-9.102222 2.844444-13.084445 5.688888l-5.688888 5.688889c-2.275556 2.844444-3.982222 5.688889-5.688889 9.102223a30.72 30.72 0 0 0 4.551111 30.151111L228.786347 343.04z m742.968889-2.275556l-18.773334-34.702222a11.776 11.776 0 0 0-20.48 0l-18.773333 34.702222c-1.137778 1.706667-2.844444 3.413333-5.12 4.551112l-34.702222 18.773333c-7.964444 4.551111-7.964444 15.928889 0 20.48l34.702222 19.342222c2.275556 0.568889 3.982222 2.275556 5.12 4.551111l18.773333 34.702222c4.551111 7.964444 15.928889 7.964444 20.48 0l18.773334-34.702222c1.137778-2.275556 2.844444-3.982222 4.551111-4.551111l34.702222-19.342222c7.964444-4.551111 7.964444-15.928889 0-20.48l-34.702222-18.773333a17.294222 17.294222 0 0 1-4.551111-4.551112z" p-id="6821"></path></svg>
              <div className='xqword'>全文缩写</div>
            </div>
          </div>
          <div
            className={cn(
              style.chatInput,
              'rounded',
              !handleNewConversation && '!w-full',
              isDragActive
                    && 'border border-dashed border-components-option-card-option-selected-border',
            )}
          >
            <div className={cn('relative')}>
              <div className="flex flex-col items-start max-h-[150px] bg-white border border-gray-G5 rounded overflow-y-auto">
                <FileListInChatInput fileConfig={visionConfig!} />
                <div ref={wrapperRef} className="flex items-center justify-between w-full">
                  <div className="relative flex items-center w-full grow">
                    <Textarea
                      ref={textareaRef}
                      className={cn(
                        'block w-full h-[48px] px-4 pr-[118px] py-[12px] text-[14px] leading-H3 max-h-none text-gray-G1 outline-none appearance-none resize-none',
                      )}
                      placeholder={t('common.chat.inputPlaceholder') || ''}
                      autoSize={{ minRows: 1 }}
                      onResize={handleTextareaResize}
                      value={query}
                      onChange={(e) => {
                        setQuery(e.target.value)
                        handleTextareaResize()
                      }}
                      onKeyUp={handleKeyUp}
                      onKeyDown={handleKeyDown}
                      onPaste={handleClipboardPasteFile}
                      onDragEnter={handleDragFileEnter}
                      onDragLeave={handleDragFileLeave}
                      onDragOver={handleDragFileOver}
                      onDrop={handleDropFile}
                    />
                  </div>
                  <div
                    className={cn(
                      'absolute top-0 bottom-0 h-full flex items-center',
                      'right-3',
                    )}
                  >
                    {query
                      ? (
                        <div
                          className="flex items-center justify-center w-8 h-8 ml-2 cursor-pointer"
                          onClick={() => setQuery('')}
                        >
                          <XCircle className="w-4 h-4 text-gray-G5 hover:text-gray-G4" />
                        </div>
                      )
                      : null}

                    {!isMultipleLine && operation}
                    {showVoiceInput && (
                      <VoiceInput
                        onCancel={() => setShowVoiceInput(false)}
                        onConverted={text => setQuery(text)}
                      />
                    )}
                    {isMultipleLine && <div className="px-[9px]">{operation}</div>}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {visionConfig?.enabled && isMobile && showFiles && (
          <>
            <FileUploaderInChatInputMobile fileConfig={visionConfig!} />
          </>
        )}
        {showInputBottomTip && (
          <div className="text-[12px] leading-H1 text-gray-G4 text-center mt-2 pb-3">
            {t('common.chat.tip')}
          </div>
        )}
      </div>
      </div>:
      <div></div>
    )
  ) : (
    <div className="flex flex-col justify-center items-center w-full">
      <div className="flex justify-end items-center gap-3 w-full">
        {/* 新增对话 */}
        {handleNewConversation && <NewChat onClick={handleNewConversation} />}
        {/* 输入框 */}
        <div
          className={cn(
            style.chatInput,
            'rounded relative',
            !handleNewConversation && '!w-full',
            isDragActive
                && 'border border-dashed border-components-option-card-option-selected-border',
          )}
        >
          <div className="flex flex-col items-start max-h-[150px] bg-white rounded overflow-y-auto">
            <FileListInChatInput fileConfig={visionConfig!} />
            <div ref={wrapperRef} className="flex items-center justify-between w-full">
              <div className="flex items-center relative grow w-full">
                <Textarea
                  ref={textareaRef}
                  className={cn(
                    'block w-full h-[48px] px-4 pr-[118px] py-[12px] text-[14px] leading-H3 max-h-none text-gray-G1 outline-none appearance-none resize-none',
                  )}
                  placeholder={t('common.chat.inputPlaceholder') || ''}
                  autoSize={{ minRows: 1 }}
                  onResize={handleTextareaResize}
                  value={query}
                  onChange={(e) => {
                    setQuery(e.target.value)
                    handleTextareaResize()
                  }}
                  onKeyUp={handleKeyUp}
                  onKeyDown={handleKeyDown}
                  onPaste={handleClipboardPasteFile}
                  onDragEnter={handleDragFileEnter}
                  onDragLeave={handleDragFileLeave}
                  onDragOver={handleDragFileOver}
                  onDrop={handleDropFile}
                />
              </div>
              <div
                className={cn('absolute top-0 bottom-0 h-full flex items-center', 'right-3')}
              >
                {/* 删除按钮 */}
                {query
                  ? (
                    <div
                      className="flex justify-center items-center ml-2 w-8 h-8 cursor-pointer"
                      onClick={() => setQuery('')}
                    >
                      <XCircle className="w-4 h-4 text-gray-G5 hover:text-gray-G4" />
                    </div>
                  )
                  : null}
                {/* 发送/中止按钮 */}
                {!isMultipleLine && operation}
                {/* 音频输入 */}
                {/* {
                    showVoiceInput && (
                      <VoiceInput
                        onCancel={() => setShowVoiceInput(false)}
                        onConverted={text => setQuery(text)}
                      />
                    )
                  } */}
                {isMultipleLine && <div className="px-[9px]">{operation}</div>}
              </div>
            </div>
          </div>
        </div>
      </div>
      {visionConfig?.enabled && isMobile && showFiles && (
        <>
          <FileUploaderInChatInputMobile fileConfig={visionConfig!} />
        </>
      )}
      {/* 底部提示 */}
      {showInputBottomTip && (
        <div className="text-[12px] leading-H1 text-gray-G4 text-center mt-2 pb-3">
          {t('common.chat.tip')}
        </div>
      )}
      {/* {showFeatureBar && <FeatureBar showFileUpload={showFileUpload} disabled={featureBarDisabled} onFeatureBarClick={onFeatureBarClick} />} */}
    </div>
  )
}

const ChatInputAreaWrapper = (props: ChatInputAreaProps) => {
  return (
    <FileContextProvider>
      <ChatInputArea {...props} />
    </FileContextProvider>
  )
}

export default ChatInputAreaWrapper
