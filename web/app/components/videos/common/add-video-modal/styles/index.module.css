
.cardInfo {
    @apply w-full shrink overflow-hidden flex flex-col justify-between gap-2 h-full rounded bg-white border border-gray-G5 cursor-pointer;
    padding: 8px;
  }
  .cardInfoHeader {
    @apply flex justify-between items-center text-[14px] text-gray-G1;
  }
  .cardInfoContent {
    @apply flex justify-between items-center text-[12px] text-gray-G2;
  }
  .cardInfo:hover,
  .cardInfo.selected {
    border-color: var(--color-primary-P1);
  }
  
  .cardInfo.disabled {
    @apply bg-white border-gray-200 cursor-default;
  }
  
  
  .card {
    @apply !border-gray-G5 !h-[154px] !px-4 !py-3 shrink-0;
    @apply relative col-span-1 rounded flex flex-col transition-all duration-200 ease-in-out cursor-pointer;
    box-shadow: 0px 0px 1px 0px rgba(159, 176, 201, 0.20);
    border-width: 1px !important;
  }
  .card:hover {
    border: 1px solid #4086ff !important;
    box-shadow: 0px 3px 8px 0px rgba(159, 176, 201, 0.50);
    background: linear-gradient(180deg, #F1F7FF 0%, #FFF 100%);
  }