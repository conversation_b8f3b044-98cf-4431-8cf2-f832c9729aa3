import React, { useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import produce from 'immer'
import FeatureCollapse from '@/app/components/base/features/feature-collapse'
import { useFeatures, useFeaturesStore } from '@/app/components/base/features/hooks'
import type { OnFeaturesChange } from '@/app/components/base/features/types'
import { FeatureEnum } from '@/app/components/base/features/types'

type Props = {
  disabled?: boolean
  onChange?: OnFeaturesChange
}

const Citation = ({
  disabled,
  onChange,
}: Props) => {
  const { t } = useTranslation()
  const features = useFeatures(s => s.features)
  const inWorkflow = useFeatures(s => s.inWorkflow)
  const featuresStore = useFeaturesStore()

  const handleChange = useCallback((type: FeatureEnum, enabled: boolean) => {
    const {
      features,
      setFeatures,
    } = featuresStore!.getState()

    const newFeatures = produce(features, (draft) => {
      draft[type] = {
        ...draft[type],
        enabled,
      }
    })
    setFeatures(newFeatures)
    if (onChange)
      onChange(newFeatures)
  }, [featuresStore, onChange])

  return (
    <FeatureCollapse
      inWorkflow={inWorkflow}
      title={t('appDebug.feature.citation.title')}
      value={!!features.citation?.enabled}
      tooltip={t('appDebug.feature.citation.description')!}
      desc={t('appDebug.feature.citation.description')}
      onChange={state => handleChange(FeatureEnum.citation, state)}
      disabled={disabled}
      showExpand={false}
    />
  )
}

export default Citation
