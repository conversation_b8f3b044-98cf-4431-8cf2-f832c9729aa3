import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import ReactEcharts from 'echarts-for-react'
import { EChartsOption } from 'echarts-for-react'
import ErrorBoundary from '@/app/components/base/error-boundary/index'
import useChartsBlock from './hooks'
import TotalEvents from './components/total-events'
import cn from '@/utils/classnames'
import { getChartStyle } from './utils'
// import testJson1 from './test1'
// import testJson2 from './test2'
// import testJson3 from './test3'
type Props = {
  content: string
}
const ChartsBlock = ({
  content
}: Props) => {
  const { t } = useTranslation()

  const {
    getEchartOptionList
  } = useChartsBlock()

  // 处理图表数据
  const chartList = useMemo<EChartsOption[]>(() => {
    if(!content) return []
    try {
      const list = JSON.parse(String(content).replace(/\n$/, ''))
      if(!Array.isArray(list)) 
        throw new Error()

      return getEchartOptionList(list)
    }
    catch (error) {
      return [{
        className: 'w-1/3',
        layout: {
          size: 'small'
        },
        option: {
          title: {
            subtext: t('common.echarts.jsonFormatError')
          }
        }
      }]
    }
  }, [content])

  return (
    <div className='flex flex-wrap min-w-[800px]'>
      {chartList.map((chart, index) => (
        <div key={index} className={cn(chart.className)}>
          <div className='rounded border border-gray-G6 m-2 p-4' style={getChartStyle(chart.layout?.size)}>
            <ErrorBoundary errorText={t('common.echarts.echartsError') || ''}>
              {/* 事件总数图表 额外处理 */}
              {chart.externalConfig?.count && (
                  <TotalEvents chart={chart} style={getChartStyle(chart.layout?.size)} />
                )
              }
              {/* 其他普通图表 */}
              {!chart.externalConfig?.count && (
                <ReactEcharts option={chart.option || {}} style={getChartStyle(chart.layout?.size)}/>
              )}
            </ErrorBoundary>
          </div>
        </div>
      )) }
    </div>
    
  )

}

export default ChartsBlock
