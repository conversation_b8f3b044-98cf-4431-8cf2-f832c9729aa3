import React, { use, useContext, useEffect, useMemo, useRef, useState } from 'react'
import {
  Affix,
  Button,
  Col,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Row,
  Select,
  Table,
  Tabs,
  Image,
  Skeleton,
  Empty
} from 'antd'

import { IS_CE_EDITION, PUBLIC_API_PREFIX, TEMPLATE_URL } from '@/config'
import { AiEditor, AiEditorPro } from 'aieditor-pro'
import 'aieditor-pro/dist/style.css'
import './styles/style.longTxetModal.scss'
import { DeleteOutlined,CloseOutlined} from '@ant-design/icons'
import style from '@/app/components/base/chat/chat-with-history/styles/style.module.css'


interface Outline {
  id: string
  level: number
  text: string
  pos: number
  size: number
}

interface DataType {
  key: React.Key
  label: string
  value: string
}

interface EditableRowProps {
  index: number
}

interface Item {
  key: string
  label: string
  value: string
}

interface ChatItem {
  id: string
  content: string
}

interface IsChatItem {
  id: string
  answer: string
}



interface EditableCellProps {
  title: React.ReactNode
  editable: boolean
  dataIndex: keyof Item
  record: Item
  handleSave: (record: Item) => void
}

interface ConversationVariable {
  value: any
  id: string
  description: string
  name: string
  value_type: string
}

interface BasicLongChatModelProps {
  isOpen: boolean
  item: any
  LongChatModelClose: () => void
}

let  islisten=false

const BasicLongChatModel: React.FC<BasicLongChatModelProps> = ({
  isOpen,
  item,
  LongChatModelClose
}) => {
  const AiEditorRef = useRef(null)
  const editorRef = useRef<AiEditorPro | null>(null)
  const [contentOry, setcontentOry] = useState<any>('');//当前选中的历史记录
  const [content, setcontent] = useState<any>('');//当前选中的历史记录

  useEffect(() => {
    if (AiEditorRef.current && !editorRef.current && content) {
      const cleanedItem = content.replace(/正在生成长文\.\.\.\n/g, '').split('\n')
      const Editor = new AiEditorPro({
        element: AiEditorRef.current,
        dragActionsEnable: false,
        contentIsMarkdown: true,
        draggable: false,
        placeholder: "点击输入内容...",
        content:cleanedItem.join('\n'),
        toolbarKeys: [],
        textSelectionBubbleMenu: {
          enable: false,
          items: [],
      },
      image: {
        bubbleMenuItems:[]
      },
    onCreated: editor => {
        updateOutLine(editor)
        //setLession(editor)
    }
    })
    editorRef.current = Editor
    return ()=>{
        Editor.destroy();
    }
    }
  },[content,AiEditorRef])
  const ifhaveData=(data:any)=>{
    let remarklist=['rowspan','colwidth','min-','col','\\','<div>','</div>','<p>','</p>','<strong>','</strong>','<em>','</em>','<table>','</table>','<thead>','</thead>','<tbody>','</tbody>','<footer>','</footer>','<tr>','</tr>','<th>','</th>','<td>','</td>']
    const containsElement = remarklist.some(element => data.includes(element));
    if (containsElement) {
      return true
    } else {
      return false
    }
  }
  const ifhaveSpan=(data:any)=>{
    let remarklist=['#','rgb','strong','<span','style','</span>']
    const containsElement = remarklist.some(element => data.includes(element));
    if (containsElement) {
      return true
    } else {
      return false
    }
  }
  useEffect(() => {
     setcontentOry(item);
     let content="";
     if(item.diff && item.diff.length>0){
        item.diff.forEach((item:any)=>{
          if(item.type=='unchanged' || ifhaveData(item.content)){
            content+=item.content
          }else if(item.type=='added'){
            content+=`<span style="color:#f74f52"><mark data-color="#f7cde1" style="background-color:#f7cde1; color:inherit">`+item.content+`</mark></span>`
          }else if(item.type=='removed'){
            content+=`<span style="color:#f74f52"><s><mark data-color="#f7cde1" style="background-color:#f7cde1; color:inherit">`+item.content+`</mark></s></span>` 
          }
        })
        setcontent(content);
     }else{
        setcontent(item.content); 
     }
  }, [isOpen])

  const updateOutLine = (editor: AiEditor) => {
    const outlineContainer = document.querySelector(`#idDiv_${item.id}`)
    if (outlineContainer) {
      outlineContainer.innerHTML = ''
    }

    const outlines = editor.getOutline()
    outlines.forEach(outline => {
      const outlineElement = createOutlineElement(outline, editor)
      outlineContainer?.appendChild(outlineElement)
    })
  }

  const createOutlineElement = (outline: Outline, editor: AiEditor) => {
    const child = document.createElement('div')
    child.classList.add(`aie-title${outline.level}`)
    child.style.padding = `10px 0`
    child.style.marginLeft = `${14 * (outline.level - 1)}px`
    child.innerHTML = `<a href="#${outline.id}" key=${outline.id}>${outline.text}</a>`
    // 为大纲项添加点击事件
    child.addEventListener('click', e => handleOutlineClick(e, outline, editor,child))

    return child
  }

  const handleOutlineClick = (e: MouseEvent, outline: Outline, editor: AiEditor,cuele:any) => {
 
    e.preventDefault()
    e.stopPropagation()
    
     //目录展示及实时定位
     islisten=true
     if (cuele.parentNode) {
      const siblings = cuele.parentNode.children;
      for (let i = 0; i < siblings.length; i++) {
        if (siblings[i] instanceof HTMLElement && siblings[i]!== cuele) {
          //siblings[i].classList.remove('active');
        }
      }
    }
    //cuele.classList.add('active');
    //end

    const el =
      typeof window !== 'undefined'
        ? editor.innerEditor.view.dom.querySelector(`#${outline.id}`)
        : null

    if (el) {
      el.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'nearest' })
      setTimeout(() => {
        editor.focusPos(outline.pos + outline.size - 1);
      }, 1000)
    } else {
      console.warn(`Element with id ${outline.id} not found.`)
    }
  }
  return (
    <>
      <Row className="flex h-full w-full overflow-hidden long-text-modal">
       <Col
          span={4}
          // key={currentConversationId}
          className="w-[20%] h-[calc(100vh-20px)] p-5 aie-directory-content !overflow-y-auto"
          id={'idDiv_' + item.id}
          style={{ background: '#fafafa' }}
        ></Col>
         <Col span={13}  style={{ background: '#ffffff' }}>
              <div  className="xq-full border-r" ref={AiEditorRef} id={'longWeb_' + item.id}>
                <div className="aie-header" style={{ margin: '0 auto' }}></div>
                <div className="aie-main"></div>
                <div className="aie-footer"></div>
               </div>
         </Col>
         <Col
          span={7}
         >
             <div className='xq-flex-end' onClick={()=>{LongChatModelClose()}}>
               <div className='xqtr'><CloseOutlined></CloseOutlined></div>
              </div>
         </Col>
      </Row>
    </>
  )
}

export default BasicLongChatModel
