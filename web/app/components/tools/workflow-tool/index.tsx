'use client'
import type { FC } from 'react'
import React, { useState } from 'react'
import { useAsyncEffect } from 'ahooks'
import { useTranslation } from 'react-i18next'
import produce from 'immer'
import type { TableColumnsType } from 'antd'
import { Form, Input, Table } from 'antd'
import type { Collection, Emoji, WorkflowCollectionDetail, WorkflowToolProviderParameter, WorkflowToolProviderRequest } from '../types'
import { getCollectionDetail } from '../utils'
import MethodSelector from '@/app/components/tools/workflow-tool/method-selector'
import { createApp } from '@/service/apps'
import { fetchWorkflowToolDetailByAppID, initWorkflowToolProvider, saveWorkflowToolProvider } from '@/service/tools'

/* 公共组件 */
import { useProviderContext } from '@/context/provider-context'
import { useFormDisabled } from '@/hooks/use-form'
import cn from '@/utils/classnames'
import Modal from '@/app/components/base/modal'
import Avatar from '@/app/components/base/avatar'
import Button from '@/app/components/base/button'
import Confirm from '@/app/components/base/confirm'
import Toast from '@/app/components/base/toast'
import { MAX_APP_NAME_LENGTH } from '@/config'

type Props = {
  collection?: Collection
  appId?: string
  mode?: 'normal' | 'param'
  parameters?: WorkflowToolProviderParameter[]
  onHide: () => void
  onCreate?: (data: WorkflowToolProviderRequest & Partial<{
    workflow_app_id: string
    workflow_tool_id: string
  }>) => void
  onSave?: (data: WorkflowToolProviderRequest & Partial<{
    workflow_app_id: string
    workflow_tool_id: string
  }>) => Promise<void>
}
// Add and Edit
const WorkflowToolAsModal: FC<Props> = ({
  collection,
  appId,
  mode = 'normal',
  parameters,
  onHide,
  onSave,
  onCreate,
}) => {
  const { t } = useTranslation()
  const { onPlanInfoChanged } = useProviderContext()
  const [form] = Form.useForm()
  const disabled = useFormDisabled(form)
  const isAdd = !collection && !appId

  // 应用头像
  const [emoji, setEmoji] = useState<Emoji>({
    content: '/assets/avatar/tool.svg',
    background: '#fff',
  })
  // 工具集详情
  const [collectionDetail, setCollectionDetail] = useState<WorkflowCollectionDetail>()
  // 参数
  const [currentParameters, setCurrentParameters] = useState<WorkflowToolProviderParameter[]>([])
  // 是否显示确认弹窗
  const [showModal, setShowModal] = useState(false)

  // 变更应用头像
  const changeAppHead = (value: string) => {
    setEmoji({
      content: value,
      background: '#fff',
    })
  }
  // 参数变更动作
  const handleParameterChange = (key: string, value: string, index: number) => {
    const newData = produce(currentParameters, (draft: WorkflowToolProviderParameter[]) => {
      if (key === 'description')
        draft[index].description = value
      else
        draft[index].form = value
    })
    setCurrentParameters(newData)
  }
  // 名称是否有效
  const isNameValid = (name: string) => {
    if (name === '')
      return false

    return /^[a-zA-Z0-9_]+$/.test(name)
  }

  // 新增工作流工具
  const handleAdd = async (value: WorkflowToolProviderRequest) => {
    // 先创建工作流应用
    const app = await createApp({
      name: value.name,
      description: value.description,
      mode: 'workflow',
      icon: value.icon.content,
      is_tool_workflow: 'true',
    })
    // 创建工具
    await initWorkflowToolProvider({
      ...value,
      workflow_app_id: app.id,
    })
    // 工具限制变动
    onPlanInfoChanged()
    Toast.notify({
      type: 'success',
      message: t('tools.notify.successCreateWorkflow'),
    })
    onCreate && onCreate({
      ...value,
      workflow_app_id: app.id,
    })
  }
  // 编辑工作流工具
  const handleEdit = async (data: WorkflowToolProviderRequest & Partial<{
    workflow_app_id: string
    workflow_tool_id: string
  }>) => {
    onSave && await onSave(data).then(async () => {
      await saveWorkflowToolProvider(data)
      Toast.notify({
        type: 'success',
        message: t('tools.notify.updateWorkflowSuccess'),
      })
    })
  }
  // 确认新增或编辑
  const onConfirm = async () => {
    const { name, description, label, privacy_policy } = form.getFieldsValue()
    const requestParams = {
      name: name || collectionDetail?.name,
      description: description || collectionDetail?.description,
      icon: emoji,
      label: label || collectionDetail?.label,
      labels: [],
      parameters: currentParameters.map(item => ({
        name: item.name,
        description: item.description,
        form: item.form,
      })),
      privacy_policy: '',
    }
    // 如果存在工具集
    if (!isAdd && collectionDetail) {
      await handleEdit?.({
        ...requestParams,
        workflow_tool_id: collectionDetail.workflow_tool_id,
        workflow_app_id: collectionDetail.workflow_app_id,
      })
    }
    else {
      await handleAdd?.({
        ...requestParams,
      })
    }
    setShowModal(false)
  }

  useAsyncEffect(async () => {
    let payload: WorkflowCollectionDetail | undefined
    try {
      if (appId)
        payload = await fetchWorkflowToolDetailByAppID(appId)
      if (collection)
        payload = await getCollectionDetail(collection) as unknown as WorkflowCollectionDetail
    }
    catch (error) {
      setShowModal(false)
    }

    if (payload) {
      form.setFieldsValue({
        label: payload?.label || '',
        name: payload?.name || '',
        description: payload?.description || '',
        privacyPolicy: payload?.privacy_policy || '',
      })
      setCollectionDetail(payload)
      // 如果是有入参的，我们要对入参进行一个覆盖处理
      if (payload.parameters && parameters) {
        setCurrentParameters(parameters.map((item) => {
          const publishedParams = payload.parameters.find(param => param.name === item.name)
          if (publishedParams) {
            return {
              name: item.name,
              required: item.required,
              type: item.type,
              description: publishedParams?.description,
              form: publishedParams?.form,
            }
          }
          else {
            return item
          }
        }))
      }
      else {
        setCurrentParameters(payload.parameters)
      }
      setEmoji(payload?.icon || {
        content: '/assets/avatar/tool.svg',
        background: '#fff',
      })
    }
  }, [collection, form])

  // 表格列
  const columns: TableColumnsType<WorkflowToolProviderParameter> = [
    {
      title: t('tools.info.toolInput.name'),
      key: 'name',
      render: (_: any, record: WorkflowToolProviderParameter) => (
        <div className='text-[13px] leading-[18px]'>
          <div title={record.name} className='flex'>
            <span className='font-semibold text-gray-900 truncate'>{record.name}</span>
            <span className='shrink-0 pl-1 text-[#ec4a0a] text-xs leading-[18px]'>{record.required ? t('tools.info.toolInput.required') : ''}</span>
          </div>
          <div className='text-gray-500'>{record.type}</div>
        </div>
      ),
      width: 100,
      ellipsis: true,
    },
    {
      title: t('tools.info.toolInput.method'),
      key: 'method',
      width: 250,
      render: (_: any, record: WorkflowToolProviderParameter, index: number) => (
        <>
          {record.name === '__image' && (
            <div className={cn(
              'flex items-center gap-1 min-h-[56px] px-3 py-2 h-9 bg-white cursor-default',
            )}>
              <div className={cn('grow text-[13px] leading-[18px] text-gray-700 truncate')}>
                {t('tools.info.toolInput.methodParameter')}
              </div>
            </div>
          )}
          {record.name !== '__image' && (
            <MethodSelector value={record.form} onChange={value => handleParameterChange('form', value, index)} />
          )}
        </>
      ),
    },
    {
      title: t('tools.info.toolInput.description'),
      key: 'description',
      width: 350,
      render: (_: any, record: WorkflowToolProviderParameter, index) => (
        <Input
          placeholder={t('tools.info.toolInput.descriptionPlaceholder')!}
          value={record.description}
          onChange={e => handleParameterChange('description', e.target.value, index)}
        />
      ),
    },
  ]
  // 弹窗标题
  const modalTitle = isAdd ? t('tools.modal.createWorkflowTool') : t('tools.modal.toolConfig')

  return (
    <>
      <Modal
        isShow
        onClose={onHide}
        closable
        title={modalTitle}
        className='!w-[800px]'
        footer={
          <>
            <Button className='!w-[92px]' variant='secondary-accent' onClick={onHide}>{t('common.operation.cancel')}</Button>
            <Button disabled={disabled} className='ml-4 !w-[92px]' variant='primary' onClick={() => {
              if (isAdd)
                onConfirm()
              else
                setShowModal(true)
            }}>{ isAdd ? t('common.operation.create') : t('common.operation.save')}</Button>
          </>
        }
      >
        {
          mode === 'normal'
            ? <Form form={form} layout='vertical'>
              {/* 头像部分 */}
              <Form.Item required label={t('tools.info.name')}>
                <div className='flex items-center justify-between gap-3'>
                  <Avatar
                    avatar={collectionDetail?.icon_url || ((emoji.content === '🤖' || !emoji.content) ? '/assets/avatar/tools.svg' : emoji.content)}
                    size={36}
                    showUpload
                    className='!rounded'
                    onChange={changeAppHead}
                  ></Avatar>
                  <Form.Item
                    name={'label'}
                    label={t('tools.info.name')}
                    validateFirst
                    rules={[
                      {
                        required: true,
                        whitespace: true,
                        min: 2,
                        max: 20,
                      },
                    ]}
                    validateTrigger='onBlur'
                    noStyle
                  >
                    <Input placeholder={t('tools.placeholder.name')!}></Input>
                  </Form.Item>
                </div>
              </Form.Item>
              {/* 工具调用名称部分 */}
              <Form.Item
                name='name'
                required
                label={t('tools.info.nameForToolCall')}
                validateFirst
                rules={[{
                  validator: (_, value: string) => {
                    // eslint-disable-next-line prefer-promise-reject-errors
                    return (isNameValid(value) ? Promise.resolve() : Promise.reject(`${t('tools.notify.nameForToolCallTip')}`))
                  },
                }, {
                  required: true,
                  whitespace: true,
                  max: MAX_APP_NAME_LENGTH,
                }]}
                validateTrigger={'onBlur'}
              >
                <Input placeholder={t('tools.placeholder.nameForToolCall')!}/>
              </Form.Item>
              {/* 描述部分 */}
              <Form.Item
                name={'description'}
                label={t('tools.info.description')}
                rules={[() => ({
                  required: true,
                  whitespace: true,
                })]}
              >
                <Input.TextArea
                  maxLength={100}
                  placeholder={t('tools.placeholder.description') || ''}
                ></Input.TextArea>
              </Form.Item>
              {
                !isAdd
            && <>
              {/* 参数部分 */}
              <Form.Item label={t('tools.info.toolInput.title')}>
                <Table
                  size='middle'
                  pagination={false}
                  columns={columns}
                  dataSource={currentParameters}
                  className='border-gray-G5 rounded border'
                ></Table>
              </Form.Item>
            </>
              }
            </Form>
            : <Table
              size='middle'
              pagination={false}
              columns={columns}
              dataSource={currentParameters}
              className='border-gray-G5 rounded border'
            ></Table>
        }

      </Modal>
      {showModal && (
        <Confirm
          isShow={showModal}
          type='warning'
          onCancel={() => setShowModal(false)}
          title={t('tools.modal.confirmTitle')}
          content={t('tools.modal.confirmTip')}
          onConfirm={onConfirm}
        />
      )}
    </>

  )
}
export default React.memo(WorkflowToolAsModal)
