'use client'
import { useTranslation } from 'react-i18next'
import { useMemo } from 'react'
import { useContext } from 'use-context-selector'
import { useProviderContext } from '@/context/provider-context'
import type { Member } from '@/models/common'
import { deleteMemberOrCancelInvitation, updateMemberRole } from '@/service/common'
/* 公共组件 */
import { ToastContext } from '@/app/components/base/toast'
import CheckSelect from '@/app/components/base/select/check-select'

const itemTitleClassName = `
  leading-[20px] text-sm text-gray-700 whitespace-nowrap
`
const itemDescClassName = `
  leading-[18px] text-xs text-gray-500 whitespace-nowrap text-ellipsis overflow-hidden
`

type IOperationProps = {
  member?: Member
  onOperate: (role: string) => void
  value?: string
  placeholder?: string
  allowClear?: boolean
}

const Operation = ({
  member,
  onOperate,
  value,
  placeholder = '',
  allowClear = false,
}: IOperationProps) => {
  const { t } = useTranslation()
  const { datasetOperatorEnabled } = useProviderContext()
  const { notify } = useContext(ToastContext)
  const toHump = (name: string) => name.replace(/_(\w)/g, (all, letter) => letter.toUpperCase())

  /* 角色列表 */
  const roleList = useMemo(() => {
    return [
      {
        label: t('account.role.admin'),
        title: t('account.role.adminTipPublicCloud'),
        value: 'admin',
      },
      {
        label: t('account.role.normal'),
        title: t('account.role.normalTipPublicCloud'),
        value: 'normal',
      },
      {
        label: t('account.role.owner'),
        title: t('account.role.ownerTipPublicCloud'),
        value: 'owner',
      },
    ]
  }, [])
  /* 更新成员角色 */
  const handleUpdateMemberRole = (role: string) => {
    onOperate(role || '')
  }
  return (
    <CheckSelect
      className='w-full'
      // value={value || ''}
      options={roleList}
      placeholder={placeholder}
      allowClear={allowClear}
      onChange={handleUpdateMemberRole}
      optionRender={option => (
        <>
          <div className={itemTitleClassName}>{t(`account.role.${toHump(option.value as string)}`)}</div>
          <div className={itemDescClassName}>{t(`account.role.${toHump(option.value as string)}TipPublicCloud`)}</div>
        </>
      )}
      dropdownRender={menu => (
        <>
          {menu}
        </>
      )}
    ></CheckSelect>
  )
}

export default Operation
