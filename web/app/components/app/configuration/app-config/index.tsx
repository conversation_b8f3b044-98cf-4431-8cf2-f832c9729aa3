'use client'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { useContext } from 'use-context-selector'
import { useTranslation } from 'react-i18next'
import produce from 'immer'
import cn from 'classnames'
import { useFormattingChangedDispatcher } from '../debug/hooks'
import { updateAppInfo } from '@/service/apps'
import ConfigContext from '@/context/debug-configuration'
import AppForm from '@/app/components/app/configuration/app-form'
import type { AppFormProps } from '@/app/components/app/configuration/app-form'
import ConfigPrompt from '@/app/components/app/configuration/config-prompt'
import { type ModelConfig, type PromptVariable } from '@/models/debug'
import type { AppType } from '@/types/app'
import style from '@/app/components/app/configuration/styles/style.module.scss'

import { useToastContext } from '@/app/components/base/toast'
import { useStore as useAppStore } from '@/app/components/app/store'

type AppConfigProps = {
  // app: App
}

const AppConfig = ({
  // app
}: AppConfigProps) => {
  const {
    appId,
    mode,
    modelConfig,
    setModelConfig,
    setPrevPromptConfig,
  } = useContext(ConfigContext)
  const { t } = useTranslation()
  const { notify } = useToastContext()

  const app = useAppStore(state => state.appDetail)
  const setAppDetail = useAppStore(state => state.setAppDetail)
  // 编辑应用信息
  const onEdit: AppFormProps['onConfirm'] = useCallback(async ({
    name,
    icon_type,
    icon,
    icon_background,
    description,
    use_icon_as_answer_icon,
  }) => {
    if (!app)
      return
    try {
      const appInfo = await updateAppInfo({
        appID: app.id,
        name,
        icon_type,
        icon,
        icon_background,
        description,
        use_icon_as_answer_icon,
      })
      // notify({
      //   type: 'success',
      //   message: t('app.notify.editDone'),
      // })
      setAppDetail(appInfo)
    }
    catch (e) {
      notify({ type: 'error', message: t('app.notify.editFailed') })
    }
  }, [app?.id, notify, t, setAppDetail])

  const formattingChangedDispatcher = useFormattingChangedDispatcher()

  const promptTemplate = modelConfig.configs.prompt_template
  const promptVariables = modelConfig.configs.prompt_variables
  // simple mode
  const handlePromptChange = (newTemplate: string, newVariables: PromptVariable[]) => {
    const newModelConfig = produce(modelConfig, (draft: ModelConfig) => {
      draft.configs.prompt_template = newTemplate
      // draft.configs.prompt_variables = [...draft.configs.prompt_variables, ...newVariables]
    })
    if (modelConfig.configs.prompt_template !== newTemplate)
      formattingChangedDispatcher()

    setPrevPromptConfig(modelConfig.configs)
    setModelConfig(newModelConfig)
  }

  const wrapRef = useRef<HTMLDivElement>(null)
  const appFormRef = useRef<HTMLDivElement>(null)
  const [editorHeight, setEditorHeight] = useState<number>(0)

  useEffect(() => {
    const handleResizeEditor = () => {
      if (wrapRef.current && appFormRef.current) {
        const height = wrapRef.current.clientHeight
        const appFormHeight = appFormRef.current?.clientHeight
        setEditorHeight(height - appFormHeight - 144)
      }
    }
    handleResizeEditor()
    // 添加事件监听器
    window.addEventListener('resize', handleResizeEditor);

    // 清理事件监听器
    return () => {
      window.removeEventListener('resize', handleResizeEditor);
    };
  }, [])

  return (
    <>
      <div
        ref={wrapRef}
        className={cn(style.content, style.flexColGap20, 'h-full')}
      >
        <div className={cn('title-14-24')}>{t('appDebug.pageTitle.basicInfo')}</div>
        {/* 应用信息 */}
        {app && (
          <AppForm
            ref={appFormRef}
            className={style.appConfig}
            size={40}
            app={app}
            appName={app.name}
            appIcon={app.icon}
            appDescription={app.description}
            onConfirm={onEdit}
            blurToSave
          />
        )}

        {/* 提示词 Template */}
        <ConfigPrompt
          mode={mode as AppType}
          promptTemplate={promptTemplate}
          promptVariables={promptVariables}
          onChange={handlePromptChange}
          editorHeight={editorHeight}
        />
      </div>
    </>
  )
}
export default React.memo(AppConfig)
