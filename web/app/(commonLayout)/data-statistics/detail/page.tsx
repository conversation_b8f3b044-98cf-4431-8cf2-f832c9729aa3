'use client'
import { useTranslation } from 'react-i18next'
import DetailNav from '@/app/components/data-statistics/detail/detail-nav/index'
import DetailContent from '@/app/components/data-statistics/detail/detail-content/index'
import s from '@/app/components/data-statistics/detail/styles/style.module.scss'
import cn from '@/utils/classnames'

// 数据统计详情
const DataStatisticsDetail = () => {
  return <div className={cn('h-full bg-white', s['dataStatistics-detail'])}>
    <DetailNav />
    <DetailContent />
  </div>
}
export default DataStatisticsDetail