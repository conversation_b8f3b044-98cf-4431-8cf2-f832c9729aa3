import type { CommonNodeType, ValueSelector } from '@/app/components/workflow/types'

export type VideoNodeType = CommonNodeType & {
  // 视频库节点id列表
  library_ids: Array<string>
  // 开始时间
  start_time_variable_selector: ValueSelector
  // 结束时间
  end_time_variable_selector: ValueSelector
  // 事件
  event_name_variable_selector: ValueSelector
  // 位置
  location_variable_selector: ValueSelector
  // 设备
  device_variable_selector: ValueSelector
  // 是否私有
  fork: boolean
}
