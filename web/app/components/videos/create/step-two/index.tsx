'use client'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { Form, Select } from 'antd'
import useSWR from 'swr'
import type { VideoInfoType } from '../index'

import type { MediaType, Video } from '@/types/videos'
import { useVideoCreateMode } from '@/app/components/videos/hook'

// 公共组件
import { useVideoDetailContext } from '@/context/video-detail'
import Button from '@/app/components/base/button'
import { useFormDisabled } from '@/hooks/use-form'
import { addFiles, addVideoStream, fetchVideoDocuments } from '@/service/videos'
import Toast from '@/app/components/base/toast'

type StepTwoProps = {
  type: MediaType
  ready?: boolean
  onSave?: (video?: Video) => void
  getFormInfo?: () => Promise<VideoInfoType | undefined>
}
const MAX_FILE_NUM = 30

const StepTwo = ({
  type,
  ready = true,
  getFormInfo,
  onSave,
}: StepTwoProps) => {
  const { t } = useTranslation()
  const { video: currentVideo, mutateVideoRes } = useVideoDetailContext()
  const { isVideoMode, isVideoStreamMode, isImageMode } = useVideoCreateMode(type)
  const [form] = Form.useForm()
  const disabled = useFormDisabled(form)
  // 获取文档结果数据
  const { data: documentsRes, isLoading, mutate } = useSWR(
    {
      action: 'fetchVideoDocuments',
      id: currentVideo!.id,
      params: {
        page: 1,
        size: 10,
        keyword: '',
      },
    },
    apiParams => fetchVideoDocuments(apiParams.id, apiParams.params),
  )

  // 创建或编辑视频库
  const createHandle = async () => {
    try {
      let info
      if (getFormInfo)
        info = await getFormInfo()
      if (getFormInfo && !info)
        return false
      if ((isVideoMode || isImageMode) && (MAX_FILE_NUM - documentsRes!.data.total) < info!.files.length) {
        Toast.notify({ type: 'error', message: t('videos.create.form.fileNumLimit', { max: MAX_FILE_NUM, num: MAX_FILE_NUM - documentsRes!.data.total > 0 ? MAX_FILE_NUM - documentsRes!.data.total : 0 })! })
        return false
      }
      const frameRate = form.getFieldValue('frameRate')

      if (isVideoStreamMode) {
        const params = {
          urls: info?.voice_stream_config?.map((config) => {
            return {
              url: config.url,
              name: config.name,
              frame_interval: frameRate,
            }
          }) || [],
        }
        await addVideoStream(currentVideo!.id, params)
      }
      else if (isVideoMode || isImageMode) {
        const params = {
          file_ids: info!.files.map(file => file.fileID ? file.file.id! : '').filter(Boolean).map((item) => {
            return {
              id: item,
              frame_interval: frameRate,
            }
          }),
          file_type: currentVideo!.type,
        }
        await addFiles(currentVideo!.id, params)
      }
      if (mutateVideoRes)
        mutateVideoRes()
      onSave && onSave()
    }
    catch (err) {
    }
  }

  return (
    <Form form={form} layout='vertical'>
      {/* 切频频率 */}
      { (isVideoMode || isVideoStreamMode) && <Form.Item
        label={t('videos.create.form.frameRate')}
        name="frameRate"
        rules={[{ required: true, message: t('videos.create.form.frameRatePlaceholder')! }]}
        validateTrigger='onChange'
      >
        <Select
          placeholder={t('videos.create.form.frameRatePlaceholder') || ''}
          options={[
            { value: 3, label: '3s' },
            { value: 5, label: '5s' },
            { value: 10, label: '10s' },
            { value: 30, label: '30s' },
            { value: 60, label: '60s' },
            { value: 300, label: '300s' },
          ]}
        />
      </Form.Item> }
      {/* 按钮组 */}
      <div className='flex items-center'>
        <Button size='large' disabled={!ready || disabled || !documentsRes} variant='primary' onClick={createHandle}>{t('common.operation.save')}</Button>
      </div>
    </Form>
  )
}

export default StepTwo
