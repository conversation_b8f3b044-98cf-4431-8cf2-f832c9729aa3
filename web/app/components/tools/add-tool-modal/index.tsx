'use client'
import type { FC } from 'react'
import React, { useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useContext } from 'use-context-selector'
import { useMount } from 'ahooks'
import { Tabs } from 'antd'
import { cloneDeep } from 'lodash-es'
import { CollectionType } from '../types'
import type { Collection, Tool, ToolParameter } from '../types'

import s from './styles/index.module.css'
import I18n from '@/context/i18n'
import { getLanguage } from '@/i18n/language'
import EditCustomToolModal from '@/app/components/tools/detail/custom-tool'
import {
  fetchAllBuiltInTools,
  fetchAllCustomTools,
  fetchAllWorkflowTools,
} from '@/service/tools'
import type { ToolWithProvider } from '@/app/components/workflow/types'
import { MAX_TOOLS_NUM } from '@/config'
import type { ToolItem } from '@/types/tools'
// 公共组件
import cn from '@/utils/classnames'
import { AddCircle } from '@/app/components/base/icons/src/vender/line/general'
import Button from '@/app/components/base/button'
import Toast from '@/app/components/base/toast'
import Modal from '@/app/components/base/modal'
import TextButton from '@/app/components/base/button/text-button'
import CardList from '@/app/components/base/card-list'
import Card from '@/app/components/base/card'
import Avatar from '@/app/components/base/avatar'

export type AddToolModalProps = {
  tools?: Array<ToolItem>
  max?: number
  hasParamsDetail?: boolean
  onChange?: (value: Array<ToolItem>) => void
  onHide: () => void
  isInWorkflow?: boolean
}
// Add and Edit
const AddToolModal: FC<AddToolModalProps> = ({
  tools,
  max = MAX_TOOLS_NUM,
  hasParamsDetail = false,
  onChange,
  onHide,
  isInWorkflow = false,
}) => {
  const { t } = useTranslation()
  const { locale } = useContext(I18n)
  const language = getLanguage(locale)

  // 选中的工具列表
  const [selectedTools, setSelectedTools] = useState(cloneDeep(tools) || [])
  // 当前筛选类型
  const [currentType, setCurrentType] = useState('all')
  // 当前分类
  const [currentCategory, setCurrentCategory] = useState('')
  // 查询关键字
  const [keywords, setKeywords] = useState<string>('')
  // 工具列表
  const [toolList, setToolList] = useState<ToolWithProvider[]>([])
  // 显示编辑自定义
  const [isShowEditCollectionToolModal, setIsShowEditCustomCollectionModal] = useState(false)
  // 列表是否正在加载
  const [listLoading, setListLoading] = useState(true)

  // 分类列表
  const tabList = [{
    key: 'all',
    label: t('tools.type.all'),
  }, {
    key: 'builtin',
    label: t('tools.type.builtIn'),
  }, {
    key: 'api',
    label: t('tools.type.custom'),
  }]
  // 是否可以新增
  const addable = useMemo(() => {
    return selectedTools?.length < max
  }, [max, selectedTools?.length])

  // 工具是否需要授权
  const needAuth = (tool: ToolWithProvider) => {
    return Boolean(tool.allow_delete && !tool.is_team_authorization && tool.type === CollectionType.builtIn)
  }
  // 是否选中
  const isSelectd = (tool: Tool, collection: Collection) => {
    // @ts-expect-error 类型无误
    return selectedTools.some(item => item.tool_name === tool.name && item.provider_id === (collection.id || collection.name))
  }
  // 生成工具名称
  const generateToolName = (tool: Tool, collection: Collection) => {
    return collection.type === CollectionType.custom ? `${collection.name}：${tool.label[language]}` : tool.label[language]
  }

  // 获取所有工具
  const getAllTools = async () => {
    setListLoading(true)
    const buildInTools = await fetchAllBuiltInTools()
    const customTools = await fetchAllCustomTools()
    const workflowTools = await fetchAllWorkflowTools()
    const mergedToolList = [
      ...buildInTools,
      ...customTools,
      ...workflowTools.filter((toolWithProvider) => {
        return !toolWithProvider.tools.some((tool) => {
          return !!tool.parameters.find(item => item.name === '__image')
        })
      }),
    ]
    setToolList(mergedToolList)
    setListLoading(false)
  }
  // 过滤后的工具列表
  const filteredList = useMemo(() => {
    // 类型过滤
    return toolList.filter((toolWithProvider) => {
      if (currentType === 'all')
        return true
      else if (currentType === 'api')
        return toolWithProvider.type === 'api' || toolWithProvider.type === 'workflow'

      else
        return toolWithProvider.type === currentType
      // 工具标签过滤
    }).filter((toolWithProvider) => {
      if (!currentCategory)
        return true
      else
        return toolWithProvider.labels.includes(currentCategory)
      // 关键字过滤
    }).filter((toolWithProvider) => {
      return toolWithProvider.tools.some((tool) => {
        return Object.values(tool.label).some((label) => {
          return label.toLowerCase().includes(keywords.toLowerCase())
        })
      })
      // 过滤调未授权的
    }).filter((toolWithProvider) => {
      return !needAuth(toolWithProvider)
    })
  }, [currentType, currentCategory, toolList, keywords])
  // 创建自定义工具
  const doCreateCustomToolCollection = async () => {
    Toast.notify({
      type: 'success',
      message: t('common.actionMsg.actionSuccess'),
    })
    setIsShowEditCustomCollectionModal(false)
    getAllTools()
  }
  // 添加工具后的回调
  const toolSelectHandle = (collection: Collection, tool: Tool) => {
    const selectedStatus = isSelectd(tool, collection)
    let tempTools: Array<ToolItem> = []
    if (selectedStatus) {
      // @ts-expect-error 类型无误
      tempTools = cloneDeep(selectedTools.filter(item => !(item.tool_name === tool.name && item.provider_id === (collection.id || collection.name))))
      setSelectedTools(tempTools)
      onChange && onChange(tempTools)
    }
    else {
      const parameters: Record<string, ToolParameter | string> = {}
      if (!addable) {
        Toast.notify({
          type: 'warning',
          message: t('tools.notify.addTip', { max }),
        })
        return
      }
      if (tool.parameters) {
        tool.parameters.forEach((item) => {
          parameters[item.name] = hasParamsDetail ? item : ''
        })
      }
      tempTools = cloneDeep([...selectedTools, {
        provider_id: collection.id || collection.name,
        provider_type: collection.type,
        provider_name: collection.name,
        collection: {
          name: collection.name,
          type: collection.type,
          id: collection.id,
          icon_url: collection.icon_url, // 兼容旧版，后续删除
        },
        tool_name: tool.name,
        tool_icon: collection.icon_url,
        icon_url: collection.icon_url,
        tool_description: tool.description[language],
        tool_label: tool.label[locale] || tool.label[locale.replaceAll('-', '_')],
        tool_parameters: parameters,
        enabled: true,
        isDeleted: false,
      }])
      setSelectedTools(tempTools)
      onChange && onChange(tempTools)
    }
  }

  useMount(() => {
    getAllTools()
  })

  return (
    <>
      <Modal
        isShow
        onClose={onHide}
        closable
        width={720}
        title={t('tools.action.addTool')}
        headerExtra={(
          <TextButton size='middle' onClick={() => setIsShowEditCustomCollectionModal(true)}>
            <AddCircle className='w-4 h-4'></AddCircle>
            {t('tools.action.createCustomTool')}
          </TextButton>
        )}
        scrollable={false}
      >
        <Tabs
          activeKey={currentType}
          items={tabList}
          className='mb-5 px-8'
          onChange={setCurrentType}
        ></Tabs>
        <CardList
          type='normal'
          className='px-8'
          loading={listLoading}
          layout='line'
        >
          {
            filteredList.map(toolProvider => (
              toolProvider.tools.map(tool => (
                <Card
                  replaceClassName={s.card}
                  key={`${toolProvider.id || toolProvider.name}-${tool.name}`}
                  layout='line'
                  title={generateToolName(tool, toolProvider)}
                  description={tool.description[language]}
                  generateHead={
                    () => <Avatar
                      size={44}
                      className={cn('!rounded !bg-white')}
                      avatar={toolProvider.icon_url}
                    ></Avatar>}
                  className='w-[656px]'
                >
                  {
                    isSelectd(tool, toolProvider)
                      ? <div className='group'>
                        <Button
                          className='group-hover:hidden !border-none !w-[72px] !text-primary-P1 !bg-primary-P4 !shrink-0'
                          size={'small'}
                          variant='secondary'
                        >{t('common.status.added')}</Button>
                        <Button
                          className='group-hover:flex hidden !w-[72px] !shrink-0'
                          size={'small'}
                          variant={'warning'}
                          onClick={() => toolSelectHandle(toolProvider, tool)}
                        >{t('common.operation.remove')}</Button>
                      </div>
                      : <Button
                        className='!w-[72px] !shrink-0'
                        size={'small'}
                        variant='secondary-accent'
                        onClick={() => toolSelectHandle(toolProvider, tool)}
                      >{t('common.operation.add')}</Button>
                  }
                </Card>
              ))
            ))
          }

        </CardList>
      </Modal>
      {isShowEditCollectionToolModal && (
        <EditCustomToolModal
          onHide={() => setIsShowEditCustomCollectionModal(false)}
          onAdd={doCreateCustomToolCollection}
        />
      )}
    </>

  )
}
export default React.memo(AddToolModal)
