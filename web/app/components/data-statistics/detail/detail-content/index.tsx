'use client'
import { useTranslation } from 'react-i18next'
import React, { useEffect, useState } from 'react';
import { Select, DatePicker } from 'antd';
import s from '@/app/components/data-statistics/detail/styles/style.module.scss'
import MultiplSelect from '@/app/components/base/select/multiple-select';
const { RangePicker } = DatePicker;
import DetailCharts from '../detail-charts/index'


// TODO 后续根据不同的类型修改
const selectOptions = [
  { value: '1', label: 'TeleAl/TeleMM', color: '#3168F5' },
  { value: '2', label: 'TeleChat2-115b', color: '#FF9C28' },
  { value: '3', label: 'TeleChat2-7b', color: '#15B881' },
  { value: '4', label: 'qwen-vl-max', color: '#9C63DB' },
]

// 详情界面主要内容
const DetailContent = () => {
  const [params, setParams] = useState({
    dateRange: ['2023-01-01', '2023-01-07'],
    yAxisName: '数量'
  });

  // 获取chart的数据
  const getChartData = async (params: any, tabKey: string) => {    
    // 生成日期范围
    const generateDates = (start: string, end: string) => {
      const dates = [];
      const startDate = new Date(start);
      const endDate = new Date(end);
      while (startDate <= endDate) {
        dates.push(startDate.toLocaleDateString());
        startDate.setDate(startDate.getDate() + 1);
      }
      return dates;
    };

    const xAxis = generateDates(params.dateRange[0], params.dateRange[1]);
    return {
      xAxis,
      // TODO 假数据
      statistics: {
        total: 800,
        input: 400,
        output: 400
      },
      // TODO 假数据
      series: [
        { label: 'TeleAl/TeleMM', data: [61, 70, 163, 129, 96, 65, 99] },
        { label: 'TeleChat2-115b', data: [71, 39, 34, 83, 71, 30, 61] },
        { label: 'TeleChat2-7b', data: [36, 61, 99, 40, 38, 44, 25] },
        {label: 'qwen-vl-max', data: [10, 10, 10, 10, 10, 10, 10] }
      ]
    };
  };

  // 当前选中的series
  const [activeKeys, setActiveKeys] = useState<string[]>(['1', '2', '3', '4']);
  const [allSeries, setSeries] = useState(selectOptions);
  // 切换选项
  const handleSelect = (value, options) => {
    setSeries(options)
    setActiveKeys(value)
  }
  
  return <div className={s['detail-content']}>
    <div className={s['detail-search']}>
      <MultiplSelect
        className='w-[400px]'
        defaultValue={[]}
        placeholder='请选择'
        options={selectOptions}
        onChange={handleSelect}
        maxTagCount={'responsive'}
        showSearch={false}
        />
      <RangePicker />
    </div>
    <DetailCharts
      fetchData={getChartData}
      params={params}
      allSeries={allSeries}
      activeSeriesKeys={activeKeys}
      />
  </div>
}
export default DetailContent
