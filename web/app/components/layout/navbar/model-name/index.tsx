import type { FC, PropsWithChildren } from 'react'
import {
  modelTypeFormat,
  sizeFormat,
} from '@/app/components/account-setting/model-provider-page/utils'
import { useLanguage } from '@/app/components/account-setting/model-provider-page/hooks'
import type { ModelItem } from '@/app/components/account-setting/model-provider-page/declarations'
import FeatureIcon from './feature-icon'
// 公共组件
import Tag from '@/app/components/base/tag'
import classNames from '@/utils/classnames'

type ModelItemC = {
  model: string
  model_label?: string
  icon?: string
  label: string
  // model_type: ModelTypeEnum
  // features?: ModelFeatureEnum[]
  // fetch_from: ConfigurationMethodEnum
  // status: ModelStatusEnum
  model_properties: Record<string, string | number>
  load_balancing_enabled: boolean
  deprecated?: boolean
}
type ModelNameProps = PropsWithChildren<{
  modelItem: ModelItemC
  className?: string
  showModelType?: boolean
  modelTypeClassName?: string
  showMode?: boolean
  modeClassName?: string
  showFeatures?: boolean
  featuresClassName?: string
  showContextSize?: boolean
  tagStyle?: string
}>
const ModelName: FC<ModelNameProps> = ({
  modelItem,
  className,
  showModelType,
  modelTypeClassName,
  showMode,
  modeClassName,
  showFeatures,
  featuresClassName,
  showContextSize,
  children,
  tagStyle = 'ml-[6px] px-1',
}) => {
  const language = useLanguage()

  if (!modelItem)
    return null
  return (
    <div
      className={`
        flex items-center truncate text-S1 leading-H1 
        ${className}
      `}
    >
      <div
        className='truncate'
        title={modelItem?.label}
      >
        {modelItem?.label}
      </div>
      {/* <Tag color='blue' bordered size='small' className={classNames(tagStyle)}>{modelItem?.label}</Tag> */}
      {/* {
        showModelType && modelItem.model_type && (
          <Tag color='blue' bordered size='small' className={classNames(tagStyle, modelTypeClassName)}>{modelTypeFormat(modelItem.model_type)}</Tag>
        )
      }
      {
        modelItem.model_properties.mode && showMode && (
          <Tag color='blue' bordered size='small' className={classNames(tagStyle, modeClassName)}>{(modelItem.model_properties.mode as string).toLocaleUpperCase()}</Tag>
        )
      } */}
      {/* {
        showFeatures && modelItem.features?.map(feature => (
          <FeatureIcon
            key={feature}
            feature={feature}
            className={featuresClassName}
          />
        ))
      } */}
      {/* {
        showContextSize && modelItem.model_properties.context_size && (
          <Tag color='blue' bordered size='small' className={tagStyle}>{sizeFormat(modelItem.model_properties.context_size as number)}</Tag>
        )
      } */}
      {children}
    </div>
  )
}

export default ModelName
