.bg {
  @apply flex flex-col h-full;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.09) 0%, rgba(255, 255, 255, 0.09) 100%),
    linear-gradient(179deg, #f8f7f9e6 0.84%, #f0f5ffe6 103.39%);
}
.content {
  width: 100%;
  height: calc(100% - 60px);
}

.chatIntroWrap {
  @apply flex flex-col items-center justify-start h-full py-[110px];
  width: 100%;
}

.chatInroAppInfo {
  @apply flex flex-col items-center justify-center w-full gap-3 mb-3 mb-4;
}

.chatIntroAppTitle {
  @apply font-semibold text-S6 leading-H6;
  color: var(--color-gray-G1);
  text-align: center;
}

.openingStatement {
  width: 100%;
  color: var(--color-gray-G1);
  text-align: left;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 171.429% */
  margin-bottom: 24px;
  background: rgba(255, 255, 255, 0.9);
  padding: 12px 16px;
  border-radius: 4px;
  word-break: break-all;
}
/* 多模型样式 */
.debug-with-multiple-model .chatInroAppInfo {
  @apply gap-1;
}
.debug-with-multiple-model .chatIntroAppTitle {
  @apply text-S4 leading-H4;
}
.debug-with-multiple-model.chatIntroWrap {
  @apply !py-0 lg:py-6;
}
.debug-with-multiple-model .openingStatement {
  @apply mb-4 border border-gray-G5;
}
.suggestedQuestionWrap {
  @apply flex flex-col w-full gap-3 pb-[20px];
}
.suggestedQuestion {
  @apply flex justify-center items-center px-4 py-[7px] rounded border border-gray-G5 w-fit max-w-full text-gray-G2 hover:border-gray-G3 hover:text-gray-G1;
  min-height: 36px;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 169.231% */
  cursor: pointer;
  word-break: break-all;
  /* overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; */
}
.hasBgSuggestedQuestion {
  @apply flex justify-center items-center px-4 py-[7px] rounded border border-gray-G5 w-fit max-w-full text-gray-G2 hover:border-gray-G3 hover:text-gray-G1;
  min-height: 36px;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 169.231% */
  cursor: pointer;
  word-break: break-all;
  /* overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; */
  color: #EDEFF2;
  fill: rgba(24, 24, 24, 0.25);
  stroke-width: 1px;
  stroke: rgba(255, 255, 255, 0.30);
  backdrop-filter: blur(8px);
}

.marketAppInfo {
  @apply w-[260px] h-full px-5 py-6 flex flex-col gap-6;
  background: #fafbfc;
  filter: drop-shadow(0px 0.969px 3.876px rgba(0, 0, 0, 0.08));
}

.marketAppInfo .appTitle {
  color: var(--color-gray-G1);
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: 28px; /* 155.556% */
}
.marketAppInfo .appLabel {
  color: var(--color-gray-G1);
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px; /* 171.429% */
  margin-bottom: 8px;
}
.marketAppInfo .appDesc {
  color: var(--color-gray-G2);
  text-align: justify;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 171.429% */
}

.chatContainerInnerClassName {
  @apply mx-auto md:ml-[160px] w-full w-1/2 sm:w-2/3 max-w-[1200px];
}

.chatFooterInnerClassName {
  @apply mx-auto md:ml-[160px] w-1/2 sm:w-2/3 max-w-[1200px];
}

@media screen and (max-width: 640px) {
  .chatContainerInnerClassName {
    @apply mx-auto w-full w-[calc(100%-20px)] sm:w-[90%] max-w-[1200px];
  }
  
  .chatFooterInnerClassName {
    @apply mx-auto w-[calc(100%-20px)] sm:w-[90%] max-w-[1200px];
  }
}



.configPanel {
  @apply flex flex-col mb-8 mx-auto md:ml-[160px] max-h-[80%] w-1/2 sm:w-2/3 max-w-[1200px] max-w-[1200px];
  margin:0 auto !important;
}

.logo{
  width:100%;
  text-align: center;
  margin-top: 5%;
  margin-bottom: 2%;
}

.logo_img{
  width: 68px;
  height: 68px;
  margin: 0 auto;
  display: block;
  border-radius: 0 !important;
}

.logo .name{
  font-size: 20px;
  font-weight: 600;
  color: #333;
  text-align: center;
  margin-top: 10px;
}

.chatInroAppInfo{
  /* display: none; */
}

.chatInroAppInfo__ocnvV{
  /* display: none; */
}

.TopicButtons button{
  border: none !important;
    padding: 0 !important;
    font-weight: 600;
    font-size: 14px  !important;
}

.TopicButtons{
  display: block;
    height: auto;
    text-align: left;
    /* padding: 15px; */
    margin-right: 15px;
    margin-top: 10px;
    float:left;
    border-radius: 4px;
    /* width:20%; */
    color:#181818;
}

.mt_tip{
  font-size: 13px;
  color: #878ea0;
}


.mt_text{
  /* font-size: 14px; */
  /* color: #181818; */
  /* font-weight:bold; */
  padding:4px 0;
  height:auto;
}


.mt_icon{
  width:100%;
  display: block;
font-size:16px;
}

.TopicActive{
  background-color: #f0f7ff;
    border-color: #3168f5;
    color:#3168f5;
}

.customTitle{
  /* text-overflow: inherit;
    white-space: inherit; */
    font-size: 14px;
    color: #181818;
    white-space: normal;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    /* font-weight: 600; */
}

.new-classname{
  padding:25px;
}

.title_class{
  font-weight: 600;
  border:solid 1px #3168f5;
}

.dagang_button{
  color:#3168f5 !important;
}

.icon_add{
  width:20px;
  height:20px;
  border-radius: 100%;
  color:#fff !important;
}

.dagang_view{
  padding:10px 0;
  background-color: #f1f7ff;
  font-weight:600;
  font-size:16px;
  /* display: none; */
  /* 隐藏添加章节按钮 */
}

.title_ly{
  color:#181818;
  font-size: 14px;
}

.title_h1{
  font-size: 15px;
}
.shield{
  /* display: none; */
}

.chatIcon{
  margin-left:10px;
}

.p-33{
  margin-top:-25px;
}

.configPanelLong {
  @apply flex flex-col mb-6 mx-auto md:ml-[160px] w-1/2 sm:w-2/3 max-w-[800px] max-w-[800px];
}



.wDiv{
  padding-top:20px;
}
.titleDiv{
  background:#EBF4FF;
  border-radius:2px;
  padding:4px 10px;
}
.titleWord{
  color:#5C6273;
}
.bigtel{
  font-size:24px;
  font-weight:bold;
  color:#181818;
  margin-top:24px;
  line-height:40px;
}
.topremarkPic{
  display:block;
  width:180px;
  height:180px;
}
.hDiv{
  width:100%;
  height:70px;
}
.pro{
  padding:0px 0 20px 0;
  border-bottom:1px solid #EDEFF2;
  margin-bottom:15px;
}
.prom{
  margin:0 20px;
}
.pross{
  border-radius:100px;
  font-size:12px;
  padding:9px 16px;
  cursor: pointer;
}
.prossDiv{
  background:rgba(239, 240, 255, 0.80);
}
.prossDiv span{
  color: #9751FF;
}
.prossDivHey{
  background:rgba(239, 240, 255, 0.60);
  color:#a2a2a3;
}
.prossDivHey span{
  color:#b9b3fa;
}
.prossOn{
  background: linear-gradient(to right, #4382FF, #868CFF);
  color:#fff;
}
.prossOng{
  background:#EFF0FF;
  color:#181818;
}
.prossOng span{
  color:#9751FF;
}

.smallTel{
  font-size: 14px;
  line-height:24px;
  color:#140E35;
  font-weight:bold;
  margin-bottom:4px;
}
.smallTel span{
  color:#3B74F7;
  font-size:14px;
  margin-right:5px;
}
.smallTelBotDIV{
  margin-top:10px;
}
.smallRemark{
  font-weight:bold;
  margin:20px 0 10px 0;
}

.abtn{
  padding:0px 16px !important;
  height:36px !important;
  line-height:36px !important;
}
.abtn img{
  margin-right:8px;
}

.divBor{
  border-radius:100px;
  overflow: hidden;
  box-sizing:border-box;
  position: relative;
  z-index:1;
}
.divBor2{
  margin-top:20px;
}
.divBorBj{
  position:absolute;
  width:100%;
  height:100%;
  box-sizing:border-box;
  border-radius:100px;
  overflow: hidden;
  border:1px solid #999;
  left:0;
  top:0;
}
.sBtn{
  border-color:transparent;
  box-sizing:border-box;
  position: relative;
  z-index:999;
  background:none;
  padding:0px 16px !important;
  height:36px!important;
  line-height:36px!important;
}
.sBtn2{
  width:145px;
  height:100px;
  box-sizing:border-box;
  text-align:left;
  display:block;
  color:#999;
  padding:15px 16px 15px 16px !important;
  align-items:flex-start !important;
  justify-content:flex-start!important;
}
.sBtn2c{
  width:24px;
  height:24px;
  text-align:center;
  line-height:23px;
  font-size:11px;
  font-weight:bold;
  border-radius:100px;
}
.sBtn2txt{
  font-size:13px;
  font-weight:bold;
  margin-top:10px;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  white-space: normal !important;
  line-height:18px;
  height:36px;
}
.textword{
  font-weight:bold;
  font-size:14px;
}
.icodiv{
  width:40px;
  height:40px;
  border-radius:100px;
  margin-bottom:10px;
}

.completeDiv{
  position:relative;
  /* background:red; */
}
.completePosw{
  position:absolute;
  top:0px;
  right:10px;
  height:100%;
}
.completePos{
  color:#3168F5;
  font-size: 12px;
  background:rgba(166, 202, 255, 0.2);
  border-radius:4rpx;
  padding:2px 12px;
  cursor: pointer;
  margin-top:2px;
}
.completesvg{
  margin-right:4px;
}

.pd20{
  padding:20px 24px 24px 24px;
}