import { useTranslation } from 'react-i18next'
import Button from '@/app/components/base/button'
import '@/app/styles/switchLongTxt.scss'
import { useChatWithHistoryContext } from '../../context'
import { useEffect, useState } from 'react'
import Field from '@/app/components/app/configuration/config-var/config-modal/field'
import Textarea from 'rc-textarea'
import { Space, DatePicker, DatePickerProps, Select, Input, Tooltip } from 'antd'
import type { RangePickerProps } from 'antd/es/date-picker'
import { GetTemplateList } from '@/service/share'
import { PlusOutlined, EllipsisOutlined } from '@ant-design/icons'

interface TypeSelectorProps {
  selectedType: string
  chatId:any,
  onTypeSelect: (type: string, item: any) => void
}

const TypeSelector = ({ selectedType, chatId, onTypeSelect }: TypeSelectorProps) => {
  const { t } = useTranslation()
  const [topicType, setopicType] = useState<string>('')
  const [ReportType, seReportType] = useState<string>('')
  const [variable, setVariable] = useState<string>('')
  const { setChatAllTypeResult, chatAllTypeResult, appId } = useChatWithHistoryContext()
  const { RangePicker } = DatePicker
  const [formValues, setFormValues] = useState<Record<string, string>>({})
  const [templateList, setTemplateList] = useState<any[]>([])
  const [templateList1, setTemplateList1] = useState<any[]>([])
  const [templateList2, setTemplateList2] = useState<any[]>([])

  useEffect(() => {
    handleGetTemplateList(selectedType)
  }, [selectedType])

  const handleGetTemplateList = async (e: any) => {
    const res = await GetTemplateList(
      {
        app_id: chatId,
        level1: e,
        get_type: 'all'
      },
      true
    )
    if (res) {
      setTemplateList(res as any)
    }
  }

  const handlePayloadChange = (key: string, value: any) => {
    setVariable(value)
    const newFormValues = {
      ...formValues,
      [key]: value
    }
    setFormValues(newFormValues)
    onTypeSelect('Form',newFormValues);

    // const updatedForm = {
    //   form: newFormValues
    // }

    // setChatAllTypeResult(updatedForm)
  }

  const clearFormValues = () => {
    setFormValues({})
    const updatedForm = {
      form: {}
    }
    setChatAllTypeResult(updatedForm)
  }

  return (
    <div className="TopicType">
      <div className="mt-2">
        <div>{t('sampleTemplate.IntelligentAgent.TopicType')}</div>
        {templateList.map((item: any, index: number) => (
          <Button
            key={index}
            variant="secondary-accent"
            size="small"
            className={`shrink-0 TopicButtons flowbtn ${topicType === item.text ? 'TopicActive' : ''}`}
            onClick={() => {
              seReportType('')
              setopicType(item.text)
              setTemplateList1(item.children)
              const data = {
                text: item.text
              }
              onTypeSelect('topic', data)
            }}
          >
            {/* <PlusOutlined size={16} /> */}
            {item.text}
          </Button>
        ))}
      </div>
      <div className="mt-2">
        {topicType && (
          <>
            <div>{t('sampleTemplate.IntelligentAgent.ReportType')}</div>
            {templateList1.map((item: any, index: number) => (
              <Button
                key={index}
                variant="secondary-accent"
                size="small"
                className={`shrink-0 TopicButtons flowbtn ${ReportType === item.text ? 'TopicActive' : ''}`}
                onClick={() => {
                  clearFormValues()
                  seReportType(item.text)
                  setTemplateList2(item.children)
                  const data = {
                    text: item.text
                  }
                  onTypeSelect('Report', data)
                }}
              >
                 {/* <PlusOutlined size={16} /> */}
                {item.text}
              </Button>
            ))}
          </>
        )}
      </div>
      <div>
        {ReportType
          ? templateList2.map((item: any, index: number) => (
            <Field
              key={index}
              className={`mt-2 Fieldbtn ${item.required ? 'FieldBefore' : ''}`}
              title={item.title}
            >
              <Tooltip title={item.placeholder}>
                <Input
                  key={index}
                  className="mt-1"
                  type={item.type}
                  value={formValues[item.field ? item.field : item.title]}
                  onChange={e => {
                    const value = e.target.value;
                    const numericValue = item.type === 'number' ? Math.max(0, Number(value)) : value;
                    handlePayloadChange(item.field ? item.field : item.title, String(numericValue));
                  }}
                  placeholder={item.placeholder}
                />
              </Tooltip>
            </Field>
          ))
          : null}
      </div>
      {/* <div>{JSON.stringify(chatAllTypeResult)}</div> */}
    </div>
  )
}

export default TypeSelector
