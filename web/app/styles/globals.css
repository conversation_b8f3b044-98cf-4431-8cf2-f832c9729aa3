@import "preflight.css";
@tailwind base;
@tailwind components;
@tailwind utilities;

@import '../../themes/light.css';
@import '../../themes/dark.css';
@import './custom.css';
@import './antd.css';
@import '../components/base/button/styles/index.css';
@import '../components/base/action-button/index.css';
@import '../components/base/modal/styles/index.css';
@import './components.css';

html[data-changing-theme] * {
  transition: none !important;
}
:root {
  --max-width: 1100px;
  --border-radius: 12px;
  --font-mono: ui-monospace, Menlo, Monaco, "Cascadia Mono", "Segoe UI Mono",
    "Roboto Mono", "Oxygen Mono", "Ubuntu Monospace", "Source Code Pro",
    "Fira Mono", "Droid Sans Mono", "Courier New", monospace;

  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;

  --primary-glow: conic-gradient(from 180deg at 50% 50%,
      #16abff33 0deg,
      #0885ff33 55deg,
      #54d6ff33 120deg,
      #0071ff33 160deg,
      transparent 360deg);
  --secondary-glow: radial-gradient(rgba(255, 255, 255, 1),
      rgba(255, 255, 255, 0));

  --tile-start-rgb: 239, 245, 249;
  --tile-end-rgb: 228, 232, 233;
  --tile-border: conic-gradient(#00000080,
      #00000040,
      #00000030,
      #00000020,
      #00000010,
      #00000010,
      #00000080);

  --callout-rgb: 238, 240, 241;
  --callout-border-rgb: 172, 175, 176;
  --card-rgb: 180, 185, 188;
  --card-border-rgb: 131, 134, 135;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  font-family: "PingFang SC" !important;
  outline: none !important;
/*   scrollbar-width: thin;
  scrollbar-color: #d9dce3 transparent; */
}
html,
body {
  max-width: 100vw;
  overflow: hidden;
}
body {
  color: rgb(var(--foreground-rgb));
  user-select: none;
}
a {
  color: inherit;
  text-decoration: none;
  outline: none;
}
button:focus-within {
  outline: none;
}
.h1 {
  padding-bottom: 1.5rem;
  line-height: 1.5;
  font-size: 1.125rem;
  color: #111928;
}
.h2 {
  font-size: 14px;
  font-weight: 600;
  color: #111928;
  line-height: 1.5;
}
::-webkit-scrollbar {
  width: 7px;
  height: 7px;
  margin-right: 4px;
}
::-webkit-scrollbar-thumb {
  background: #d9dce3; /* 滑块背景色 */
  border-radius: 5px;
}
::-webkit-scrollbar-thumb:hover {
  background: #BEC3CF; /* 滑块 hover 时的背景色 */
}
::-webkit-scrollbar-track {
  background: transparent; /* 轨道背景色 */
}
.midWen{
  margin-left:auto !important;
}
.f16{font-size:16px !important;}
.messageDiv{
  width:32px;
  height:32px;
  border:1px solid #ddd;
  border-radius:100px;
  margin-left:6px;
  margin-right:0px;
  font-size:18px;
  color:#666;
}
.smallBrd{
  border-radius:4px;
  padding:0 12px;
}
.smallBrd:hover{
  background:#4086FF;
}

.del-modal.ant-modal .ant-modal-body{
  padding:15px !important;
  margin-bottom:0 !important;
}
.del-modal.ant-modal .ant-modal-content{
  border:none !important;
}

.pbN{
  padding-bottom:0 !important;
}
.hhd{
  white-space: normal;
  line-height:18px;
  height:36px;
}

.bot20{
  bottom:20px !important;
}

.mt25{}

.fz14{
  font-size:14px !important;
  height: 1.75rem !important;
}

.gap-x{
  gap: 0.52rem;
}
.wrr{
  word-break: break-all;
}

.socDiv{
  min-height:400px;
  overflow-y:scroll;
}

.xq-columns{display:flex; flex-direction:column;}
.xq-wrap{display:flex;  flex-direction:row; flex-wrap:wrap;}
.xq-nowrap{display:flex; flex-direction:row; flex-wrap:nowrap;}
.xq-flex-between{display:flex; flex-direction:row; justify-content:space-between;}
.xq-flex-around{display:flex; flex-direction:row; justify-content:space-around;}
.xq-flex-center{display:flex; flex-direction:row; justify-content:center;}
.xq-flex-end{display:flex; flex-direction:row; justify-content:flex-end;}
.xq-flex-cbetween{display:flex; flex-direction:row; justify-content:space-between;align-items:center;}
.xq-flex-vtop{align-items:flex-start;}
.xq-flex-vcenter{align-items:center;}
.xq-flex-vbottom{align-items:flex-end;}
.xq-no-scale{flex-shrink:0;}
.xq-flex1{flex:1;}
.xq-flex-v1{display:flex; flex-direction:column; flex:1;}
.xq11{font-size:11px !important;}
.xq12{font-size:12px !important;}
.xq13{font-size:13px !important;}
.xq14{font-size:14px !important;}
.xq15{font-size:15px !important;}
.xq16{font-size:16px !important;}
.xq17{font-size:17px !important;}
.xq18{font-size:18px !important;}
.xq20{font-size:20px !important;}
.xq-border-box{
  box-sizing:border-box;
-moz-box-sizing:border-box; /* Firefox */
-webkit-box-sizing:border-box; /* Safari */
}

.bigTel{
  font-weight:bold;
  font-size:22px;
  margin-bottom:5px;
}
.xq100{width:100%;}

.active a{
  color:blue;
}
.inputbtn{
  padding:7px 8px;
  border:1px solid #D9DCE3;
  margin:0px 8px 8px 0;
  border-radius:4px;
  cursor: pointer;
  background:#fff;
}
.xqicon{
  width:14px;
  height:14px;
  color: #666;
  margin-right:4px;
}
.xqword{
  font-size:13px;
  color:#181818;
}

.xq-full{
  height:calc(100vh - 60px);
}
.xqAc{
  height:40px;
  background:#fff;
}
.hisc{
  margin-right:10px;
  padding:4px 8px;
  position:relative;
  z-index:9999;
  cursor: pointer;
}
.hischild{
  width:320px;
  min-height:150px;
  position:absolute;
  left:0;
  background:#fff;
  box-shadow: 0 0 10px rgba(128, 128, 128, 0.5);
  z-index:9998;
  padding:20px;
  border-radius:5px;
}
.hischildTel{
  font-size:14px;
  font-weight:bold;
  line-height:30px;
}

.hisproce{
  padding-left:16px;
  border-left:1px solid #ddd;
  margin-top:10px;
}
.onetel{
  color:blue;
  font-size:13px;
  position: relative;
  line-height:20px;
  margin-top:-4px;
}
.acq,.acqc{
  width:18px;
  height:18px;
  background:#3168F5;
  border-radius:100px;
  color:#fff;
  font-size:16px;
  text-align:center;
  line-height:14px;
  position:absolute;
  left:-25px;
  top:0px;
}
.bigTel{
  color:#000;
  font-size:13px;
  position: relative;
  line-height:20px;
  margin-top:6px;
}
.acqc{
  background:#EDEFF2;
  line-height:16px !important;
}
.chidsmall,.onesmall{
  color:#5C6273;
  font-size:12px;
  line-height:24px;
  cursor: pointer;
  padding:2px 6px;
}
.rollback{
  color:blue;
  display:none;
}
.chidsmall:hover{
  background:#F7F9FF;
}
.chidsmall:hover .rollback{
  display:block;
}
.webModel{
  position:fixed;
  left:0;
  top:0;
  width:100vw;
  height:100vh;
  background:#fff;
  z-index:9999999999;
}
.xqtr{
  padding:5px 8px;
  cursor: pointer;
}

.bodyDiv{
  background: url(/assets/topbj.png);
  background-size:100% 100%;
}

.xqnormal{
  font-weight:normal !important;
}
.sticky{
  position:sticky;
  bottom:0px !important;
  right:4px;
  background:#eff3f7;
  background: url(/assets/background/background.png);
  background-size:100% auto;
  padding:10px 0px;
  box-sizing:border-box;
  -moz-box-sizing:border-box; /* Firefox */
  -webkit-box-sizing:border-box; /* Safari */
}
.longmodeBot{
  padding-bottom:0px !important;
}

.xqcur{
  cursor: pointer;
}

.p-2-24{
  padding:12px 0.5rem !important;
}