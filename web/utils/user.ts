import Cookies from 'js-cookie'

// 删除用户认证
export const removeAuth = () => {
  localStorage.removeItem('sso_token')
  localStorage.removeItem('refresh_token')
  localStorage.removeItem('console_token')
  localStorage.removeItem('token')
  localStorage.removeItem('operToken')
  Cookies.remove('operationToken')
}

// 获取用户operToken
export const getOperToken = () => {
  if (localStorage.getItem('sso_token'))
    return localStorage.getItem('sso_token')!

  else if (Cookies.get('operationToken'))
    return Cookies.get('operationToken')!

  else
    return ''
}
// 获取公有云用户凭据
export const getPublicUserToken = () => {
  return Cookies.get('operToken')
}
