'use client'

import { useMemo } from 'react'
import { ConfigProvider, Menu } from 'antd'
import type { MenuProps } from 'antd'
import { t } from 'i18next'
import { useContext } from 'use-context-selector'
import s from './styles/dropdown.module.css'
import type { IWorkspace } from '@/models/common'
import { switchWorkspace } from '@/service/common'
// 公共组件
import { useWorkspacesContext } from '@/context/workspace-context'
import { ToastContext } from '@/app/components/base/toast'
import { Check } from '@/app/components/base/icons/src/vender/line/general'
import cn from '@/utils/classnames'
import { PRIMARY } from '@/themes/var-define'

const itemIconClassName = `
  shrink-0 mr-2 flex items-center justify-center w-4 h-4 bg-[#DEECFF] rounded-sm text-[10px] leading-[10px] text-primary-P1
`
const itemNameClassName = `
  grow mr-2 text-S1 leading-H1 text-gray-G1 text-left
`
const itemCheckClassName = `
  shrink-0 w-4 h-4 text-primary-600
`
export const labelRender = (item: IWorkspace, showCurrent = true) => {
  return (
    <div className='flex items-center h-full'>
      <div className={itemIconClassName}>{item.name[0].toLocaleUpperCase()}</div>
      <div className={itemNameClassName}>{item.name}</div>
      {showCurrent && item.current && <Check className={itemCheckClassName} />}
    </div>
  )
}

const WorkspaceSelector = () => {
  const { notify } = useContext(ToastContext)
  const { workspaces } = useWorkspacesContext()
  const currentWorkspace = workspaces.find(item => item.current)

  // 菜单列表
  const menuItemList = useMemo(() => ([{
    key: `${currentWorkspace!.id}-0`,
    label: labelRender(currentWorkspace!, false),
    children: workspaces.map((item) => {
      return {
        key: `${item.id}`,
        label: labelRender(item),
      }
    }),
    popupClassName: 'worker-selector-submenu',
    popupOffset: [0, -14],
  }]), [currentWorkspace, workspaces])

  /* 切换工作空间 */
  const onClick: MenuProps['onClick'] = async (e) => {
    const tenant_id = e.key
    if (currentWorkspace?.id === tenant_id)
      return
    await switchWorkspace({ url: '/workspaces/switch', body: { tenant_id } })
    notify({ type: 'success', message: t('common.actionMsg.modifiedSuccessfully') })
    location.assign(`${location.origin}`)
  }

  if (workspaces.length === 1)
    return null

  return (
    <>
      <div className={cn(s.workSpaceTip)}>{t('account.info.workspace')}</div>
      <ConfigProvider
        theme={
          {
            components: {
              Menu: {
                fontSize: 12,
                itemMarginBlock: 0,
                itemBorderRadius: 0,
                subMenuItemBorderRadius: 0,
                // itemHoverBg: 'transparent',
                subMenuItemBg: PRIMARY.P4,
                borderRadiusLG: 4,
                borderRadius: 4,
                paddingXS: 4,
              },
            },
          }
        }
      >
        <Menu
          className='worker-selector pb-2 border-gray-G6 border-b'
          items={menuItemList}
          mode='vertical'
          onClick={onClick}
        ></Menu>
      </ConfigProvider>
    </>
  )
}
export default WorkspaceSelector
