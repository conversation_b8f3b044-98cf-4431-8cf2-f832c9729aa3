
.radioItem:hover .radio {
  border-color: var(--color-primary-P1);
}
.radioItem {
  border-radius: 4px;
}
.enhancementSetSty{
  :global{
    .ant-card-body{
      padding: 12px;
    }

  }
}
.Kno-card-sty{
  :global{
    .ant-card-body{
      padding: 10px;
    }

  }
}
.Kno-card-sty:hover{
  border-color: #4086ff ;
}
.Kno-card-active{
  @apply  border-primary-P1;
  background: linear-gradient(180deg, #F1F7FF 0%, #FFF 100%);
  border-color: #4086ff ;
}
// .abled-radio-card:hover {
//   @apply border-primary-P1;
//   background: linear-gradient(180deg, #F1F7FF 0%, #FFF 100%);
// }