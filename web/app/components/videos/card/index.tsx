import { useRouter } from 'next/navigation'
import VideoTag from '../tag'
import Operations from './operations'
import { type Video } from '@/types/videos'

// 公共组件
import VideoAvatar from '@/app/components/base/app-info/avatar'
import Card from '@/app/components/base/card'

export type VideosCardProps = {
  video: Video
  onSuccess?: () => void
}

const VideosCard = ({
  video,
  onSuccess,
}: VideosCardProps) => {
  const { push } = useRouter()

  return (
    <Card
      onClick={() => push(`/videos/${video.id}/documents`)}
      generateHead={() => <VideoAvatar appMode={video.type} size={48}></VideoAvatar>}
      generateTag={() => (
        <VideoTag mode={video.type}></VideoTag>
      )}
      description={video.description || ''}
      title={video.name}
      layout='line'
    >
      {<Operations video={video} onSuccess={onSuccess}></Operations>}
    </Card>
  )
}

export default VideosCard
