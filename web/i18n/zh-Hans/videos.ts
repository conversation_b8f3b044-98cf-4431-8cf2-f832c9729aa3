const translation = {
  title: '视频库',
  type: {
    all: '全部',
    img: '图片',
    video: '视频',
    videosBuket: '视频流',
  },
  // 动作
  action: {
    createVideos: '创建视频库',
    editVideos: '编辑视频库',
    selectVideos: '选择视频库',
    addFile: '添加文件',
  },
  // 弹窗标题
  modalTitle: {
    deleteVideoConfirmTitle: '视频库删除',
    videoSetting: '视频库编辑',
    detail: '文件详情',
    edit: '编辑文件',
  },
  // 提示和公告
  notify: {
    deleteDocTip: '确定删除吗？',
    deleteDocTipDesc: '如果您需要稍后恢复处理，您将从您离开的地方继续',
    videoTip: '只存储近10天实时视频内容',
    noPermission: '想使用该功能请联系官方人员',
    videoFull: '最多可创建{{num}}个视频库，请删除后重试。',
    noVideo: '没有可用的视频库',
    videoDeleted: '视频库已删除',
    videoUsedByApp: '某些应用正在使用该视频库。应用将无法再使用该视频库,所有的提示配置和日志将被永久删除。',
    deleteVideoConfirmContent:
    '删除视频库后，调用该视频库的工具和应用会同步失效。删除操作不可逆，是否确认删除?',
  },
  // 输入占位符
  placeholder: {
    namePlaceholder: '请输入视频库名称',
    descPlaceholder: '描述这个视频库中的内容。详细的描述可以让 AI 及时访问视频库的内容。如果为空，智能体平台 将使用默认的命中策略。',
  },
  info: {
    createType: '创建类型',
    name: '视频库名称',
    nameError: '视频库名称不符合要求',
    nameDescription: '视频库名称仅支持中文、英文、数字、下划线(_)、中划线(-)、英文点(.)(1~50字符)',
    desc: '视频库描述',
    doc: '文档',
  },
  create: {
    header: {
      creation: '创建视频库',
      update: '上传文件',
    },
    uploader: {
      title: '上传文本文件',
      button: '拖拽文件至此，或者',
      browse: '选择文件',
      tip: '支持 {{supportTypes}}',
      tip2: '每个文件不超过 {{size}}MB，最多同时上传 {{filesNumber}} 个文件',
      validation: {
        typeError: '文件类型不支持',
        size: '文件太大了，不能超过 {{size}}MB',
        count: '暂不支持多个文件',
        filesNumber: '批量上传限制 {{filesNumber}}。',
      },
      cancel: '取消',
      change: '更改文件',
      failed: '上传失败',
    },
    form: {
      name: '视频库名称',
      namePlaceholder: '请输入视频库名称',
      nameDescription: '视频库名称仅支持中文、英文、数字、下划线(_)、中划线(-)、英文点(.)(1~50字符)',
      nameExist: '视频库名称已存在',
      desc: '视频库描述',
      descPlaceholder: '请输入视频库描述',
      uploader: '数据上传',
      urlNamePlaceholder: '请输入url名称',
      urlNameMaxLength: 'url名称最多50个字符',
      urlName: 'url名称',
      urlInfo: 'url网址信息',
      urlInfoPlaceholder: '请输入url网址信息',
      urlInfoMaxLength: 'url网址信息最多255个字符',
      urlInfoFormat: '请输入正确的url网址信息，当前仅支持rtsp协议',
      urlInfoRepeat: 'url网址信息已存在',
      frameRate: '切帧频率',
      frameRatePlaceholder: '请选择频率',
      fileNumLimit: '单个视频库最多支持上传{{max}}个文件，您还可以上传{{num}}个文件',
    },
  },
  doc: {
    info: {
      fileName: '文件名',
      uploadTime: '上传时间',
      addTime: '添加时间',
      deviceId: '设备ID',
      locationIp: '地点IP',
      urlName: 'url名称',
      urlSite: 'url网址',
      action: '操作',
      siteStatus: '网址实时状态',
      status: '处理状态',
      processTime: '入库时间',
      errorInfo: '异常开始时间点：',
      errorInfoTip: '（仅展示最近五条）',
      originFileName: '原始文件名称',
      originFileSize: '原始文件大小',
      frameRate: '切帧频率',
      lastUpdateTime: '最后更新时间',
    },
    status: {
      waiting: '待处理',
      processing: '处理中',
      failed: '处理失败',
      success: '处理完成',
      abnormal: '异常',
      normal: '正常',
      paused: '暂停',
    },
  },
}

export default translation
