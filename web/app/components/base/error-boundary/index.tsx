import { Component } from 'react'

// **Add an ECharts runtime error handler
// Avoid error #7832 (Crash when ECharts accesses undefined objects)
// This can happen when a component attempts to access an undefined object that references an unregistered map, causing the program to crash.

interface ErrorBoundaryProps {
  errorText?: string;
  children?: React.ReactNode;
}
export default class ErrorBoundary extends Component<ErrorBoundaryProps> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  componentDidCatch(error: any, errorInfo: any) {
    this.setState({ hasError: true })
    console.error(error, errorInfo)
  }

  render() {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    if (this.state.hasError) {
      const { errorText } = this.props
      if (errorText) 
        return (
          <div className='desc-12'>{errorText}</div>
        )
      else
        return (
          <div>
            Oops! An error occurred. This could be due to an ECharts runtime error or invalid SVG
            content. <br />
            (see the browser console for more information)
          </div>
        )
    }
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    return this.props.children
  }
}