import { memo } from 'react'
import { useTranslation } from 'react-i18next'
import type { Collection } from '../types'
import type { AgentTool } from '@/types/tools'
// 公共能力
import cn from '@/utils/classnames'
import { DefaultToolIcon } from '@/app/components/base/icons/src/public/other'
import { AlertTriangle } from '@/app/components/base/icons/src/vender/line/alertsAndFeedback'
import Tooltip from '@/app/components/base/tooltip'
import Avatar from '@/app/components/base/avatar'
import { GRAY } from '@/themes/var-define'

type ToolItemProps = {
  item: AgentTool & { icon_url: any; collection?: Collection }
  operations?: React.ReactElement
  lack?: boolean
  border?: boolean
  className?: string
  textClassName?: string
}

const ToolItem = ({
  item,
  operations,
  border = false,
  lack = false,
  className,
  textClassName,
}: ToolItemProps) => {
  const { t } = useTranslation()
  /* 默认图标 */
  const DefaultIcon = () => {
    return (
      <DefaultToolIcon className='w-6 h-6' />
    )
  }
  /* 被删除后图标 */
  const deletedIcon = () => {
    return (
      <Tooltip popupContent={t('common.status.deleted')}>
        <div className='shrink-0 flex items-center justify-center w-6 h-6 bg-[#F5F8FF] rounded-md border-[0.5px] border-[#E0EAFF]'>
          <AlertTriangle className='w-4 h-4 text-[#F79009]' />
        </div>
      </Tooltip>
    )
  }

  return (
    <div
      className={cn(
        'group relative flex justify-between items-center last-of-type:mb-0 h-[40px] py-2 w-full rounded',
        className,
        border ? 'border border-gray-G5 !px-3' : '',
      )}
    >
      <div className='flex grow w-0 items-center'>
        {/* 图标 */}
        { lack
          ? deletedIcon()
          : (!(item.icon_url || item.tool_icon)
            ? DefaultIcon()
            : <Avatar
              size={24}
              className={cn('!rounded !bg-white')}
              avatar={item.icon_url}
            ></Avatar>
          )}
        {/* 名称 */}
        <div title={item.tool_label} className={cn('text-S3 leading-H3 ml-2 grow w-0 truncate', textClassName)} style={{
          color: GRAY.G1,
        }}>{ item.tool_label }</div>
      </div>
      {/* 操作栏 */}
      { operations }
    </div>
  )
}

export default memo(ToolItem)
