'use client'

import type { FC } from 'react'
import { useTranslation } from 'react-i18next'
import ErrorMsgPanel from '../error-msg-panel'
import s from './styles/index.module.css'
import type { ErrorMsg } from '@/types/videos'
import { ProcessStatus } from '@/types/videos'

import cn from '@/utils/classnames'
import type { IndicatorProps } from '@/app/components/base/indicator'
import Indicator from '@/app/components/base/indicator'

// 获取索引状态
const useIndexStatus = () => {
  const { t } = useTranslation()
  return {
    [ProcessStatus.Waiting]: { color: 'blue', text: t('videos.doc.status.waiting') }, // waiting
    [ProcessStatus.Processing]: { color: 'blue', text: t('videos.doc.status.processing') }, // indexing splitting parsing cleaning
    [ProcessStatus.Failed]: { color: 'red', text: t('videos.doc.status.failed') }, // error
    [ProcessStatus.Success]: { color: 'green', text: t('videos.doc.status.success') }, // completed，archived = true
  }
}

// status item for list
const ProceessStatusIndicator: FC<{
  status: ProcessStatus
  textCls?: string
  errorMessage?: Array<ErrorMsg>
}> = ({ status, textCls = '', errorMessage }) => {
  const DOC_INDEX_STATUS_MAP = useIndexStatus()
  const localStatus = status as keyof typeof DOC_INDEX_STATUS_MAP
  return <div className={
    cn('flex items-center', s.ProceessStatusIndicatorDetail)
  }>
    {/* 状态标识 */}
    <Indicator color={DOC_INDEX_STATUS_MAP[localStatus]?.color as IndicatorProps['color']} />
    {/* 状态词 */}
    <span className={textCls}>{DOC_INDEX_STATUS_MAP[localStatus]?.text}</span>

    {/* 错误信息 */}
    {
      (errorMessage && errorMessage.length)
        ? (
          <ErrorMsgPanel msg={errorMessage}></ErrorMsgPanel>
        )
        : <></>
    }
  </div>
}

export default ProceessStatusIndicator
