import type { FC } from 'react'
import { Tooltip as ATooltip } from 'antd'
import type { TooltipPlacement } from 'antd/lib/tooltip'
import Image from 'next/image'
import React from 'react'
export type TooltipProps = {
  arrow?: boolean
  position?: TooltipPlacement
  triggerClassName?: string
  disabled?: boolean
  triggerMethod?: 'hover' | 'click' | 'focus' | 'contextMenu'
  popupContent?: string | React.ReactNode
  children?: React.ReactNode
  overlayInnerStyle?: object
  needsDelay?: boolean
  zIndex?: number
}

const Tooltip: FC<TooltipProps> = ({
  arrow = true,
  position = 'top',
  triggerMethod = 'hover',
  triggerClassName,
  disabled,
  popupContent,
  children,
  overlayInnerStyle,
  needsDelay = false,
  zIndex,
}) => {
  return (
    <ATooltip
      arrow={arrow}
      title={disabled ? null : popupContent}
      placement={position}
      trigger={triggerMethod}
      mouseEnterDelay={needsDelay ? 0.5 : 0.1}
      // overlayInnerStyle={overlayInnerStyle}
      styles={overlayInnerStyle}
      zIndex={zIndex}
    >
      {children || (<div className={triggerClassName || 'w-4 h-4 shrink-0'}>
        <Image src='/assets/icons/question.svg' width={16} height={16} alt='question' />
      </div>)}
    </ATooltip>
  )
}

export default React.memo(Tooltip)
