import { useCallback, useEffect, useRef, useState } from 'react'
import produce from 'immer'
import { useAsyncEffect } from 'ahooks'
import { isArray } from 'lodash-es'
import { useTranslation } from 'react-i18next'
import { NODES_INITIAL_DATA } from '../../constants/block-data'
import { type AdvanceSetting, AgentCreateMode, type AgentNodeType } from './types'
import DefaultConfig from './default'

import useAvailableVarList from '@/app/components/workflow/nodes/_base/hooks/use-available-var-list'
import useNodeCrud from '@/app/components/workflow/nodes/_base/hooks/use-node-crud'
import useOneStepRun from '@/app/components/workflow/nodes/_base/hooks/use-one-step-run'
import { WINDOW_SIZE_DEFAULT } from '@/app/components/workflow/nodes/_base/components/memory-config'
import useOutputVarList from '@/app/components/workflow/nodes/_base/hooks/use-output-var-list'
import type { AgentTool, ToolItem } from '@/types/tools'
import type { DataSet } from '@/models/datasets'
import type { DatasetConfigs } from '@/models/debug'
import { fetchDatasets } from '@/service/datasets'

// 账户设置公共能力
import { useModelListAndDefaultModelAndCurrentProviderAndModel } from '@/app/components/account-setting/model-provider-page/hooks'
import { ModelTypeEnum } from '@/app/components/account-setting/model-provider-page/declarations'
// 工作流公共能力
import { BlockEnum, EditionType, PromptRole, TextVarFormat, VarType } from '@/app/components/workflow/types'
import type { Memory, ModelConfig, PromptItem, ValueSelector, Var, Variable } from '@/app/components/workflow/types'
import { useStore, useWorkflowStore } from '@/app/components/workflow/store'
import {
  useConfigVision,
  useIsChatMode,
  useNodesReadOnly,
} from '@/app/components/workflow/hooks'
import { RETRIEVAL_OUTPUT_STRUCT } from '@/app/components/workflow/constants/index'
// 公共能力
import { checkHasContextBlock, checkHasHistoryBlock, checkHasQueryBlock } from '@/app/components/base/prompt-editor/constants'
import { CollectionType } from '@/app/components/tools/types'
import { checkAppExist } from '@/service/apps'

export const useMiniConfig = (id: string, payload: AgentNodeType) => {
  const { t } = useTranslation()
  const workflowStore = useWorkflowStore()
  const { customTools } = workflowStore.getState()
  const { inputs, setInputs: doSetInputs } = useNodeCrud<AgentNodeType>(id, {
    // @ts-expect-error 存在没有该值的情况
    tools: DefaultConfig.defaultValue.tools,
    // @ts-expect-error 存在没有该值的情况
    dataset_configs: DefaultConfig.defaultValue.dataset_configs,
    // @ts-expect-error 存在没有该值的情况
    advance_setting: DefaultConfig.defaultValue.advance_setting,
    // @ts-expect-error 存在没有该值的情况
    query: DefaultConfig.defaultValue.query,
    ...payload,
    model: {
      ...DefaultConfig.defaultValue.model,
      ...payload.model,
    },
  })
  // 节点数据
  const inputRef = useRef(inputs)
  // 包装用户输入
  const setInputs = useCallback((newInputs: AgentNodeType) => {
    doSetInputs(newInputs)
    inputRef.current = newInputs
  }, [doSetInputs])
  // 知识库详情
  const [datasetList, setDatasetList] = useState<DataSet[]>([])
  // 当前智能体节点模式
  const isEmptyMode = inputs.create_mode === AgentCreateMode.Empty

  // 删除知识库配置
  const handleRemoveDataset = useCallback((id: string) => {
    const newInputs = produce(inputs, (draft) => {
      const index = (draft.dataset_configs.datasets?.datasets || []).findIndex(item => item.dataset.id === id)
      if (index > -1) {
        draft.dataset_configs.datasets?.datasets.splice(index, 1)
        draft.dataset_configs.lack = datasetList.filter(item => item.id !== id).length !== draft.dataset_configs.datasets?.datasets.length
      }
    })
    const newDatasets = produce(datasetList, (draft) => {
      const index = draft.findIndex(item => item.id === id)
      draft.splice(index, 1)
    })
    setInputs(newInputs)
    setDatasetList(newDatasets)
  }, [datasetList, inputs, setInputs])
  // 选择知识库选项
  const handleSelectDataset = useCallback((datasets: DataSet[]) => {
    const newInputs = produce(inputs, (draft) => {
      draft.dataset_configs.datasets = {
        datasets: datasets.map(item => ({
          dataset: {
            id: item.id,
            enabled: true,
          },
        })),
      }
      draft.dataset_configs.fork = false
      draft.dataset_configs.lack = datasets.length !== draft.dataset_configs.datasets?.datasets.length
    })
    setInputs(newInputs)
    setDatasetList(datasets)
  }, [inputs, setInputs])
  // 变更知识库配置
  const handleConfigDataset = useCallback((datasetConfig: DatasetConfigs) => {
    const newInputs = produce(inputs, (draft) => {
      draft.dataset_configs = {
        ...datasetConfig,
        datasets: {
          datasets: datasetConfig.datasets?.datasets.map(item => ({
            dataset: {
              enabled: item.enabled,
              id: item.id,
            },
          })) || [],
        },
      }
    })
    setInputs(newInputs)
  }, [inputs, setInputs])
  // 获取知识库
  useAsyncEffect(async () => {
    let result: any = {
      data: [],
    }
    if (inputs.dataset_configs.datasets?.datasets.length) {
      result = await fetchDatasets({ url: '/datasets', params: { page: 1, ids: inputs.dataset_configs.datasets.datasets.map(item => item.dataset.id) } })
      setDatasetList(result.data)
    }
    const newInputs = produce(inputs, (draft) => {
      if (isArray(draft.prompt_template)) {
        if (!draft.prompt_template[0].new_level || !draft.prompt_template[1]?.new_level) {
          draft.prompt_template = [
            {
              role: PromptRole.system,
              new_level: true,
              text: draft.prompt_template.map(item => item.role === PromptRole.system ? item.text : '').join(''),
            },
            {
              role: PromptRole.user,
              new_level: true,
              text: draft.prompt_template.map(item => item.role === PromptRole.user ? item.text : '').join(''),
            },
            ...draft.prompt_template,
          ]
        }
      }
      draft.dataset_configs.lack = result.data.length !== draft.dataset_configs.datasets?.datasets.length
    })
    setInputs(newInputs)
  }, [])
  // 处理工具初始化
  useEffect(() => {
    const newInputs = produce(inputs, (draft) => {
      if (draft.tools.length) {
        draft.tool_lack = (draft.tools as AgentTool[]).some(item => item.provider_type === CollectionType.custom
            && customTools.find(provider => provider.id === item.provider_id && provider.tools.some(tool => tool.name === item.tool_name)))
        draft.tools = (draft.tools as AgentTool[]).map((item) => {
          if (item.provider_type === CollectionType.custom) {
            const provider = customTools.find(provider => provider.id === item.provider_id)
            const tool = provider?.tools.find(tool => tool.name === item.tool_name)
            if (!tool || !provider)
              return { ...item, isDeleted: true }
            return item
          }
          return item
        })
      }
    })
    setInputs(newInputs)
  }, [customTools])
  // 处理智能体节点
  useAsyncEffect(async () => {
    if (!isEmptyMode) {
      // 查询appid是否存在
      const result = await checkAppExist(inputs.site_app_id)
      if (!result.exists) {
        setInputs({
          ...inputs,
          ...NODES_INITIAL_DATA.agent,
          title: t(`workflow.blocks.${BlockEnum.Agent}`),
        })
      }
    }
  }, [])

  return {
    datasetList,
    setDatasetList,
    handleRemoveDataset,
    handleSelectDataset,
    handleConfigDataset,
  }
}

const useConfig = (id: string, payload: AgentNodeType) => {
  const filterInputVar = useCallback((varPayload: Var) => {
    return [VarType.number, VarType.string, VarType.secret, VarType.arrayString, VarType.arrayNumber].includes(varPayload.type)
  }, [])
  const filterMemoryPromptVar = useCallback((varPayload: Var) => {
    return [VarType.arrayObject, VarType.array, VarType.number, VarType.string, VarType.secret, VarType.arrayString, VarType.arrayNumber].includes(varPayload.type)
  }, [])

  const { nodesReadOnly: readOnly } = useNodesReadOnly()
  const isChatMode = useIsChatMode()
  const defaultConfig = useStore(s => s.nodesDefaultConfigs)[payload.type]
  const { datasetList, handleConfigDataset, handleSelectDataset, handleRemoveDataset } = useMiniConfig(id, payload)
  const { inputs, setInputs: doSetInputs } = useNodeCrud<AgentNodeType>(id, {
    // @ts-expect-error 存在没有该值的情况
    tools: DefaultConfig.defaultValue.tools,
    // @ts-expect-error 存在没有该值的情况
    dataset_configs: DefaultConfig.defaultValue.dataset_configs,
    // @ts-expect-error 存在没有该值的情况
    advance_setting: DefaultConfig.defaultValue.advance_setting,
    // @ts-expect-error 存在没有该值的情况
    query: DefaultConfig.defaultValue.query,
    ...payload,
    model: {
      ...DefaultConfig.defaultValue.model,
      ...payload.model,
    },
  })
  const { currentProvider, currentModel } = useModelListAndDefaultModelAndCurrentProviderAndModel(ModelTypeEnum.textGeneration)
  const {
    availableVars,
    availableNodesWithParent,
  } = useAvailableVarList(id, {
    onlyLeafNodeVar: false,
    filterVar: filterMemoryPromptVar,
  })
  // single run
  const {
    isShowSingleRun,
    hideSingleRun,
    getInputVars,
    runningStatus,
    handleRun,
    handleStop,
    runInputData,
    setRunInputData,
    runResult,
    toVarInputs,
  } = useOneStepRun<AgentNodeType>({
    id,
    data: inputs,
    defaultRunInputData: {
      '#context#': [RETRIEVAL_OUTPUT_STRUCT],
      '#files#': [],
    },
  })

  // 角色前缀
  const [defaultRolePrefix, setDefaultRolePrefix] = useState<{ user: string; assistant: string }>({ user: '', assistant: '' })
  // 节点数据
  const inputRef = useRef(inputs)
  // 模型变化
  const [modelChanged, setModelChanged] = useState(false)
  // 包装用户输入
  const setInputs = useCallback((newInputs: AgentNodeType) => {
    // 如果没有角色前缀，使用默认前缀
    if (newInputs.memory && !newInputs.memory.role_prefix) {
      const newPayload = produce(newInputs, (draft) => {
        draft.memory!.role_prefix = defaultRolePrefix
      })
      doSetInputs(newPayload)
      inputRef.current = newPayload
      return
    }
    doSetInputs(newInputs)
    inputRef.current = newInputs
  }, [doSetInputs, defaultRolePrefix])
  // model信息
  const model = inputs.model
  const modelMode = inputs.model?.mode
  const isChatModel = modelMode === 'chat'
  const isCompletionModel = !isChatModel
  // 是否显示jinja输入变量面板
  const isShowVars = (() => {
    if (isChatModel)
      return (inputs.prompt_template as PromptItem[]).some(item => item.edition_type === EditionType.jinja2)

    return (inputs.prompt_template as PromptItem).edition_type === EditionType.jinja2
  })()
  // 单次运行，输入变量值
  const inputVarValues = (() => {
    const vars: Record<string, any> = {}
    Object.keys(runInputData)
      .filter(key => !['#context#', '#files#'].includes(key))
      .forEach((key) => {
        vars[key] = runInputData[key]
      })
    return vars
  })()
  // 节点状态，不知道有啥用
  const hasSetBlockStatus = (() => {
    const promptTemplate = inputs.prompt_template
    const hasSetContext = isChatModel ? (promptTemplate as PromptItem[]).some(item => checkHasContextBlock(item.text)) : checkHasContextBlock((promptTemplate as PromptItem).text)
    if (!isChatMode) {
      return {
        history: false,
        query: false,
        context: hasSetContext,
      }
    }
    if (isChatModel) {
      return {
        history: false,
        query: (promptTemplate as PromptItem[]).some(item => checkHasQueryBlock(item.text)),
        context: hasSetContext,
      }
    }
    else {
      return {
        history: checkHasHistoryBlock((promptTemplate as PromptItem).text),
        query: checkHasQueryBlock((promptTemplate as PromptItem).text),
        context: hasSetContext,
      }
    }
  })()
  // 是否显示上下文提示
  const shouldShowContextTip = !hasSetBlockStatus.context && inputs.context.enabled
  // 视觉配置
  const {
    isVisionModel,
    handleVisionResolutionEnabledChange,
    handleVisionResolutionChange,
    handleModelChanged: handleVisionConfigAfterModelChanged,
  } = useConfigVision(model, {
    payload: inputs.vision,
    onChange: (newPayload) => {
      const newInputs = produce(inputs, (draft) => {
        draft.vision = newPayload
      })
      setInputs(newInputs)
    },
  })
  const contexts = runInputData['#context#']
  const visionFiles = runInputData['#files#']
  const allVarStrArr = (() => {
    const arr = isChatModel ? (inputs.prompt_template as PromptItem[]).filter(item => item.edition_type !== EditionType.jinja2).map(item => item.text) : [(inputs.prompt_template as PromptItem).text]
    if (isChatMode && isChatModel && !!inputs.memory) {
      arr.push('{{#sys.query#}}')
      arr.push(inputs.memory.query_prompt_template)
    }

    return arr
  })()
  const varInputs = (() => {
    const vars = getInputVars(allVarStrArr)
    if (isShowVars)
      return [...vars, ...toVarInputs(inputs.prompt_config?.jinja2_variables || [])]

    return vars
  })()
  // 输入变量hook
  const {
    handleAddVariable: handleAddJsonOutput,
    handleRemoveVariable: handleRemoveJsonOutput,
    handleVarsChange: handleChangeJsonOutput,
  } = useOutputVarList({
    id,
    inputs: inputs.model,
    setInputs: (value: ModelConfig) => {
      const newInputs = produce(inputs, (draft) => {
        draft.model = value
      })
      doSetInputs(newInputs)
      inputRef.current = newInputs
    },
    outputKeyOrders: inputs.model.outputs ? Object.keys(inputs.model.outputs) : [],
    onOutputKeyOrdersChange: () => {},
  })

  // 追加默认提示词配置
  const appendDefaultPromptConfig = useCallback((draft: AgentNodeType, defaultConfig: any, passInIsChatMode?: boolean) => {
    const promptTemplates = defaultConfig.prompt_templates
    if (passInIsChatMode === undefined ? isChatModel : passInIsChatMode) {
      draft.prompt_template = promptTemplates.chat_model.prompts
    }
    else {
      draft.prompt_template = promptTemplates.completion_model.prompt

      setDefaultRolePrefix({
        user: promptTemplates.completion_model.conversation_histories_role.user_prefix,
        assistant: promptTemplates.completion_model.conversation_histories_role.assistant_prefix,
      })
    }
  }, [isChatModel])

  // 变更高级设置
  const handleChangeAdvanceSetting = useCallback((setting: AdvanceSetting) => {
    const newInputs = produce(inputs, (draft) => {
      draft.advance_setting = setting
    })
    setInputs(newInputs)
  }, [inputs, setInputs])
  // 选择工具选项
  const handleSelectTool = useCallback((tools: Array<ToolItem>) => {
    const newInputs = produce(inputs, (draft) => {
      draft.tools = tools
    })
    setInputs(newInputs)
  }, [inputs, setInputs])
  // 处理变更工具
  const handleChangeTool = useCallback((currentTool: any, params: Record<string, any>) => {
    const newInputs = produce(inputs, (draft) => {
      const tool = (draft.tools).find((item: any) => item.provider_id === currentTool?.collection?.id && item.tool_name === currentTool?.tool_name)
      if (tool)
        (tool as AgentTool).tool_parameters = params
    })
    setInputs(newInputs)
  }, [inputs, setInputs])
  // 处理删除工具
  const handleDeleteTool = useCallback((index: number) => {
    const newInputs = produce(inputs, (draft) => {
      draft.tools.splice(index, 1)
    })
    setInputs(newInputs)
  }, [inputs, setInputs])
  // 变更输入变量
  const handleChangeQuery = useCallback((query: string) => {
    const newInputs = produce(inputs, (draft) => {
      draft.query = query
    })
    setInputs(newInputs)
  }, [inputs, setInputs])
  // 变更场景说明
  const handleChangeSceneDesc = useCallback((desc: string) => {
    const newInputs = produce(inputs, (draft) => {
      draft.scene_desc = desc
    })
    setInputs(newInputs)
  }, [inputs, setInputs])
  // 变更输出配置
  const handleOuputConfig = useCallback((type: TextVarFormat) => {
    const newInputs = produce(inputs, (draft) => {
      draft.model.response_format = type
    })
    setInputs(newInputs)
  }, [inputs, setInputs])
  // 变更模型
  const handleModelChanged = useCallback((model: { provider: string; modelId: string; mode?: string }) => {
    const newInputs = produce(inputRef.current, (draft) => {
      draft.model.provider = model.provider
      draft.model.name = model.modelId
      draft.model.mode = model.mode!
      draft.model.response_format = TextVarFormat.Markdown
      const isModeChange = model.mode !== inputRef.current.model.mode
      if (isModeChange && defaultConfig && Object.keys(defaultConfig).length > 0)
        appendDefaultPromptConfig(draft, defaultConfig, model.mode === 'chat')
    })
    setInputs(newInputs)
    setModelChanged(true)
  }, [setInputs, defaultConfig, appendDefaultPromptConfig])
  // 变更模型参数
  const handleCompletionParamsChange = useCallback((newParams: Record<string, any>) => {
    const newInputs = produce(inputs, (draft) => {
      draft.model.completion_params = newParams
    })
    setInputs(newInputs)
  }, [inputs, setInputs])
  // 输入变量位置——添加jinjia变量
  const handleAddEmptyVariable = useCallback(() => {
    const newInputs = produce(inputRef.current, (draft) => {
      if (!draft.prompt_config) {
        draft.prompt_config = {
          jinja2_variables: [],
        }
      }
      if (!draft.prompt_config.jinja2_variables)
        draft.prompt_config.jinja2_variables = []

      draft.prompt_config.jinja2_variables.push({
        variable: '',
        value_selector: [],
      })
    })
    setInputs(newInputs)
  }, [setInputs])
  // 提示词jinjia——添加jinja配置
  const handleAddVariable = useCallback((payload: Variable) => {
    const newInputs = produce(inputRef.current, (draft) => {
      if (!draft.prompt_config) {
        draft.prompt_config = {
          jinja2_variables: [],
        }
      }
      if (!draft.prompt_config.jinja2_variables)
        draft.prompt_config.jinja2_variables = []

      draft.prompt_config.jinja2_variables.push(payload)
    })
    setInputs(newInputs)
  }, [setInputs])
  // 变更jinja变量列表
  const handleVarListChange = useCallback((newList: Variable[]) => {
    const newInputs = produce(inputRef.current, (draft) => {
      if (!draft.prompt_config) {
        draft.prompt_config = {
          jinja2_variables: [],
        }
      }
      if (!draft.prompt_config.jinja2_variables)
        draft.prompt_config.jinja2_variables = []

      draft.prompt_config.jinja2_variables = newList
    })
    setInputs(newInputs)
  }, [setInputs])
  // 输入变量位置——变更jinja变量名称
  const handleVarNameChange = useCallback((oldName: string, newName: string) => {
    const newInputs = produce(inputRef.current, (draft) => {
      if (isChatModel) {
        const promptTemplate = draft.prompt_template as PromptItem[]
        promptTemplate.filter(item => item.edition_type === EditionType.jinja2).forEach((item) => {
          item.jinja2_text = (item.jinja2_text || '').replaceAll(`{{ ${oldName} }}`, `{{ ${newName} }}`)
        })
      }
      else {
        if ((draft.prompt_template as PromptItem).edition_type !== EditionType.jinja2)
          return

        const promptTemplate = draft.prompt_template as PromptItem
        promptTemplate.jinja2_text = (promptTemplate.jinja2_text || '').replaceAll(`{{ ${oldName} }}`, `{{ ${newName} }}`)
      }
    })
    setInputs(newInputs)
  }, [isChatModel, setInputs])
  // 提示词变更
  const handlePromptChange = useCallback((newPrompt: PromptItem[] | PromptItem) => {
    const newInputs = produce(inputRef.current, (draft) => {
      draft.prompt_template = newPrompt
    })
    setInputs(newInputs)
  }, [setInputs])
  // 变更记忆配置
  const handleMemoryChange = useCallback((newMemory?: Memory) => {
    const newInputs = produce(inputs, (draft) => {
      draft.memory = newMemory
    })
    setInputs(newInputs)
  }, [inputs, setInputs])
  // 变更记忆提示词配置
  const handleSyeQueryChange = useCallback((newQuery: string) => {
    const newInputs = produce(inputs, (draft) => {
      if (!draft.memory) {
        draft.memory = {
          window: {
            enabled: false,
            size: WINDOW_SIZE_DEFAULT,
          },
          query_prompt_template: newQuery,
        }
      }
      else {
        draft.memory.query_prompt_template = newQuery
      }
    })
    setInputs(newInputs)
  }, [inputs, setInputs])
  // 设置输入变量值
  const setInputVarValues = useCallback((newPayload: Record<string, any>) => {
    const newVars = {
      ...newPayload,
      '#context#': runInputData['#context#'],
      '#files#': runInputData['#files#'],
    }
    setRunInputData(newVars)
  }, [runInputData, setRunInputData])
  const setContexts = useCallback((newContexts: string[]) => {
    setRunInputData({
      ...runInputData,
      '#context#': newContexts,
    })
  }, [runInputData, setRunInputData])
  const setVisionFiles = useCallback((newFiles: any[]) => {
    setRunInputData({
      ...runInputData,
      '#files#': newFiles,
    })
  }, [runInputData, setRunInputData])
  // 上下文变量变更
  const handleContextVarChange = useCallback((newVar: ValueSelector | string) => {
    const newInputs = produce(inputs, (draft) => {
      draft.context.variable_selector = newVar as ValueSelector || []
      draft.context.enabled = !!(newVar && newVar.length > 0)
    })
    setInputs(newInputs)
  }, [inputs, setInputs])

  // 不更新的话inputRef.current拿到的是旧值，会覆盖掉最新值
  useEffect(() => {
    inputRef.current = inputs
  }, [inputs])
  useEffect(() => {
    const isReady = defaultConfig && Object.keys(defaultConfig).length > 0

    if (isReady && !inputs.prompt_template) {
      const newInputs = produce(inputs, (draft) => {
        appendDefaultPromptConfig(draft, defaultConfig)
      })
      setInputs(newInputs)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultConfig, isChatModel])
  useEffect(() => {
    if (currentProvider?.provider && currentModel?.model && !model.provider) {
      handleModelChanged({
        provider: currentProvider?.provider,
        modelId: currentModel?.model,
        mode: currentModel?.model_properties?.mode as string,
      })
    }
  }, [model.provider, currentProvider, currentModel, handleModelChanged])
  // change to vision model to set vision enabled, else disabled
  useEffect(() => {
    if (!modelChanged)
      return
    setModelChanged(false)
    handleVisionConfigAfterModelChanged()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isVisionModel, modelChanged])

  return {
    readOnly,
    isChatMode,
    isChatModel,
    isCompletionModel,
    isVisionModel,
    isShowVars,
    isShowSingleRun,
    hasSetBlockStatus,
    shouldShowContextTip,

    inputs,
    runningStatus,
    filterInputVar,
    filterVar: filterMemoryPromptVar,
    availableVars,
    availableNodesWithParent,
    inputVarValues,
    visionFiles,
    contexts,
    varInputs,
    runResult,
    datasetList,

    handleChangeQuery,
    handleChangeSceneDesc,
    handleModelChanged,
    handleCompletionParamsChange,
    handleVarListChange,
    handleVarNameChange,
    handleAddVariable,
    handleAddEmptyVariable,
    handleContextVarChange,
    handlePromptChange,
    handleMemoryChange,
    handleSyeQueryChange,
    handleVisionResolutionEnabledChange,
    handleVisionResolutionChange,
    hideSingleRun,
    setInputVarValues,
    setVisionFiles,
    setContexts,
    handleRun,
    handleStop,
    handleOuputConfig,
    handleRemoveDataset,
    handleConfigDataset,
    handleSelectDataset,
    handleAddJsonOutput,
    handleRemoveJsonOutput,
    handleChangeJsonOutput,
    handleChangeTool,
    handleSelectTool,
    handleDeleteTool,
    handleChangeAdvanceSetting,
  }
}

export default useConfig
