// 操作动作来源：应用 数据库 工具 团队成员
export enum OperationSourceEnum {
  app = 'app',
  dataset = 'dataset',
  tool = 'tool',
  member = 'member'
}
// 操作动作类型： 新增 修改 删除 复制 发布 角色变更 人员移除
export enum OperationTypeEnum {
  add = 'add',
  modify = 'modify',
  delete = 'delete',
  copy = 'copy',
  publish = 'publish',
  roleChange = 'roleChange',
  memberRemove = 'memberRemove'
}

// 操作动作具体细节
export type OperationContent = {
  source: OperationSourceEnum,
  type: OperationTypeEnum,
  target: string,
} 

export type OperationLog = {
  id: string
  user_id: string
  user_name: string
  account: string
  created_at: number
  workspace: string
  operation: string
  content: OperationContent
}

export type OperationLogResponse = {
  data: Array<OperationLog>
  has_more: boolean
  limit: number
  total: number
  page: number
}