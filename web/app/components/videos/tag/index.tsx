import { useTranslation } from 'react-i18next'
import Badge from '@/app/components/base/badge'
import { ImgTagIcon, VideoStreamTagIcon, VideoTagIcon } from '@/app/components/base/icons/src/vender/line/tag'
import { MediaType } from '@/types/videos'

type VideoTagProps = {
  mode: MediaType
  className?: string
}

const VideoTag = ({ mode }: VideoTagProps) => {
  const { t } = useTranslation()

  return (
    <>
      {/* 视频流 */}
      {mode === MediaType.VideoStream && <Badge className='rounded-[2px] bg-gray-G10 text-gray-G2'>
        <VideoStreamTagIcon className='w-3 h-3 mr-1 text-primary-P1' />
        {t('videos.type.videosBuket')}
      </Badge>
      }
      {/* 视频 */}
      {
        mode === MediaType.Video && <Badge className='rounded-[2px] bg-gray-G10 text-gray-G2'>
          <VideoTagIcon className='w-3 h-3 mr-1 text-primary-P1' />
          {t('videos.type.video')}
        </Badge>
      }
      {/* 图片 */}
      {
        mode === MediaType.Img && <Badge className='rounded-[2px] bg-gray-G10 text-gray-G2'>
          <ImgTagIcon className='w-3 h-3 mr-1 text-primary-P1' />
          {t('videos.type.img')}
        </Badge>
      }
    </>
  )
}

export default VideoTag
