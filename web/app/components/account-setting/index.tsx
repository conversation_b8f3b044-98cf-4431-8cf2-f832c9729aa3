'use client'
import { useTranslation } from 'react-i18next'
import { useEffect, useMemo, useState } from 'react'
import type { ItemType, MenuItemGroupType, MenuItemType } from 'antd/es/menu/interface'
import { ConfigProvider, Menu } from 'antd'
import { useSearchParams } from 'next/navigation'
import s from './styles/index.module.css'
import { useAppContext } from '@/context/app-context'
import cn from '@/utils/classnames'
/* 公共组件 */
import AccountPage from '@/app/components/account-setting/account-page'
import MembersPage from '@/app/components/account-setting/members-page'
import TeamPage from '@/app/components/account-setting/team-page'
import OperationLogPage from '@/app/components/account-setting/operation-log-page'
// import ApiBasedExtensionPage from '@/app/components/account-setting/api-based-extension-page'
// import DataSourcePage from '@/app/components/account-setting/data-source-page'
import ModelProviderPage from '@/app/components/account-setting/model-provider-page'
import { useSystemContext } from '@/context/system-context'

type IAccountSettingProps = {
  activeTab?: string
  className?: string
}

export default function AccountSetting({
  activeTab = 'provider',
  className,
}: IAccountSettingProps) {
  const { t } = useTranslation()
  const { isVip, userProfile } = useAppContext()
  const searchParams = useSearchParams()
  const category = searchParams.get('category') || 'account' // 团队 tab 标识
  const { isPrivate } = useSystemContext()
  // 当前激活菜单key
  const [activeMenu, setActiveMenu] = useState(activeTab)

  // 工作空间菜单项
  const workplaceGroupItems: MenuItemGroupType[] = (() => {
    return [{
      key: 'workspace-group',
      label: t('account.settingMenu.workplaceGroup'),
      type: 'group',
      children: [
        {
          key: 'provider',
          label: t('account.settingMenu.provider'),
        },
        // 团队 !isPrivate && userProfile.is_vip
        // {
        //   key: 'team',
        //   label: t('account.team.title'),
        // },
        ...(isVip
          ? [{
            key: 'team',
            label: t('account.team.title'),
          }]
          : []),
        // 成员
        ...(isPrivate
          ? [
            {
              key: 'members',
              label: t('account.settingMenu.members'),
            },
            {
              key: 'operationLog',
              label: t('account.settingMenu.operationLog'),
            },
          ]
          : []),
        /* {
          key: 'data-source',
          label: t('account.settingMenu.dataSource'),
        },
        {
          key: 'extension',
          label: t('account.settingMenu.apiBasedExtension'),
        }, */
      ].filter(item => !!item.key) as ItemType[],
    }]
  })()
  // 菜单项
  const menuItems: Array<MenuItemGroupType> = [
    ...workplaceGroupItems,
    ...(isPrivate
      ? [{
        key: 'account-group',
        label: t('account.settingMenu.accountGroup'),
        type: 'group',
        children: [
          {
            key: 'account',
            label: t('account.settingMenu.account') as string,
          },
        ],
      }] as Array<MenuItemGroupType>
      : []),
  ]
  // 当前激活菜单信息
  const activeMenuInfo = useMemo(() => {
    const allMenuItem = menuItems.reduce((red, item) => {
      return red.concat(item.children as never[])
    }, []) as Array<MenuItemType>
    const menu = allMenuItem.find(item => item.key === activeMenu)
    if (activeMenu === 'team' && isVip) {
      return {
        key: 'team',
        label: userProfile.is_parent ? t('account.team.hostTeam') : t('account.team.sonTeam'),
      }
    }
    else {
      return menu
    }
  }, [menuItems, activeMenu, isVip, userProfile.is_parent, t])

  useEffect(() => {
    if (category && category === 'team') {
      setActiveMenu('team')
      const newUrl = location.origin + location.pathname + location.search.replace(/category=[^&]+/, '')
      history.replaceState(null, '', newUrl)
    }
  }, [category])
  return (
    <div className={cn(s['account-setting'], className)}>
      <ConfigProvider
        theme={{
          components: {
            Menu: {
              itemMarginBlock: 0,
            },
          },
        }}
      >
        <Menu
          className='w-[120px] shrink-0'
          items={menuItems}
          selectedKeys={[activeMenu]}
          onClick={({ key }) => setActiveMenu(key)}
        ></Menu>
      </ConfigProvider>
      <div className={s['account-setting-content']}>
        <div className={s.title}>{activeMenuInfo?.label}</div>
        <div className={s.content}>
          {activeMenu === 'account' && <AccountPage />}
          {activeMenu === 'members' && <MembersPage />}
          {activeMenu === 'operationLog' && <OperationLogPage/>}
          {activeMenu === 'provider' && <ModelProviderPage />}
          {activeMenu === 'team' && <TeamPage />}
          {/* {activeMenu === 'data-source' && <DataSourcePage />}
          {activeMenu === 'extension' && <ApiBasedExtensionPage />} */}
        </div>
      </div>
    </div>
  )
}
