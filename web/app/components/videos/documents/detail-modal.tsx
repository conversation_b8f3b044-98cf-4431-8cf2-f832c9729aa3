import type { FC } from 'react'
import { memo } from 'react'
import { useTranslation } from 'react-i18next'
import { useVideoCreateMode } from '../hook'
import type { MediaType } from '@/types/videos'
import Modal from '@/app/components/base/modal'
import FieldInfo from '@/app/components/videos/common/field-info'

type DetailModalProps = {
  mode: MediaType
  detail: {
    originFileName: string
    originFileSize: string
    uploadTime: string
    frameRate: string
    lastUpdateTime: string
  }
  onClose: () => void
}

const DetailModal: FC<DetailModalProps> = ({ onClose, detail, mode }) => {
  const { t } = useTranslation()
  const { isImageMode, isVideoMode, isVideoStreamMode } = useVideoCreateMode(mode)

  return (
    <Modal
      isShow
      closable
      onClose={onClose}
      title={t('videos.modalTitle.detail')}
    >
      {/* 文件名和文件大小 */}
      {
        (isImageMode || isVideoMode) && <>
          <FieldInfo
            labelClassName='w-[98px] flex justify-end items-center'
            label={t('videos.doc.info.originFileName')}
            displayedValue={detail.originFileName}
          ></FieldInfo>
          <FieldInfo
            labelClassName='w-[98px] flex justify-end items-center'
            label={t('videos.doc.info.originFileSize')}
            displayedValue={detail.originFileSize}
          ></FieldInfo>
        </>
      }
      {/* 切帧帧率 */}
      {
        (isVideoStreamMode || isVideoMode) && <>
          <FieldInfo
            labelClassName='w-[98px] flex justify-end items-center'
            label={t('videos.doc.info.frameRate')}
            displayedValue={detail.frameRate}
          ></FieldInfo>
        </>
      }
      {/* 上传时间 */}
      <FieldInfo
        labelClassName='w-[98px] flex justify-end items-center'
        label={t('videos.doc.info.uploadTime')}
        displayedValue={detail.uploadTime}
      ></FieldInfo>
      {/* 最后更新时间 */}
      <FieldInfo
        labelClassName='w-[98px] flex justify-end items-center'
        label={t('videos.doc.info.lastUpdateTime')}
        displayedValue={detail.lastUpdateTime}
      ></FieldInfo>
    </Modal>
  )
}

export default memo(DetailModal)
