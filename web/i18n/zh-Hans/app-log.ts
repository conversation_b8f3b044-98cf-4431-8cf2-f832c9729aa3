const translation = {
  title: '日志',
  description: '日志记录了应用的运行情况，包括用户的输入和 AI 的回复。',
  dateTimeFormat: 'YYYY-MM-DD HH:mm',
  empty: '暂无日志，运行一个试试吧',
  table: {
    header: {
      updatedTime: '更新时间',
      time: '创建时间',
      endUser: '用户或账户',
      source: '数据来源',
      input: '输入',
      output: '输出',
      summary: '标题',
      messageCount: '消息数',
      userRate: '用户反馈',
      reteContent: '反馈内容',
      adminRate: '管理员反馈',
      startTime: '开始时间',
      status: '状态',
      runtime: '运行时间',
      tokens: 'TOKENS',
      user: '用户或账户',
      version: '版本',
    },
    pagination: {
      previous: '上一页',
      next: '下一页',
    },
    empty: {
      noChat: '未开始的对话',
      noOutput: '无输出',
      element: {
        title: '这里有人吗',
        content: '在这里观测和标注最终用户和 AI 应用程序之间的交互，以不断提高 AI 的准确性。您可以<testLink>试试</testLink> WebApp 或<shareLink>分享</shareLink>出去，然后返回此页面。',
      },
    },
  },
  detail: {
    time: '时间',
    conversationId: '对话 ID',
    promptTemplate: '前缀提示词',
    promptTemplateBeforeChat: '对话前提示词 · 以系统消息提交',
    annotationTip: '{{user}} 标记的改进回复',
    timeConsuming: '耗时',
    second: ' 秒',
    tokenCost: '消耗Token',
    loading: '加载中',
    operation: {
      like: '点赞',
      dislike: '点踩',
      addAnnotation: '标记改进回复',
      editAnnotation: '编辑改进回复',
      annotationPlaceholder: '输入你希望 AI 回复的预期答案，这在今后可用于模型微调，持续改进文本生成质量。',
    },
    variables: '变量',
    uploadImages: '上传的图片',
  },
  filter: {
    period: {
      today: '今天',
      last7days: '过去 7 天',
      last4weeks: '过去 4 周',
      last3months: '过去 3 月',
      last12months: '过去 12 月',
      monthToDate: '本月至今',
      quarterToDate: '本季度至今',
      yearToDate: '本年至今',
      allTime: '所有时间',
    },
    likeOrDislike: {
      title: '赞踩情况',
      all: '全部',
      like: '有点赞',
      dislike: '有点踩',
      likeAndNoDislike: '有点赞且无点踩',
      noLikeAndDislike: '有点踩且无点赞',
      noLikeAndNoDislike: '无赞且无踩',
    },
    datasetType: {
      title: '数据类型',
      text: '文本',
      textCot: '文本-CoT',
    },
    annotation: {
      all: '全部',
      annotated: '已标注改进（{{count}} 项）',
      not_annotated: '未标注',
    },
    source: {
      title: '数据来源',
      all: '全部',
      web: '前端页面',
      api: '后端api',
      embed: '嵌入网站',
      market: '应用广场',
      local: '本地数据',
    },
    sortBy: '排序：',
    descending: '降序',
    ascending: '升序',
  },
  workflowTitle: '日志',
  workflowSubtitle: '日志记录了应用的执行情况',
  runDetail: {
    title: '对话日志',
    workflowTitle: '日志详情',
  },
  log: '日志',
  agentLogDetail: {
    agentMode: 'Agent 模式',
    toolUsed: '使用工具',
    iterations: '迭代次数',
    iteration: '迭代',
    finalProcessing: '最终处理',
  },
  dataReflux: {
    create: '创建回流数据',
    modal: {
      title: '创建回流数据集',
      datasetType: '数据集类型',
      annotationState: '是否已标注',
      isAnnotationYes: '是',
      isAnnotationNo: '否',
      datasetName: '数据集英文名称',
      datasetNameTip: {
        title: '数据集英文名称，创建后不可修改',
        content: '长度在2到64字符范围\n必须以字母开头\n必须以字母或者数字结尾\n只能包含字母、数字与特殊字符_.-\n特殊字符不能并列出现'
      },
      datasetAlias: '数据集别名',
      datasetAliasTip: '别名未设置时，等于英文名',
      datasetDesc: '数据集简介',
      public: '公开',
      publicDesc: '平台上的任何用户都可以看到并使用这个数据集，只有你或你所在组织的成员可以编辑。',
      private: '私有',
      privateDesc: '只有你或你所在组织的成员可以看到、使用和编辑这个数据集。',
      placeholder: {
        datasetType: '请选择数据集类型',
        datasetName: '请输入数据集英文名称',
        datasetAlias: '请输入数据集别名',
        datasetDesc: '请输入数据集简介',
      },
      notify: {
        datasetNameLength: '长度在2到64字符范围',
        datasetNameStart: '必须以字母开头',
        datasetNameEnd: '必须以字母或者数字结尾',
        datasetNameSpecial: '只能包含字母、数字与特殊字符_.-',
        datasetNameSpecialConsecutive: '特殊字符不能并列出现',
        datasetAliasCallTip: '长度在64字符以内，只能包括中英文、数字以及特殊字符_.-',
      },
      typeOptions: {
        text: '文本',
        multimodal: '多模态',
      },
    },
    confirm: {
      success: {
        title: '创建成功',
        desc1: '该数据集创建成功，可前往 ',
        link: '数据回流状态',
        desc2: ' 页查看。'
      },
      failed: {
        title: '创建失败',
        desc: '该数据集创建失败，请重新创建。'
      }
    },
    tip: {
      requireSelectTip: '',
      diffTypeTip: '不支持多数据类型创建回流数据集',
      noSupportTip: '功能开发中，待上线'
    },
  }
}

export default translation
