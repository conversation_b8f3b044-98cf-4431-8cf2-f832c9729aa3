'use client'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useRouter } from 'next/navigation'
import { Divider, Form, Input } from 'antd'
import { debounce } from 'lodash-es'

import style from '../signin/page.module.css'
import { getRegisterPicCOde, getRegisterSmsCode, register } from '@/service/common'
import { validatePhone } from '@/utils/validate'
import cn from '@/utils/classnames'

// 公共组件
import Button from '@/app/components/base/button'
import Scrollbar from '@/app/components/base/scrollbar'
import Verify from '@/app/components/base/verify'
import SmsVerify from '@/app/components/base/verify/sms-verify'
import Toast from '@/app/components/base/toast'

const RegisterForm = () => {
  const { t } = useTranslation()
  const router = useRouter()
  const [form] = Form.useForm()
  const values = Form.useWatch([], form)

  // 是否可提交
  const [disabled, setDisabled] = useState(true)
  // 短信验证码disabled
  const [smsDisabled, setSmsDisabled] = useState(true)
  // 图形验证码配置
  const [picConfig, setPicConfig] = useState<{ uuid: string; captcha_image: string }>()

  // 获取图形验证码
  const getPicCode = async () => {
    try {
      setPicConfig(undefined)
      const res = await getRegisterPicCOde()
      setPicConfig(res)
    }
    catch {
    }
  }
  // 获取短信验证码
  const getSmsCode = async () => {
    const { phone, pic_code } = form.getFieldsValue()
    await getRegisterSmsCode({
      phone,
      pic_code,
      uuid: picConfig!.uuid,
    })
  }
  // 提交注册账号
  const onSubmit = async () => {
    // console.log({
    //   ...form.getFieldsValue(),
    //   uuid: picConfig?.uuid,
    // })
    try {
      const res = await register({
        ...form.getFieldsValue(),
        uuid: picConfig?.uuid,
      })
      if (res.result === 'success') {
        Toast.notify({
          type: 'success',
          message: t('login.notify.registerSuccess'),
        })
        router.push('/signin')
      }
    }
    catch {
      getPicCode()
    }
  }

  useEffect(debounce(() => {
    const { invite_code, phone, pic_code } = form.getFieldsValue()
    if (invite_code && phone && pic_code && picConfig && validatePhone(phone))
      setSmsDisabled(false)
    else
      setSmsDisabled(true)

    form.validateFields({ validateOnly: true }).then(() => {
      setDisabled(false)
    },
    ).catch(() => {
      setDisabled(true)
    })
  }, 100), [form, values])
  useEffect(() => {
    getPicCode()
  }, [])

  return (
    <>
      <Scrollbar className={style.content}>
        <Form
          form={form}
          layout='vertical'
        >
          {/* 邀请码 */}
          <Form.Item
            name={'invite_code'}
            label={t('login.info.inviteCode')}
            rules={[
              { required: true, whitespace: true },
            ]}
            validateTrigger='onBlur'
          >
            <Input
              autoComplete='off'
              placeholder={t('login.placeholder.inviteCode') as string}
              className={cn(style.noAutofillBg, 'h-[42px]')}
            ></Input>
          </Form.Item>
          {/* 用户名  */}
          <Form.Item
            name={'name'}
            label={t('login.info.username')}
            validateFirst={true}
            rules={[
              { required: true, whitespace: true, max: 20 },
            ]}
            validateTrigger='onBlur'
          >
            <Input
              autoComplete='off'
              placeholder={t('login.placeholder.username') as string}
              className={cn(style.noAutofillBg, 'h-[42px]')}
            ></Input>
          </Form.Item>
          {/* 公司名称 */}
          <Form.Item
            label={t('login.info.company')}
            name='corporate_name'
            validateTrigger='onBlur'
            validateFirst={true}
            rules={[
              {
                // required: true,
                // whitespace: true,
                max: 40,
              },
            ]}
          >
            <Input
              placeholder={t('login.placeholder.company') || ''}
              autoComplete='off'
              className={cn(style.noAutofillBg, 'h-[42px]')}
            />
          </Form.Item>
          {/* 业务场景 */}
          {/* <Form.Item
            label={t('login.info.business')}
            name={'business_scenarios'}
            validateTrigger='onBlur'
            required
            validateFirst={true}
            rules={[
              {
                required: true,
                whitespace: true,
                max: 100,
              },
            ]}
          >
            <Input.TextArea
              maxLength={100}
              placeholder={t('login.placeholder.business') || ''}
            />
          </Form.Item> */}
          {/* 手机号 */}
          <Form.Item
            name={'phone'}
            label={t('login.info.mobile')}
            validateTrigger='onBlur'
            validateFirst={true}
            rules={[
              () => ({
                required: true,
                whitespace: true,
                validator: async (_, value: string) => {
                  const phoneValid = validatePhone(value)
                  if (!phoneValid)
                    return await Promise.reject(t('login.error.phoneInValid'))
                  return await Promise.resolve()
                },
              })]}
          >
            <Input
              placeholder={t('login.placeholder.phone') || ''}
              autoComplete='off'
              className={cn(style.noAutofillBg, 'h-[42px]')}
            />
          </Form.Item>
          {/* 验证码 */}
          <Form.Item
            name={'pic_code'}
            label={t('login.info.picCode')}
            validateTrigger='onBlur'
            rules={[
              {
                required: true,
                whitespace: true,
              },
            ]}
          >
            <Input
              className={cn(style.noAutofillBg, 'h-[42px]')}
              autoComplete="off"
              placeholder={t('login.placeholder.picCode') || ''}
              suffix={
                <Verify onRefresh={getPicCode} width={86} height={30} url={`data:image/png;base64,${picConfig?.captcha_image}` || ''}></Verify>
              }
            />
          </Form.Item>
          {/* 短信验证码 */}
          <Form.Item
            name={'sms_code'}
            label={t('login.info.smsCode')}
            validateTrigger='onBlur'
            rules={[
              {
                required: true,
                whitespace: true,
              },
            ]}
          >
            <Input
              className={cn(style.noAutofillBg, 'h-[42px]')}
              autoComplete="off"
              suffix={
                <div className='w-[102px] flex justify-between items-center'>
                  <Divider type='vertical' className='h-[22px] !ml-[16px] !mt-[3px]'></Divider>
                  <SmsVerify sendFunc={getSmsCode} interval={60} disabled={smsDisabled}></SmsVerify>
                </div>
              }
              placeholder={t('login.placeholder.smsCode') || ''}
            />
          </Form.Item>
        </Form>
        <Button disabled={disabled} variant='primary' className="w-full h-[48px] mt-4 text-base font-semibold" onClick={onSubmit}>
          {t('login.action.register')}
        </Button>
      </Scrollbar>
    </>
  )
}

export default RegisterForm
