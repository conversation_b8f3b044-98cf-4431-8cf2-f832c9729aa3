// 先安装 yarn add slate-react slate slate-history slate-dom
import { useTranslation } from 'react-i18next'
import React, { useMemo, useState, useEffect } from 'react'
import { createEditor, Transforms, Text, Descendant } from 'slate'
import { Slate, Editable, withReact } from 'slate-react'
import { useStore } from '@/app/components/workflow/store'
// import { useChatWithHistoryContext } from '../../chat-with-history/context'
import { t } from 'i18next'
// 添加类型定义
type CustomText = { text: string }
type CustomElement = {
  type: string
  content?: string
  children: (CustomText | { type: string; content: string; children: CustomText[] })[]
}

const renderElement = (props: { element: any; attributes: any; children: any }) => {
  const { element, attributes, children } = props
  switch (element.type) {
    case 'placeholder':
      return (
        <span
          {...attributes}
          contentEditable={false}
          style={{
            color: '#aaccfe',
            fontWeight: 'bold',
            backgroundColor: '#f0f6fe',
            minWidth: '55px',
            display: 'inline-block',
            padding: '0 2px',
            textAlign: 'center',
          }}
        >
          {children}
        </span>
      )
    default:
      return <p {...attributes}>{children}</p>
  }
}

const renderLeaf = (props: { attributes: any; children: any }) => {
  const { attributes, children } = props
  return <span {...attributes}>{children}</span>
}

// 添加自定义 decorate 函数
const withPlaceholders = (editor: any) => {
  const { isInline } = editor

  editor.isInline = (element: any) => {
    return element.type === 'placeholder' ? true : isInline(element)
  }

  return editor
}

const createInitialValue = (data: any) => {
  // 检查 data 是否为空对象
  if (!data.form || Object.keys(data.form).length === 0) {
    return [
      {
        type: 'paragraph',
        children: [{ text: '' }]
      }
    ]
  }

  return [
    {
      type: 'paragraph',
      children: [
        { text: String(t('sampleTemplate.longText.article1'))+' ' },
        {
          type: 'placeholder',
          content:t('sampleTemplate.longText.article2'),
          children: [{ text: data?.form?.Theme ? String(data.form.Theme) : t('sampleTemplate.longText.article2') }]
        },
        { text: t('sampleTemplate.longText.article3') },
        // { text: data?.topicType?.text ? String(data.topicType.text) + '研究报告' : '研究报告' },
        { text: String(data.reportType.text) },
        { text: ` ，${t('sampleTemplate.longText.article4')}` },
        {
          type: 'placeholder',
          content: t('sampleTemplate.longText.article5'),
          children: [{ text: data?.form?.Number ? String(data.form.Number) : t('sampleTemplate.longText.article5') }]
        },
        { text: t('sampleTemplate.longText.article6') }
      ]
    }
  ]
}

const RichTextEditor = React.forwardRef((props, ref) => {
  const { t } = useTranslation()
  // const { chatAllTypeResult, setChatAllTypeResult } = useChatWithHistoryContext()
  const chatAllTypeResult = useStore(s => s.chatAllTypeResultflow)
  const editor = useMemo(() => withPlaceholders(withReact(createEditor())), [])

  // 添加一个 ref 来存储编辑器内容
  const [value, setValue] = useState<CustomElement[]>(createInitialValue(chatAllTypeResult))

  // 添加获取内容的方法
  const getContent = () => {
    // 获取所有文本内容
    return value
      .map(node => {
        return node.children
          .map(child => {
            if (Text.isText(child)) {
              return child.text
            }
            // 处理占位符节点
            if ('content' in child) {
              return child.children[0].text
            }
            return ''
          })
          .join('')
      })
      .join('\n')
  }

  // 使用 useEffect 来处理初始值和更新
  useEffect(() => {
    if (chatAllTypeResult) {
      const newValue = createInitialValue(chatAllTypeResult)
      editor.children = newValue
      Transforms.select(editor, { path: [0, 0], offset: 0 })
      setValue(newValue)
    }
  }, [chatAllTypeResult, editor])

  // 添加类型转换
  const onChange = (newValue: Descendant[]) => {
    setValue(newValue as CustomElement[])
  }

  React.useImperativeHandle(ref, () => ({
    getContent
  }))

  return (
    <Slate editor={editor} initialValue={value} onChange={onChange}>
      <Editable
        renderElement={renderElement}
        renderLeaf={renderLeaf}
        placeholder={t('common.chat.inputPlaceholder') || ''}
        spellCheck
        className="slate-editor"
        style={{
          width: '94%',
          padding: '10px 50px 10px 10px',
          borderRadius: '5px',
          whiteSpace: 'pre-wrap',
          position: 'relative'
        }}
        onKeyDown={event => {
          if (event.key === 'Enter') {
            event.preventDefault()
            Transforms.insertText(editor, '\n')
          }
        }}
      />
      <style>{`
        .slate-editor [data-slate-placeholder] {
          position: absolute;
          top: auto !important;
          pointer-events: none;
          opacity: 0.5;
          width: 100%;
          max-width: 100%;
          display: block;
          user-select: none;
        }
      `}</style>
    </Slate>
  )
})

// 将组件改为 forwardRef 以便父组件可以调用方法
export default RichTextEditor
