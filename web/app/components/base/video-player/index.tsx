import React, { useCallback } from 'react'
import { useContext } from 'use-context-selector'
import { useTranslation } from 'react-i18next'
import { debounce } from 'lodash-es'
import {
  BigPlayButton,
  ControlBar,
  CurrentTimeDisplay,
  PlaybackRateMenuButton,
  Player,
  TimeDivider,
  VolumeMenuButton,
} from 'video-react'
import 'video-react/dist/video-react.css' // import css
import s from './styles/style.module.scss'
// 公告组件
import { ExclamationOutlined } from '@/app/components/base/icons/src/vender/line/alertsAndFeedback'
import { ToastContext } from '@/app/components/base/toast'
import cn from '@/utils/classnames'

type VideoPlayerProps = {
  src: string
  width?: number
  className?: string
  style?: React.CSSProperties
}

const VideoPlayer = ({
  src,
  width,
  style,
  className,
}: VideoPlayerProps) => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  const playerRef = React.useRef(null)
  const [loading, setLoading] = React.useState(true)
  const [canPlay, setCanPlay] = React.useState(true)

  // 判断无法播放
  const handleError = useCallback(() => {
    setLoading(false)
    setCanPlay(false)
  }, [])
  // 判断可以播放
  const handleCanPlay = useCallback(() => {
    setLoading(false)
    setCanPlay(true)
  }, [])
  // 改变音量
  const handleVolumeChange = useCallback(debounce((e: any) => {
    if (e.target?.volume > 0.75) {
      notify({
        type: 'warning',
        message: t('common.actionMsg.volumeAloudTip'),
      })
    }
  }, 500), [])
  return (
    <div style={style} className={cn(className, s.videoPlayerContainer, !canPlay && s.errorPlayer)}>
      <Player
        ref={playerRef}
        src={src}
        onError={handleError}
        onCanPlay={handleCanPlay}
        fluid={true}
        onVolumeChange={handleVolumeChange}
      >
        <BigPlayButton position="center" />
        <ControlBar>
          {/* <ReplayControl seconds={10} order={1.1} /> */}
          {/* <ForwardControl seconds={30} order={1.2} /> */}
          <CurrentTimeDisplay order={4.1} />
          <TimeDivider order={4.2} />
          <PlaybackRateMenuButton rates={[2, 1.5, 1.25, 1, 0.75, 0.5]} order={7.1} />
          <VolumeMenuButton vertical order={7.2}/>
        </ControlBar>
        {
          !loading && !canPlay && (
            <div className={s.errorDisplayBg}>
              <div className={s.errorTitle}>
                <ExclamationOutlined className='w-4 h-4 text-white'/>
                {t('common.component.videoPlayer.playError')}
              </div>
              <div className={s.errorDesc}>{t('common.component.videoPlayer.playErrorDesc')}</div>
            </div>
          )
        }
      </Player>
    </div>
  )
}

export default React.memo(VideoPlayer)
