'use client'
import type { FC } from 'react'
import useS<PERSON> from 'swr'
import { useTranslation } from 'react-i18next'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import produce, { setAutoFreeze } from 'immer'
import { useBoolean } from 'ahooks'

import { useContext } from 'use-context-selector'
import { useShallow } from 'zustand/react/shallow'
import cn from 'classnames'
import { Divider } from 'antd'
import FormattingChanged from '../base/warning-mask/formatting-changed'
import CannotQueryDataset from '../base/warning-mask/cannot-query-dataset'
import HasNotSetAPIKEY from '../base/warning-mask/has-not-set-api'
import DebugWithMultipleModel from './debug-with-multiple-model'
import DebugWithSingleModel from './debug-with-single-model'
import type { DebugWithSingleModelRefType } from './debug-with-single-model'
import type { ModelAndParameter } from './types'
import {
  APP_CHAT_WITH_MULTIPLE_MODEL,
  APP_CHAT_WITH_MULTIPLE_MODEL_RESTART,
} from './types'
import s from './styles/index.module.css'
import { AppType } from '@/types/app'
import { ModelModeType, TransferMethod } from '@/types/model'
import ConfigContext from '@/context/debug-configuration'
import { sendCompletionMessage } from '@/service/debug'
import type { ModelConfig as BackendModelConfig, VisionFile } from '@/types/model'
import { promptVariablesToUserInputsForm } from '@/utils/model-config'
import { IS_CE_EDITION } from '@/config'
import type { Inputs } from '@/models/debug'
import { fetchFileUploadConfig } from '@/service/common'
import { useDefaultModel } from '@/app/components/account-setting/model-provider-page/hooks'
import { ModelTypeEnum } from '@/app/components/account-setting/model-provider-page/declarations'
import type { ModelParameterModalProps } from '@/app/components/account-setting/model-provider-page/model-parameter-modal'
import { useEventEmitterContextContext } from '@/context/event-emitter'
import { useProviderContext } from '@/context/provider-context'
import { useStore as useAppStore } from '@/app/components/app/store'
import style from '@/app/components/app/configuration/styles/style.module.scss'
import { getCropperFontCss } from '@/app/components/app/app-bg-cropper/utils'

// 公共组件
import Tooltip from '@/app/components/base/tooltip'
import { ToastContext } from '@/app/components/base/toast'
import Button from '@/app/components/base/button'
import AgentLogModal from '@/app/components/base/agent-log-modal'
import PromptLogModal from '@/app/components/base/prompt-log-modal'
import { useFeatures, useFeaturesStore } from '@/app/components/base/features/hooks'
import { FeatureEnum } from '@/app/components/base/features/types'
import CropperWrapper from '@/app/components/base/cropper/wrapper'

type IDebug = {
  isAPIKeySet: boolean
  onSetting: () => void
  inputs: Inputs
  modelParameterParams: Pick<ModelParameterModalProps, 'setModel' | 'onCompletionParamsChange' | 'onModelParamsModeChange' | 'getModelConfigStoraged'>
  bestModel: string
  debugWithMultipleModel: boolean
  multipleModelConfigs: ModelAndParameter[]
  onMultipleModelConfigsChange: (multiple: boolean, modelConfigs: ModelAndParameter[], bestModel: string) => void
}

const Debug: FC<IDebug> = ({
  isAPIKeySet = true,
  onSetting,
  inputs,
  modelParameterParams,
  bestModel = '',
  debugWithMultipleModel,
  multipleModelConfigs,
  onMultipleModelConfigsChange,
}) => {
  const { t } = useTranslation()
  const {
    appId,
    mode,
    modelModeType,
    hasSetBlockStatus,
    isAdvancedMode,
    promptMode,
    chatPromptConfig,
    completionPromptConfig,
    formattingChanged,
    setFormattingChanged,
    dataSets,
    modelConfig,
    completionParams,
    hasSetContextVar,
    datasetConfigs,
  } = useContext(ConfigContext)
  const { currentLogItem, setCurrentLogItem, showPromptLogModal, setShowPromptLogModal, showAgentLogModal, setShowAgentLogModal } = useAppStore(useShallow(state => ({
    currentLogItem: state.currentLogItem,
    setCurrentLogItem: state.setCurrentLogItem,
    showPromptLogModal: state.showPromptLogModal,
    setShowPromptLogModal: state.setShowPromptLogModal,
    showAgentLogModal: state.showAgentLogModal,
    setShowAgentLogModal: state.setShowAgentLogModal,
  })))
  const features = useFeatures(s => s.features)
  const featuresStore = useFeaturesStore()
  const bgConfig = useFeatures(s => s.features[FeatureEnum.chatBg])
  const { notify } = useContext(ToastContext)
  const { eventEmitter } = useEventEmitterContextContext()
  const { data: text2speechDefaultModel } = useDefaultModel(ModelTypeEnum.textEmbedding)
  const { textGenerationModelList } = useProviderContext()
  const { data: fileUploadConfigResponse } = useSWR({ url: '/files/upload' }, fetchFileUploadConfig)

  // 是否正在响应
  const [isResponding, { setTrue: setRespondingTrue, setFalse: setRespondingFalse }] = useBoolean(false)
  // 是否显示格式化变更弹窗
  const [isShowFormattingChangeConfirm, setIsShowFormattingChangeConfirm] = useState(false)
  // 是否显示不能查询知识库弹窗
  const [isShowCannotQueryDataset, setShowCannotQueryDataset] = useState(false)
  const [completionFiles, setCompletionFiles] = useState<VisionFile[]>([])
  const [completionRes, setCompletionRes] = useState('')
  // 消息id
  const [messageId, setMessageId] = useState<string | null>(null)
  const [width, setWidth] = useState(0)
  const ref = useRef<HTMLDivElement>(null)
  // 单一调试节点
  const debugWithSingleModelRef = React.useRef<DebugWithSingleModelRefType | null>(null)

  const varList = modelConfig.configs.prompt_variables.map((item: any) => {
    return {
      label: item.key,
      value: inputs[item.key],
    }
  })

  const handleClearConversation = () => {
    debugWithSingleModelRef.current?.handleRestart()
  }
  const clearConversation = async () => {
    if (debugWithMultipleModel) {
      eventEmitter?.emit({
        type: APP_CHAT_WITH_MULTIPLE_MODEL_RESTART,
      } as any)
      return
    }

    handleClearConversation()
  }

  const handleConfirm = () => {
    clearConversation()
    setIsShowFormattingChangeConfirm(false)
    setFormattingChanged(false)
  }

  const handleCancel = () => {
    setIsShowFormattingChangeConfirm(false)
    setFormattingChanged(false)
  }

  const logError = useCallback((message: string) => {
    notify({ type: 'error', message })
  }, [notify])

  const checkCanSend = useCallback(() => {
    if (isAdvancedMode && mode !== AppType.completion) {
      if (modelModeType === ModelModeType.completion) {
        if (!hasSetBlockStatus.history) {
          notify({ type: 'error', message: t('appDebug.otherError.historyNoBeEmpty') })
          return false
        }
        if (!hasSetBlockStatus.query) {
          notify({ type: 'error', message: t('appDebug.otherError.queryNoBeEmpty') })
          return false
        }
      }
    }
    let hasEmptyInput = ''
    const requiredVars = modelConfig.configs.prompt_variables.filter(({ key, name, required, type }) => {
      if (type !== 'string' && type !== 'paragraph' && type !== 'select')
        return false
      const res = (!key || !key.trim()) || (!name || !name.trim()) || (required || required === undefined || required === null)
      return res
    }) // compatible with old version
    // debugger
    requiredVars.forEach(({ key, name }) => {
      if (hasEmptyInput)
        return

      if (!inputs[key])
        hasEmptyInput = name
    })

    if (hasEmptyInput) {
      logError(t('appDebug.errorMessage.valueOfVarRequired', { var: hasEmptyInput }))
      return false
    }

    if (completionFiles.find(item => item.transfer_method === TransferMethod.local_file && !item.upload_file_id)) {
      notify({ type: 'info', message: t('appDebug.errorMessage.waitForFileUpload') })
      return false
    }
    return !hasEmptyInput
  }, [
    completionFiles,
    hasSetBlockStatus.history,
    hasSetBlockStatus.query,
    inputs,
    isAdvancedMode,
    mode,
    modelConfig.configs.prompt_variables,
    t,
    logError,
    notify,
    modelModeType,
  ])

  const sendTextCompletion = async () => {
    if (isResponding) {
      notify({ type: 'info', message: t('appDebug.errorMessage.waitForResponse') })
      return false
    }

    if (dataSets.length > 0 && !hasSetContextVar) {
      setShowCannotQueryDataset(true)
      return true
    }

    if (!checkCanSend())
      return

    const postDatasets = dataSets.map(({ id }) => ({
      dataset: {
        enabled: true,
        id,
      },
    }))
    const contextVar = modelConfig.configs.prompt_variables.find(item => item.is_context_var)?.key

    const postModelConfig: BackendModelConfig = {
      pre_prompt: !isAdvancedMode ? modelConfig.configs.prompt_template : '',
      prompt_type: promptMode,
      chat_prompt_config: {},
      completion_prompt_config: {},
      user_input_form: promptVariablesToUserInputsForm(modelConfig.configs.prompt_variables),
      dataset_query_variable: contextVar || '',
      dataset_configs: {
        ...datasetConfigs,
        datasets: {
          datasets: [...postDatasets],
        } as any,
      },
      agent_mode: {
        enabled: false,
        tools: [],
      },
      model: {
        provider: modelConfig.provider,
        name: modelConfig.model_id,
        mode: modelConfig.mode,
        completion_params: completionParams as any,
      },
      more_like_this: features.moreLikeThis as any,
      sensitive_word_avoidance: features.moderation as any,
      text_to_speech: features.text2speech as any,
      file_upload: features.file as any,
      opening_statement: features?.opening?.enabled ? (features.opening?.opening_statement || '') : '',
      suggested_questions_after_answer: modelConfig.suggested_questions_after_answer || { enabled: false },
      speech_to_text: features.speech2text as any,
      retriever_resource: features.citation as any,
    }

    if (isAdvancedMode) {
      postModelConfig.chat_prompt_config = chatPromptConfig
      postModelConfig.completion_prompt_config = completionPromptConfig
    }

    const data: Record<string, any> = {
      inputs,
      model_config: postModelConfig,
    }

    if ((features.file as any).enabled && completionFiles && completionFiles?.length > 0) {
      data.files = completionFiles.map((item) => {
        if (item.transfer_method === TransferMethod.local_file) {
          return {
            ...item,
            url: '',
          }
        }
        return item
      })
    }

    setCompletionRes('')
    setMessageId('')
    let res: string[] = []

    setRespondingTrue()
    sendCompletionMessage(appId, data, {
      onData: (data: string, _isFirstMessage: boolean, { messageId }) => {
        res.push(data)
        setCompletionRes(res.join(''))
        setMessageId(messageId)
      },
      onMessageReplace: (messageReplace) => {
        res = [messageReplace.answer]
        setCompletionRes(res.join(''))
      },
      onCompleted() {
        setRespondingFalse()
      },
      onError() {
        setRespondingFalse()
      },
    })
  }

  const handleSendTextCompletion = () => {
    if (debugWithMultipleModel) {
      eventEmitter?.emit({
        type: APP_CHAT_WITH_MULTIPLE_MODEL,
        payload: {
          message: '',
          files: completionFiles,
        },
      } as any)
      return
    }

    sendTextCompletion()
  }
  const handleChangeToSingleModel = (item: ModelAndParameter) => {
    const currentProvider = textGenerationModelList.find(modelItem => modelItem.provider === item.provider)
    const currentModel = currentProvider?.models.find(model => model.model === item.model)

    modelParameterParams.setModel({
      modelId: item.model,
      provider: item.provider,
      mode: currentModel?.model_properties.mode as string,
      features: currentModel?.features,
    })
    modelParameterParams.onCompletionParamsChange(item.parameters)
    onMultipleModelConfigsChange(
      false,
      [],
      '',
    )
  }

  const handleVisionConfigInMultipleModel = useCallback(() => {
    if (debugWithMultipleModel && mode) {
      const supportedVision = multipleModelConfigs.some((modelConfig) => {
        const currentProvider = textGenerationModelList.find(modelItem => modelItem.provider === modelConfig.provider)
        const currentModel = currentProvider?.models.find(model => model.model === modelConfig.model)

        // return currentModel?.features?.includes(ModelFeatureEnum.vision)
        return true
      })
      const {
        features,
        setFeatures,
      } = featuresStore!.getState()

      const newFeatures = produce(features, (draft) => {
        draft.file = {
          ...draft.file,
          enabled: supportedVision,
        }
      })
      setFeatures(newFeatures)
    }
  }, [debugWithMultipleModel, featuresStore, mode, multipleModelConfigs, textGenerationModelList])

  const returnWithBestModel = useCallback(() => {
    const bestModelItem = multipleModelConfigs.find(model => model.id === bestModel)
    if (bestModelItem)
      handleChangeToSingleModel(bestModelItem)
  }, [bestModel, multipleModelConfigs, handleChangeToSingleModel])

  const adjustModalWidth = () => {
    if (ref.current)
      setWidth(document.body.clientWidth - (ref.current?.clientWidth + 16) - 8)
  }

  useEffect(() => {
    setAutoFreeze(false)
    return () => {
      setAutoFreeze(true)
    }
  }, [])
  useEffect(() => {
    if (formattingChanged)
      setIsShowFormattingChangeConfirm(true)
  }, [formattingChanged])
  useEffect(() => {
    handleVisionConfigInMultipleModel()
  }, [multipleModelConfigs, mode, handleVisionConfigInMultipleModel])
  useEffect(() => {
    adjustModalWidth()
  }, [])

  return (
    /* 目前多模型暂时不支持生效 */
    <CropperWrapper
      className={cn(style.appDebugWrap, 'bg-chatbot-bg')}
      config={(!debugWithMultipleModel && bgConfig?.enabled) ? bgConfig.narrow : undefined }
      header={
        <>
          <div className={s['debug-header']}>
            <div className={cn(s['debug-title'], !debugWithMultipleModel && getCropperFontCss(bgConfig))}>{t('appDebug.inputs.title')}</div>
            <div className='flex items-center gap-3'>
              {
                debugWithMultipleModel
                  ? (
                    <>
                      <Tooltip popupContent={t('appDebug.errorMessage.notSelectBestModel')} disabled={!!bestModel}>
                        <Button
                          variant={'secondary-accent'}
                          size='small'
                          onClick={returnWithBestModel}
                          disabled={!bestModel}
                          className='!bg-transparent'
                        >
                          {t('appDebug.returnWithBestModel')}
                        </Button>
                      </Tooltip>
                      <Button
                        variant={'secondary'}
                        size='small'
                        onClick={() => onMultipleModelConfigsChange(true, [...multipleModelConfigs, { id: `${Date.now()}`, model: '', provider: '', parameters: {} }], bestModel)}
                        disabled={multipleModelConfigs.length >= 4}
                        className='!bg-transparent'
                      >
                        {t('account.modelProvider.addModel')}
                      </Button>
                    </>
                  )
                  : null
              }
              {/* 重新开始 */}
              {/* {mode !== AppType.completion && (
              <Button variant='secondary' size='small' className='gap-1' onClick={clearConversation}>
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M2.66663 2.66629V5.99963H3.05463M3.05463 5.99963C3.49719 4.90505 4.29041 3.98823 5.30998 3.39287C6.32954 2.7975 7.51783 2.55724 8.68861 2.70972C9.85938 2.8622 10.9465 3.39882 11.7795 4.23548C12.6126 5.07213 13.1445 6.16154 13.292 7.33296M3.05463 5.99963H5.99996M13.3333 13.333V9.99963H12.946M12.946 9.99963C12.5028 11.0936 11.7093 12.0097 10.6898 12.6045C9.67038 13.1993 8.48245 13.4393 7.31203 13.2869C6.1416 13.1344 5.05476 12.5982 4.22165 11.7621C3.38854 10.926 2.8562 9.83726 2.70796 8.66629M12.946 9.99963H9.99996" stroke="#3168F5" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
                <span>{t('common.operation.refresh')}</span>
              </Button>
            )} */}
            </div>
          </div>
          <div className='px-6'>
            <Divider className={'!my-0'}></Divider>
          </div>
        </>
      }
    >
      {/* {mode !== AppType.completion && expanded && (
          <div className='mx-3'>
            <ChatUserInput inputs={inputs} />
          </div>
        )} */}
      {/* {mode === AppType.completion && (
          <PromptValuePanel
            appType={mode as AppType}
            onSend={handleSendTextCompletion}
            inputs={inputs}
            visionConfig={{
              ...features.file! as VisionSettings,
              transfer_methods: features.file!.allowed_file_upload_methods || [],
              image_file_size_limit: fileUploadConfigResponse?.image_file_size_limit,
            }}
            onVisionFilesChange={setCompletionFiles}
          />
        )} */}
      {/* 多模型调试 */}
      {
        debugWithMultipleModel && (
          <div className='grow mt-5 overflow-hidden' ref={ref}>
            <DebugWithMultipleModel
              multipleModelConfigs={multipleModelConfigs}
              bestModel={bestModel}
              onMultipleModelConfigsChange={onMultipleModelConfigsChange}
              onDebugWithMultipleModelChange={handleChangeToSingleModel}
              checkCanSend={checkCanSend}
              handleNewConversation={clearConversation}
            />
            {showPromptLogModal && (
              <PromptLogModal
                width={width}
                currentLogItem={currentLogItem}
                onCancel={() => {
                  setCurrentLogItem()
                  setShowPromptLogModal(false)
                }}
              />
            )}
            {showAgentLogModal && (
              <AgentLogModal
                width={width}
                currentLogItem={currentLogItem}
                onCancel={() => {
                  setCurrentLogItem()
                  setShowAgentLogModal(false)
                }}
              />
            )}
          </div>
        )
      }
      {/* 单一模型调试 */}
      {
        !debugWithMultipleModel && (
          <div className={cn(s['debug-content'], (!debugWithMultipleModel && bgConfig?.enabled) && '!h-0')} ref={ref}>
            {/* Chat 聊天 预览与调试 */}
            {mode !== AppType.completion && (
              <DebugWithSingleModel
                ref={debugWithSingleModelRef}
                checkCanSend={checkCanSend}
                handleNewConversation={clearConversation}
              />
            )}
            {/* Text  Generation */}
            {/* {mode === AppType.completion && (
              <>
                {(completionRes || isResponding) && (
                  <>
                    <div className='mx-4 mt-3'><GroupName name={t('common.info.result')} /></div>
                    <div className='mx-3 mb-8'>
                      <TextGeneration
                        className="mt-2"
                        content={completionRes}
                        isLoading={!completionRes && isResponding}
                        isShowTextToSpeech={textToSpeechConfig.enabled && !!text2speechDefaultModel}
                        isResponding={isResponding}
                        isInstalledApp={false}
                        messageId={messageId}
                        isError={false}
                        onRetry={() => { }}
                        supportAnnotation
                        appId={appId}
                        varList={varList}
                        siteInfo={null}
                      />
                    </div>
                  </>
                )}
                {!completionRes && !isResponding && (
                  <div className='grow flex flex-col items-center justify-center gap-2'>
                    <RiSparklingFill className='w-12 h-12 text-text-empty-state-icon' />
                    <div className='text-text-quaternary system-sm-regular'>{t('appDebug.noResult')}</div>
                  </div>
                )}
              </>
            )} */}
            {mode === AppType.completion && showPromptLogModal && (
              <PromptLogModal
                width={width}
                currentLogItem={currentLogItem}
                onCancel={() => {
                  setCurrentLogItem()
                  setShowPromptLogModal(false)
                }}
              />
            )}
            {isShowCannotQueryDataset && (
              <CannotQueryDataset
                onConfirm={() => setShowCannotQueryDataset(false)}
              />
            )}
          </div>
        )
      }
      {isShowFormattingChangeConfirm && (
        <FormattingChanged
          onConfirm={handleConfirm}
          onCancel={handleCancel}
        />
      )}
      {!isAPIKeySet && (<HasNotSetAPIKEY isTrailFinished={!IS_CE_EDITION} onSetting={onSetting} />)}
    </CropperWrapper>
  )
}
export default React.memo(Debug)
