import type {
  Edge as ReactFlowEdge,
  Node as ReactFlowNode,
  Viewport,
} from 'reactflow'
import type { AgentDefaultValue } from './nodes/agent/types'
import type { OutputVar } from './nodes/code/types'
import type { Resolution, TransferMethod } from '@/types/model'
import type { ToolDefaultValue, VarType as VarKindType } from '@/app/components/workflow/nodes/tool/types'
import type { NodeTracing } from '@/types/workflow'
import type { FileResponse } from '@/types/public/file'

import type { Collection, Tool } from '@/app/components/tools/types'

export enum ControlMode {
  Pointer = 'pointer',
  Hand = 'hand',
}

export type Branch = {
  id: string
  name: string
}

export type CommonEdgeType = {
  _hovering?: boolean
  _connectedNodeIsHovering?: boolean
  _connectedNodeIsSelected?: boolean
  _run?: boolean
  _isBundled?: boolean
  isInIteration?: boolean
  iteration_id?: string
  sourceType: BlockEnum
  targetType: BlockEnum
}

export type SelectedNode = Pick<Node, 'id' | 'data'>
export type NodePanelProps<T> = {
  id: string
  data: CommonNodeType<T>
}
export type Edge = ReactFlowEdge<CommonEdgeType>

export type WorkflowDataUpdater = {
  nodes: Node[]
  edges: Edge[]
  viewport: Viewport
}

export type Block = {
  classification?: string
  type: BlockEnum
  title: string
  description?: string
}

export type NodeDefault<T> = {
  defaultValue: Partial<T>
  getAvailablePrevNodes: (isChatMode: boolean) => BlockEnum[]
  getAvailableNextNodes: (isChatMode: boolean) => BlockEnum[]
  checkValid: (payload: T, t: any, moreDataForCheckValid?: any) => { isValid: boolean; errorMessage?: string }
}

export type OnSelectBlock = (type: BlockEnum, defaultValue?: ToolDefaultValue | AgentDefaultValue) => void

export enum logStatus {
  Succeeded = 'succeeded',
  Failed = 'failed',
  Stopped = 'stopped',
  Running = 'running',
}

export enum WorkflowRunningStatus {
  Waiting = 'waiting',
  Running = 'running',
  Succeeded = 'succeeded',
  Failed = 'failed',
  Stopped = 'stopped',
}

export type OnNodeAdd = (
  newNodePayload: {
    nodeType: BlockEnum
    sourceHandle?: string
    targetHandle?: string
    toolDefaultValue?: ToolDefaultValue
  },
  oldNodesPayload: {
    prevNodeId?: string
    prevNodeSourceHandle?: string
    nextNodeId?: string
    nextNodeTargetHandle?: string
  }
) => void

export type RunFile = {
  type: string
  transfer_method: TransferMethod[]
  url?: string
  upload_file_id?: string
}

export type WorkflowRunningData = {
  task_id?: string
  workflow_run_id?: string
  message_id?: string
  conversation_id?: string
  result: {
    id: string
    sequence_number?: number
    workflow_id?: string
    inputs?: string
    process_data?: string
    outputs?: string
    status: string
    error?: string
    elapsed_time?: number
    total_tokens?: number
    created_at?: number
    created_by?: string
    finished_at?: number
    steps?: number
    showSteps?: boolean
    total_steps?: number
    files?: FileResponse[]
  }
  tracing?: NodeTracing[]
}

export type HistoryWorkflowData = {
  id: string
  sequence_number: number
  status: string
  conversation_id?: string
}

export enum ChangeType {
  changeVarName = 'changeVarName',
  remove = 'remove',
}

export type MoreInfo = {
  type: ChangeType
  payload?: {
    beforeKey: string
    afterKey?: string
  }
}

export type ToolWithProvider = Collection & {
  tools: Tool[]
}

export type RetryConfig = {
  retry_enabled: boolean
  max_retries: number
  retry_interval: number
}

// 节点
export type Node<T = {}> = ReactFlowNode<CommonNodeType<T>>
// 节点类
export enum NodeClass {
  custom = 'custom',
  customNote = 'custom-note',
  customIterationStart = 'custom-iteration-start',
}
// 节点类型
export enum BlockEnum {
  Start = 'start',
  End = 'end',
  Answer = 'answer',
  LLM = 'llm',
  KnowledgeRetrieval = 'knowledge-retrieval',
  QuestionClassifier = 'question-classifier',
  IfElse = 'if-else',
  Code = 'code',
  TemplateTransform = 'template-transform',
  HttpRequest = 'http-request',
  VariableAssigner = 'variable-assigner',
  VariableAggregator = 'variable-aggregator',
  Tool = 'tool',
  ParameterExtractor = 'parameter-extractor',
  Iteration = 'iteration',
  DocExtractor = 'document-extractor',
  ListFilter = 'list-operator',
  IterationStart = 'iteration-start',
  Assigner = 'assigner', // is now named as VariableAssigner
  // 智能体节点
  Agent = 'agent',
  Video = 'video-library',
}
// 节点分组
export enum BlockClassificationEnum {
  Default = 'default',
  Logic = 'logic',
  KnowledgeAndMemory = 'knowledge-and-memory',
  InformationProcessing = 'information-processing',
  Development = 'development',
}
// 节点入参
export type NodeProps<T = unknown> = { id: string; data: CommonNodeType<T> }
// 节点运行状态
export enum NodeRunningStatus {
  NotStart = 'not-start',
  Waiting = 'waiting',
  Running = 'running',
  Succeeded = 'succeeded',
  Failed = 'failed',
}
// 通用节点数据
export type CommonNodeType<T = {}> = {
  _connectedSourceHandleIds?: string[]
  _connectedTargetHandleIds?: string[]
  _targetBranches?: Branch[]
  _isSingleRun?: boolean
  _runningStatus?: NodeRunningStatus
  _singleRunningStatus?: NodeRunningStatus
  _isCandidate?: boolean
  _isBundled?: boolean
  _children?: string[]
  _isEntering?: boolean
  _showAddVariablePopup?: boolean
  _holdAddVariablePopup?: boolean
  _iterationLength?: number
  _iterationIndex?: number
  _inParallelHovering?: boolean
  isInIteration?: boolean
  iteration_id?: string
  selected?: boolean
  title: string
  desc: string
  type: BlockEnum
  width?: number
  height?: number
  icon?: string
} & T & Partial<Pick<ToolDefaultValue, 'provider_id' | 'provider_type' | 'provider_name' | 'tool_name' | 'fork'>>

// 边类
export enum EdgeClass {
  custom = 'custom',
}

/* 变量部分开始 ———— start */
// 变量类型枚举
export enum VarType {
  string = 'string',
  number = 'number',
  secret = 'secret',
  boolean = 'boolean',
  object = 'object',
  file = 'file',
  array = 'array',
  longTxt = 'longTxt',
  arrayString = 'array[string]',
  arrayNumber = 'array[number]',
  arrayObject = 'array[object]',
  arrayFile = 'array[file]',
  arrayBoolean = 'array[boolean]',
  any = 'any',
}
// 变量（主要描述变量限制，类型描述信息）
export type Var = {
  variable: string
  type: VarType
  // if type is obj, has the children struct
  children?: Var[]
  isParagraph?: boolean
  isSelect?: boolean
  options?: string[]
  required?: boolean
  des?: string
} & Partial<UploadFileSetting>
// 节点输出变量
export type NodeOutPutVar = {
  nodeId: string
  title: string
  vars: Var[]
  isStartNode?: boolean
}
// 支持的上传文件类型
export enum SupportUploadFileTypes {
  image = 'image',
  document = 'document',
  audio = 'audio',
  video = 'video',
  custom = 'custom',
}
// 上传文件设置
export type UploadFileSetting = {
  allowed_file_upload_methods: TransferMethod[]
  allowed_file_types: SupportUploadFileTypes[]
  allowed_file_extensions?: string[]
  max_length: number
  number_limits?: number
}
// 输入变量类型枚举
export enum InputVarType {
  textInput = 'text-input',
  paragraph = 'paragraph',
  select = 'select',
  number = 'number',
  url = 'url',
  files = 'files',
  longTxt = 'longTxt',
  json = 'json', // obj, array
  contexts = 'contexts', // knowledge retrieval
  iterator = 'iterator', // iteration input
  singleFile = 'file',
  multiFiles = 'file-list',
}
// 输入变量
export type InputVar = {
  type: InputVarType
  label: string | {
    nodeType: BlockEnum
    nodeName: string
    variable: string
    isChatVar?: boolean
  }
  isLongTxtSelected?: boolean
  variable: string
  max_length?: number
  default?: string
  required: boolean
  hint?: string
  options?: string[]
  value_selector?: ValueSelector
} & Partial<UploadFileSetting>
// 变量选择器
export type ValueSelector = string[] // [nodeId, key | obj key path]
// 变量（包含节点信息，以及变量的值等描述信息）
export type Variable = {
  variable: string
  label?: string | {
    nodeType: BlockEnum
    nodeName: string
    variable: string
  }
  value_selector: ValueSelector
  variable_type?: VarKindType
  value?: string
  options?: string[]
  required?: boolean
  isParagraph?: boolean
}
export type EnvironmentVariable = {
  id: string
  name: string
  value: any
  value_type: 'string' | 'number' | 'secret'
}
export type ConversationVariable = {
  id: string
  name: string
  value_type: ChatVarType
  value: any
  description: string
}
export type GlobalVariable = {
  name: string
  value_type: 'string' | 'number'
  description: string
}
export type VariableWithValue = {
  key: string
  value: string
}
// 文本变量格式
export enum TextVarFormat {
  text = 'TEXT',
  Markdown = '',
  Json = 'JSON',
}
// 会话变量类型
export enum ChatVarType {
  Number = 'number',
  String = 'string',
  Object = 'object',
  ArrayString = 'array[string]',
  ArrayNumber = 'array[number]',
  ArrayObject = 'array[object]',
}

/* 变量部分类型 ———— 结束 */

/* 面板配置类型 -- start */
export type RolePrefix = {
  user: string
  assistant: string
}
export type Memory = {
  role_prefix?: RolePrefix
  window: {
    enabled: boolean
    size: number | string | null
  }
  query_prompt_template: string
}
// 模型配置
export type ModelConfig = {
  provider: string
  name: string
  mode: string
  completion_params: Record<string, any>
  outputs: OutputVar
  response_format: TextVarFormat
}
// 提示词角色
export enum PromptRole {
  system = 'system',
  user = 'user',
  assistant = 'assistant',
}
// 提示词配置方式
export enum EditionType {
  basic = 'basic',
  jinja2 = 'jinja2',
}
// 提示词单项
export type PromptItem = {
  id?: string
  role?: PromptRole
  text: string
  edition_type?: EditionType
  jinja2_text?: string
  new_level?: boolean
}
// 视觉配置
export type VisionSetting = {
  variable_selector: ValueSelector
  detail: Resolution
}
/* 面板配置类型 -- end */
