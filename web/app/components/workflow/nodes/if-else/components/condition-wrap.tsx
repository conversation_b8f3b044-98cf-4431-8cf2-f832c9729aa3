'use client'
import type { FC } from 'react'
import React, { useCallback, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { ReactSortable } from 'react-sortablejs'
import type { CaseItem, HandleAddCondition, HandleAddSubVariableCondition, HandleRemoveCondition, HandleToggleConditionLogicalOperator, HandleToggleSubVariableConditionLogicalOperator, HandleUpdateCondition, HandleUpdateSubVariableCondition, handleRemoveSubVariableCondition } from '../types'
import type { Node, NodeOutPutVar, Var } from '../../../types'
import { VarType } from '../../../types'
import { useGetAvailableVars } from '../../variable-assigner/hooks'
import { SUB_VARIABLES } from '../default'
import ConditionList from './condition-list'
import ConditionAdd from './condition-add'
// 公共组件
import cn from '@/utils/classnames'
import Button from '@/app/components/base/button'
import PopoverSelect from '@/app/components/base/select/popover-select'
import Tooltip from '@/app/components/base/tooltip'
import { Draggable } from '@/app/components/base/icons/src/vender/line/action'
import { Delete } from '@/app/components/base/icons/src/vender/line/general'
import { AddButton } from '@/app/components/base/button/add-button'

const i18nPrefix = 'workflow.nodes.ifElse'

type Props = {
  isSubVariable?: boolean
  caseId?: string
  conditionId?: string
  cases: CaseItem[]
  readOnly: boolean
  handleSortCase?: (sortedCases: (CaseItem & { id: string })[]) => void
  handleRemoveCase?: (caseId: string) => void
  handleAddCondition?: HandleAddCondition
  handleRemoveCondition?: HandleRemoveCondition
  handleUpdateCondition?: HandleUpdateCondition
  handleToggleConditionLogicalOperator?: HandleToggleConditionLogicalOperator
  handleAddSubVariableCondition?: HandleAddSubVariableCondition
  handleRemoveSubVariableCondition?: handleRemoveSubVariableCondition
  handleUpdateSubVariableCondition?: HandleUpdateSubVariableCondition
  handleToggleSubVariableConditionLogicalOperator?: HandleToggleSubVariableConditionLogicalOperator
  nodeId: string
  nodesOutputVars: NodeOutPutVar[]
  availableNodes: Node[]
  varsIsVarFileAttribute?: Record<string, boolean>
  filterVar: (varPayload: Var) => boolean
}

const ConditionWrap: FC<Props> = ({
  isSubVariable,
  caseId,
  conditionId,
  nodeId: id = '',
  cases = [],
  readOnly,
  handleSortCase = () => { },
  handleRemoveCase,
  handleUpdateCondition,
  handleAddCondition,
  handleRemoveCondition,
  handleToggleConditionLogicalOperator,
  handleAddSubVariableCondition,
  handleRemoveSubVariableCondition,
  handleUpdateSubVariableCondition,
  handleToggleSubVariableConditionLogicalOperator,
  nodesOutputVars = [],
  availableNodes = [],
  varsIsVarFileAttribute = {},
  filterVar = () => true,
}) => {
  const { t } = useTranslation()
  const getAvailableVars = useGetAvailableVars()

  // 即将删除的分支id
  const [willDeleteCaseId, setWillDeleteCaseId] = useState('')
  const casesLength = cases.length
  const subVarOptions = SUB_VARIABLES.map(item => ({
    label: item,
    key: item,
  }))
  // 过滤数值类型
  const filterNumberVar = useCallback((varPayload: Var) => {
    return varPayload.type === VarType.number
  }, [])

  return (
    <>
      {/* Else 以及 ELSEIF节点 */}
      <ReactSortable
        list={cases.map(caseItem => ({ ...caseItem, id: caseItem.case_id }))}
        setList={handleSortCase}
        handle='.handle'
        animation={150}
        disabled={readOnly || isSubVariable}
        className='flex flex-col gap-2'
      >
        {
          cases.map((item, index) => (
            <div
              key={item.case_id}
              className={cn(
                'group relative rounded flex items-start gap-3 bg-gray-G7 h-auto px-2 py-3',
                willDeleteCaseId === item.case_id && 'bg-state-destructive-hover',
              )}
            >
              {/* 不是子变量选择 */}
              {!isSubVariable && (
                <div className='flex items-center gap-2 shrink-0'>
                  {/* 拖拽 */}
                  <Draggable className={cn(
                    'w-4 h-4 text-gray-G3 cursor-pointer',
                    casesLength === 1 && 'hidden',
                  )} />
                  {/* 标题 */}
                  <div className='flex flex-col'>
                    <span className='text-S1 leading-H1 text-gray-G1'>{ index === 0 ? 'IF' : 'ELIF' }</span>
                    {
                      casesLength >= 1 && (
                        <div className='text-S1 leading-H1 text-gray-G4'>CASE {index + 1}</div>
                      )
                    }
                  </div>
                </div>
              )}
              {/* 分支内容 */}
              <div className='flex flex-col w-full'>
                {
                  !!item.conditions.length && (
                    <ConditionList
                      disabled={readOnly}
                      caseItem={item}
                      caseId={isSubVariable ? caseId! : item.case_id}
                      conditionId={conditionId}
                      onUpdateCondition={handleUpdateCondition}
                      onRemoveCondition={handleRemoveCondition}
                      onToggleConditionLogicalOperator={handleToggleConditionLogicalOperator}
                      nodeId={id}
                      nodesOutputVars={nodesOutputVars}
                      availableNodes={availableNodes}
                      filterVar={filterVar}
                      numberVariables={getAvailableVars(id, '', filterNumberVar)}
                      varsIsVarFileAttribute={varsIsVarFileAttribute}
                      onAddSubVariableCondition={handleAddSubVariableCondition}
                      onRemoveSubVariableCondition={handleRemoveSubVariableCondition}
                      onUpdateSubVariableCondition={handleUpdateSubVariableCondition}
                      onToggleSubVariableConditionLogicalOperator={handleToggleSubVariableConditionLogicalOperator}
                      isSubVariable={isSubVariable}
                    />
                  )
                }
                {/* 底部操作部分 */}
                <div className={cn(
                  'flex items-center justify-between mt-2',
                )}>
                  {isSubVariable
                    ? (
                      <PopoverSelect
                        onChange={value => handleAddSubVariableCondition?.(caseId!, conditionId!, value)}
                        options={subVarOptions}
                        defaultValue=''
                        triggerNode={() => (
                          <AddButton
                            className='!rounded !h-8'
                            disabled={readOnly}
                            size={'small'}
                          >
                            { t('workflow.nodes.ifElse.addSubVariable') }
                          </AddButton>
                        )}
                        showCheck={false}
                      />
                    )
                    : (
                      <ConditionAdd
                        className='!rounded !h-8'
                        disabled={readOnly}
                        caseId={item.case_id}
                        variables={getAvailableVars(id, '', filterVar)}
                        onSelectVariable={handleAddCondition!}
                      />
                    )}
                  {
                    ((index === 0 && casesLength > 1) || (index > 0)) && (
                      <Button
                        className='hover:text-components-button-destructive-ghost-text hover:bg-components-button-destructive-ghost-bg-hover w-20'
                        size='small'
                        variant='ghost'
                        disabled={readOnly}
                        onClick={() => handleRemoveCase?.(item.case_id)}
                        onMouseEnter={() => setWillDeleteCaseId(item.case_id)}
                        onMouseLeave={() => setWillDeleteCaseId('')}
                      >
                        <Delete className='mr-1 w-4 h-4' />
                        {t('common.operation.remove')}
                      </Button>
                    )
                  }
                </div>
              </div>

            </div>
          ))
        }
      </ReactSortable>
      {/* ELSE 节点 */}
      {cases.length > 0 && !isSubVariable && (
        <div className='flex gap-1 items-center p-3 mt-2 bg-gray-G7 rounded'>
          <span className='text-S1 leading-H1 text-gray-g1'>
            {t(`${i18nPrefix}.else`)}
          </span>
          <Tooltip popupContent={t(`${i18nPrefix}.elseDescription`) || ''}/>
        </div>
      )
      }
      {(cases.length === 0) && (
        <PopoverSelect
          onChange={value => handleAddSubVariableCondition?.(caseId!, conditionId!, value)}
          options={subVarOptions}
          defaultValue=''
          triggerNode={() => (
            <AddButton
              className='!rounded !h-8'
              disabled={readOnly}
              size={'small'}
            >
              { t('workflow.nodes.ifElse.addSubVariable') }
            </AddButton>
          )}
          showCheck={false}
        />
      )}
    </>
  )
}
export default React.memo(ConditionWrap)
