.fieldInfo {
    @apply flex flex-row items-center py-2 pr-10 gap-1;
  }
  .fieldInfo > .label {
    @apply overflow-hidden text-ellipsis shrink-0 whitespace-nowrap text-S3 leading-H3 text-gray-G2;
    font-weight: 400;
  }
  .fieldInfo > .value {
    overflow-wrap: anywhere;
    @apply grow text-gray-G1;
    font-size: 14px;
    font-weight: 400;
    line-height: 24px;
  }

  .textArea {
    @apply placeholder:text-gray-400 bg-gray-50 px-2 py-1 caret-primary-P1 rounded hover:bg-gray-100 focus-visible:outline-none focus-visible:bg-white focus-visible:border focus-visible:border-gray-300 hover:shadow-[0_1px_2px_rgba(16,24,40,0.05);];
  }