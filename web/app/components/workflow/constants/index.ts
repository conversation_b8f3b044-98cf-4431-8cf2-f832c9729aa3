import type { Var } from '../types'
import { BlockEnum, VarType } from '../types'

// 全部节点key值
const BLOCKS_KEYS = [
  BlockEnum.Agent,
  BlockEnum.Answer,
  BlockEnum.Assigner,
  BlockEnum.Code,
  BlockEnum.DocExtractor,
  BlockEnum.End,
  BlockEnum.HttpRequest,
  BlockEnum.IfElse,
  BlockEnum.Iteration,
  BlockEnum.IterationStart,
  BlockEnum.KnowledgeRetrieval,
  BlockEnum.LLM,
  BlockEnum.ListFilter,
  BlockEnum.ParameterExtractor,
  BlockEnum.QuestionClassifier,
  BlockEnum.Start,
  BlockEnum.TemplateTransform,
  BlockEnum.Tool,
  BlockEnum.VariableAggregator,
  BlockEnum.VariableAssigner,
  BlockEnum.Video,
]
// 对话有效节点-不是开始和节点节点
export const ALL_CHAT_AVAILABLE_BLOCKS = BLOCKS_KEYS.filter(key => key !== BlockEnum.End && key !== BlockEnum.Start) as BlockEnum[]
// 应用有效节点-不是回答和结束节点
export const ALL_COMPLETION_AVAILABLE_BLOCKS = BLOCKS_KEYS.filter(key => key !== BlockEnum.Answer && key !== BlockEnum.Start) as BlockEnum[]

export const NODE_WIDTH = 240
export const X_OFFSET = 60
export const NODE_WIDTH_X_OFFSET = NODE_WIDTH + X_OFFSET
export const Y_OFFSET = 39
export const MAX_TREE_DEPTH = 50
// 初始化时，第一个节点位置
export const START_INITIAL_POSITION = { x: 80, y: 282 }
export const AUTO_LAYOUT_OFFSET = {
  x: -42,
  y: 243,
}
export const ITERATION_NODE_Z_INDEX = 1
export const ITERATION_CHILDREN_Z_INDEX = 1002
export const ITERATION_PADDING = {
  top: 65,
  right: 16,
  bottom: 20,
  left: 16,
}
export const PARALLEL_LIMIT = 10
export const PARALLEL_DEPTH_LIMIT = 3

export const RETRIEVAL_OUTPUT_STRUCT = `{
  "content": "",
  "title": "",
  "url": "",
  "icon": "",
  "metadata": {
    "dataset_id": "",
    "dataset_name": "",
    "document_id": [],
    "document_name": "",
    "document_data_source_type": "",
    "segment_id": "",
    "segment_position": "",
    "segment_word_count": "",
    "segment_hit_count": "",
    "segment_index_node_hash": "",
    "score": ""
  }
}`

export const SUPPORT_OUTPUT_VARS_NODE = [
  BlockEnum.Start, BlockEnum.LLM, BlockEnum.KnowledgeRetrieval, BlockEnum.Code, BlockEnum.TemplateTransform,
  BlockEnum.HttpRequest, BlockEnum.Tool, BlockEnum.VariableAssigner, BlockEnum.VariableAggregator, BlockEnum.QuestionClassifier,
  BlockEnum.ParameterExtractor, BlockEnum.Iteration,
  BlockEnum.DocExtractor, BlockEnum.ListFilter, BlockEnum.Agent, BlockEnum.Video,
]

// LLM节点输出变量
export const LLM_OUTPUT_STRUCT: Var[] = [
  {
    variable: 'text',
    type: VarType.string,
  },
  {
    variable: 'longTxt',
    type: VarType.longTxt, // 添加 longTxt 类型输出
  },
]
// 智能体节点输出变量
export const AGENT_OUTPUT_STRUCT: Var[] = [
  {
    variable: 'text',
    type: VarType.string,
  },
]
// 知识检索节点输出变量
export const KNOWLEDGE_RETRIEVAL_OUTPUT_STRUCT: Var[] = [
  {
    variable: 'result',
    type: VarType.arrayObject,
  },
]
// 模板转换节点输出变量
export const TEMPLATE_TRANSFORM_OUTPUT_STRUCT: Var[] = [
  {
    variable: 'output',
    type: VarType.string,
  },
]
// 问题分类节点输出变量
export const QUESTION_CLASSIFIER_OUTPUT_STRUCT = [
  {
    variable: 'class_name',
    type: VarType.string,
  },
]
// http请求输出变量
export const HTTP_REQUEST_OUTPUT_STRUCT: Var[] = [
  {
    variable: 'body',
    type: VarType.string,
  },
  {
    variable: 'status_code',
    type: VarType.number,
  },
  {
    variable: 'headers',
    type: VarType.object,
  },
  {
    variable: 'files',
    type: VarType.arrayFile,
  },
]
// 工具节点输出变量
export const TOOL_OUTPUT_STRUCT: Var[] = [
  {
    variable: 'text',
    type: VarType.string,
  },
  {
    variable: 'files',
    type: VarType.arrayFile,
  },
  {
    variable: 'json',
    type: VarType.arrayObject,
  },
]
// 参数提取节点输出变量
export const PARAMETER_EXTRACTOR_COMMON_STRUCT: Var[] = [
  {
    variable: '__is_success',
    type: VarType.number,
  },
  {
    variable: '__reason',
    type: VarType.string,
  },
]

export const FILE_STRUCT: Var[] = [
  {
    variable: 'name',
    type: VarType.string,
  },
  {
    variable: 'size',
    type: VarType.number,
  },
  {
    variable: 'type',
    type: VarType.string,
  },
  {
    variable: 'extension',
    type: VarType.string,
  },
  {
    variable: 'mime_type',
    type: VarType.string,
  },
  {
    variable: 'transfer_method',
    type: VarType.string,
  },
  {
    variable: 'url',
    type: VarType.string,
  },
]

export const DEFAULT_FILE_UPLOAD_SETTING = {
  allowed_file_upload_methods: ['local_file', 'remote_url'],
  max_length: 5,
  allowed_file_types: ['image'],
  allowed_file_extensions: [],
}
export const VIDEO_OUTPUT_STRUCT: Var[] = [
  {
    variable: 'result',
    type: VarType.arrayObject,
  },
]

export const WORKFLOW_DATA_UPDATE = 'WORKFLOW_DATA_UPDATE'
export const CUSTOM_NODE = 'custom'
export const CUSTOM_EDGE = 'custom'
export const DSL_EXPORT_CHECK = 'DSL_EXPORT_CHECK'
