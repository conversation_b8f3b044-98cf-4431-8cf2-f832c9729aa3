'use client'
import type { FC } from 'react'
import React from 'react'
import Image from 'next/image'
import { useTranslation } from 'react-i18next'
import style from './styles/style.module.css'
import cn from '@/utils/classnames'
import Tag from '@/app/components/base/tag'
import { useDatasetDetailContext } from '@/context/dataset-detail'

export type IPreviewItemProps = {
  type: string
  index: number
  content?: string
  qa?: {
    answer: string
    question: string
  }
}
export enum PreviewType {
  TEXT = 'text',
  QA = 'QA',
}

const textIcon = (
  <Image src='/assets/icons/app/text.svg' width={16} height={16} alt='count' className='mr-1' />
)

const PreviewItem: FC<IPreviewItemProps> = ({
  type = PreviewType.TEXT,
  index,
  content,
  qa,
}) => {
  const { t } = useTranslation()
  const { dataset } = useDatasetDetailContext()
  const charNums = type === PreviewType.TEXT
    ? (content || '').length
    : (qa?.answer || '').length + (qa?.question || '').length
  const formattedIndex = (() => String(index).padStart(3, '0'))()

  return (
    <div className={cn(style.previewItem, !dataset && '!bg-white')}>
      <div className={style.header}>
        <Tag color='blue' bordered={false}>
          <span>#{formattedIndex}</span>
        </Tag>
        <div className='flex items-center'>
          {textIcon}
          <span >{charNums} {t('common.unit.characters')}</span>
        </div>
      </div>
      <div className={cn(style.content, 'max-h-[120px] line-clamp-6 overflow-hidden')}>
        {type === PreviewType.TEXT && (
          <div style={{ whiteSpace: 'pre-line' }}>{content}</div>
        )}
        {type === PreviewType.QA && (
          <div style={{ whiteSpace: 'pre-line' }}>
            <div className='flex'>
              <div className='shrink-0 mr-2 text-medium text-gray-400'>Q</div>
              <div style={{ whiteSpace: 'pre-line' }}>{qa?.question}</div>
            </div>
            <div className='flex'>
              <div className='shrink-0 mr-2 text-medium text-gray-400'>A</div>
              <div style={{ whiteSpace: 'pre-line' }}>{qa?.answer}</div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
export default React.memo(PreviewItem)
