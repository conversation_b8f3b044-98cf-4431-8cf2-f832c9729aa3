const translation = {
  title: 'Logs',
  description: 'Logs record the running status of the application, including user inputs and AI replies.',
  dateTimeFormat: 'YYYY-MM-DD HH:mm',
  empty: 'No logs available yet. Try running one.',
  table: {
    header: {
      updatedTime: 'Update Time',
      time: 'Creation Time',
      endUser: 'User or Account',
      source: 'Data Source',
      input: 'Input',
      output: 'Output',
      summary: 'Title',
      messageCount: 'Message Count',
      userRate: 'User Feedback',
      adminRate: 'Administrator Feedback',
      startTime: 'Start Time',
      status: 'Status',
      runtime: 'Running Time',
      tokens: 'TOKENS',
      user: 'User or Account',
      version: 'Version',
      reteContent: 'Feedback content'
    },
    pagination: {
      previous: 'Previous Page',
      next: 'Next Page',
    },
    empty: {
      noChat: 'Unstarted Conversation',
      noOutput: 'No Output',
      element: {
        title: 'Is anyone here?',
        content: 'Observe and annotate the interactions between end - users and AI applications here to continuously improve the accuracy of AI. You can <testLink>try</testLink> the WebApp or <shareLink>share</shareLink> it, then return to this page.',
      },
    },
  },
  detail: {
    time: 'Time',
    conversationId: 'Conversation ID',
    promptTemplate: 'Prefix Prompt',
    promptTemplateBeforeChat: 'Pre - conversation Prompt · Submitted as a system message',
    annotationTip: 'Improved reply marked by {{user}}',
    timeConsuming: 'Time Consumed',
    second: ' seconds',
    tokenCost: 'Tokens Consumed',
    loading: 'Loading...',
    operation: {
      like: 'Approve',
      dislike: 'Disapprove',
      addAnnotation: 'Mark Improved Reply',
      editAnnotation: 'Edit Improved Reply',
      annotationPlaceholder: 'Enter the expected answer you want the AI to reply with. This can be used for model fine - tuning in the future to continuously improve the text generation quality.',
    },
    variables: 'Variables',
    uploadImages: 'Uploaded Images',
  },
  filter: {
    period: {
      today: 'Today',
      last7days: 'Last 7 Days',
      last4weeks: 'Last 4 Weeks',
      last3months: 'Last 3 Months',
      last12months: 'Last 12 Months',
      monthToDate: 'Month to Date',
      quarterToDate: 'Quarter to Date',
      yearToDate: 'Year to Date',
      allTime: 'All Time',
    },
    likeOrDislike: {
      title: 'Like/Dislike Status',
      all: 'All',
      like: 'With Likes',
      dislike: 'With Dislikes',
      likeAndNoDislike: 'With Likes and No Dislikes',
      noLikeAndDislike: 'With Dislikes and No Likes',
      noLikeAndNoDislike: 'No Likes and No Dislikes',
    },
    datasetType: {
      title: 'Data Type',
      text: 'text',
      textCot: 'Text-CoT'
    },
    annotation: {
      all: 'All',
      annotated: 'Annotated for Improvement ({{count}} items)',
      not_annotated: 'Not Annotated',
    },
    source: {
      title: 'Data Source',
      all: 'All',
      web: 'Front - end Page',
      api: 'Back - end API',
      embed: 'Embedded Website',
      market: 'App Square',
      local: 'Local Data',
    },
    sortBy: 'Sort by:',
    descending: 'Descending',
    ascending: 'Ascending',
  },
  workflowTitle: 'Logs',
  workflowSubtitle: 'Logs record the execution status of the application',
  runDetail: {
    title: 'Conversation Logs',
    workflowTitle: 'Log Details',
  },
  log: 'Logs',
  agentLogDetail: {
    agentMode: 'Agent Mode',
    toolUsed: 'Tools Used',
    iterations: 'Iteration Count',
    iteration: 'Iteration',
    finalProcessing: 'Final Processing',
  },
  dataReflux: {
    create: 'Create Reflux Data',
    modal: {
      title: 'Create Reflux Dataset',
      datasetType: 'Dataset Type',
      annotationState: 'Annotated or Not',
      isAnnotationYes: 'Yes',
      isAnnotationNo: 'No',
      datasetName: 'Dataset English Name',
      datasetNameTip: {
        title: 'Dataset English name, cannot be modified after creation',
        content: 'Length should be between 2 and 64 characters.\nMust start with a letter.\nMust end with a letter or a number.\nCan only contain letters, numbers, and special characters _.-.\nSpecial characters cannot appear consecutively.'
      },
      datasetAlias: 'Dataset Alias',
      datasetAliasTip: 'When the alias is not set, it is equal to the English name.',
      datasetDesc: 'Dataset Introduction',
      public: 'Public',
      publicDesc: 'Any user on the platform can see and use this dataset. Only you or members of your organization can edit it.',
      private: 'Private',
      privateDesc: 'Only you or members of your organization can see, use, and edit this dataset.',
      placeholder: {
        datasetType: 'Please select the dataset type',
        datasetName: 'Please enter the dataset English name',
        datasetAlias: 'Please enter the dataset alias',
        datasetDesc: 'Please enter the dataset introduction',
      },
      notify: {
        datasetNameLength: 'Length should be between 2 and 64 characters',
        datasetNameStart: 'Must start with a letter',
        datasetNameEnd: 'Must end with a letter or a number',
        datasetNameSpecial: 'Can only contain letters, numbers, and special characters _.-',
        datasetNameSpecialConsecutive: 'Special characters cannot appear consecutively',
        datasetAliasCallTip: 'Length should be within 64 characters, and can only include Chinese, English, numbers, and special characters _.-',
      },
      typeOptions: {
        multimodal: 'Multimodal',
        text: 'Text'
      }
    },
    confirm: {
      success: {
        title: 'Creation Successful',
        desc1: 'The dataset has been successfully created. You can go to the ',
        link: 'Data Reflux Status',
        desc2: ' page to view it.'
      },
      failed: {
        title: 'Creation Failed',
        desc: 'The dataset creation failed. Please try again.'
      }
    },
    tip: {
      requireSelectTip: '',
      diffTypeTip: 'Creating a reflux dataset with multiple data types is not supported.',
      noSupportTip: 'This feature is under development and will be available soon.'
    }
  }
}

export default translation
