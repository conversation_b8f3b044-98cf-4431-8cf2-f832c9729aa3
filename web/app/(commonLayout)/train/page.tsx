'use client'
import { useRouter, useSearchParams } from 'next/navigation'
import { useCallback, useEffect, useState } from 'react'
import dynamic from 'next/dynamic'
import { CSGHUB_PORTAL, CSGHUB_PORTAL_URL } from '@/config/micro'
import type { MicroOpenModalType } from '@/types/micro'
import { MicroEvent } from '@/types/micro'
import { useAppContext } from '@/context/app-context'
import { microPush } from '@/utils/micro'
import type { CreateAppDialogProps } from '@/app/components/app/modal/add-or-edit'
import type { AddToolModalProps } from '@/app/components/tools/add-tool-modal'
import { useModalContext } from '@/context/modal-context'
const WujieReactDynamic = dynamic(() => import('wujie-react'), { ssr: false })

const CsghubProtalPage = () => {
  const searchParams = useSearchParams()
  const { isSuperUser } = useAppContext()
  const { setShowCreateAppModal, setShowAddToolModal } = useModalContext()
  const router = useRouter()
  // wujie模块
  const [wujieReact, setWujieReact] = useState<any>()
  // 跨应用token
  const token = localStorage.getItem('sso_token')
  // token传递
  const handleInit = useCallback(() => {
    if (wujieReact) {
      const { bus } = wujieReact.default
      bus.$emit(MicroEvent.Init, {
        microName: CSGHUB_PORTAL,
        isSuperUser,
        token,
      })
      bus.$on(MicroEvent.Redirect, (type: 'apps' | 'datasets' | 'square') => {
        if (type === 'apps')
          router.replace('/apps')
        if (type === 'datasets')
          router.replace('/datasets')
        if (type === 'square')
          router.replace('/square')
      })
      bus.$on(MicroEvent.OpenModal, ({ type, params }: { type: MicroOpenModalType; params: CreateAppDialogProps | AddToolModalProps }) => {
        if (type === 'createApp') {
          setShowCreateAppModal(params as CreateAppDialogProps)
        }
        else if (type === 'selectTool') {
          setShowAddToolModal({
            ...params,
            hasParamsDetail: true,
          } as AddToolModalProps)
        }
      })
    }
  }, [isSuperUser, router, setShowAddToolModal, setShowCreateAppModal, token, wujieReact])

  // 子应用初始化
  useEffect(() => {
    import('wujie-react').then(module => setWujieReact(module))
    return () => {
      import('wujie-react').then(module => module.default.destroyApp(CSGHUB_PORTAL))
    }
  }, [])
  // 初始化init
  useEffect(() => {
    handleInit()
  }, [handleInit])
  // 处理参数变化
  useEffect(() => {
    microPush(decodeURIComponent(searchParams as unknown as string))
  }, [searchParams])

  return <WujieReactDynamic
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    width="100%"
    height="100%"
    name="model-dev"
    sync={true}
    props={{
      token,
    }}
    url={CSGHUB_PORTAL_URL}
  ></WujieReactDynamic>
}
export default CsghubProtalPage
