'use client'
import { useTranslation } from 'react-i18next'
import React, { useEffect, useState } from 'react';
import Modal from '@/app/components/base/modal'
import { Table } from 'antd';
import type { TableProps } from 'antd';
import { getModelPricing } from '@/service/data-statistics';

const i18nPrefix = 'dataStatistics.modelPricing';

type PriceListModalProps = {
  onCancel: () => void
}
type ColumnsType<T extends object = object> = TableProps<T>['columns'];

interface DataSourceType {
  model_name: string;
  input_token_cost: number ;
  output_token_cost: number; 
}


// 价格清单弹窗
const PriceListModal: React.FC<PriceListModalProps> = ({
  onCancel,
}) => {
  const { t } = useTranslation();
  const [dataSource, setDataSource] = useState<DataSourceType[]>();
  const columns: ColumnsType<DataSourceType> = [
    {
      title: t(`${i18nPrefix}.name`),
      dataIndex: 'model_name',
    },
    {
      title: t(`${i18nPrefix}.inputConsume`),
      dataIndex: 'input_token_cost',
    },
    {
      title: t(`${i18nPrefix}.outputConsume`),
      dataIndex: 'output_token_cost',
    },
  ]

  useEffect(() => {
    const test = [
      {
        "model_name": "千问",               
        "input_token_cost": 5,            // 输入千 token 消耗点数
        "output_token_cost": 10           // 输出千 token 消耗点数
      },
      {
        "model_name": "Doubao-pro",
        "input_token_cost": 8,
        "output_token_cost": 15
      }
    ]
    setDataSource(test)
    getModelPricing().then(res => {
      setDataSource(res.data)
    })
  }, [])
  return (
    <Modal
      isShow
      width={720}
      closable
      onClose={onCancel}
      title={t(`${i18nPrefix}.title`)}
    >
    <Table<DataSourceType>
        columns={columns}
        rowKey="id"
        dataSource={dataSource}
        pagination={false}
        />
    </Modal>
  ) 
}

export default PriceListModal