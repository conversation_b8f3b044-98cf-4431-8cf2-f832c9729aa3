import { useTranslation } from 'react-i18next'
import s from '@/app/components/data-statistics/styles/style.module.scss'
import { Button, Divider } from 'antd';
import { Filter } from '@/app/components/base/icons/src/vender/statisticsData'
import { AppCount, KnowledgeCapacity, KnowledgeNum, ResPackPoints, ToolNum } from '@/app/components/base/icons/src/public/statisticsData'
import { useDataStatisticsContext } from '../context';

const i18nPrefix = 'dataStatistics.dataSummary'
// 图标卡片
const DataCard = ({ title, value, Icon }) => (
  <div className={s['data-card']}>
   <div className={s['data-icon']}>
      { Icon }
   </div>
    <div className={s['data-content']}>
      <div className={s['data-title']}>{title}</div>
      <div className={s['data-value']}>{value}</div>
    </div>
   
  </div>
);
// 数据概览
const DataSummary = () => {
  const { t } = useTranslation()

  const {
    resourcePoints,
    datasetUsage,
    appUsage,
    toolUsage
  } = useDataStatisticsContext()


  // 购买资源包 TODO
  const buyResPack = () => {
  }

  return (
    <div className={s['card-wrap']}>
      <div className={s['data-summary']}>
        <div className={s['summary-header']}>
         <div className='flex'>
            <div className={s['card-title']}>{t(`${i18nPrefix}.title`)}</div>
            {/* <div className={s['day-count-down']}>
              <Filter />
              <span>{t('dataStatistics.dayCount', {day: data.days})}</span>
            </div> */}
         </div>
          {/* <Button type='primary' onClick={buyResPack}>{t(`dataStatistics.purchase`)}</Button> */}
        </div>
        <div className={s['summary-card']}>
          {/* 资源包点数 */}
          {/* <DataCard 
            title={t(`${i18nPrefix}.resourcePackPoints`)}
            Icon={<ResPackPoints/>}
            value={`${resourcePoints.consumed}/${resourcePoints.total}`}
          />
          <Divider type='vertical' className='m-0 h-[56px]'/> */}
          {/* 应用个数 */}
          <DataCard 
            title={t(`${i18nPrefix}.appNum`)}
            Icon={<AppCount/>}
            value={`${appUsage.consumed || '-'}/${appUsage.total || '-'}`}
          />
          <Divider type='vertical' className='m-0 h-[56px]'/>
          {/* 知识库个数 */}
          <DataCard 
            title={t(`${i18nPrefix}.datasetNum`)}
            Icon={<KnowledgeNum/>}
            value={`${datasetUsage.knowledge_creation_limit?.consumed || '-'}/${datasetUsage.knowledge_creation_limit?.total || '-'}`}
          />
          <Divider type='vertical' className='m-0 h-[56px]'/>
          {/* 知识库容量 */}
          {/* <DataCard 
            title={t(`${i18nPrefix}.datasetCapacity`)}
            Icon={<KnowledgeCapacity/>}
            value={`${datasetUsage.total_knowledge_storage_space?.consumed || '-'}/${datasetUsage.total_knowledge_storage_space?.total || '-'}`}
          />
          <Divider type='vertical' className='m-0 h-[56px]'/> */}
          {/* 工具个数 */}
          <DataCard 
            title={t(`${i18nPrefix}.toolNum`)}
            Icon={<ToolNum/>}
            value={`${toolUsage.consumed || '-'}/${toolUsage.total || '-'}`}
          />
        </div>
      </div>
      </div>
  );
};

export default DataSummary;
