import type { FC } from 'react'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { Divider } from 'antd'
import type { ToolNodeType } from './types'
import useConfig from './use-config'
import InputVarList from './components/input-var-list'
import Button from '@/app/components/base/button'
import Field from '@/app/components/workflow/nodes/_base/components/field'
import type { NodePanelProps } from '@/app/components/workflow/types'
import Form from '@/app/components/account-setting/model-provider-page/model-modal/model-form'
import ConfigCredential from '@/app/components/tools/detail/build-in/config-credentials'
import Loading from '@/app/components/base/loading'
import BeforeRunForm from '@/app/components/workflow/nodes/_base/components/before-run-form'
import OutputVars from '@/app/components/workflow/nodes/_base/components/variable/output-vars'
import ResultPanel from '@/app/components/workflow/run/result-panel'

const i18nPrefix = 'workflow.nodes.tool'

const Panel: FC<NodePanelProps<ToolNodeType>> = ({
  id,
  data,
}) => {
  const { t } = useTranslation()

  const {
    readOnly,
    inputs,
    toolInputVarSchema,
    setInputVar,
    handleOnVarOpen,
    filterVar,
    toolSettingSchema,
    toolSettingValue,
    setToolSettingValue,
    currCollection,
    isShowAuthBtn,
    showSetAuth,
    showSetAuthModal,
    hideSetAuthModal,
    handleSaveAuth,
    isLoading,
    isShowSingleRun,
    hideSingleRun,
    singleRunForms,
    runningStatus,
    handleRun,
    handleStop,
    runResult,
  } = useConfig(id, data)

  if (isLoading) {
    return <div className='flex h-[200px] items-center justify-center'>
      <Loading />
    </div>
  }

  return (
    <>
      {!readOnly && isShowAuthBtn && (
        <Button
          variant='primary'
          className='w-full'
          onClick={showSetAuthModal}
        >
          {t(`${i18nPrefix}.toAuthorize`)}
        </Button>
      )}
      {!isShowAuthBtn && <>
        {toolInputVarSchema.length > 0 && (
          <Field
            title={t(`${i18nPrefix}.inputVars`)}
          >
            <InputVarList
              readOnly={readOnly}
              nodeId={id}
              schema={toolInputVarSchema as any}
              value={inputs.tool_parameters}
              onChange={setInputVar}
              filterVar={filterVar}
              isSupportConstantValue
              onOpen={handleOnVarOpen}
            />
          </Field>
        )}

        {toolInputVarSchema.length > 0 && (
          <Divider />
        )}
        <Form
          value={toolSettingValue}
          onChange={setToolSettingValue}
          formSchemas={toolSettingSchema as any}
          isEditMode={false}
          showOnVariableMap={{}}
          readonly={readOnly}
        />
        {toolSettingSchema.length > 0 && <Divider></Divider>}
      </>}

      {showSetAuth && (
        <ConfigCredential
          collection={currCollection!}
          onCancel={hideSetAuthModal}
          onSaved={handleSaveAuth}
          isHideRemoveBtn
        />
      )}
      <OutputVars
        vars={[
          {
            name: 'text',
            type: 'String',
            description: t(`${i18nPrefix}.outputVars.text`),
          },
          {
            name: 'files',
            type: 'Array[File]',
            description: t(`${i18nPrefix}.outputVars.files.title`),
          },
          {
            name: 'json',
            type: 'Array[Object]',
            description: t(`${i18nPrefix}.outputVars.json`),
          },
        ]}
      >
      </OutputVars>

      {/* 单步运行 */}
      {isShowSingleRun && (
        <BeforeRunForm
          nodeName={inputs.title}
          onHide={hideSingleRun}
          forms={singleRunForms}
          runningStatus={runningStatus}
          onRun={handleRun}
          onStop={handleStop}
          result={<ResultPanel {...runResult} showSteps={false} />}
        />
      )}
    </>
  )
}

export default React.memo(Panel)
