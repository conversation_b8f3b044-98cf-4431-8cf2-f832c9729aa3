'use client'
import { useBoolean } from 'ahooks'
import type { FC } from 'react'
import React, { useCallback } from 'react'
import type { Video } from '@/types/videos'

// 公共能力
import SelectVideo from '@/app/components/videos/common/add-video-modal'
import { AddTextButton } from '@/app/components/base/button/add-button'

type Props = {
  selected: Video[]
  onChange: (dataSets: Video[]) => void
}

const AddVideo: FC<Props> = ({
  selected,
  onChange,
}) => {
  // 显示选择知识库弹窗
  const [isShowModal, {
    setTrue: showModal,
    setFalse: hideModal,
  }] = useBoolean(false)

  const handleSelect = useCallback((videos: Video[]) => {
    onChange(videos)
  }, [onChange])
  return (
    <>
      <AddTextButton onClick={showModal} />
      {isShowModal && (
        <SelectVideo
          isShow={isShowModal}
          onClose={hideModal}
          value={selected}
          onSelect={handleSelect}
        />
      )}
    </>
  )
}
export default React.memo(AddVideo)
