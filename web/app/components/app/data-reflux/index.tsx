'use client'
import React, { useState, useCallback } from 'react'
import useSWR from 'swr'
import { useTranslation } from 'react-i18next'
import { Pagination } from 'antd'
import List from './list'
import Filter from './filter'
import { fetchDatasetList } from '@/service/data-reflux'
import { APP_PAGE_LIMIT } from '@/config'

type dataRefluxProps = {
  appId: string
}
export type QueryParam = {
  start?: string,
  end?: string
  keyword?: string
  sort_by?: string
  status?: string
  dataset_type?: string
}
const DataReflux = ({
  appId
}: dataRefluxProps) => {
  const { t } = useTranslation()
  // 当前查询参数
  const [queryParams, setQueryParams] = useState<QueryParam>({
    status: '',
    sort_by: '-created_at',
  })
  // 当前页
  const [currPage, setCurrPage] = React.useState<number>(1)
  const query = {
    page: currPage,
    limit: APP_PAGE_LIMIT,
    keyword: queryParams.keyword || '',
    status: queryParams.status || '',
    dataset_type: queryParams.dataset_type || '',
    ...{ sort_by: queryParams.sort_by },
  } as QueryParam
  if(queryParams.start && queryParams.end) {
    query.start = queryParams.start
    query.end = queryParams.end
  }
  const { data: datasetList, mutate: mutateDataList, isLoading } = useSWR(() => ({
    url: `/${appId}/databackflow`,
    params: query,
  }), fetchDatasetList)

  const total = datasetList?.total

  return (
    <div className='flex flex-col flex-1'>
      {/* 查询条件 */}
      <Filter 
        queryParams={queryParams} 
        setQueryParams={(params) => {
          setQueryParams(params)
          setCurrPage(1)
        }} 
      />
      {/* 列表页 */}
      <List
        queryParams={queryParams} 
        setQueryParams={setQueryParams} 
        loading={isLoading} 
        datasetList={datasetList}
        onRefresh={mutateDataList}
      />
      {/* 分页组件 */}
      <Pagination
        className='mt-3'
        align='end'
        current={currPage}
        hideOnSinglePage
        onChange={setCurrPage}
        total={total}
        pageSize={APP_PAGE_LIMIT}
        showQuickJumper={false}
        showSizeChanger={false}
      />
    </div>
  )
}

export default DataReflux