// 媒体类型
export enum MediaType {
  VideoStream = 'STREAM',
  Video = 'VIDEO',
  Img = 'IMAGE',
}
// 网址状态
export enum SiteStatus {
  Normal = 'NORMAL',
  Abnormal = 'ERROR',
  Paused = 'PAUSED',
}
// 处理状态
export enum ProcessStatus {
  Waiting = 'PENDING',
  Processing = 'PROCESSING',
  Failed = 'FAILED',
  Success = 'COMPLETED',
}
// 错误信息
export type ErrorMsg = {
  time: string
  errorMsg: string
}

// 视频库
export type Video = {
  id: string
  name: string
  description: string
  type: MediaType
  created_by: string
  created_at: number
  updated_by: string
  updated_at: number
}
// 视频库列表响应
export type VideoListResponse = {
  info: Video[]
  total: number
  fork: boolean
  has_more: boolean
}
// 视频库单项
export type VideoStreamConfig = {
  name: string
  url: string
}

// 视频库文件响应信息
export type VideoDocListResponse = {
  total: number
  lib_type: MediaType
  file_info: VideoStreamItem[] | VideoItem[] | ImageItem[]
}
// 视频库基础单项
export type VideoBasicItem = {
  created_at: number
  enabled: boolean
  id: string
  updated_at: number
  // 处理状态
  process_status: ProcessStatus
  // 错误信息
  msg: Array<ErrorMsg>
  // 预览url
  preview_url: string
}
// 视频流单项
export type VideoStreamItem = {
  device_id: string
  location_ip: string
  url_name: string
  url: string
  // 网址状态
  status: SiteStatus
  frame_interval: number
  // 入库时间
  process_time: number
} & VideoBasicItem
// 视频单项
export type VideoItem = {
  file_name: string
  // 原始文件名称
  name: string
  // 原始文件大小
  size: string
  frame_interval: number

} & VideoBasicItem
// 图像单项
export type ImageItem = {
  file_name: string
  // 原始文件名称
  name: string
  // 原始文件大小
  size: number
} & VideoBasicItem
// 文档类型和文档数据映射
export type DocMap = {
  [MediaType.VideoStream]: VideoStreamItem
  [MediaType.Video]: VideoItem
  [MediaType.Img]: ImageItem
}
// 文档单项
export type DocItem = VideoStreamItem | VideoItem | ImageItem
// 文档列表
export type DocList = VideoStreamItem[] | VideoItem[] | ImageItem[]

// 文档索引状态查询结果
export type DocHandleStatusResponse = Array<{
  id: string
  process_status: ProcessStatus
}>

export type CustomFile = File & {
  id?: string
  extension?: string
  mime_type?: string
  created_by?: string
  created_at?: number
}

export type FileItem = {
  fileID: string
  file: CustomFile
  progress: number
}
