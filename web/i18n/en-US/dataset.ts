const translation = {
  externalTag: 'External',
  // Actions
  action: {
    createDataset: 'Create Knowledge Base',
    setting: 'Knowledge retrieval settings',
    config: 'Configuration selection',
    learnMore: 'Learn more',
    rerankSettings: 'Rerank setting',
    retrievalSettings: 'Recall settings'
  },
  // Modal titles
  modalTitle: {
      deleteDatasetConfirmTitle: 'Knowledge Base Deletion',
      datasetSetting: 'Knowledge Base Editing'
  },
  // Notifications and announcements
  notify: {
    embeddingExpire: 'The embedding model has been taken offline. Go to the knowledge base settings to change the embedding model.',
    datasetFull: 'You can create a maximum of {{num}} knowledge bases. Please delete some and try again.',
    noDataset: 'No available knowledge bases',
    datasetDeleted: 'The knowledge base has been deleted',
    datasetUsedByApp: 'Some applications are using this knowledge base. The applications will no longer be able to use this knowledge base, and all prompt configurations and logs will be permanently deleted.',
    deleteDatasetConfirmContent: 
    'After deleting the knowledge base, the tools and applications that call this knowledge base will become invalid synchronously. The deletion operation is irreversible. Are you sure you want to delete it?',
    inconsistentEmbeddingModelTip: 'When the Embedding model configured for the selected knowledge base is inconsistent, you need to configure the Rerank model.',
    defaultRetrievalTip: 'Multiplex recall is used by default. Retrieve knowledge from multiple knowledge bases, and then reorder it.',
    docsFailedNotice: 'The document cannot be indexed',
    mixtureHighQualityAndEconomicTip: 'Using a mix of high-quality and cost-effective knowledge bases requires the configuration of the Rerank model.',
    unavailableTip: 'Since the embedding model is not available, you need to configure the default embedding model',
    knowledgeTip: 'Click the " button to add a knowledge base',
    rerankModelRequired: 'Please select the Rerank model',
    retrieveChangeTip: 'Modifying the indexing and retrieval patterns can affect the applications associated with that knowledge base.',
    addTip: 'Once the knowledge base is added, the large model will provide more accurate answers based on the uploaded documents',
    nameError: 'The knowledge base name does not meet the requirements',
    nameRepeat: 'The knowledge base name is duplicated'
  },
  // Input placeholders
  placeholder: {
    namePlaceholder: 'Please enter the knowledge base name',
    descPlaceholder: 'Describe the content in this knowledge base. A detailed description allows the AI to access the knowledge base content in a timely manner. If left blank, the Agent Platform will use the default hit strategy.',
    rerankModel: 'Please select the Rerank model'
  },
  info: {
    name: 'Knowledge Base Name',
    desc: 'Knowledge Base Description',
    embeddingModelTip: 'To modify the Embedding model, please go',
    indexMethod: 'Index mode',
    unavailable: 'not available',
    retrievalTip: 'About the search method.',
    embeddingModel: 'Embedding model settings'
  },
  retrieval: {
    semantic_search: {
        title: 'Semantic Search',
        description: 'Retrieve text segments by matching the content similarity of the text.'
    },
    full_text_search: {
        title: 'Full-text Search',
        description: 'The full-text search processes the text, segments words to build an index, quickly searches for keywords, and sorts relevant documents.'
    },
    hybrid_search: {
        title: 'Hybrid Search',
        description: 'Hybrid search is a combination of semantic search and full-text search, which improves the quality and accuracy of search results.',
        recommend: 'Recommended'
    },
    invertedIndex: {
        title: 'Inverted Index',
        description: 'An inverted index is a structure for efficient retrieval. It is organized by terms, and each term points to the documents or web pages that contain it.'
    }
  },
  indexingTechnique: {
    high_quality: 'High Quality',
    economy: 'Economy',
    economy_tip: 'The use of offline vector engines, keyword indexing, etc., reduces the accuracy but does not need to consume tokens',
    high_quality_tip: 'The Embedding model is invoked for processing to provide greater accuracy when the user queries.'
  },
  indexingMethod: {
      semantic_search: 'Vector Search',
      full_text_search: 'Full-text Search',
      hybrid_search: 'Hybrid Search',
      invertedIndex: 'Inverted Index'
  },
  weightedScore: {
    title: 'Weight Settings',
    description: 'By adjusting the assigned weights, the reordering strategy determines whether to prioritize semantic matching or keyword matching.',
    semantic: 'Semantic',
    keyword: 'Keyword'
  },
  datasetMenus: {
    hitTesting: 'Hit test',
    settings: 'Knowledge base settings',
    documents: 'file'
  },
  permission: {
    permissionsAllMember: 'All team members',
    permissions: 'Visible permissions',
    permissionsInvitedMembers: 'Part of the team members',
    permissionsOnlyMe: 'It\'s just me',
    me: '(You)'
  },
  hit: {
    sourceValue: {
      plugin: 'tool',
      hit_testing: 'knowledge base',
      app: 'apply'
    },
    input: 'Ordered Chinese case',
    hitSeg: 'Hit the slice',
    emptyTip: 'The hit clip will be displayed in this area',
    recents: 'Hit history',
    text: 'Search for copy',
    score: 'Score:',
    viewDetail: 'Find out more',
    result: 'Hit the result',
    textPlaceholder: 'Enter the text content of the desired query',
    viewChart: 'View the vector chart',
    customSeg: 'Automatic slicing',
    source: 'Search area',
    time: 'Hit time'
  },
  title: 'knowledge base',
  paramConfig: {
    top_kTip: 'Find the top K results that are most relevant or important to the search term in a large amount of data, and fill in the value for the user.',
    score_threshold: 'Similarity match value',
    top_k: 'Maximum number of recalled fragments',
    score_thresholdTip: 'A quantitative measure of the degree of relevance or match between search results and search terms. The higher the score, the more relevant or matched the result is, and the data value is between 0 and 1.'
  }
}

export default translation
