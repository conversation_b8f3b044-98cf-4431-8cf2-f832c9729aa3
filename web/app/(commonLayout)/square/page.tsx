'use client'
import { Config<PERSON>rovider, Tabs } from 'antd'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useRouter } from 'next/navigation'
import { useMount } from 'ahooks'
import s from './styles/index.module.css'
import cn from '@/utils/classnames'
import { GRAY } from '@/themes/var-define'
import type { AppMarket } from '@/types/app-market'
import { AppMarketFeature, AppMarketSort } from '@/types/app-market'
import { fetchMarketAppspublic, fetchMarketCategories } from '@/service/market'
/* 公共组件 */
import Banner from '@/app/components/app-square/banner'
import SearchInput from '@/app/components/base/input/search-input'
import AppSquareCard from '@/app/components/app-square/card'
import IntersectScrollbar from '@/app/components/base/scrollbar/intersect-scrollbar'
import Empty from '@/app/components/base/empty'
import Select from '@/app/components/base/select/new-index'
import { APP_NAME } from '@/config'

// 生成获取应用广场列表接口参数
const getKey = (
  pageIndex: number,
  activeTab: string,
  sort: AppMarketSort,
  keywords: string,
) => {
  const params: any = { page: pageIndex, limit: 30, name: keywords, sort_by: sort, featured: AppMarketFeature.Normal }

  if (activeTab !== 'all')
    params.category_id = activeTab
  else
    delete params.category_id
  return params
}

const AppSquare = () => {
  const { t } = useTranslation()
  const router = useRouter()
  // 当前激活tab
  const [activeTab, setActiveTab] = useState('all')
  // 排序方式
  const [sort, setSort] = useState<AppMarketSort>(AppMarketSort.NEW)
  // 关键字
  const [keywords, setKeywords] = useState('')
  // 是否正在加载
  const [loading, setLoading] = useState(true)
  // 分页信息
  const [pagination, setPagination] = useState({
    page: 1,
    total: 0,
  })
  // 卡片列表
  const [cardList, setCardList] = useState<Array<AppMarket>>([])
  // 应用页面tab列表
  const [tabList, setTabList] = useState([{
    key: 'all',
    label: t('app.types.all'),
  }])
  // 排序列表
  const sortList = [{
    label: t('app.sort.new'),
    value: AppMarketSort.NEW,
  }, {
    label: t('app.sort.hot'),
    value: AppMarketSort.HOT,
  }]

  // 是否还有未加载的部分
  const hasMore = useMemo(() => {
    return pagination.total > (pagination.page * 30)
  }, [pagination])

  // 获取应用分类
  const getMarketCategories = async () => {
    await fetchMarketCategories().then((res) => {
      setTabList([
        ...tabList,
        ...res.map(item => ({
          key: item.id,
          label: item.name,
        })),
      ])
    })
  }
  // 获取应用列表接口
  const fetchAppSquareCard = useCallback(async (type: 'normal' | 'clear' = 'normal', params?: any) => {
    const param = params || getKey(pagination.page, activeTab, sort, keywords)
    setLoading(true)
    fetchMarketAppspublic(param).then((res) => {
      setCardList([])
      setCardList(type === 'normal'
        ? [
          ...cardList,
          ...res.data,
        ]
        : [...res.data])
      setPagination({
        page: param.page,
        total: res.total,
      })
    }).finally(() => {
      setLoading(false)
    })
  }, [activeTab, cardList, keywords, pagination.page, sort])
  // 重置应用列表
  const resetAppSquareCard = useCallback((
    tab = activeTab,
    sorting = sort,
    name = keywords,
  ) => {
    const param = getKey(1, tab, sorting, name)
    setPagination({
      page: 1,
      total: 0,
    })
    fetchAppSquareCard('clear', param)
  }, [activeTab, fetchAppSquareCard, keywords, sort])
  // 获取下一页
  const nextAppSquareCard = async () => {
    if (hasMore) {
      setPagination({
        page: pagination.page + 1,
        total: pagination.total,
      })
      await fetchAppSquareCard('normal', getKey(pagination.page + 1, activeTab, sort, keywords))
    }
  }

  // 变更标签
  const changeSort = (value: AppMarketSort) => {
    setSort(value)
    resetAppSquareCard(activeTab, value, keywords)
  }
  // 变更关键字方法
  const changeKeyWord = (value: string) => {
    setKeywords(value)
    resetAppSquareCard(activeTab, sort, value)
  }
  // 变更tab方法
  const changeTab = (value: string) => {
    setActiveTab(value)
    resetAppSquareCard(value, sort, keywords)
  }

  useMount(() => {
    resetAppSquareCard()
    getMarketCategories()
  })
  useEffect(() => {
    if (typeof window !== 'undefined')
      document.title = `${t('menus.appMarket')} - ${APP_NAME}`
  }, [t, router])

  // 头部信息node
  const HeaderNode = (
    <div className={s['app-square-list-header']}>
      <span className={s['header-title']}>{t('app.info.appMarket')}</span>
    </div>
  )
  // 筛选node
  const FilterNode = (
    <div className={s['app-square-list-filter']}>
      <Tabs
        className={s['search-tab']}
        activeKey={activeTab}
        items={tabList}
        onChange={changeTab}
      ></Tabs>
      <div className={s['search-criteria']}>
        <ConfigProvider
          theme={{
            components: {
              Select: {
                colorBgContainer: 'transparent',
                colorBorder: GRAY['G3-5'],
              },
              Input: {
                colorBgContainer: 'transparent',
                colorBorder: GRAY['G3-5'],
              },
            },
          }}
        >
          <Select
            value={sort}
            onChange={changeSort}
            options={sortList}
            className='mr-3 w-[110px] h-[36px]'
          ></Select>
          <SearchInput placeholder={t('app.placeholder.defaultInput') as string} value={keywords} className='w-[220px]' onChange={changeKeyWord} />
        </ConfigProvider>
      </div>
    </div>
  )

  return (
    <IntersectScrollbar
      fixClass={s['fix-content']}
      target={'app-square-list-banner'}
      onScrollY={nextAppSquareCard}
      fixContent={
        <>
          {/* 头部信息 */}
          { HeaderNode }
          {/* 筛选条件 */}
          { FilterNode }
        </>
      }
    >
      {/* 头部信息 */}
      { HeaderNode }
      {/* banner图 */}
      <Banner id='app-square-list-banner' className={s.banner}></Banner>
      {/* 筛选条件 */}
      { FilterNode }

      {/* 卡片列表 */}
      <div className={cn(s['base-card-list'], cardList.length && s['card-list'])}>
        {cardList.length
          ? cardList?.map(app => (
            <AppSquareCard key={app.id} app={app} />
          ))
          : <Empty text={t('app.notify.noApp')}></Empty>}
      </div>
    </IntersectScrollbar>
  )
}

export default AppSquare
