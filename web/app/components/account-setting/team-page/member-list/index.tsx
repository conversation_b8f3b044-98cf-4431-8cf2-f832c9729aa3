'use client'
import type { FC } from 'react'
import React, { useEffect, useMemo, useState } from 'react'
import useSWR from 'swr'
import { useTranslation } from 'react-i18next'
import { useRouter, usePara<PERSON>, useSearchParams  } from 'next/navigation'
import { omit } from 'lodash-es'
import { Pagination } from 'antd'
import { changeIndexStatus, judgeIsIndexing } from '../utils'
// import List from './list'
import List from './list'
import { get } from '@/service/base'
import { fetchDocuments, fetchDocumentsIndexingStatus } from '@/service/datasets'
import { useDatasetDetailContext } from '@/context/dataset-detail'
import type { SimpleDocumentDetail, docIndexingStatusResponse } from '@/models/datasets'
import { DataSourceType } from '@/models/datasets'
import CreateModal from '../create-modal'
import { getTeamMemberList } from '@/service/team'
import type { QueryTeamList,TeamDataType, TeamDataItemsType, QueryTeamDataType, QueryTeamMemberType } from '@/models/common'
import Operation from './operation'
import { useAppContext } from '@/context/app-context'

// 知识库公共能力
import style from '@/app/components/datasets/styles/style.module.css'
// 公共组件
import SearchInput from '@/app/components/base/input/search-input'
import Loading from '@/app/components/base/loading'
import Button from '@/app/components/base/button'
import { Add } from '@/app/components/base/icons/src/vender/solid/general'
import { asyncRunSafe } from '@/utils'
import Tooltip from '@/app/components/base/tooltip'

// Custom page count is not currently supported.
// const limit = 15

type IDocumentsProps = {
  datasetId: string
}


export const fetcher = (url: string) => get(url, {}, {})

const Documents: FC<IDocumentsProps> = ({ datasetId }) => {
  const { t } = useTranslation()
  const { dataset } = useDatasetDetailContext()
  const router = useRouter()
  const { userProfile } = useAppContext()
  const searchParams = useSearchParams()
  const { teamId='' } = useParams<{ teamId: string;}>()
  const teamRole = searchParams.get('role')
  // if (userProfile.is_vip) //is_vip 当前账户有无团队权限，is_parent 区分主子账号
  const [isParent, setIsParent] = useState(userProfile?.is_parent || false)


  // 当前团队ID
  const [teamIdValue, setTeamIdValue] = useState<string>(teamId)
  
  // 当前创建弹窗是否可见
  const [createModalVisible, setCreateModalVisible] = useState(false)
  // 邀请结果
  const [invitationResults, setInvitationResults] = useState<InvitationResult[]>([])
  // 创建结果
  const [createResults, setCreateResults] = useState<InvitationResult[]>([])
  // 是否正在加载
  const [isLoading, setIsLoading] = useState(true)
  // 成员列表信息
  const [teamMemberData, setTeamMemberData] = useState<QueryTeamMemberType[]>([])
  // 搜索值
  const [searchValue, setSearchValue] = useState<string>('')
  // 角色
  const [roleValue, setRoleValue] = useState<string>('')
  // 当前页码
  const [currPage, setCurrPage] = useState<number>(1)
  // 每页条数
  const [sizeValue, setSizeValue] = useState<number>(15)
  // 总条数
  const [totalValue, setTotalValue] = useState<number>(0)
  // 最大上限
  const [maxUpperLimit, setMaxUpperLimit] = useState(2000)
  const getTeamData = async () => {
    try {
      const params = {
        team_id: teamIdValue,
        role: roleValue || '',
        page: currPage,
        size: sizeValue,
        search: searchValue,
      }
      const response = await getTeamMemberList(params)
      // 条件日志输出，仅在开发环境下生效
      // if (process.env.NODE_ENV === 'development')
      //   console.log(response, '团队成员列表===')
      const data = response?.data || {}
      setTeamMemberData(data.items || [])
      setCurrPage(typeof data.page === 'number' ? data.page : 1)
      setSizeValue(typeof data.size === 'number' ? data.size : 10)
      // setSizeValue(2)
      setTotalValue(typeof data.total === 'number' ? data.total : 0)
      setMaxUpperLimit(typeof data.max_upper_limit === 'number' ? data.max_upper_limit : 2000)
    } catch (error) {
      // console.error('获取团队成员列表失败:', error.message)
    } finally {
      setIsLoading(false)
    }
  }
  useEffect(() => {
    setTeamIdValue(teamId)
    getTeamData()
  }, [searchValue, currPage, sizeValue, teamId, isLoading, teamRole,roleValue])

 const [indexingDocuments, setIndexingDocuments] = useState<Array<string>>([])

  // 数据源类型
  const isDataSourceNotion = dataset?.data_source_type === DataSourceType.NOTION

  // 文档列表
  const [documentsList, setDocumentsList] = useState<SimpleDocumentDetail[]>([])

  // 删除文档后更新列表
  const updateDocumentsListByDelete = () => {
    if (documentsList.length === 1 && currPage > 1)
      setCurrPage(currPage - 1)
    // else
    //   mutate()
  }

  // 创建团队
  const clickCreateMember = () => {
    setCreateModalVisible(true)
  }
  // 更新角色
  const onUpdateRole = (doc: QueryTeamMemberType) => {
    getTeamData()
  }


  return (
    <div className={style['left-part']}>
      {/* <div className={style['left-title']}>{t('datasetDocuments.list.title')}</div> */}
      <div className={style['left-content']}>
        <div className='flex items-center justify-between flex-wrap mb-3'>
          <div className='flex items-center justify-between flex-wrap mb-3'>
            <SearchInput
              className='!w-[360px]'
              value={searchValue}
              onChange={e => setSearchValue(e)}
            />
            <div className='flex items-center min-w-[300px] inline-block ml-[10px]'>
              <Operation
                value={roleValue}
                onOperate={(role)=>{
                  setRoleValue(role)
                }}
                placeholder={t('account.team.table.selectRolePlaceholder')}
                allowClear={true}
              />
            </div>
          </div>
          <div className='flex gap-2 justify-center items-center'>
            {
              teamRole !== 'normal' && (
                <>
                  <Tooltip
                    popupContent={totalValue >= maxUpperLimit ? t('account.team.table.createMembersTooltip') : ''}
                  >
                    <Button
                      variant='primary'
                      className='shrink-0'
                      // onClick={() => (isCurrentWorkspaceManager && !isMemberFull) && setInviteModalVisible(true)}
                      onClick={clickCreateMember}
                      disabled={totalValue >= maxUpperLimit}
                    >
                      <Add className='h-4 w-4' />
                      <span className='pl-2'>
                        {t('account.team.table.addMembers')}
                      </span>
                    </Button>
                  </Tooltip>
                </>
              )
            }
          </div>
        </div>
        {isLoading
          ? <Loading type='area' />
          : <List
            embeddingAvailable={true}
            documents={teamMemberData}
            // onUpdate={mutate}
            onUpdate={(e)=>{console.log(e)}}
            // onDelete={(e)=>{console.log(e)}}
            onDelete={updateDocumentsListByDelete}
            currPage={currPage}
            pageSize={sizeValue}
            teamId={teamIdValue}
            onUpdateRole={onUpdateRole}
            teamRole={teamRole}
            isParent={isParent}
            rowKey='account_id'
          />
        }
        <Pagination
          className='mt-3'
          align='end'
          current={currPage}
          hideOnSinglePage
          onChange={page => setCurrPage(page)}
          total={totalValue}
          pageSize={sizeValue}
          showQuickJumper={false}
          showSizeChanger={false}
        />
      </div>
      {
        createModalVisible && (
          <CreateModal
            title={t('account.team.table.addMembers')}
            showTeamName={false}
            showEditor = {false}
            showDatasetOperator = {false}
            teamIdValue={teamIdValue}
            onCancel={() => setCreateModalVisible(false)}
            onSend={(invitationResults) => {
              // setInvitedModalVisible(true)
              setCreateResults(invitationResults)
              getTeamData()// 刷新数据
            }}
          />
        )
      }
    </div>
  )
}

export default Documents
