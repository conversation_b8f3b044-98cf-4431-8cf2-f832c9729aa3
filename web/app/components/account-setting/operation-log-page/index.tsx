'use client'
import React, { useState, useCallback } from 'react'
import useSWR from 'swr'
import { useTranslation } from 'react-i18next'
import { Pagination } from 'antd'
import List from './list'
import Filter from './filter'
import { APP_PAGE_LIMIT } from '@/config'
import { OperationLogResponse } from './type'

export type QueryParam = {
  start?: string,
  end?: string
  keyword?: string
  sort_by?: string
}
const DataReflux = () => {
  const { t } = useTranslation()
  // 当前查询参数
  const [queryParams, setQueryParams] = useState<QueryParam>({
    sort_by: '-created_at',
  })
  // 当前页
  const [currPage, setCurrPage] = React.useState<number>(1)
  const query = {
    page: currPage,
    limit: APP_PAGE_LIMIT,
    keyword: queryParams.keyword || '',
    ...{ sort_by: queryParams.sort_by },
  } as QueryParam
  if(queryParams.start && queryParams.end) {
    query.start = queryParams.start
    query.end = queryParams.end
  }
  

  // 列表查询接口
  const operationLogData = {
    data: [
      {
        id: '1',
        user_id: '123',
        user_name: '张三',
        account: 'zhangsan',
        created_at: **********,
        workspace: 'test',
        operation: 'add',
        content: {
          type: 'add',
          source: 'app',
          target: '测试a'
        }
      },
      {
        id: '2',
        user_id: '123',
        user_name: '张三',
        account: 'zhangsan',
        created_at: **********,
        workspace: 'test',
        operation: 'modify',
        content: {
          type: 'modify',
          source: 'tool',
          target: '测试b'
        }
      },
    ],
    total: 20,
    has_more: false,
    limit: 10,
    page: 1
  } as OperationLogResponse
  const mutateDataOperationLog = () => {}
  const isLoading = false
  // const { data: operationLogData, mutate: mutateDataOperationLog, isLoading } = useSWR(() => ({
  //   url: `/operationLog`,
  //   params: query,
  // }), fetchOperation)

  const total = operationLogData?.total

  return (
    <div className='flex flex-col flex-1'>
      {/* 查询条件 */}
      <Filter 
        queryParams={queryParams} 
        setQueryParams={(params) => {
          // console.log(params, '===检索===')
          setQueryParams(params)
          setCurrPage(1)
        }} 
      />
      {/* 列表页 */}
      <List
        queryParams={queryParams} 
        setQueryParams={setQueryParams} 
        loading={isLoading} 
        operationLogData={operationLogData}
        onRefresh={mutateDataOperationLog}
      />
      {/* 分页组件 */}
      <Pagination
        className='mt-3'
        align='end'
        current={currPage}
        hideOnSinglePage
        onChange={setCurrPage}
        total={total}
        pageSize={APP_PAGE_LIMIT}
        showQuickJumper={false}
        showSizeChanger={false}
      />
    </div>
  )
}

export default DataReflux