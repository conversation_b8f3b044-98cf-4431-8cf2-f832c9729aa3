'use client'
import type { FC } from 'react'
import React, { useEffect, useMemo, useState } from 'react'
import useSWR from 'swr'
import { useTranslation } from 'react-i18next'
import { useRouter } from 'next/navigation'
import { Pagination } from 'antd'

import { changeProcessStatus, judgeIsProcessing } from '../utils'
import { useVideoCreateMode } from '../hook'
import { VideoActionBtn } from '../common/video-action-btn'
import List from './list'

import { get } from '@/service/base'
import { fetchVideoDocuments, fetchVideoDocumentsHandleStatus } from '@/service/videos'
import type { DocList, ImageItem, VideoItem } from '@/types/videos'
// 知识库公共能力
import style from '@/app/components/videos/styles/style.module.css'
// 公共组件
import SearchInput from '@/app/components/base/input/search-input'
import Loading from '@/app/components/base/loading'
import { Add } from '@/app/components/base/icons/src/vender/solid/general'
import { asyncRunSafe } from '@/utils'
import { useVideoDetailContext } from '@/context/video-detail'
import Tooltip from '@/app/components/base/tooltip'

// Custom page count is not currently supported.
const limit = 15

type IDocumentsProps = {
  videoId: string
}

export const fetcher = (url: string) => get(url, {}, {})

const Documents: FC<IDocumentsProps> = ({ videoId }) => {
  const { t } = useTranslation()
  const router = useRouter()
  const { video } = useVideoDetailContext()
  const { isImageMode, isVideoMode } = useVideoCreateMode(video!.type)
  // 搜索值
  const [searchValue, setSearchValue] = useState<string>('')
  // 当前页码
  const [currPage, setCurrPage] = React.useState<number>(1)
  // 正在索引的文档
  const [processingDocuments, setProcessingDocuments] = useState<Array<string>>([])
  // 文档列表
  const [documentsList, setDocumentsList] = useState<DocList>([])

  const query = useMemo(() => {
    return { page: currPage, size: limit, keyword: searchValue }
  }, [currPage, searchValue])

  // 获取文档结果数据
  const { data: documentsRes, isLoading, mutate } = useSWR(
    {
      action: 'fetchVideoDocuments',
      id: videoId,
      params: query,
    },
    apiParams => fetchVideoDocuments(apiParams.id, apiParams.params),
  )
  // 文档总数
  const total = documentsRes?.data.total || 0

  // 添加文件
  const routeToDocCreate = () => {
    router.push(`/videos/${videoId}/upload`)
  }
  // 批量更新正在索引的
  const batchGetProcessStatus = async () => {
    if (processingDocuments.length) {
      const [e, res] = await asyncRunSafe(
        fetchVideoDocumentsHandleStatus(videoId, { ids: processingDocuments }))
      if (!e) {
        const completedSeg: Array<any> = []
        const unCompletedSeg: Array<string> = []

        res.data.forEach((item) => {
          if (judgeIsProcessing(item as any))
            unCompletedSeg.push(item.id)
          else
            completedSeg.push(res)
        })

        // 更新分段的信息
        for (const doc of documentsList) {
          const currentStatus = completedSeg.find(item => item.key === doc.id)
          if (currentStatus)
            changeProcessStatus(doc as (VideoItem | ImageItem), currentStatus)
        }
        setDocumentsList([...documentsList] as DocList)
        // 变更还要索引的分段列表
        setProcessingDocuments(unCompletedSeg)
      }
    }
  }
  // 删除文档后更新列表
  const updateDocumentsListByDelete = () => {
    if (documentsList.length === 1 && currPage > 1)
      setCurrPage(currPage - 1)
    else
      mutate()
  }

  // 更新正在处理的文档
  useEffect(() => {
    if (documentsRes?.data && isImageMode && isVideoMode)
      setProcessingDocuments(documentsRes?.data.file_info?.filter(doc => judgeIsProcessing(doc as (ImageItem | VideoItem)))?.map(doc => doc.id) || [])
  }, [documentsRes, isImageMode, isVideoMode])
  // 更新文档列表
  useEffect(() => {
    setDocumentsList(documentsRes?.data.file_info || [])
  }, [documentsRes])
  // 轮询获取正在索引的文档列表
  useEffect(() => {
    if (isImageMode || isVideoMode) {
      const intervalController = setInterval(() => {
        batchGetProcessStatus()
      }, 1000)
      return () => clearInterval(intervalController)
    }
  }, [isImageMode, isVideoMode, processingDocuments])

  return (
    <div className={style['left-part']}>
      <div className={style['left-title']}>
        {t('videos.info.doc')}
        {isVideoMode && <Tooltip triggerClassName='ml-1' popupContent={t('videos.notify.videoTip')}></Tooltip>}
      </div>
      <div className={style['left-content']}>
        <div className='flex items-center justify-between flex-wrap mb-3'>
          <SearchInput
            className='!w-[360px]'
            value={searchValue}
            onChange={e => setSearchValue(e)}
          />
          <VideoActionBtn variant='primary' onClick={routeToDocCreate} className='shrink-0'>
            <Add className='h-4 w-4' />
            <span className='pl-2'>
              { t('videos.action.addFile')}
            </span>
          </VideoActionBtn>
        </div>
        {isLoading
          ? <Loading type='area' />
          : <List
            documents={documentsList || []}
            onUpdate={mutate}
            onDelete={updateDocumentsListByDelete}
          />
        }
        <Pagination
          className='mt-3'
          align='end'
          current={currPage}
          hideOnSinglePage
          onChange={setCurrPage}
          total={total}
          pageSize={limit}
          showQuickJumper={false}
          showSizeChanger={false}
        />
      </div>
    </div>
  )
}

export default Documents
