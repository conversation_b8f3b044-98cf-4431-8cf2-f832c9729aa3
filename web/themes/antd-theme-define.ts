import type { ThemeConfig } from 'antd'
import { GRAY, GRE<PERSON>, PRIMARY, RED, YELLOW } from './var-define'

const themeConfig: ThemeConfig = {
  token: {
    colorPrimary: PRIMARY.P1,
  },
  components: {
    Menu: {
      colorSplit: '#FFF',
      itemColor: GRAY.G1,
      colorBgContainer: 'transparent',
      itemHoverColor: GRAY.G1,
      itemSelectedColor: PRIMARY.P1,
      itemHoverBg: PRIMARY.P4,
      itemSelectedBg: PRIMARY.P4,
      itemBorderRadius: 4,
      itemMarginBlock: 4,
      itemPaddingInline: 16,
      itemMarginInline: 0,
      groupTitleColor: GRAY.G2,
      groupTitleLineHeight: 1.28571,
      groupTitleFontSize: 12,
    },
    Tabs: {
      itemActiveColor: PRIMARY.P1,
      inkBarColor: PRIMARY.P1,
      colorText: GRAY.G2,
      itemHoverColor: GRAY.G1,
      lineHeight: 1.28571,
      margin: 0,
    },
    Select: {
      colorText: GRAY.G1,
      colorPrimaryHover: PRIMARY.P1,
      colorPrimary: PRIMARY.P1,
      controlHeight: 36,
      optionSelectedBg: '#fff',
      optionSelectedColor: PRIMARY.P1,
      borderRadiusLG: 4,
      paddingXXS: 0,
      optionSelectedFontWeight: 400,
      optionActiveBg: PRIMARY.P4,
      optionHeight: 36,
      optionLineHeight: 1.71429,
      optionPadding: '6px 12px',
      colorTextPlaceholder: GRAY.G4,
      colorBorder: GRAY.G5,
      controlOutline: 'transparent',
      multipleItemBg: GRAY.G6,
      multipleItemHeight: 24,
      borderRadius: 4,
      borderRadiusSM: 2,
    },
    Steps: {
      customIconSize: 24,
      customIconFontSize: 14,
      iconSize: 24,
      iconFontSize: 14,
      titleLineHeight: 24,
    },
    Modal: {
      titleColor: '#272E47',
      titleFontSize: 18,
      titleLineHeight: '28px',
      borderRadiusLG: 4,
    },
    Drawer: {
      fontSizeLG: 18,
      lineHeightLG: 1.55,
      padding: 12,
      paddingLG: 24,
      footerPaddingBlock: 12,
      footerPaddingInline: 32,
    },
    Radio: {
      colorText: GRAY.G1,
      colorBorder: GRAY.G4,
      lineHeight: 1.71429,
      controlHeight: 36,
      colorPrimaryActive: PRIMARY.P1,
      colorPrimaryBorder: PRIMARY.P1,
      wrapperMarginInlineEnd: 52,
    },
    Tooltip: {
      colorBgSpotlight: '#fff',
      colorTextLightSolid: GRAY.G2,
      boxShadow: '0px 2px 8px rgba(24, 24, 24, 0.15)',
      fontSize: 12,
      lineHeight: 1.66667,
      paddingSM: 16,
      paddingXS: 12,
    },
    Checkbox: {
      motionDurationFast: '0s',
      motionDurationMid: '0s',
      motionDurationSlow: '0s',
    },
    Input: {
      borderRadius: 4,
      controlOutline: 'transparent',
      colorText: GRAY.G1,
      colorPrimaryHover: PRIMARY.P1,
      colorPrimary: PRIMARY.P1,
      borderRadiusLG: 4,
      paddingXXS: 0,
      colorTextPlaceholder: GRAY.G4,
      paddingInline: 12,
      colorBorder: GRAY.G5,
      borderRadiusSM: 4,
      controlHeight: 36,
    },
    Form: {
      labelColor: GRAY.G1,
      labelRequiredMarkColor: RED.R1,
      labelHeight: 24,
      lineHeight: 1.71429,
      itemMarginBottom: 24,
      colorError: RED.R2,
    },
    Table: {
      headerBg: GRAY.G7,
      headerSplitColor: GRAY.G7,
      headerBorderRadius: 6,
      rowHoverBg: PRIMARY.P4,
      headerColor: GRAY.G1,
      colorText: GRAY.G1,
      borderColor: GRAY.G6,
      fontWeightStrong: 500,
      lineHeight: 1.71429,
      cellFontSizeSM: 12,
      cellPaddingBlock: 12,
      cellPaddingInline: 16,
      cellPaddingBlockMD: 8,
      cellPaddingInlineMD: 20,
      cellPaddingBlockSM: 4,
      cellPaddingInlineSM: 12,
      selectionColumnWidth: 48,
    },
    Popover: {
      borderRadiusLG: 4,
      boxShadowSecondary: '0px 4px 30px 0px rgba(24, 24, 24, 0.15)',
    },
    Alert: {
      defaultPadding: '6px 16px',
      withDescriptionPadding: '16px 24px',
      withDescriptionIconSize: 16,
      colorSuccessBorder: GREEN.G2,
      colorSuccessBg: GREEN.G2,
      colorSuccess: GREEN.G1,
      colorInfoBorder: PRIMARY.P4,
      colorInfoBg: PRIMARY.P4,
      colorInfo: PRIMARY.P1,
      colorWarningBorder: YELLOW.Y2,
      colorWarningBg: YELLOW.Y2,
      colorWarning: YELLOW.Y1,
      colorErrorBorder: RED.R3,
      colorErrorBg: RED.R3,
      colorError: RED.R2,
      colorTextHeading: GRAY.G1,
      colorText: GRAY.G2,
      colorIcon: GRAY.G4,
      colorIconHover: GRAY.G3,
    },
    Dropdown: {
      colorPrimary: PRIMARY.P1,
      controlItemBgActive: PRIMARY.P4,
      colorText: GRAY.G1,
      borderRadiusLG: 4,
    },
    Button: {
      colorPrimary: PRIMARY.P1,
      colorLinkHover: PRIMARY.P2,
      colorLinkActive: PRIMARY.P1,
    },
    Divider: {
      colorSplit: GRAY.G5,
      colorBgElevated: GRAY.G5,
      margin: 20,
      marginLG: 20,
    },
    Tag: {
      defaultBg: '#ffffff',
      defaultColor: GRAY.G2,
      borderRadiusSM: 4,
      fontSizeSM: 14,
      lineHeightSM: 1.4285,
    },
    InputNumber: {
      activeBorderColor: PRIMARY.P1,
      hoverBorderColor: PRIMARY.P1,
      addonBg: '#ffffff',
      controlWidth: 100,
      handleActiveBg: '#ffffff',
      handleBorderColor: GRAY.G5,
      handleFontSize: 8,
      handleHoverColor: GRAY.G2,
      paddingBlock: 6,
      paddingInline: 12,
    },
    Slider: {
      handleColor: '#ffffff',
      handleActiveColor: '#ffffff',
      handleLineWidthHover: 2,
      handleSize: 8,
      handleSizeHover: 8,
      trackBg: PRIMARY.P1,
      trackHoverBg: PRIMARY.P1,
      handleActiveOutlineColor: PRIMARY.P2,
      colorPrimaryBorderHover: '#ffffff',
      railBg: GRAY.G6,
      railHoverBg: GRAY.G6,
    },
    Pagination: {
      colorBgTextHover: '#ffffff',
      colorText: GRAY.G2,
      itemActiveBg: PRIMARY.P4,
    },
    DatePicker: {
      borderRadius: 4,
      controlHeight: 36,
      cellBgDisabled: GRAY.G7,
      cellHeight: 32,
      cellWidth: 32,
      colorSplit: GRAY.G5,
    },
  },
}

export default themeConfig
