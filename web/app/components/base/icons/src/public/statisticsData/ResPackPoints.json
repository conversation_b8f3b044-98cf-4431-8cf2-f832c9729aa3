{"icon": {"type": "element", "isRootNode": true, "name": "svg", "attributes": {"width": "34", "height": "32", "viewBox": "0 0 34 32", "fill": "none", "xmlns": "http://www.w3.org/2000/svg"}, "children": [{"type": "element", "name": "foreignObject", "attributes": {"x": "3.33346", "y": "1.33346", "width": "33.3331", "height": "33.3331"}, "children": [{"type": "element", "name": "div", "attributes": {"xmlns": "http://www.w3.org/1999/xhtml", "style": "backdrop-filter:blur(2.12px);clip-path:url(#bgblur_0_12415_22046_clip_path);height:100%;width:100%"}, "children": []}]}, {"type": "element", "name": "g", "attributes": {"filter": "url(#filter0_i_12415_22046)", "data-figma-bg-blur-radius": "4.24271"}, "children": [{"type": "element", "name": "rect", "attributes": {"x": "8", "y": "6", "width": "24", "height": "24", "rx": "4", "fill": "#CCDAFB", "fill-opacity": "0.3"}, "children": []}, {"type": "element", "name": "rect", "attributes": {"x": "7.78786", "y": "5.78786", "width": "24.4243", "height": "24.4243", "rx": "4.21214", "stroke": "url(#paint0_linear_12415_22046)", "stroke-width": "0.424271"}, "children": []}]}, {"type": "element", "name": "g", "attributes": {"filter": "url(#filter1_di_12415_22046)"}, "children": [{"type": "element", "name": "rect", "attributes": {"x": "4", "y": "2", "width": "24", "height": "24", "rx": "4", "fill": "#3068F5"}, "children": []}]}, {"type": "element", "name": "path", "attributes": {"d": "M12.4844 14.2551C12.4844 14.9749 12.7703 15.6652 13.2793 16.1742C13.7883 16.6832 14.4786 16.9691 15.1984 16.9691C15.9183 16.9691 16.6086 16.6832 17.1176 16.1742C17.6266 15.6652 17.9125 14.9749 17.9125 14.2551C17.9125 13.5353 17.6266 12.8449 17.1176 12.3359C16.6086 11.827 15.9183 11.541 15.1984 11.541C14.4786 11.541 13.7883 11.827 13.2793 12.3359C12.7703 12.8449 12.4844 13.5353 12.4844 14.2551Z", "fill": "white"}, "children": []}, {"type": "element", "name": "path", "attributes": {"d": "M9.27344 11C9.27344 11.4258 9.44258 11.8342 9.74367 12.1352C10.0448 12.4363 10.4531 12.6055 10.8789 12.6055C11.3047 12.6055 11.7131 12.4363 12.0141 12.1352C12.3152 11.8342 12.4844 11.4258 12.4844 11C12.4844 10.5742 12.3152 10.1658 12.0141 9.86476C11.7131 9.56368 11.3047 9.39453 10.8789 9.39453C10.4531 9.39453 10.0448 9.56368 9.74367 9.86476C9.44258 10.1658 9.27344 10.5742 9.27344 11Z", "fill": "white"}, "children": []}, {"type": "element", "name": "path", "attributes": {"d": "M15.4805 8.89844C15.4805 9.11543 15.5232 9.33029 15.6062 9.53076C15.6893 9.73123 15.811 9.91339 15.9644 10.0668C16.1179 10.2203 16.3 10.342 16.5005 10.425C16.701 10.508 16.9158 10.5508 17.1328 10.5508C17.3498 10.5508 17.5647 10.508 17.7651 10.425C17.9656 10.342 18.1478 10.2203 18.3012 10.0668C18.4546 9.91339 18.5763 9.73123 18.6594 9.53076C18.7424 9.33029 18.7852 9.11543 18.7852 8.89844C18.7852 8.68145 18.7424 8.46658 18.6594 8.26611C18.5763 8.06564 18.4546 7.88349 18.3012 7.73005C18.1478 7.57662 17.9656 7.45491 17.7651 7.37187C17.5647 7.28883 17.3498 7.24609 17.1328 7.24609C16.9158 7.24609 16.701 7.28883 16.5005 7.37187C16.3 7.45491 16.1179 7.57662 15.9644 7.73005C15.811 7.88349 15.6893 8.06564 15.6062 8.26611C15.5232 8.46658 15.4805 8.68145 15.4805 8.89844Z", "fill": "white"}, "children": []}, {"type": "element", "name": "path", "attributes": {"d": "M10.4922 19.2602C10.4922 19.6741 10.6566 20.0712 10.9494 20.3639C11.2421 20.6566 11.6391 20.8211 12.0531 20.8211C12.4671 20.8211 12.8641 20.6566 13.1569 20.3639C13.4496 20.0712 13.6141 19.6741 13.6141 19.2602C13.6141 18.8462 13.4496 18.4491 13.1569 18.1564C12.8641 17.8637 12.4671 17.6992 12.0531 17.6992C11.6391 17.6992 11.2421 17.8637 10.9494 18.1564C10.6566 18.4491 10.4922 18.8462 10.4922 19.2602Z", "fill": "white"}, "children": []}, {"type": "element", "name": "path", "attributes": {"d": "M17.4805 18.1254C17.4805 18.4617 17.6141 18.7842 17.8518 19.022C18.0896 19.2598 18.4122 19.3934 18.7484 19.3934C19.0847 19.3934 19.4072 19.2598 19.645 19.022C19.8828 18.7842 20.0164 18.4617 20.0164 18.1254C20.0164 17.7891 19.8828 17.4666 19.645 17.2288C19.4072 16.991 19.0847 16.8574 18.7484 16.8574C18.4122 16.8574 18.0896 16.991 17.8518 17.2288C17.6141 17.4666 17.4805 17.7891 17.4805 18.1254Z", "fill": "white"}, "children": []}, {"type": "element", "name": "path", "attributes": {"d": "M19.1797 12.9695C19.1797 13.4494 19.3703 13.9096 19.7096 14.249C20.049 14.5883 20.5092 14.7789 20.9891 14.7789C21.4689 14.7789 21.9292 14.5883 22.2685 14.249C22.6078 13.9096 22.7984 13.4494 22.7984 12.9695C22.7984 12.4897 22.6078 12.0294 22.2685 11.6901C21.9292 11.3508 21.4689 11.1602 20.9891 11.1602C20.5092 11.1602 20.049 11.3508 19.7096 11.6901C19.3703 12.0294 19.1797 12.4897 19.1797 12.9695Z", "fill": "white"}, "children": []}, {"type": "element", "name": "path", "attributes": {"d": "M15.0664 14.1704L16.9323 8.88867L17.3588 9.03935L15.4929 14.3211L15.0664 14.1704Z", "fill": "white"}, "children": []}, {"type": "element", "name": "path", "attributes": {"d": "M18.5734 18.2656L14.8516 14.1078L21.1586 12.7344L21.2547 13.1773L15.707 14.3844L18.9086 17.9633L18.5734 18.2656Z", "fill": "white"}, "children": []}, {"type": "element", "name": "path", "attributes": {"d": "M11.8516 19.1266L14.9961 14.127L15.379 14.3678L12.2345 19.3674L11.8516 19.1266Z", "fill": "white"}, "children": []}, {"type": "element", "name": "path", "attributes": {"d": "M10.7305 11.1725L10.9992 10.8086L15.4108 14.0663L15.1421 14.4302L10.7305 11.1725Z", "fill": "white"}, "children": []}, {"type": "element", "name": "defs", "attributes": {}, "children": [{"type": "element", "name": "filter", "attributes": {"id": "filter0_i_12415_22046", "x": "3.33346", "y": "1.33346", "width": "33.3331", "height": "33.3331", "filterUnits": "userSpaceOnUse", "color-interpolation-filters": "sRGB"}, "children": [{"type": "element", "name": "feFlood", "attributes": {"flood-opacity": "0", "result": "BackgroundImageFix"}, "children": []}, {"type": "element", "name": "feBlend", "attributes": {"mode": "normal", "in": "SourceGraphic", "in2": "BackgroundImageFix", "result": "shape"}, "children": []}, {"type": "element", "name": "feColorMatrix", "attributes": {"in": "SourceAlpha", "type": "matrix", "values": "0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0", "result": "hardAl<PERSON>"}, "children": []}, {"type": "element", "name": "feOffset", "attributes": {"dx": "0.909152", "dy": "3.63661"}, "children": []}, {"type": "element", "name": "feG<PERSON><PERSON><PERSON>lur", "attributes": {"stdDeviation": "4.54576"}, "children": []}, {"type": "element", "name": "feComposite", "attributes": {"in2": "hardAl<PERSON>", "operator": "arithmetic", "k2": "-1", "k3": "1"}, "children": []}, {"type": "element", "name": "feColorMatrix", "attributes": {"type": "matrix", "values": "0 0 0 0 0.0524819 0 0 0 0 0.0428362 0 0 0 0 0.869173 0 0 0 0.2 0"}, "children": []}, {"type": "element", "name": "feBlend", "attributes": {"mode": "normal", "in2": "shape", "result": "effect1_innerShadow_12415_22046"}, "children": []}]}, {"type": "element", "name": "clipPath", "attributes": {"id": "bgblur_0_12415_22046_clip_path", "transform": "translate(-3.33346 -1.33346)"}, "children": [{"type": "element", "name": "rect", "attributes": {"x": "8", "y": "6", "width": "24", "height": "24", "rx": "4"}, "children": []}]}, {"type": "element", "name": "filter", "attributes": {"id": "filter1_di_12415_22046", "x": "0.363394", "y": "0.181697", "width": "31.2732", "height": "31.2732", "filterUnits": "userSpaceOnUse", "color-interpolation-filters": "sRGB"}, "children": [{"type": "element", "name": "feFlood", "attributes": {"flood-opacity": "0", "result": "BackgroundImageFix"}, "children": []}, {"type": "element", "name": "feColorMatrix", "attributes": {"in": "SourceAlpha", "type": "matrix", "values": "0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0", "result": "hardAl<PERSON>"}, "children": []}, {"type": "element", "name": "feOffset", "attributes": {"dy": "1.8183"}, "children": []}, {"type": "element", "name": "feG<PERSON><PERSON><PERSON>lur", "attributes": {"stdDeviation": "1.8183"}, "children": []}, {"type": "element", "name": "feComposite", "attributes": {"in2": "hardAl<PERSON>", "operator": "out"}, "children": []}, {"type": "element", "name": "feColorMatrix", "attributes": {"type": "matrix", "values": "0 0 0 0 0.017679 0 0 0 0 0.198 0 0 0 0 0.653548 0 0 0 0.2 0"}, "children": []}, {"type": "element", "name": "feBlend", "attributes": {"mode": "normal", "in2": "BackgroundImageFix", "result": "effect1_dropShadow_12415_22046"}, "children": []}, {"type": "element", "name": "feBlend", "attributes": {"mode": "normal", "in": "SourceGraphic", "in2": "effect1_dropShadow_12415_22046", "result": "shape"}, "children": []}, {"type": "element", "name": "feColorMatrix", "attributes": {"in": "SourceAlpha", "type": "matrix", "values": "0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0", "result": "hardAl<PERSON>"}, "children": []}, {"type": "element", "name": "feOffset", "attributes": {"dx": "1.8183", "dy": "1.8183"}, "children": []}, {"type": "element", "name": "feG<PERSON><PERSON><PERSON>lur", "attributes": {"stdDeviation": "1.8183"}, "children": []}, {"type": "element", "name": "feComposite", "attributes": {"in2": "hardAl<PERSON>", "operator": "arithmetic", "k2": "-1", "k3": "1"}, "children": []}, {"type": "element", "name": "feColorMatrix", "attributes": {"type": "matrix", "values": "0 0 0 0 0.0272837 0 0 0 0 0.208621 0 0 0 0 0.665202 0 0 0 0.2 0"}, "children": []}, {"type": "element", "name": "feBlend", "attributes": {"mode": "normal", "in2": "shape", "result": "effect2_innerShadow_12415_22046"}, "children": []}]}, {"type": "element", "name": "linearGradient", "attributes": {"id": "paint0_linear_12415_22046", "x1": "14.4858", "y1": "12.8482", "x2": "25.6985", "y2": "22.8487", "gradientUnits": "userSpaceOnUse"}, "children": [{"type": "element", "name": "stop", "attributes": {"stop-color": "white", "stop-opacity": "0.6"}, "children": []}, {"type": "element", "name": "stop", "attributes": {"offset": "1", "stop-color": "white", "stop-opacity": "0.6"}, "children": []}]}]}]}, "name": "ResPackPoints"}