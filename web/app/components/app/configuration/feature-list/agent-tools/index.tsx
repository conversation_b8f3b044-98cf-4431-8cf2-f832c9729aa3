'use client'
import type { FC } from 'react'
import React, { useCallback, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useContext } from 'use-context-selector'
import produce from 'immer'
import { Switch } from 'antd'

import { useFormattingChangedDispatcher } from '../../debug/hooks'
import SettingBuiltInTool from './setting-built-in-tool'
import ConfigContext from '@/context/debug-configuration'
import type { AgentTool, ToolItem } from '@/types/tools'
import { type Collection, CollectionType } from '@/app/components/tools/types'
import type { ModelConfig } from '@/models/debug'
import AddToolModal from '@/app/components/tools/add-tool-modal'

import Toast from '@/app/components/base/toast'
import { AddToolPanelContent } from '@/app/components/tools/add-tool-panel'

type AgentToolWithMoreInfo = AgentTool & { icon: any; collection?: Collection } | null
const AgentTools: FC = () => {
  const { t } = useTranslation()
  const formattingChangedDispatcher = useFormattingChangedDispatcher()
  const {
    modelConfig,
    setModelConfig,
    collectionList,
    forkTools,
  } = useContext(ConfigContext)

  // 是否显示添加工具弹窗
  const [isShowChooseTool, setIsShowChooseTool] = useState(false)
  // 当前选中工具
  const [currentTool, setCurrentTool] = useState<AgentToolWithMoreInfo>(null)
  // 是否显示设置工具弹窗
  const [isShowSettingTool, setIsShowSettingTool] = useState(false)

  // 整理工具信息
  const tools = (modelConfig?.agentConfig?.tools as AgentTool[] || []).map((item) => {
    const collection = collectionList.find(collection => collection.id === item.provider_id && collection.type === item.provider_type)
    const icon_url = collection?.icon_url
    return {
      ...item,
      icon_url,
      collection,
      isDeleted: !collection,
    }
  })

  // 工具设置变更
  const handleToolSettingChange = (value: Record<string, any>) => {
    const newModelConfig = produce(modelConfig, (draft) => {
      const tool = (draft.agentConfig.tools).find((item: any) => item.provider_id === currentTool?.collection?.id && item.tool_name === currentTool?.tool_name)
      if (tool)
        (tool as AgentTool).tool_parameters = value
    })
    setModelConfig(newModelConfig)
    setIsShowSettingTool(false)
    formattingChangedDispatcher()
  }
  // 关闭工具fork提示
  const handleCloseForkTip = () => {
    const newModelConfig = produce(modelConfig, (draft) => {
      draft.agentConfig.fork = false
    })
    setModelConfig(newModelConfig)
    formattingChangedDispatcher()
  }
  // 点击显示工具详情弹窗
  const handleShowToolSetting = useCallback((tool: AgentToolWithMoreInfo) => {
    if (!tool?.collection) {
      Toast.notify({
        type: 'error',
        message: t('tools.notify.toolRemoved'),
      })
      return
    }
    setCurrentTool(tool)
    setIsShowSettingTool(true)
  }, [])
  // 变更选中工具
  const handleChangeTools = (tools: Array<ToolItem>) => {
    const nexModelConfig = produce(modelConfig, (draft: ModelConfig) => {
      draft.agentConfig.tools = tools
      draft.agentConfig.fork = false
    })
    setModelConfig(nexModelConfig)
    formattingChangedDispatcher()
  }

  return (
    <>
      <AddToolPanelContent
        tools={tools}
        forkTools={forkTools}
        onCloseFock={handleCloseForkTip}
        onAdd={() => setIsShowChooseTool(true)}
        onView={handleShowToolSetting}
        onRemove={(index) => {
          const newModelConfig = produce(modelConfig, (draft) => {
            draft.agentConfig.tools.splice(index, 1)
            draft.agentConfig.fork = false
          })
          setModelConfig(newModelConfig)
          formattingChangedDispatcher()
        }}
        scene='app'
        border={true}
        extra={(item, index) => (
          <Switch
            value={(item.isDeleted || item.notAuthor) ? false : item.enabled}
            disabled={(item.isDeleted || item.notAuthor)}
            size='small'
            onChange={(enabled) => {
              const newModelConfig = produce(modelConfig, (draft) => {
                (draft.agentConfig.tools[index] as any).enabled = enabled
              })
              setModelConfig(newModelConfig)
              formattingChangedDispatcher()
            }} />
        )}
      ></AddToolPanelContent>

      {/* 添加工具弹窗 */}
      {isShowChooseTool && (
        <AddToolModal onChange={handleChangeTools} tools={modelConfig.agentConfig.tools} onHide={() => setIsShowChooseTool(false)}/>
      )}
      {/* 设置鉴权弹窗 */}
      {
        isShowSettingTool && (
          <SettingBuiltInTool
            toolName={currentTool?.tool_name as string}
            setting={currentTool?.tool_parameters as any}
            collection={currentTool?.collection as Collection}
            isBuiltIn={currentTool?.collection?.type === CollectionType.builtIn}
            isModel={currentTool?.collection?.type === CollectionType.model}
            onSave={handleToolSettingChange}
            onHide={() => setIsShowSettingTool(false)}
          />)
      }
    </>
  )
}
export default React.memo(AgentTools)
