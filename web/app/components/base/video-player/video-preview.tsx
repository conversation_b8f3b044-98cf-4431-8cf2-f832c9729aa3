import type { FC } from 'react'
import React, { useCallback, useEffect, useState } from 'react'
import { t } from 'i18next'
import { createPortal } from 'react-dom'
import './styles/index.css'
// 公告组件
import VideoPlayer from '.'
import Tooltip from '@/app/components/base/tooltip'
import Toast from '@/app/components/base/toast'
import { ZoomIn, ZoomOut } from '@/app/components/base/icons/src/vender/workflow'
import { Download02 } from '@/app/components/base/icons/src/vender/solid/general'
import { Close } from '@/app/components/base/icons/src/vender/line/general'

type VideoPreviewProps = {
  url: string
  title: string
  onCancel: () => void
}

const VideoPreview: FC<VideoPreviewProps> = ({
  url,
  title,
  onCancel,
}) => {
  // 缩放比例
  const [scale, setScale] = useState(1)
  // 位置信息
  const [position, setPosition] = useState({ x: 0, y: 0 })

  // 下载视频
  const downloadVideo = () => {
    if (url.startsWith('http') || url.startsWith('https') || url.startsWith('/')) {
      const a = document.createElement('a')
      a.href = url
      a.download = title
      a.click()
    }
    else {
      Toast.notify({
        type: 'error',
        message: t('common.component.videoPlayer.downloadError', { url }),
      })
    }
  }
  // 缩放
  const zoomIn = () => {
    setScale(prevScale => Math.min(prevScale * 1.2, 15))
  }
  // 放大
  const zoomOut = () => {
    setScale((prevScale) => {
      const newScale = Math.max(prevScale / 1.2, 0.5)
      if (newScale === 1)
        setPosition({ x: 0, y: 0 }) // Reset position when fully zoomed out

      return newScale
    })
  }
  // 处理滑轮滚动事件
  const handleWheel = useCallback((e: React.WheelEvent<HTMLDivElement>) => {
    if (e.deltaY < 0)
      zoomIn()
    else
      zoomOut()
  }, [])

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape')
        onCancel()
    }

    window.addEventListener('keydown', handleKeyDown)

    return () => {
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [onCancel])

  return createPortal(
    <div
      className='fixed inset-0 p-[70px] flex items-center justify-center bg-black/60 z-[1000]'
      onClick={e => e.stopPropagation()}
      onWheel={handleWheel}
      tabIndex={-1}
    >
      <VideoPlayer
        src={url}
        className='video-preview-player !max-w-full !max-h-full !w-full !h-full'
        style={{
          transform: `scale(${scale}) translate(${position.x}px, ${position.y}px)`,
        }}
      />

      <Tooltip popupContent={t('common.operation.zoomOut')}>
        <div className='absolute top-6 right-32 flex items-center justify-center w-8 h-8 cursor-pointer'
          onClick={zoomOut}>
          <ZoomOut className='w-4 h-4 text-white'/>
        </div>
      </Tooltip>
      <Tooltip popupContent={t('common.operation.zoomIn')}>
        <div className='absolute top-6 right-24 flex items-center justify-center w-8 h-8 cursor-pointer'
          onClick={zoomIn}>
          <ZoomIn className='w-4 h-4 text-white'/>
        </div>
      </Tooltip>
      <Tooltip popupContent={t('common.operation.download')}>
        <div className='absolute top-6 right-16 flex items-center justify-center w-8 h-8 cursor-pointer'
          onClick={downloadVideo}>
          <Download02 className='w-4 h-4 text-white'/>
        </div>
      </Tooltip>
      <Tooltip popupContent={t('common.operation.cancel')}>
        <div
          className='absolute top-6 right-6 flex items-center justify-center w-8 h-8 bg-white/8 rounded-lg backdrop-blur-[2px] cursor-pointer'
          onClick={onCancel}>
          <Close className='w-4 h-4 text-white'/>
        </div>
      </Tooltip>
    </div>,
    document.body,
  )
}

export default VideoPreview
