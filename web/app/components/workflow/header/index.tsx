import type { FC } from 'react'
import {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react'
import { useEdges, useNodes } from 'reactflow'
import { useTranslation } from 'react-i18next'
import { useContext } from 'use-context-selector'
import { Divider } from 'antd'
import {
  useStore,
  useWorkflowStore,
} from '../store'
import type { CommonEdgeType } from '../types'
import {
  BlockEnum,
  InputVarType,
} from '../types'
import type { StartNodeType } from '../nodes/start/types'
import {
  useChecklist,
  useChecklistBeforePublish,
  useNodesReadOnly,
  useNodesSyncDraft,
  useWorkflowMode,
  useWorkflowRun,
} from '../hooks'
import type { WorkflowToolProviderRequest } from '../../tools/types'
import WorkflowTool from '../../tools/workflow-tool'
import RunAndHistory from './run-and-history'
import WorkflowTitle from './title'
import ViewHistory from './view-history'
import style from './styles/style.module.css'
import AppPublishModal from '@/app/components/app-publish/app-publish-modal'
import AppPublisher from '@/app/components/app/app-publisher'
import { useAppPublishInit } from '@/app/components/app-publish/hooks/use-app-publish'
import { publishWorkflow } from '@/service/workflow'
import { publishApp } from '@/service/market'

// 公共组件
import { ToastContext } from '@/app/components/base/toast'
import Button from '@/app/components/base/button'
import { useStore as useAppStore } from '@/app/components/app/store'
import { ArrowNarrowLeft } from '@/app/components/base/icons/src/vender/line/arrows'
import { Feature } from '@/app/components/base/icons/src/vender/line/general'
import { useFeatures } from '@/app/components/base/features/hooks'
import { useAppContext } from '@/context/app-context'
import { ParamConfig } from '@/app/components/base/icons/src/vender/line/editor'

const Header: FC = () => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  const workflowStore = useWorkflowStore()
  const { canAdmin } = useAppContext()
  const { nodesReadOnly, getNodesReadOnly } = useNodesReadOnly()
  const {
    handleLoadBackupDraft,
    handleBackupDraft,
    handleRestoreFromPublishedWorkflow,
  } = useWorkflowRun()
  const {
    normal,
    restoring,
    viewHistory,
  } = useWorkflowMode()
  const { handleCheckBeforePublish } = useChecklistBeforePublish()
  const { handleSyncWorkflowDraft, doSyncWorkflowDraft } = useNodesSyncDraft()
  const {
    getAppPublishInfoData,
    appPublishInfo,
  } = useAppPublishInit()

  // 应用详情
  const appDetail = useAppStore(s => s.appDetail)
  const appMode = appDetail?.mode
  const appId = appDetail?.id
  // 工作流详情
  const publishedAt = useStore(s => s.publishedAt)
  const draftUpdatedAt = useStore(s => s.draftUpdatedAt)
  const toolPublished = useStore(s => s.toolPublished)
  // 画布详情
  const nodes = useNodes<StartNodeType>()
  const startNode = nodes.find(node => node.data.type === BlockEnum.Start)
  const startVariables = startNode?.data.variables
  const edges = useEdges<CommonEdgeType>()
  const needWarningNodes = useChecklist(nodes, edges)
  // 功能详情
  const fileSettings = useFeatures(s => s.features.file)

  // 是否存在私有节点
  const [notPublishedAfterCopied, setNotPublishedAfterCopied] = useState<boolean>(false)
  // 是否显示参数配置弹窗
  const [showConfigModal, setShowConfigModal] = useState(false)
  // 是否显示应用发布弹窗
  const [showAppPublishModal, setShowAppPublishModal] = useState(false)
  // 是否已发布过
  const [isPublished, setIsPublished] = useState<boolean>(false)

  // 变量列表
  const variables = useMemo(() => {
    const data = startVariables || []
    if (fileSettings?.image?.enabled) {
      return [
        ...data,
        {
          type: InputVarType.files,
          variable: '__image',
          required: false,
          label: 'files',
        },
      ]
    }

    return data
  }, [fileSettings?.image?.enabled, startVariables])
  // 参数配置入参
  const parameters = useMemo(() => {
    return (variables || []).map((item) => {
      return {
        name: item.variable,
        description: '',
        form: 'llm',
        required: item.required,
        type: item.type,
      }
    })
  }, [variables])

  // 显示功能弹窗
  const handleShowFeatures = useCallback(() => {
    const {
      showFeaturesPanel,
      isRestoring,
      setShowFeaturesPanel,
    } = workflowStore.getState()
    if (getNodesReadOnly() && !isRestoring)
      return
    setShowFeaturesPanel(!showFeaturesPanel)
  }, [workflowStore, getNodesReadOnly])
  // 取消恢复，恢复到未恢复状态
  const handleCancelRestore = useCallback(() => {
    handleLoadBackupDraft()
    workflowStore.setState({ isRestoring: false })
  }, [workflowStore, handleLoadBackupDraft])
  // 确认恢复，保存画布
  const handleRestore = useCallback(() => {
    workflowStore.setState({ isRestoring: false })
    workflowStore.setState({ backupDraft: undefined })
    handleSyncWorkflowDraft(true)
  }, [handleSyncWorkflowDraft, workflowStore])
  // 复制/创建同款产物去除复制私有文档/私有工具后才可以运行
  const judgeNotPublishedAfterCopied = useCallback(() => {
    if (nodes.filter(node => node.data?.fork === true).length > 0)
      setNotPublishedAfterCopied(true)
    else
      setNotPublishedAfterCopied(false)
  }, [nodes])

  // 再调用发布/更新接口之前先调用画布保存接口
  const onPublish = useCallback(async () => {
    if (handleCheckBeforePublish()) {
      await doSyncWorkflowDraft()
      const res = await publishWorkflow(`/apps/${appId}/workflows/publish`)
      if (res) {
        workflowStore.getState().setPublishedAt(res.created_at)
        judgeNotPublishedAfterCopied()
      }
      else {
        throw new Error('Publish failed')
      }
    }
    else {
      throw new Error('Checklist failed')
    }
  }, [handleCheckBeforePublish, doSyncWorkflowDraft, appId, notify, t, workflowStore, judgeNotPublishedAfterCopied])
  // 保存应用画布
  const onSave = useCallback(async () => {
    const res = await doSyncWorkflowDraft()
    if (res?.success) {
      notify({
        type: 'success',
        message: t('common.actionMsg.saveSuccessfully'),
      })
    }
  }, [doSyncWorkflowDraft, notify, t])
  // 点击恢复，调用接口查询上次发布保存的画布数据并替换当前画布
  const onStartRestoring = useCallback(() => {
    workflowStore.setState({ isRestoring: true })
    // 本地备份当前画布数据，用于取消恢复
    handleBackupDraft()
    handleRestoreFromPublishedWorkflow()
  }, [handleBackupDraft, handleRestoreFromPublishedWorkflow, workflowStore])
  // 更新工具配置
  const handleToolConfigureUpdate = useCallback(() => {
    workflowStore.setState({ toolPublished: true })
  }, [workflowStore])
  // 点击发布
  const clickPublish = useCallback(async () => {
    setShowAppPublishModal(true)
  }, [])
  // 应用正式发布
  const handlePublish = useCallback(async (appPublishConfig: any) => {
    if (!appId)
      return
    await onPublish().then(async () => {
      await publishApp(appId, appPublishConfig)
      notify({
        type: 'success',
        message: t('app.notify.successPublishApp'),
      })
      setShowAppPublishModal(false)
      getAppPublishInfoData()
      setIsPublished(true)
    })
  }, [appId, getAppPublishInfoData, notify, onPublish, t])

  // 更新工作流工具
  const updateWorkflowToolProvider = async (data: WorkflowToolProviderRequest & Partial<{
    workflow_app_id: string
    workflow_tool_id: string
  }>) => {
    await onPublish().then(() => {
      handleToolConfigureUpdate?.()
      setShowConfigModal(false)
    })
  }

  const onPublisherToggle = useCallback((state: boolean) => {
    if (state)
      handleSyncWorkflowDraft(true)
  }, [handleSyncWorkflowDraft])
  const handleGoBackToEdit = useCallback(() => {
    handleLoadBackupDraft()
    workflowStore.setState({ historyWorkflowData: undefined })
  }, [workflowStore, handleLoadBackupDraft])

  useEffect(() => {
    if (isPublished)
      return
    const getIsPublished = appPublishInfo?.status || appDetail?.web_site?.access_token || appDetail?.embed_site?.access_token || appDetail?.api_site?.access_token
    setIsPublished(!!getIsPublished)
  }, [isPublished, appPublishInfo, appDetail])
  // 创建同款产物去除复制私有文档/工具后才可以运行
  useEffect(() => {
    judgeNotPublishedAfterCopied()
  }, [publishedAt, judgeNotPublishedAfterCopied])

  return (
    <>
      <div
        className='absolute top-0 left-0 z-10 flex items-center justify-between w-full px-3 h-[42px]'
      >
        {/* 左上角的发布时间等信息 */}
        <WorkflowTitle />
        {/* 右上角的按钮操作列表 */}
        <div className='flex absolute justify-end top-[-60px] right-[24px] h-[60px]'>
          {/* 正常情况 */}
          {
            normal && (
              <div className='flex items-center gap-2'>
                {/* 功能 */}
                <Button variant={'secondary-accent'} onClick={handleShowFeatures}>
                  <Feature className='w-4 h-4 mr-2' />
                  {t('workflow.common.features')}
                </Button>
                {/* 运行以及历史记录 */}
                <RunAndHistory />
                {/* 参数配置 */}
                { appMode === 'workflow'
                  && <Button
                    variant={'secondary-accent'}
                    disabled={!toolPublished || !canAdmin}
                    onClick={() => setShowConfigModal(true)}
                    className='!text-gray-G1'
                  >
                    <ParamConfig className='w-4 h-4 mr-1'></ParamConfig>
                    { t('workflow.common.toolConfig') }
                  </Button>}
                {/* 保存 */}
                {<Button className={style.iconBtn} onClick={onSave}>{t('common.operation.save')}</Button>}
                {/* 应用发布和提交 */}
                { appDetail?.mode !== 'workflow' && (
                  <>
                    {/* <AppSave
                    {...{
                      notPublishedAfterCopied,
                      publishedAt,
                      draftUpdatedAt,
                      disabled: nodesReadOnly,
                      toolPublished,
                      inputs: variables,
                      onRefreshData: handleToolConfigureUpdate,
                      onPublish,
                      onRestore: onStartRestoring,
                      onToggle: onPublisherToggle,
                      crossAxisOffset: 4,
                    }}
                  /> */}
                    <Button variant={'primary'} onClick={clickPublish} disabled={!handleCheckBeforePublish(false)}>
                      { isPublished ? t('app.action.updatePublish') : t('app.action.publish') }
                    </Button>
                  </>
                )}
                {
                  appDetail?.mode === 'workflow'
                  && <AppPublisher
                    {...{
                      notPublishedAfterCopied,
                      publishedAt,
                      draftUpdatedAt,
                      disabled: nodesReadOnly || !!needWarningNodes.length,
                      publishDisabled: !!needWarningNodes.length,
                      toolPublished,
                      inputs: variables,
                      onRefreshData: handleToolConfigureUpdate,
                      onPublish,
                      onRestore: onStartRestoring,
                      onToggle: onPublisherToggle,
                      crossAxisOffset: 4,
                    }}
                  />
                }
              </div>
            )
          }
          {/* 查看历史 */}
          {
            viewHistory && (
              <div className='flex items-center'>
                <ViewHistory withText />
                <Divider type='vertical' className='!ml-0 !mr-4'></Divider>
                <Button
                  variant='primary'
                  onClick={handleGoBackToEdit}
                >
                  <ArrowNarrowLeft className='w-4 h-4 mr-1' />
                  {t('workflow.common.goBackToEdit')}
                </Button>
              </div>
            )
          }
          {/* 恢复 */}
          {
            restoring && (
              <div className='flex items-center gap-2'>
                <Button variant={'secondary-accent'} onClick={handleShowFeatures}>
                  <Feature className='w-4 h-4' />
                  {t('workflow.common.features')}
                </Button>
                <Button
                  variant={'secondary-accent'}
                  onClick={handleCancelRestore}
                >
                  {t('common.operation.cancel')}
                </Button>
                <Button
                  onClick={handleRestore}
                  variant='primary'
                >
                  {t('workflow.common.restore')}
                </Button>
              </div>
            )
          }
        </div>

      </div>
      {/* 应用发布弹窗 */}
      {showAppPublishModal && (
        <AppPublishModal
          app={appDetail!}
          appPublishInfo={appPublishInfo}
          isPublished={isPublished}
          onSave={handlePublish}
          onCancel={() => setShowAppPublishModal(false)}
        />
      )}
      {/* 参数配置弹窗 */}
      {showConfigModal && (
        <WorkflowTool
          parameters={parameters}
          appId={appDetail?.id}
          onHide={() => setShowConfigModal(false)}
          onSave={updateWorkflowToolProvider}
        />
      )}
    </>
  )
}

export default memo(Header)
