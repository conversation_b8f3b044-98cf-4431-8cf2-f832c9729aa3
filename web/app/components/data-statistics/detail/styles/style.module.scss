.dataStatistics-detail {
  height: 100%;
  background: #fff;
  .detail-nav {
    height: 62px;
    line-height: 62px;
    padding-left: 24px;
    box-shadow: 0px 1px 0px 0px #edeff2;
    color: #565772;
    .detail-icon {
      margin-right: 12px;
      cursor: pointer;
      :global {
        .anticon-left {
          color: #5c6273;
        }
      }
    }
  }
  .detail-content {
    padding: 24px;
    height: calc(100% - 62px);
    .detail-search {
      margin-bottom: 20px;
      display: flex;
      :global {
        .ant-select {
          width: 400px;
          margin-right: 12px;
        }
        .ant-picker {
          width: 300px;
        }
      }
    }
    :global {
      .ant-tabs-nav-list {
        width: 100%;
      }
      .ant-tabs-tab-active {
        background: #fff !important;
      }
      .ant-tabs-ink-bar {
        display: none !important;
      }
      .ant-tabs-tab {
        flex: 1;
        background: #f8f9fa;
        margin: 0 !important;
        border-right: 1px solid #edeff2;
        padding: 12px 16px !important;
        height: 70px;
        .ant-tabs-tab-btn {
          color: #5c6273;
          font-size: 12px;
        }
        .ant-statistic-content {
          color: #181818;
          font-size: 14px;
          font-weight: 500;
          margin-left: 0 !important;
          margin-top: 4px;
        }
      }
    }
    .detail-charts {
      height: calc(100% - 36px);
      position: relative;
      .chart-header {
        display: flex;
        justify-content: space-between;
      }
      .chart-content {
        height: calc(100% - 85px);
        position: relative;
        .detail-chart {
          width: 100%;
          margin-top: 16px;
          height: 90%;
        }
        .chart-btn {
          position: absolute;
          right: 0;
          z-index: 999;
          :global {
            .ant-radio-button-wrapper {
              height: 28px;
              padding: 4px 6px;
            }
          }
        }
      }
    }
  }
}
