import React, { useCallback, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import produce from 'immer'
import FeatureCollapse from '../../feature-collapse'
import { useFeatures, useFeaturesStore } from '@/app/components/base/features/hooks'
import type { OnFeaturesChange } from '@/app/components/base/features/types'
import { FeatureEnum } from '@/app/components/base/features/types'
import LanguageParamConfig from '@/app/components/base/features/new-feature-panel/new-voice-input/param-config-content'
import type { NewVoiceConfigType } from '@/models/app'

type Props = {
  disabled?: boolean
  onChange?: OnFeaturesChange
  isAutoPlay?: boolean
  applyType?: string
  voicesConfigData?: NewVoiceConfigType[]
}

const NewVoiceInput = ({
  disabled,
  onChange,
  isAutoPlay,
  applyType,
  voicesConfigData,
}: Props) => {
  const { t } = useTranslation()
  const voiceInput = useFeatures(s => s.features.text2speech?.voice_input)
  const inWorkflow = useFeatures(s => s.inWorkflow)
  const featuresStore = useFeaturesStore()

  // 语音输入参数配置
  const [voiceInputLanguage, setVoiceInputLanguage] = useState(voiceInput?.language)
  const [voiceInputTimbre, setVoiceInputTimbre] = useState(voiceInput?.timbre)
  const [voiceInputAutoPlay, setVoiceInputAutoPlay] = useState(voiceInput?.auto_play)
  // 区分应用标识 applyType ："selfPlanning"自主规划；workflow 工作流

  // 开启/关闭对话语音
  const handleChange = useCallback((type: FeatureEnum, enabled: boolean) => {
    const {
      features,
      setFeatures,
    } = featuresStore!.getState()

    const newFeatures = produce(features, (draft) => {
      // draft[type] = {
      //   ...draft[type],
      //   enabled,
      // }
      if (type === FeatureEnum.text2speech && draft[type]?.voice_input) {
        draft[type].voice_input = {
          ...draft[type].voice_input,
          enabled,
        }
      }
      else {
        draft[type] = {
          ...draft[type],
          enabled,
        }
      }
    })
    // console.log(newFeatures, '======newFeatures开关')
    setFeatures(newFeatures)
    if (onChange)
      onChange(newFeatures)
  }, [featuresStore, onChange])
  // 数据保存回调
  const handleSave = useCallback((ignoreVariablesCheck?: boolean) => {
    const newVoiceInput = produce(voiceInput, (draft) => {
      if (draft) {
        draft.language = voiceInputLanguage
        draft.timbre = voiceInputTimbre
        draft.auto_play = voiceInputAutoPlay
      }
    })
    // onSave(newOpening)
    const {
      features,
      setFeatures,
    } = featuresStore!.getState()
    const newFeatures = produce(features, (draft) => {
      draft.text2speech!.voice_input = newVoiceInput
    })
    // console.log(newFeatures, '======newFeatures设置')
    setFeatures(newFeatures)
    if (onChange)
      onChange(newFeatures)
  }, [voiceInput, featuresStore, onChange, voiceInputLanguage, voiceInputTimbre, voiceInputAutoPlay])
  const onLanguageChange = (value: string) => {
    setVoiceInputLanguage(value)
  }
  const onTimbreChange = (value: string) => {
    setVoiceInputTimbre(value)
  }
  const onAutoPlayChange = (value: boolean) => {
    setVoiceInputAutoPlay(value)
  }

  useEffect(() => {
    handleSave()
  }, [voiceInputLanguage, voiceInputTimbre, voiceInputAutoPlay])

  if (!voiceInput)
    return
  return (
    <FeatureCollapse
      inWorkflow={inWorkflow}
      title={t('appDebug.feature.newVoice.voiceInput.title')}
      tooltip={applyType === 'selfPlanning' && t('appDebug.feature.voiceInput.description')}
      rightTooltipDesc={ applyType === 'workflow' && t('appDebug.feature.voiceInput.rightTooltipDesc')}
      value={!!voiceInput?.enabled}
      onChange={state => handleChange(FeatureEnum.text2speech, state)}
      disabled={disabled}
    >
      {!!voiceInput?.enabled && (
        <>
          <div className='flex flex-col gap-[10px]'>
            <div>
              {/* <div className={labelStyle}>{t('appDebug.openingStatement.label1')}</div> */}
              <LanguageParamConfig
                languageValue = { voiceInput?.language || ''}
                timbreValue = { voiceInput?.timbre || '' }
                autoPlayValue = { voiceInput?.auto_play || false }
                onLanguageChange = { onLanguageChange }
                onTimbreChange = { onTimbreChange }
                isAutoPlay = { isAutoPlay }
                onAutoPlayChange = { onAutoPlayChange }
                voicesConfigData={voicesConfigData}
              />
            </div>
          </div>
        </>
      )}
    </FeatureCollapse>
  )
}

export default NewVoiceInput
