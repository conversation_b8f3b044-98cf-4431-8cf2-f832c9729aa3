import type { CommonNodeType, Memory, ModelConfig, PromptItem, ValueSelector, Variable, VisionSetting } from '@/app/components/workflow/types'
import type { SpecialDatasetConfigs } from '@/models/debug'
import type { ToolItem } from '@/types/tools'

// 智能体节点创建模式
export enum AgentCreateMode {
  Empty = 'empty',
  NoEmpty = 'noEmpty',
}
export enum SiteType {
  Web = 'web',
  Api = 'api',
  Market = 'market',
  Embed = 'embed',
}

export type AgentNodeType = CommonNodeType & {
  // 节点模式
  create_mode: AgentCreateMode
  // 智能体id——非空态
  agent_id?: string
  // 智能体描述
  agent_desc?: string
  // 节点对应模型——空态下
  model: ModelConfig
  // 节点提示词——空态下
  prompt_template: PromptItem[] | PromptItem
  // 节点提示词配置——空态下
  prompt_config?: {
    // 节点提示词变量
    jinja2_variables?: Variable[]
  }
  // 记忆配置
  memory?: Memory
  // 上下文配置
  context: {
    enabled: boolean
    variable_selector: ValueSelector
  }
  // 视觉配置
  vision: {
    enabled: boolean
    configs?: VisionSetting
  }
  // 输入变量
  query: string
  // 场景说明
  scene_desc: string
  // 工具列表
  tools: Array<ToolItem>
  // 知识库配置
  dataset_configs: SpecialDatasetConfigs
  // 高级设置
  advance_setting: AdvanceSetting
  // 站点id
  site_app_id: string
  // 站点类型
  site_type: SiteType
  // 工具缺失
  tool_lack?: boolean
}
// 智能体节点默认值
export type AgentDefaultValue = {
  // 创建模式
  create_mode: AgentCreateMode
  // 智能体ID
  agent_id?: string
  // 标题
  title?: string
  // 图标
  icon?: string
  // 智能体描述
  agent_desc?: string
  // 站点id
  site_app_id?: string
  // 站点类型
  site_type?: SiteType
}
// 高级设置
export type AdvanceSetting = {
  enabled: boolean
  iteration_num: number
}
