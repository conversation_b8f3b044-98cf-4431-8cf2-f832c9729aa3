.header-title {
  @apply flex items-center text-gray-G1 text-S7 leading-H6.25 font-semibold first:pt-9 first:pb-6;
  padding: 16px 32px 16px;
}
.day-count-down {
  @apply ml-3 flex items-center gap-1 text-S3 leading-H1 text-gray-G2 font-normal;
}
.sub-title {
  @apply mt-2 mb-3 text-gray-G1 text-S4 leading-H3 font-semibold;
}
.card-wrap {
  @apply px-5 py-4 pb-8 mb-4;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.80);
  backdrop-filter: blur(10px);
}
.card-title {
  @apply text-gray-G1 text-S3 leading-H3 font-semibold;
}
.dataStatistics-consume-detail,
.personal-space-content {
  padding: 0 32px;
}

/* 数据概要总览 */
.summary-header {
  @apply flex justify-between items-center h-[36px] mb-8;
}
.summary-card {
  display: flex;
  justify-content: space-around;
}
.data-card {
  display: flex;
  align-items: center;
}
.data-icon {
  width: 52px;
  height: 52px;
  background: rgba(166, 202, 255, 0.2);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.data-content {
  margin-left: 8px;
}
.data-title {
  @apply text-gray-G2 text-S3 leading-H3;
}
.data-value {
  @apply text-gray-G1 text-S6 leading-H6.25 font-semibold;
}

.table-flex {
  @apply flex flex-col;
}