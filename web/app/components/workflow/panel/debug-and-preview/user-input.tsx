import {
  memo, useEffect, useState,
} from 'react'
import { useNodes } from 'reactflow'
import FormItem from '../../nodes/_base/components/before-run-form/form-item'
import { BlockEnum } from '../../types'
import {
  useStore,
  useWorkflowStore,
} from '../../store'
import type { StartNodeType } from '../../nodes/start/types'
// 公共组件
import cn from '@/utils/classnames'

const UserInput = () => {
  const workflowStore = useWorkflowStore()
  const inputs = useStore(s => s.inputs)
  const nodes = useNodes<StartNodeType>()
  const startNode = nodes.find(node => node.data.type === BlockEnum.Start)
  const variables = startNode?.data.variables || []

  // 变量变更
  const handleValueChange = (variable: string, v: string) => {
    const {
      inputs,
      setInputs,
    } = workflowStore.getState()
    setInputs({
      ...inputs,
      [variable]: v,
    })
  }
  const [IsLong, setIsLong] = useState(false)

  useEffect(() => {
    if (variables.length > 0) {
      variables?.forEach((item) => {
        if (item.type === 'longTxt')
          setIsLong(true)
      })
    }
  }, [])
  if (!variables.length)
    return null

  return (
    <div className={cn('bg-white rounded border border-gray-G5 shadow-xs px-4 pt-3 pb-4')}>
      {variables.map((variable, index) => (
        (!IsLong || (IsLong && variable.type === 'longTxt'))
          ? (
            <div
              key={variable.variable}
              className='mb-4 last-of-type:mb-0'
            >
              <FormItem
                autoFocus={index === 0}
                payload={variable}
                value={inputs[variable.variable]}
                onChange={v => handleValueChange(variable.variable, v)}
              />
            </div>
          )
          : null
      ))}
    </div>
  )
}

export default memo(UserInput)
