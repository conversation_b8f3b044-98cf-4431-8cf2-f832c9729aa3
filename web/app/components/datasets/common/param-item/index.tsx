'use client'
import { useState } from 'react'
import type { FC } from 'react'
import { InputNumber, Switch } from 'antd'
import Tooltip from '@/app/components/base/tooltip'
import Slider from '@/app/components/base/slider'
import cn from '@/utils/classnames'

type Props = {
  className?: string
  labelClassName?: string
  valueClassName?: string
  id: string
  name: string
  noTooltip?: boolean
  tip?: string
  value: number
  enable: boolean
  step?: number
  precision?: number
  min?: number
  max: number
  onChange: (key: string, value: number) => void
  onSwitchChange?: (key: string, enable: boolean) => void
  hasSwitch?: boolean
  hasInputNumber?: boolean
}

const ParamItem: FC<Props> = ({ labelClassName, valueClassName, hasInputNumber = true, className, id, name, noTooltip, tip, step = 0.1, precision, min = 0, max, value, enable, onChange, hasSwitch, onSwitchChange }) => {
  const [enableValue, setEnableValue] = useState<boolean>(enable)
  return (
    <div className={className}>
      <div className={cn(labelClassName, 'flex items-center h-6 shrink-0')}>
        {hasSwitch && (
          <Switch
            className='mr-2'
            size='small'
            value={enableValue}
            defaultValue={enable}
            onChange={async (val) => {
              setEnableValue(val)
              onSwitchChange?.(id, val)
            }}
          />
        )}
        <div className="mr-1 title-14-24 !font-normal truncate" title={name}>{name}</div>
        {!noTooltip && (
          <Tooltip
            triggerClassName='w-4 h-4 shrink-0'
            popupContent={<div className="w-[200px]">{tip}</div>}
          />
        )}
      </div>
      { enable && (
        <div className={cn('mt-1 flex items-center w-full gap-3', valueClassName)}>
          <Slider
            className='w-full'
            disabled={!enable}
            value={max < 5 ? value * 100 : value}
            min={min < 1 ? min * 100 : min}
            max={max < 5 ? max * 100 : max}
            onChange={value => onChange(id, value / (max < 5 ? 100 : 1))}
          />
          {hasInputNumber && <InputNumber
            className='w-[82px] shrink-0'
            controls={false}
            value={value}
            disabled={!enable}
            min={min}
            max={max}
            step={step}
            precision={precision}
            defaultValue={3}
            onChange={(value) => {
              if (value === null || value > max || value < min)
                return
              onChange(id, value)
            }}/>
          }
        </div>
      )}
    </div>
  )
}
export default ParamItem
