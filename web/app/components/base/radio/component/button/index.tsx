import type { ReactElement } from 'react'
import { useContext } from 'use-context-selector'
import RadioGroupContext from '../../context'
import s from '../../style.module.css'
import cn from '@/utils/classnames'

export type IRadioButtonProps = {
  className?: string
  children?: ReactElement | ReactElement[] | string
  checked?: boolean
  value?: string | number
  disabled?: boolean
  onChange?: (e?: IRadioButtonProps['value']) => void
}

export default function RadioButton({
  className = '',
  children = '',
  checked,
  value,
  disabled,
  onChange,
}: IRadioButtonProps): JSX.Element {
  const groupContext = useContext(RadioGroupContext)
  // 是否选中
  const isChecked = groupContext ? groupContext.value === value : checked

  // 变更当前radio状态
  const handleChange = (e: IRadioButtonProps['value']) => {
    if (disabled)
      return

    onChange?.(e)
    groupContext?.onChange(e)
  }

  return (
    <div className={cn(
      s[`radio-button-${groupContext?.type}`],
      disabled && s.disabled,
      isChecked && s.active,
      className)}
    onClick={() => handleChange(value)}
    >
      {children}
    </div>
  )
}
