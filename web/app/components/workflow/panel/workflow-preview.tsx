import {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import {
  RiClipboardLine,
  RiCloseLine,
} from '@remixicon/react'
import { useTranslation } from 'react-i18next'
import copy from 'copy-to-clipboard'
import { Tabs } from 'antd'
import { useBoolean } from 'ahooks'
import { useContext } from 'use-context-selector'
import ResultText from '../run/result-text'
import ResultPanel from '../run/result-panel'
import TracingPanel from '../run/tracing-panel'
import {
  useWorkflowInteractions,
} from '../hooks'
import { useStore } from '../store'
import {
  WorkflowRunningStatus,
} from '../types'
import { SimpleBtn } from '../../app/text-generate/item'
import Empty from '../../base/empty'
import Toast from '../../base/toast'
import IterationResultPanel from '../run/iteration-result-panel'
import TextButton from '../../base/button/text-button'
import Scrollbar from '../../base/scrollbar'
import { useTracing } from '../run/hooks/use-tracing'
import s from './styles/run.module.css'
import BatchInputsPanel from './batch-inputs-panel'
import InputsPanel from './inputs-panel'
import cn from '@/utils/classnames'
import Loading from '@/app/components/base/loading'
import { ToastContext } from '@/app/components/base/toast'
import type { NodeTracing } from '@/types/workflow'

// 运行模式
enum RunMode {
  single = 'single',
  batch = 'batch',
}
// 面板tab
enum PanelTab {
  input = 'INPUT',
  detail = 'DETAIL',
  tracing = 'TRACING',
  result = 'RESULT',
}

// 工具工作流预览页
const WorkflowPreview = () => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  const { handleCancelDebugAndPreviewPanel } = useWorkflowInteractions()
  // 工作流运行数据
  const workflowRunningData = useStore(s => s.workflowRunningData)
  const appId = useStore(s => s.appId)
  // 是否显示输入面板
  const showInputsPanel = useStore(s => s.showInputsPanel)
  // 工具是否发布
  const toolPublished = useStore(s => s.toolPublished)
  // 是否显示调试和预览面板
  const showDebugAndPreviewPanel = useStore(s => s.showDebugAndPreviewPanel)

  // 当前tab
  const [currentTab, setCurrentTab] = useState<PanelTab>(showInputsPanel ? PanelTab.input : PanelTab.tracing)
  // 当前运行模式
  const [currentRunMode, setCurrentRunMode] = useState<RunMode>(RunMode.single)
  // 运行流程结果
  const [iterationRunResult, setIterationRunResult] = useState<NodeTracing[][]>([])
  // 是否显示运行流程详情
  const [isShowIterationDetail, {
    setTrue: doShowIterationDetail,
    setFalse: doHideIterationDetail,
  }] = useBoolean(false)
  const {
    list,
    setList,
    getTracingList,
  } = useTracing()
  // 停止流式对话变量在这里进行管理，传参给InputsPanel里面流式接口调用
  const ssePostAbortControllerRef = useRef<AbortController | null>(null)

  // 单次执行tab列表
  const singleTabList = useMemo(() => {
    return [
      ...(showInputsPanel
        ? [{
          label: t('runLog.input'),
          key: PanelTab.input,
        }]
        : []),
      {
        label: t('runLog.result'),
        key: PanelTab.result,
        disabled: !workflowRunningData,
      },
      {
        label: t('runLog.detail'),
        key: PanelTab.detail,
        disabled: !workflowRunningData,
      },
      {
        label: t('runLog.tracing'),
        key: PanelTab.tracing,
        disabled: !workflowRunningData,
      },
    ]
  }, [showInputsPanel, t, workflowRunningData])
  // 批量执行tab列表
  const batchTabList = useMemo(() => {
    return [
      {
        label: t('runLog.input'),
        key: PanelTab.input,
      },
      {
        label: t('runLog.result'),
        key: PanelTab.result,
      },
    ]
  }, [t])

  // 切换当前页面
  const switchTab = async (tab: string) => {
    setCurrentTab(tab as PanelTab)
  }
  // 显示运行流程详情
  const handleShowIterationDetail = useCallback((detail: NodeTracing[][]) => {
    setIterationRunResult(detail)
    doShowIterationDetail()
  }, [doShowIterationDetail])

  // 变更运行模式
  const handleChangeRunMode = (mode: RunMode) => {
    if (mode === currentRunMode)
      return
    setCurrentRunMode(mode)
    // 如果是单次运行
    if (mode === RunMode.single) {
      // 如果存在运行数据
      if (workflowRunningData) {
        // 如果运行结束，并且没有数据
        if ((workflowRunningData?.result.status === WorkflowRunningStatus.Succeeded || workflowRunningData?.result.status === WorkflowRunningStatus.Failed) && !workflowRunningData.resultText)
          setCurrentTab(PanelTab.detail)
        else
          setCurrentTab(PanelTab.result)
      }
      else {
        if (showInputsPanel)
          setCurrentTab(PanelTab.input)
        else
          setCurrentTab(PanelTab.tracing)
      }
    }
    // 如果是批量运行
    if (mode === RunMode.batch)
      setCurrentTab(PanelTab.input)
  }

  // 初始化tab
  useEffect(() => {
    if (showDebugAndPreviewPanel && showInputsPanel)
      setCurrentTab(PanelTab.input)
  }, [showDebugAndPreviewPanel, showInputsPanel])
  // 监听工作流数据变化
  useEffect(() => {
    if (currentRunMode === RunMode.single) {
      if (
        (workflowRunningData?.result.status === WorkflowRunningStatus.Succeeded
         || workflowRunningData?.result.status === WorkflowRunningStatus.Failed)
        && !workflowRunningData.resultText
      )
        setCurrentTab(PanelTab.detail)
    }
  }, [workflowRunningData, currentRunMode])
  // 监听工作流运行状态 stop状态的话需要主动中止聊天流式接口
  useEffect(() => {
    // 重新运行前清空记录list
    if (workflowRunningData?.result.status === WorkflowRunningStatus.Running)
      setList([])

    if (workflowRunningData?.result.status === WorkflowRunningStatus.Stopped)
      ssePostAbortControllerRef.current?.abort()
  }, [workflowRunningData, workflowRunningData?.result.status, list])

  // 工作流暂停后查询记录并覆盖追踪页
  useEffect(() => {
    if (workflowRunningData?.result.status === WorkflowRunningStatus.Stopped) {
      if (workflowRunningData?.workflow_run_id)
        getTracingList(appId, workflowRunningData.workflow_run_id)
    }
  }, [workflowRunningData, workflowRunningData?.result.status])

  return (
    <div
      className={`
        flex flex-col w-[420px] h-full rounded-l border-[0.5px] border-gray-200 bg-white
      `}
      style={{
        boxShadow: '0px 4px 8px 0px rgba(152, 170, 187, 0.12)',
      }}
    >
      {/* 抽屉头部 */}
      <div className='flex items-center justify-between px-6 pb-3 pt-5'>
        {/* 标题 */}
        <div className='text-gray-G1 text-S4 leading-H4 font-semibold'>{t('runLog.action.run')}</div>
        {/* 隐藏按钮 */}
        <TextButton variant='text' onClick={() => handleCancelDebugAndPreviewPanel()}>
          <RiCloseLine className='w-4 h-4' />
        </TextButton>
      </div>
      {/* 抽屉内容 */}
      <div className={s['workflow-preview-content']}>
        {isShowIterationDetail
          ? (
            <IterationResultPanel
              list={iterationRunResult}
              onHide={doHideIterationDetail}
              onBack={doHideIterationDetail}
            />
          )
          : (
            <>
              {/* 当工具是已发布的情况下，可以切换运行模式tab */}
              { toolPublished && <div className='flex items-center'>
                <div
                  onClick={() => handleChangeRunMode(RunMode.single)}
                  className={currentRunMode === RunMode.single ? s['run-mode-active-tab'] : s['run-mode-tab']}
                >
                  {t('runLog.runMode.single')}
                </div>
                <div
                  onClick={() => handleChangeRunMode(RunMode.batch)}
                  className={currentRunMode === RunMode.batch ? s['run-mode-active-tab'] : s['run-mode-tab']}
                >
                  {t('runLog.runMode.batch')}
                </div>
              </div> }
              {/* 导航栏 */}
              <Tabs
                className={s['panel-tab']}
                activeKey={currentTab}
                items={currentRunMode === RunMode.single ? singleTabList : batchTabList}
                onChange={switchTab}
              ></Tabs>
              <div
                className={cn(
                  'bg-components-panel-bg grow overflow-hidden',
                  (currentTab === PanelTab.result || currentTab === PanelTab.tracing) && '!bg-background-section-burn')
                }
              >
                {/* 单一运行模式下, 导航内容部分 */}
                <Scrollbar className={cn((currentRunMode === RunMode.batch) && '!hidden')}>
                  {/* 输入tab页 */}
                  {currentTab === PanelTab.input && (
                    <InputsPanel
                      onRun={() => switchTab(PanelTab.result)}
                      ssePostAbortControllerRef={ssePostAbortControllerRef}
                    />
                  )}
                  {/* 结果tab页 */}
                  {currentTab === 'RESULT' && (
                    <>
                      <ResultText
                        isRunning={workflowRunningData?.result?.status === WorkflowRunningStatus.Running || !workflowRunningData?.result}
                        outputs={workflowRunningData?.resultText}
                        allFiles={workflowRunningData?.result?.files as any}
                        error={workflowRunningData?.result?.error}
                        onClick={() => switchTab(PanelTab.detail)}
                      />
                      {/* 运行结果成功，并且有输出文本的状态下 */}
                      {(workflowRunningData?.result.status === WorkflowRunningStatus.Succeeded && workflowRunningData?.resultText && typeof workflowRunningData?.resultText === 'string') && (
                        <SimpleBtn
                          className={cn('ml-6 mb-4 inline-flex space-x-1')}
                          onClick={() => {
                            const content = workflowRunningData?.resultText
                            if (typeof content === 'string')
                              copy(content)
                            else
                              copy(JSON.stringify(content))
                            Toast.notify({ type: 'success', message: t('common.actionMsg.copySuccessfully') })
                          }}>
                          <RiClipboardLine className='w-3.5 h-3.5' />
                          <div>{t('common.operation.copy')}</div>
                        </SimpleBtn>
                      )}
                    </>
                  )}
                  {/* 详情tab页 */}
                  {currentTab === 'DETAIL' && (
                    <ResultPanel
                      inputs={workflowRunningData?.result?.inputs}
                      outputs={workflowRunningData?.result?.outputs}
                      status={workflowRunningData?.result?.status || ''}
                      error={workflowRunningData?.result?.error}
                      elapsed_time={workflowRunningData?.result?.elapsed_time}
                      total_tokens={workflowRunningData?.result?.total_tokens}
                      created_at={workflowRunningData?.result?.created_at}
                      created_by={(workflowRunningData?.result?.created_by as any)?.name}
                      steps={workflowRunningData?.result?.total_steps}
                    />
                  )}
                  {currentTab === 'DETAIL' && !workflowRunningData?.result && (
                    <div className='flex h-full items-center justify-center bg-components-panel-bg'>
                      <Loading />
                    </div>
                  )}
                  {/* 追踪tab页 */}
                  {currentTab === 'TRACING' && (
                    <TracingPanel
                      className='bg-background-section-burn'
                      list={list.length ? list : (workflowRunningData?.tracing || [])}
                      onShowIterationDetail={handleShowIterationDetail}
                    />
                  )}
                  {currentTab === 'TRACING' && !workflowRunningData?.tracing?.length && workflowRunningData?.result.status !== WorkflowRunningStatus.Stopped && (
                    <div className='flex h-full items-center justify-center !bg-background-section-burn'>
                      <Loading />
                    </div>
                  )}
                  {currentTab === 'TRACING' && workflowRunningData?.tracing?.length === 0 && workflowRunningData?.result.status === WorkflowRunningStatus.Stopped && (
                    <div className='flex h-full items-center justify-center !bg-background-section-burn'>
                      <Empty text={t('common.component.empty.text')}/>
                    </div>
                  )}
                </Scrollbar>
                {/* 批量运行模式下，导航内容部分 */}
                <div className={cn((currentRunMode === RunMode.single) && '!hidden', 'h-full pt-5')}>
                  {/* 输入tab页 */}
                  <BatchInputsPanel tab={currentTab as any} onSend={() => switchTab(PanelTab.result)}></BatchInputsPanel>
                </div>
              </div>

            </>
          )}

      </div>
    </div>
  )
}

export default memo(WorkflowPreview)
