import {
  forwardRef,
  memo,
  useCallback,
  useImperative<PERSON>andle,
  useMemo,
  useState,
} from 'react'
import { useNodes } from 'reactflow'
import { BlockEnum } from '../../types'
import { useStore, useWorkflowStore } from '../../store'
import type { StartNodeType } from '../../nodes/start/types'
import UserInput from './user-input'
import ConversationVariableModal from './conversation-variable-modal'
import { useChat } from './hooks'
import type { ChatWrapperRefType } from './index'
import Chat from '@/app/components/base/chat/chat'
import type { ChatItem, OnSend } from '@/app/components/base/chat/types'
import { useFeatures } from '@/app/components/base/features/hooks'
import { fetchSuggestedQuestions, stopChatMessageResponding } from '@/service/debug'
import { useStore as useAppStore } from '@/app/components/app/store'
import { getLastAnswer } from '@/app/components/base/chat/utils'
import { FeatureEnum } from '@/app/components/base/features/types'
import cn from '@/utils/classnames'
import { getCropperFontCss } from '@/app/components/app/app-bg-cropper/utils'

type ChatWrapperProps = {
  showConversationVariableModal: boolean
  onConversationModalHide: () => void
  showInputsFieldsPanel: boolean
  onHide: () => void
}

const ChatWrapper = forwardRef<ChatWrapperRefType, ChatWrapperProps>(({
  showConversationVariableModal,
  onConversationModalHide,
  showInputsFieldsPanel,
  onHide,
}, ref) => {
  const nodes = useNodes<StartNodeType>()
  const startNode = nodes.find(node => node.data.type === BlockEnum.Start)
  const startVariables = startNode?.data.variables
  const appDetail = useAppStore(s => s.appDetail)
  const [appPrevChatList] = useState([])
  const workflowStore = useWorkflowStore()
  const inputs = useStore(s => s.inputs)
  const features = useFeatures(s => s.features)
  const bgConfig = useFeatures(s => s.features[FeatureEnum.chatBg])
  const config = useMemo(() => {
    return {
      opening_statement: features.opening?.enabled ? (features.opening?.opening_statement || '') : '',
      suggested_questions: features.opening?.enabled ? (features.opening?.suggested_questions || []) : [],
      suggested_questions_after_answer: features.suggested,
      text_to_speech: features.text2speech,
      speech_to_text: features.speech2text,
      retriever_resource: features.citation,
      sensitive_word_avoidance: features.moderation,
      file_upload: features.file,
    }
  }, [features.opening, features.suggested, features.text2speech, features.speech2text, features.citation, features.moderation, features.file])
  const setShowFeaturesPanel = useStore(s => s.setShowFeaturesPanel)

  const {
    conversationId,
    chatList,
    chatListRef,
    handleUpdateChatList,
    handleStop,
    isResponding,
    suggestedQuestions,
    handleSend,
    handleRestart,
  } = useChat(
    config,
    {
      inputs,
      inputsForm: (startVariables || []) as any,
    },
    appPrevChatList,
    taskId => stopChatMessageResponding(appDetail!.id, taskId),
  )

  // 发送消息
  const doSend = useCallback<OnSend>(
    (query, files, last_answer) => {
      handleSend(
        {
          query,
          files,
          inputs: workflowStore.getState().inputs,
          conversation_id: conversationId,
          parent_message_id: last_answer?.id || getLastAnswer(chatListRef.current)?.id || null,
        },
        {
          onGetSuggestedQuestions: (messageId, getAbortController) =>
            fetchSuggestedQuestions(appDetail!.id, messageId, getAbortController),
        },
      )
    },
    [chatListRef, conversationId, handleSend, workflowStore, appDetail],
  )

  // 重新生成回答
  const doRegenerate = useCallback(
    (chatItem: ChatItem) => {
      const index = chatList.findIndex(item => item.id === chatItem.id)
      if (index === -1)
        return

      const prevMessages = chatList.slice(0, index)
      const question = prevMessages.pop()
      const lastAnswer = getLastAnswer(prevMessages)

      if (!question)
        return

      handleUpdateChatList(prevMessages)
      doSend(question.content, question.message_files, lastAnswer)
    },
    [chatList, handleUpdateChatList, doSend],
  )

  useImperativeHandle(
    ref,
    () => {
      return {
        handleRestart,
      }
    },
    [handleRestart],
  )

  // 工作流点击预览弹出的聊天对话
  return (
    <>
      <Chat
        answerIcon={appDetail?.icon_url || appDetail?.site?.icon_url || ''}
        config={{
          ...config,
          supportCitationHitInfo: true,
        } as any}
        appData={appDetail as any}
        chatList={chatList}
        isResponding={isResponding}
        chatContainerClassName='px-6'
        chatContainerInnerClassName={cn('mx-auto w-full max-w-[800px]', getCropperFontCss(bgConfig))}
        chatFooterClassName='bottom-0'
        chatFooterInnerClassName={'mx-auto w-full max-w-[800px]'}
        showFileUpload
        showFeatureBar
        onFeatureBarClick={setShowFeaturesPanel}
        onSend={doSend}
        inputs={inputs}
        inputsForm={(startVariables || []) as any}
        onRegenerate={doRegenerate}
        onStopResponding={handleStop}
        chatNode={(
          <>
            {showInputsFieldsPanel && <UserInput />}
          </>
        )}
        suggestedQuestions={suggestedQuestions}
        showPromptLog
        showInputBottomTip
      />
      {showConversationVariableModal && (
        <ConversationVariableModal
          conversationID={conversationId}
          onHide={onConversationModalHide}
        />
      )}
    </>
  )
},
)

ChatWrapper.displayName = 'ChatWrapper'

export default memo(ChatWrapper)
