import ReactEcharts from 'echarts-for-react'
import { EChartsOption } from 'echarts-for-react'
import { Chart, size } from '../types'
import { getPercentage } from '../utils'
import cn from '@/utils/classnames'

type Props = {
  chart: Chart,
  className?: string,
  style?: React.CSSProperties
}
const TotalEvents = ({
  chart,
  className,
  style
}: Props) => {
  const count = chart.externalConfig?.count
  const changeRate = chart.externalConfig?.changeRate
  return (
    <div className={cn('h-8', className)}>
      <ReactEcharts option={chart.option || {}} style={{height: '86px'}}/>
      <div className='flex justify-between items-baseline'>
        <div>
          <span className="text-S9 text-gray-G1">{count}</span>
          <span className="text-S3 text-gray-G3">件</span>
        </div>
        <div className={changeRate > 0 ? 'text-[#F56C6C]' : 'text-[#78BF13]'}>{getPercentage(changeRate)}</div>
      </div>
    </div>
  )
}

export default TotalEvents