const translation = {
  modelProvider: {
    modalList: 'Model List',
    addLocalModal: 'Add Local Model',
    addCloudModal: 'Add Public Cloud Model',
    deleteModalTitle: 'Confirm to delete the model?',
    deleteModalTip: 'Once deleted, it cannot be recovered. Models that have been introduced, including LLM, TEXT EMBEDDING, RERANK, etc., will be lost.',
    configType: {
      'predefined-model': 'Predefined',
      'customizable-model': 'Customizable',
    },
    form: {
      providerSelect: 'Select Public Cloud',
      configTypeSelect: 'Select Configuration Method',
    },
    systemReasoningModel: {
      tip: 'Set the default inference model used to create the app, as well as features such as conversation name generation, next question suggestions, and so on.',
      title: 'System inference model',
    },
    embeddingModel: {
      title: 'Embedding model',
      tip: 'Set the default model for the embedding processing of knowledge base documents, and use the embedding model for vectorization processing in both the retrieval and imported knowledge bases, which will cause the vector dimensions between the imported knowledge base and the problem to be inconsistent after switching, resulting in retrieval failure. To avoid retrieval failures, do not switch the model at will.',
      required: 'Please select the Embedding model',
    },
    speechToTextModel: {
      tip: 'Set the default usage model for speech-to-text input in conversations.',
      title: 'Speech-to-text model',
    },
    ttsModel: {
      title: 'Text-to-speech models',
      tip: 'Sets the default usage model for conversational Chinese text-to-speech output.',
    },
    rerankModel: {
      tip: 'The results of the initial search or recommendation are re-sorted to optimize the order of the results that are finally presented to the user',
      title: 'Rerank model',
    },
    encrypted: {
      back: 'technology for encryption and storage.',
      front: 'Your key will be used',
    },
    paramMode: {
      normal: 'Regular',
      precise: 'Precision',
      custom: 'Custom',
      creative: 'Creative',
    },
    modelType: {
      localModel: 'Local model',
      cloudModel: 'Public cloud model',
      officalModel: 'Built-in models',
      thirdPartyModel: 'Third-party models',
    },
    action: {
      selectModel: 'Please select your model',
    },
    resultRearrangementTip: 'After enabling reflow, the model analyzes user problems and adjusts the slice order so that the most relevant content is ranked first',
    config: 'disposition',
    modelConfig: 'Model configuration',
    modelsNum: '{{num}} models',
    showModels: 'Display the model',
    loadPresets: 'Load the preset',
    model: 'model',
    localModelTip: 'Supports local model services deployed by LocaIAI and Ollama',
    resultRearrangement: 'Results are rearranged',
    addModel: 'Add a model',
    featureSupported: 'Support for {{feature}} features',
    modelHasBeenDeprecated: 'The model has been abandoned',
    modelSelect: 'Model selection',
    configureModel: 'Set up the model',
    parameters: 'parameter',
    deprecated: 'Deprecated',
    info: {
      paramMode: 'Parameter mode',
      paramConfig: 'Advanced parameter configuration',
      customParam: 'Custom parameters',
      defaultParam: 'Default parameters',
    },
  },
  plansCommon: {
    memberAfter: 'members',
    unlimited: 'Unlimited',
    member: 'member',
  },
  model: {
    params: {
      stop_sequencesPlaceholder: 'Enter the sequence and press the Tab key',
    },
    tone: {
      Precise: 'precise',
      Custom: 'Customize',
      Balanced: 'balance',
      Creative: 'originality',
    },
    addMoreModel: 'Add more models',
  },
  dataSource: {
    notion: {
      pagesAuthorized: 'Authorized page',
      connectedWorkspace: 'A workspace has been bound',
      changeAuthorizedPages: 'Change the authorization page',
      addWorkspace: 'Add a workspace',
      description: 'Use Notion as the data source for your knowledge base.',
      title: 'Notion',
    },
    website: {
      title: 'site',
      configuredCrawlers: 'Configured crawlers',
      description: 'Use a web crawler to import content from a website.',
    },
    add: 'Add a data source',
  },
  apiBasedExtension: {
    modal: {
      name: {
        title: 'name',
        placeholder: 'Please enter a name',
      },
      apiEndpoint: {
        title: 'API Endpoint',
        placeholder: 'Please enter the API endpoint',
      },
      apiKey: {
        lengthError: 'API-key cannot be less than 5 digits',
        title: 'API-key',
        placeholder: 'Please enter API-key',
      },
      title: 'API extensions have been added',
      editTitle: 'Edit the API extension',
    },
    selector: {
      placeholder: 'Please select an API extension',
      title: 'API extensions',
      manage: 'Manage API extensions',
    },
    title: 'API extensions provide a centralized API management that can be used directly in a variety of applications on the Twin platform after adding API configurations in a unified manner.',
    linkUrl: 'https://docs.ai/features/extension/api_based_extension',
    link: 'Learn how to develop your own API extensions.',
    add: 'API extensions have been added',
    type: 'type',
  },
  info: {
    invitationLink: 'Invitation link',
    role: 'role',
    name: 'Username',
    confirmPassword: 'Confirm your password',
    you: '(You)',
    lastActive: 'The time of the last activity',
    currentPassword: 'Original password',
    email: 'mailbox',
    myAccount: 'My Account',
    avatar: 'avatar',
    newPassword: 'New passwords',
    passwordTitle: 'password',
    provider: 'Model source',
    account: 'account',
    failedInvitationEmails: 'Failed to invite the following mailboxes',
    pending: 'Undetermined...',
    workspace: 'Workspaces',
    language: 'language',
  },
  action: {
    setPassword: 'Set a password',
    resetPassword: 'Reset your password',
    invite: 'Add to',
    removeFromTeam: 'Move out of the team',
    inviteTeamMember: 'Add team members',
    logout: 'Sign out',
    editName: 'Edit the name',
    helpCenter: 'documentation',
    sendInvite: 'Send an invitation',
  },
  notify: {
    removeFromTeamTip: 'The team access will be canceled',
    invitationSentTip: 'The invitation has been sent, and the other party can access your team\'s data after logging in to the Agent Platform.',
    inviteSuccful: 'Invite team members successfully',
    inviteTeamMemberTip: 'They can access your team\'s data when they sign in.',
    invitationSent: 'The invitation has been sent',
    passwordTip: 'If you don\'t want to log in with a verification code, you can set a permanent password',
  },
  role: {
    admin: 'administrator',
    ownerTip: 'The current workspace owner',
    adminTip: 'Ability to build applications and manage team setups',
    normalTip: 'You can only use the application, not the application',
    editor: 'edit',
    editorTip: 'Ability to build and edit applications, not ability to manage team settings',
    normal: 'member',
    datasetOperator: 'Knowledge Base Administrator',
    datasetOperatorTip: 'Only the knowledge base can be managed',
    owner: 'owner',
    adminTipPublicCloud: 'Able to create applications, knowledge bases, and tools, and can manage team members.',
    ownerTipPublicCloud: 'Able to create applications, knowledge bases, and tools, can create teams and manage team members.',
    normalTipPublicCloud: 'Able to create applications, knowledge bases, tools',
  },
  settingMenu: {
    apiBasedExtension: 'API extensions',
    accountGroup: 'general',
    plugin: 'Plug-ins',
    billing: 'bill',
    members: 'member',
    dataSource: 'Data source',
    workplaceGroup: 'Workspaces',
    account: 'My Account',
    language: 'language',
    integrations: 'integration',
    provider: 'Model source',
    operationLog: 'Operation Log',
  },
  placeholder: {
    confirmPwdPlaceholder: 'Please enter your password again',
  },
  team: {
    table: {
      addMembers: 'Add member',
      roleChange: 'Role Change',
      inputKeywordPlaceholder: 'Please enter keywords',
      delete: 'Remove',
      userName: 'User Name',
      userID: 'User ID',
      email: 'Email',
      selectMembersPlaceholder: 'Please select a member.',
      createMembersTooltip: 'The team supports adding 2000 people.',
      selectRolePlaceholder: 'Please select a character.',
    },
    disbandInfo: {
      removeWarn: 'Remove warning',
      disbandPrompt: 'Dissolution Notice',
      cancel: 'Cancel',
      disbandWarn: 'Dissolution Warning',
      exitWarn: 'Exit warning',
      confirm: 'Confirm',
      noDisbandButton: 'I understand.',
      disbandPromptDesc: 'The team space includes knowledge base, tools, and application-related content, and does not support dissolving the team. The content of the team space must be deleted before the team can be dissolved.',
      disbandWarnDesc: 'Are you sure you want to dissolve the {{key}} team?',
      exitWarnDesc: 'Are you sure you want to leave the team? After leaving the team, the content you created in the team space, including knowledge bases, tools, application-related content, and data, will not be deleted. If you wish to join the team again later, you will need an invitation from an administrator or the team creator.',
      removeWarnDesc: 'Are you sure you want to remove the team of {{key}}? After removal, you can manually add members, and this member will not have their contributions in the team\'s created knowledge base, applications, or tools deleted.',
    },
    teamNameInfo: {
      membersInfo: 'Member Information',
      membersRole: 'Member role',
    },
    operate: 'Operation',
    createTeam: 'Create a team',
    joinTime: 'Join date',
    view: 'View',
    createTime: 'Creation Time',
    disband: 'Dissolve',
    sonTeam: 'The team that joined',
    timeRole: 'Team Roles',
    hostTeam: 'The team that was created',
    teamName: 'Team Name',
    exit: 'Exit',
    createTeamDesc: 'Creation failed.',
    createTeamTitle: 'The team that was created',
    title: 'Team',
    createTeamTooltip: 'Only supports creating 100 teams.',
    timeCreatePeople: 'Team Creator',
  },
  operationLog: {
    table: {
      header: {
        userName: 'User Nickname',
        time: 'Operating time',
        content: 'Specific details',
        userId: 'User ID',
        index: '#',
        operation: 'Operating actions',
        account: 'User account',
        workspace: 'Team space',
      },
    },
    operation: {
      add: 'Add',
      delete: 'Delete',
      modify: 'Modify',
      memberRemove: 'Member removal',
      copy: 'Copy',
      publish: 'Release',
      roleChange: 'Role Change',
    },
    source: {
      app: 'Application',
      member: 'member',
      dataset: 'Knowledge Base',
      tool: 'Tool',
    },
    contentLabel: '<target><typeLabel>\'s<operationLabel>',
  },
  dateTimeFormat: 'YYYY-MM-DD HH:mm',
  firecrawl: {
    configFirecrawl: '🔥Configure Firecrawl',
    apiKeyPlaceholder: 'Get the API Key from firecrawl.dev',
    getApiKeyLinkText: 'Get your API Key from firecrawl.dev',
  },
}

export default translation
