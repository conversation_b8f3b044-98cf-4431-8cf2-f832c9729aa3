.iconBtn {
  @apply w-4 h-4 cursor-pointer text-gray-G3 hover:text-primary-P1;
}

.desctext {
  color: var(--color-gray-G2);
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 166.667% */
}
.normalTextG1 {
  @apply truncate;
  color: var(--color-gray-G2);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 166.667% */
}
.normalTextG2 {
  color: var(--color-gray-G2);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 166.667% */
}

.variableItemWrap {
  @apply flex flex-col gap-0.5 border border-gray-G5 rounded mb-2 last-of-type:mb-0;
  padding: 8px 12px;
}
.header {
  @apply flex items-center justify-between h-[24px];
}
.variableTitle {
  @apply flex items-center gap-1 grow;
}

.type-item {
  width: calc(33% - 8px);
  flex-shrink: 0;
  flex-grow: inherit;
}