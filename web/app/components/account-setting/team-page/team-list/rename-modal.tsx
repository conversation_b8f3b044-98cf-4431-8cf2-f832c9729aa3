'use client'
import type { FC } from 'react'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Form, Input } from 'antd'
import { debounce } from 'lodash-es'
import { putTeamUpdate } from '@/service/team'

// 公共组件
import Toast from '@/app/components/base/toast'
import Modal from '@/app/components/base/modal'
import Button from '@/app/components/base/button'

type Props = {
  documentId: string
  name: string
  onClose: () => void
  onSaved: () => void
}

const RenameModal: FC<Props> = ({
  documentId,
  name,
  onClose,
  onSaved,
}) => {
  const { t } = useTranslation()
  const [form] = Form.useForm()
  const values = Form.useWatch([], form)
  // 是否可提交
  const [disabled, setDisabled] = useState(!name)
  // 重命名
  const handleSave = async () => {
    console.log('团队名称重命名')
    try {
      const dody = {
        team_id: documentId,
        team_name: form.getFieldValue('name'),
      }
      const res = await putTeamUpdate(dody)
      Toast.notify({ type: 'success', message: res?.message })
      onSaved()
      onClose()
    }
    catch (error) {
    }
  }

  useEffect(() => {
    if (name) {
      form.setFieldsValue({
        name,
      })
    }
  }, [form, name])
  useEffect(debounce(() => {
    form.validateFields({ validateOnly: true }).then(() => {
      setDisabled(false)
    },
    ).catch(() => {
      setDisabled(true)
    })
  }, 100), [form, values])

  return (
    <Modal
      title={t('datasetDocuments.list.table.rename')}
      isShow
      footer={
        <>
          <Button variant='secondary-accent' className='mr-4' onClick={onClose}>{t('common.operation.cancel')}</Button>
          <Button disabled={disabled} variant='primary' onClick={handleSave}>{t('common.operation.save')}</Button>
        </>
      }
      closable
      onClose={onClose}
    >
      <Form layout='vertical' form={form}>
        <Form.Item
          name={'name'}
          label={t('datasetDocuments.list.table.name')}
          validateFirst={true}
          rules={[
            {
              required: true,
              whitespace: true,
            },
          ]}>
          <Input placeholder={t('common.placeholder.input')!}></Input>
        </Form.Item>
      </Form>
    </Modal>
  )
}
export default React.memo(RenameModal)
