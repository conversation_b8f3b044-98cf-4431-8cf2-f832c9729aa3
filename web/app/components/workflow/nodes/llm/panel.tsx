import type { FC } from 'react'
import React, { useEffect, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { Divider, Select } from 'antd'

import MemoryConfig from '../_base/components/memory-config'
import ConfigVision from '../_base/components/config-vision'

import OutputVarList from '../_base/components/variable/output-var-list'
import useConfig from './use-config'
import type { LLMNodeType } from './types'
import ConfigPrompt from './components/config-prompt'
import VarList from '@/app/components/workflow/nodes/_base/components/variable/var-list'
import { AddTextButton } from '@/app/components/base/button/add-button'
import type { AgentTool } from '@/types/tools'
import type { Collection } from '@/app/components/tools/types'
import Field from '@/app/components/workflow/nodes/_base/components/field'
import ModelParameterModalInWorkflow from '@/app/components/account-setting/model-provider-page/model-parameter-modal-in-workflow'

import { VarItem } from '@/app/components/workflow/nodes/_base/components/variable/output-vars'
import { InputVarType, type NodePanelProps, TextVarFormat } from '@/app/components/workflow/types'
import BeforeRunForm from '@/app/components/workflow/nodes/_base/components/before-run-form'
import type { Props as FormProps } from '@/app/components/workflow/nodes/_base/components/before-run-form/form'
import ResultPanel from '@/app/components/workflow/run/result-panel'
import { useImageStore } from '@/app/components/base/image-uploader/hooks'
import AddToolPanel from '@/app/components/tools/add-tool-panel'
import AddDatasetPanel from '@/app/components/datasets/common/add-dataset-panel'
import { Add } from '@/app/components/base/icons/src/vender/solid/general'
import TextButton from '@/app/components/base/button/text-button'

const i18nPrefix = 'workflow.nodes.llm'

const Panel: FC<NodePanelProps<LLMNodeType>> = ({
  id,
  data,
}) => {
  const { t } = useTranslation()
  const { clearFiles } = useImageStore()

  // 销毁时，把图片缓存清掉
  useEffect(() => {
    clearFiles()
  }, [clearFiles])

  const {
    readOnly,
    inputs,
    isChatModel,
    isChatMode,
    isVisionModel,
    hasSetBlockStatus,
    filterVar,
    availableVars,
    availableNodesWithParent,
    isShowVars,
    isShowSingleRun,
    hideSingleRun,
    inputVarValues,
    setInputVarValues,
    visionFiles,
    setVisionFiles,
    contexts,
    setContexts,
    runningStatus,
    varInputs,
    datasetList,
    runResult,
    handleCompletionParamsChange,
    handleModelChanged,
    handleRun,
    handleStop,
    handleOuputConfig,
    handleSelectTool,
    handlePromptChange,
    handleChangeTool,
    handleDeleteTool,
    handleAddEmptyVariable,
    handleAddVariable,
    handleVarListChange,
    handleVarNameChange,
    handleSyeQueryChange,
    handleMemoryChange,
    handleVisionResolutionEnabledChange,
    handleVisionResolutionChange,
    handleSelectDataset,
    handleConfigDataset,
    handleRemoveDataset,
    handleRemoveJsonOutput,
    handleChangeJsonOutput,
    handleAddJsonOutput,
  } = useConfig(id, data)

  const model = inputs.model

  const singleRunForms = (() => {
    const forms: FormProps[] = []

    if (varInputs.length > 0) {
      forms.push(
        {
          label: t(`${i18nPrefix}.singleRun.variable`)!,
          inputs: varInputs,
          values: inputVarValues,
          onChange: setInputVarValues,
        },
      )
    }

    if (inputs.context?.variable_selector && inputs.context?.variable_selector.length > 0) {
      forms.push(
        {
          label: t(`${i18nPrefix}.context`)!,
          inputs: [{
            label: '',
            variable: '#context#',
            type: InputVarType.contexts,
            required: false,
          }],
          values: { '#context#': contexts },
          onChange: keyValue => setContexts((keyValue as any)['#context#']),
        },
      )
    }

    if (isVisionModel) {
      const variableName = data.vision.configs?.variable_selector?.[1] || t(`${i18nPrefix}.files`)!
      forms.push(
        {
          label: t(`${i18nPrefix}.vision`)!,
          inputs: [{
            label: variableName!,
            variable: '#files#',
            type: InputVarType.files,
            required: false,
          }],
          values: { '#files#': visionFiles },
          onChange: keyValue => setVisionFiles((keyValue as any)['#files#']),
        },
      )
    }

    return forms
  })()
  const parameterRef = useRef<{
    getOutputFormat: () => string[]
  }>()

  return (
    <>
      {/* 模型选择 */}
      <Field title={t(`${i18nPrefix}.model`)}>
        <ModelParameterModalInWorkflow
          ref={parameterRef}
          popupClassName='!w-[387px]'
          isInWorkflow
          isAdvancedMode={true}
          mode={model?.mode}
          provider={model?.provider}
          completionParams={model?.completion_params}
          modelId={model?.name}
          setModel={handleModelChanged}
          onCompletionParamsChange={handleCompletionParamsChange}
          hideDebugWithMultipleModel
          debugWithMultipleModel={false}
          readonly={readOnly}
        />
      </Field>
      <Divider />
      <div className='flex flex-col gap-4'>
        {/* 能力拓展 */}
        {model.name && isChatModel && <Field title={t(`${i18nPrefix}.abilityDevelopment`)} supportFold>
          <div className='flex flex-col gap-2'>
            {/* 工具配置 */}
            <AddToolPanel
              tools={inputs.tools as (AgentTool & { icon_url: any; collection?: Collection })[]}
              scene='workflow'
              onSelectTool={handleSelectTool}
              onChangeTool={handleChangeTool}
              onDelete={handleDeleteTool}
              className='relative flex justify-between items-center last-of-type:mb-0 h-[40px] py-2 w-full rounded'
            ></AddToolPanel>
            {/* 知识库配置 */}
            <AddDatasetPanel
              config={{
                ...inputs.dataset_configs,
                datasets: {
                  datasets: inputs.dataset_configs.datasets?.datasets.map(item => ({
                    enabled: item.dataset.enabled,
                    id: item.dataset.id,
                  })) || [],
                },
              }}
              scene='workflow'
              dataSets={datasetList}
              onSelect={handleSelectDataset}
              onConfig={handleConfigDataset}
              onRemove={handleRemoveDataset}
            ></AddDatasetPanel>
            {/* 记忆配置 */}
            <MemoryConfig
              readonly={readOnly}
              config={{ data: inputs.memory }}
              onChange={handleMemoryChange}
            >
              {/* 记忆变量配置 */}
              {/* {!!inputs.memory && (
                <div className='mt-2'>
                  <Editor
                    title={<div className='flex items-center space-x-1'>
                      <div className='text-xs font-semibold text-gray-700 uppercase'>user</div>
                      <Tooltip
                        popupContent={
                          <div className='max-w-[180px]'>{t('workflow.nodes.llm.roleDescription.user')}</div>
                        }
                        triggerClassName='w-4 h-4'
                      />
                    </div>}
                    placeholder={t('workflow.nodes.llm.rolePlaceholder.user') || ''}
                    value={inputs.memory.query_prompt_template || '{{#sys.query#}}'}
                    onChange={handleSyeQueryChange}
                    readOnly={readOnly}
                    isShowContext={false}
                    isChatApp
                    isChatModel
                    hasSetBlockStatus={hasSetBlockStatus}
                    nodesOutputVars={availableVars}
                    availableNodes={availableNodesWithParent}
                  />

                  {inputs.memory.query_prompt_template && !inputs.memory.query_prompt_template.includes('{{#sys.query#}}') && (
                    <div className='leading-[18px] text-xs font-normal text-[#DC6803]'>{t(`${i18nPrefix}.sysQueryInUser`)}</div>
                  )}
                </div>
              )} */}
            </MemoryConfig>
            {/* 视觉配置 */}
            {model.name && isVisionModel && <ConfigVision
              nodeId={id}
              readOnly={readOnly}
              isVisionModel={isVisionModel}
              enabled={inputs.vision?.enabled}
              onEnabledChange={handleVisionResolutionEnabledChange}
              config={inputs.vision?.configs}
              onConfigChange={handleVisionResolutionChange}
            />}
          </div>
        </Field>}

        {/* 上下文配置 */}
        {/* <Field
          title={t(`${i18nPrefix}.context`)}
          tooltip={t(`${i18nPrefix}.contextTooltip`)!}
        >
          <>
            <VarReferencePicker
              readonly={readOnly}
              nodeId={id}
              isShowNodeName
              value={inputs.context?.variable_selector || []}
              onChange={handleContextVarChange}
              filterVar={filterVar}
            />
            {shouldShowContextTip && (
              <div className='leading-[18px] text-xs font-normal text-[#DC6803]'>{t(`${i18nPrefix}.notSetContextInPromptTip`)}</div>
            )}
          </>
        </Field> */}

        {/* 提示词 */}
        {model.name && (
          <ConfigPrompt
            readOnly={readOnly}
            isShowContext={false}
            isChatModel={isChatModel}
            isChatApp={isChatMode}
            nodeId={id}
            modelConfig={model}
            filterVar={filterVar}
            payload={inputs.prompt_template}
            hasSetBlockStatus={hasSetBlockStatus}
            varList={inputs.prompt_config?.jinja2_variables || []}
            onChange={handlePromptChange}
            handleAddVariable={handleAddVariable}
          />
        )}
        {/* 提示词jinja变量 */}
        {isShowVars && (
          <Field
            title={t('workflow.nodes.templateTransform.inputVars')}
            operations={
              !readOnly ? <AddTextButton onClick={handleAddEmptyVariable} /> : undefined
            }
          >
            <VarList
              nodeId={id}
              readonly={readOnly}
              list={inputs.prompt_config?.jinja2_variables || []}
              onChange={handleVarListChange}
              onVarNameChange={handleVarNameChange}
              filterVar={filterVar}
            />
          </Field>
        )}
      </div>
      <Divider />
      {/* 输出变量 */}
      <Field
        title={t('workflow.nodes.common.outputVars')}
        tooltip={t(`${i18nPrefix}.contextTooltip`)!}
        operations={
          <div className='flex items-center'>
            <span className='mr-3 text-S3'>{t(`${i18nPrefix}.outputVars.type`)}</span>
            <Select
              value={inputs.model.response_format}
              style={inputs.model.response_format === TextVarFormat.Json ? { width: 100 } : { width: 120 }}
              options={[
                { value: TextVarFormat.Markdown, label: 'Markdown', disabled: false },
                { value: TextVarFormat.Json, label: 'JSON', disabled: !parameterRef.current?.getOutputFormat().includes('JSON') },
              ]}
              onChange={handleOuputConfig}
            />
            { inputs.model.response_format === TextVarFormat.Json
              && <>
                <Divider className='!mx-2' type='vertical'></Divider>
                <TextButton
                  variant={'hover'}
                  onClick={handleAddJsonOutput}
                >
                  <Add></Add>
                </TextButton>
              </>
            }
          </div>
        }
      >
        <>
          {
            inputs.model.response_format === TextVarFormat.Markdown
              ? <div className='flex flex-col gap-2 px-3 py-2 bg-gray-G7 rounded'>
                <VarItem
                  name='text'
                  type='string'
                  description={t(`${i18nPrefix}.outputVars.output`)}
                />
              </div>
              : <></>
          }
          {
            inputs.model.response_format === TextVarFormat.Json
              ? <OutputVarList
                readonly={false}
                onRemove={handleRemoveJsonOutput}
                onChange={handleChangeJsonOutput}
                outputs={inputs.model.outputs}
                outputKeyOrders={Object.keys(inputs.model.outputs)}
              ></OutputVarList>
              : <></>
          }
        </>

      </Field>
      {/* 单次运行面板 */}
      {isShowSingleRun && (
        <BeforeRunForm
          nodeName={inputs.title}
          onHide={hideSingleRun}
          forms={singleRunForms}
          runningStatus={runningStatus}
          onRun={handleRun}
          onStop={handleStop}
          result={<ResultPanel {...runResult} showSteps={false} />}
        />
      )}
    </>
  )
}

export default React.memo(Panel)
