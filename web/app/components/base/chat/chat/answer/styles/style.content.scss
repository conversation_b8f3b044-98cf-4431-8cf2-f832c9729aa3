.li-style-icon {
  .delete-icon {
    display: none;
    margin-left: 8px;
    cursor: pointer;
  }
}

.li-style-icon:hover {
  .delete-icon {
    display: block;
    color: rgb(220, 3, 3);
    transition: all 0.5s ease-in-out;
  }
}

.custom-popconfirm {
  :where(.ant-popover-inner) {
    padding: 12px !important;
  }
}

.tipDiv{
  padding:10px 15px;
  border-radius:10px;
  color:#5C6273;
  font-size: 12px;
}
.tipioc{
  color:#878EA0;
}
.tipioc:hover{
  color:#000;
}
.onsendsvg{
  margin-right:4px;
}

.detailTel{
  color:#181818;
  font-size: 12px;
  line-height:20px;
  margin-top:15px;
}
.detailCcontent{margin-top:10px;}
.detailChild{
  border:1px solid #D9DCE3;
  border-radius:4px;
  padding:4px 8px;
  font-size: 12px;
  color:#5C6273;
  width:185px;
  height:30px;
  line-height:22px;
  margin-right:8px;
  margin-bottom:4px;
}
.detailMore{
  width:auto !important;
  cursor: pointer;
}
.moreNum{
  width:16px;
  height:16px;
  background:#F5F7FA;
  color:#878EA0;
  font-size:10px;
  border-radius:100px;
  line-height:16px;
  text-align:center;
  margin-right:6px;
}
.moreword{
  color:#5C6273;
  font-size:12px
}
.smallLogo{
  width:16px;
  height:16px;
  display:block;
  margin-right:4px;
}
.detailChildtxt{
    white-space: normal;
    display: -webkit-box;
    -webkit-line-clamp:1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    width:145px;
}

.docfileDiv{
  border:1px solid #EDEFF2;
  border-radius:4px;
  height:66px;
  padding: 8px 12px;
  margin-top:5px;
  cursor: pointer;
}
.docfileDiv:hover{
  border:1px solid #3168F5;
}
.docfileDiv:hover svg{
  color:#181818;
}
.docfilepic{
  width:32px;
  height:32px;
  display:block;
  margin-right:8px;
}
.docfileName{
  min-width:350px
}

.docfileflow{
  min-width:180px
}

.docfileNameTxt{
  font-size: 14px;
  color:#181818;
}
.docfileNameType{
  font-size: 12px;
  color: #878EA0;
  margin-top:6px;
}
.mtopdiv{
  margin-top:5px;
  line-height:16px;
}
.lcpic{
  width:16px;
  height:16px;
  display:block;
  margin-right:10px;
}
.prodiv{
  padding-left:26px;
}

.proessTop{
  margin-top:10px;
}
.proessLeft{
  padding-left:26px;
  margin-top:15px;
}
.pressDiv{
   border-radius:4px;
   border-color:#EDEFF2;
   width:385px;
   max-width:90%;
}
.pressDiv .ant-progress-text{
  color:#181818!important;
}
.pressDivflow{
  border-radius:4px;
  border-color:#EDEFF2;
  width:90%;
}
.proesstel{
  color: #181818;;
  font-size: 14px;
  line-height:24px;
  margin-left:10px;
}
.resultDiv{
  font-size:12px;
  color:#666;
  padding:4px 0 0 26px;
}
.resultDiv span{
  margin-left:10px;
  color:#3168f5 !important;
  cursor: pointer;
}
.resultDivGray{
  font-size:12px;
  color:#666;
  padding:0px 0 0 1px;
  margin-top:5px;
  cursor: pointer;
}
.undo{
  display:block;
  width:16px;
  height:16px;
  margin-right:5px;
}
.getContent{
 cursor: pointer;
 font-size:12px;
 background:#3168F5;
 border-radius:4px;
 padding:2px 6px;
 color:#fff;
}
.chrmtp{
  margin-top:5px;
}