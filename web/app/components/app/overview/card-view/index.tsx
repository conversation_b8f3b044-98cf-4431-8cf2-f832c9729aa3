'use client'
import type { FC } from 'react'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { useContext, useContextSelector } from 'use-context-selector'
import AppCard from './appCard'
import type { IAppCardProps } from './appCard'
import {
  fetchAppDetail,
  fetchAppSSO,
  updateAppSSO,
  updateAppSiteAccessToken,
  updateAppSiteConfig,
  updateAppSiteStatus,
} from '@/service/apps'
import type { App, AppSSO } from '@/types/app'
import type { UpdateAppSiteCodeResponse } from '@/models/app'
import { asyncRunSafe } from '@/utils'
import { useStore as useAppStore } from '@/app/components/app/store'

import { ToastContext } from '@/app/components/base/toast'
import SystemContext from '@/context/system-context'

export type ICardViewProps = {
  appId: string
}

const CardView: FC<ICardViewProps> = ({ appId }) => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  const appDetail = useAppStore(state => state.appDetail)
  const setAppDetail = useAppStore(state => state.setAppDetail)
  const systemFeatures = useContextSelector(SystemContext, state => state.systemFeatures)

  // 更新应用信息
  const updateAppDetail = async () => {
    try {
      const res = await fetchAppDetail({ url: '/apps', id: appId })
      if (systemFeatures.enable_web_sso_switch_component) {
        const ssoRes = await fetchAppSSO({ appId })
        setAppDetail({ ...res, enable_sso: ssoRes.enabled })
      }
      else {
        setAppDetail({ ...res })
      }
    }
    catch (error) {
      console.error(error)
    }
  }

  // 处理变更回调事件
  const handleCallbackResult = (err: Error | null, message?: string) => {
    const type = err ? 'error' : 'success'

    message ||= type === 'success' ? 'modifiedSuccessfully' : 'modifiedUnsuccessfully'

    if (type === 'success')
      updateAppDetail()

    notify({
      type,
      message: t(`common.actionMsg.${message}`),
    })
  }

  // 变更站点状态
  const onChangeSiteStatus = async (value: boolean) => {
    const [err] = await asyncRunSafe<App>(
      updateAppSiteStatus({
        url: `/apps/${appId}/site-enable`,
        body: { enable_site: value },
      }) as Promise<App>,
    )

    handleCallbackResult(err)
  }
  // 变更api状态
  const onChangeApiStatus = async (value: boolean) => {
    const [err] = await asyncRunSafe<App>(
      updateAppSiteStatus({
        url: `/apps/${appId}/api-enable`,
        body: { enable_api: value },
      }) as Promise<App>,
    )

    handleCallbackResult(err)
  }
  // 保存站点配置
  const onSaveSiteConfig: IAppCardProps['onSaveSiteConfig'] = async (params) => {
    const [err] = await asyncRunSafe<App>(
      updateAppSiteConfig({
        url: `/apps/${appId}/site`,
        body: params,
      }) as Promise<App>,
    )

    if (systemFeatures.enable_web_sso_switch_component) {
      const [sso_err] = await asyncRunSafe<AppSSO>(
        updateAppSSO({ id: appId, enabled: Boolean(params.enable_sso) }) as Promise<AppSSO>,
      )
      if (sso_err) {
        handleCallbackResult(sso_err)
        return
      }
    }

    handleCallbackResult(err)
  }
  // 生成代码回调
  const onGenerateCode = async () => {
    const [err] = await asyncRunSafe<UpdateAppSiteCodeResponse>(
      updateAppSiteAccessToken({
        url: `/apps/${appId}/site/access-token-reset`,
      }) as Promise<UpdateAppSiteCodeResponse>,
    )

    handleCallbackResult(err, err ? 'generatedUnsuccessfully' : 'generatedSuccessfully')
  }

  return (
    <div className="grid w-full grid-cols-1 gap-6 mb-6 xl:grid-cols-2">
      <AppCard
        appInfo={appDetail!}
        cardType="webapp"
        onChangeStatus={onChangeSiteStatus}
        onGenerateCode={onGenerateCode}
        onSaveSiteConfig={onSaveSiteConfig}
      />
      <AppCard
        cardType="api"
        appInfo={appDetail!}
        onChangeStatus={onChangeApiStatus}
      />
    </div>
  )
}

export default CardView
