import React, { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { v4 as uuid4 } from 'uuid'
import { Form, Input, InputNumber } from 'antd'
import s from './styles/style.module.css'
import Button from '@/app/components/base/button'
import Tooltip from '@/app/components/base/tooltip'
import { useStore } from '@/app/components/workflow/store'
import type { EnvironmentVariable } from '@/app/components/workflow/types'
import { checkKeys } from '@/utils/var'
import Modal from '@/app/components/base/modal'
import Radio from '@/app/components/base/radio'
import { useFormDisabled } from '@/hooks/use-form'
// 公共能力

export type ModalPropsType = {
  env?: EnvironmentVariable
  onClose: () => void
  onSave: (env: EnvironmentVariable) => void
}
const VariableModal = ({
  env,
  onClose,
  onSave,
}: ModalPropsType) => {
  const { t } = useTranslation()
  const envList = useStore(s => s.environmentVariables)
  const envSecrets = useStore(s => s.envSecrets)
  const [form] = Form.useForm()
  const disabled = useFormDisabled(form)

  // 变量类型
  const [type, setType] = React.useState<'string' | 'number' | 'secret'>('string')

  // 变更变量类型
  const handleChangeType = (value: string) => {
    setType(value as any)
    if (value === 'secret')
      form.setFieldsValue({ value: '' })
    if (value === 'number' && !(/^[0-9]*$/).test(value))
      form.setFieldsValue({ value: 0 })
  }
  // 保存配置
  const handleSave = () => {
    const { name, value } = form.getFieldsValue()
    onSave({
      id: env ? env.id : uuid4(),
      value_type: type,
      name,
      value: type === 'number' ? Number(value) : value,
    })
    onClose()
  }

  useEffect(() => {
    if (env) {
      setType(env.value_type)
      form.setFieldsValue({
        name: env.name,
        value: env.value_type === 'secret' ? envSecrets[env.id] : env.value,
      })
    }
  }, [env, envSecrets, form])

  return (
    <Modal
      isShow
      closable
      onClose={onClose}
      title={(!env ? t('workflow.env.modal.title') : t('workflow.env.modal.editTitle')) || ''}
      footer={
        <>
          <Button className="mr-4" variant={'secondary-accent'} onClick={onClose}>{t('common.operation.cancel')}</Button>
          <Button disabled={disabled} variant='primary' onClick={handleSave}>{t('common.operation.save')}</Button>
        </>
      }
    >
      <Form form={form} layout='vertical'>
        {/* 变量类型 */}
        <Form.Item required label={t('workflow.env.modal.type')}>
          <Radio.Group value={type} onChange={handleChangeType} className='gap-3'>
            <Radio.Button className={s['type-item']} value={'string'}>
              <span>{t('workflow.env.type.string')}</span>
            </Radio.Button>
            <Radio.Button className={s['type-item']} value={'number'}>
              <span>{t('workflow.env.type.number')}</span>
            </Radio.Button>
            <Radio.Button className={s['type-item']} value={'secret'}>
              <span>{t('workflow.env.type.secret')}</span>
              <Tooltip
                popupContent={
                  <span>
                    {t('workflow.env.modal.secretTip')}
                  </span>
                }
              />
            </Radio.Button>
          </Radio.Group>
        </Form.Item>
        {/* 变量名称 */}
        <Form.Item
          name={'name'}
          label={t('workflow.env.modal.name')}
          validateFirst={true}
          validateTrigger='onBlur'
          rules={[{
            required: true,
            whitespace: true,
          }, {
            validator: (_, value) => {
              const { isValid, errorMessageKey } = checkKeys([value], false)
              if (!isValid)
                return Promise.reject(new Error(t(`appDebug.varKeyError.${errorMessageKey}`, { key: t('workflow.env.modal.name') })!))
              if (!env && envList.some(chatVar => chatVar.name === value))
                return Promise.reject(new Error(t('workflow.env.modal.nameExist')!))
              return Promise.resolve()
            },
          }]}
        >
          <Input placeholder={t('workflow.env.modal.namePlaceholder') || ''}/>
        </Form.Item>
        {/* value */}
        <Form.Item
          rules={[{
            required: true,
          }]}
          validateFirst={true}
          validateTrigger='onBlur'
          name={'value'}
          label={t('workflow.env.modal.value')}
        >
          {type !== 'number' && (
            <Input key={'string'} placeholder={t('workflow.env.modal.valuePlaceholder') || ''}/>
          )}
          {type === 'number' && (
            <InputNumber key={'number'} className='w-full' placeholder={t('workflow.env.modal.valuePlaceholder') || ''}/>
          )}
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default VariableModal
