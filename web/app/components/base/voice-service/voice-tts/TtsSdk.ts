import { getFullWsUrl } from '../utils'
export class TTSService {
  wsUrl: string
  authData: any
  req_id: string
  text: string
  config?: any
  outputPath: string
  sampleRate: number
  audioDataRaw: ArrayBuffer[] = []
  audioChunks: ArrayBuffer[] = []
  sourceNode: AudioBufferSourceNode | null = null // 音频播放
  socket: WebSocket | null = null
  errorCallback: ((customError: string, error?: Event) => void) | null = null;
  messageCallback: ((response: any) => void) | null = null;
  finishCallback: (() => void) | null = null;
  hasSendText: boolean = false
  audioContext: AudioContext | null = null // 音频上下文
  isError: boolean = false
  isStop: boolean = false
  isEnd: boolean = false

  constructor(
    wsUrl: string,
    authData: any,
    text: string,
    req_id: string,
    config?: any
  ) {
    this.wsUrl = wsUrl
    this.authData = authData
    this.req_id = req_id // 请求全局唯一 ID
    this.text = text // 待合成的文本
    this.config = config || {}
    this.outputPath = 'test.pcm' // 合成音频的输出路径
    this.audioDataRaw = []
    this.audioChunks = [] // 用于存储接收到的音频数据块（以ArrayBuffer形式）
    this.sampleRate = 16000 // 采样率
    this.sourceNode = null // 音频播放
    this.hasSendText = false
    this.audioContext = new (window.AudioContext || window.webkitAudioContext)(); // 初始化音频上下文
    this.isError = false
    this.isStop = false
    this.isEnd = false
  }

  async init(): Promise<void> {
    if (typeof window !== 'undefined') {
      try {
        this.socket = new WebSocket(this.wsUrl)

      } catch (error) {
        this.socket = new WebSocket(getFullWsUrl(this.wsUrl))
      } 
      this.socket.onopen = this.onOpen.bind(this)
      this.socket.onmessage = this.onMessage.bind(this)
      this.socket.onclose = this.onClose.bind(this)
      this.socket.onerror = this.onError.bind(this)
    } else {
      console.error('WebSocket is not available in this environment.')
    }
  }

  onOpen(): void {
    this.sendStartSynthesisMessage()
  }

  onMessage(data: MessageEvent): void {
    const response = JSON.parse(data.data)

    if (response.status === 10000) {
      if ('result' in response) {
        const synthInfo = response.result
        const audioDataEncoded = synthInfo.audio
        const audioDataRaw = this.decodeBase64ToArrayBuffer(audioDataEncoded) // 解码Base64数据为ArrayBuffer
        this.audioChunks.push(audioDataRaw)

        // 如果当前没有音频在播放，则开始播放下一个音频数据块
        if (!this.sourceNode) {
          this.playNextChunk()
        }

        if (synthInfo.is_end) {
          this.isEnd = true
          this.finishSynthesis()
        }
      } else {
        // 鉴权建立连接后，可以发送文字
        if (!this.hasSendText) 
          this.sendTtsText()
        this.hasSendText = true
      } 
    } else {
      //console.log('合成失败:', response)
    }
    if (this.messageCallback) {
      this.messageCallback(response)
    }
  }

  onClose(): void {
    //console.log('WebSocket连接已关闭')
  }

  getAudioDataRaw(): ArrayBuffer[] {
    return this.audioDataRaw
  }

  onError(error: Event): void {
    this.isError = true
    //console.error('WebSocket错误:', error, this.req_id)
    if (this.errorCallback) 
      this.errorCallback('ttsNoInternet', error)
  }

  // 建立鉴权连接
  sendStartSynthesisMessage(): void {
    const message = {
      rec_status: 0,
      req_id: this.req_id,
      ...this.authData,
    }
    if(this.authData?.type === 0) {
      message.voice_input = this.config
    }
    if (this.socket) {
      this.socket.send(JSON.stringify(message))
      //console.log('已发送开始合成请求')
    } else {
      console.error('WebSocket未初始化')
    }
  }

  // 发送文字
  sendTtsText(): void {
    const message = {
      req_id: this.req_id,
      text: this.text,
    }
    if (this.socket) {
      this.socket.send(JSON.stringify(message))
      console.log('已发送文字', this.text)
    } else {
      console.error('WebSocket未初始化')
    }
  }

  finishSynthesis(): void {
    if (this.socket) {
      this.socket.close()
      console.log('已停止合成', this.req_id)
    }
  }

  // 辅助函数，用于将Base64编码数据转换为ArrayBuffer
  decodeBase64ToArrayBuffer(base64: string): ArrayBuffer {
    const binaryString = window.atob(base64)
    const len = binaryString.length
    const bytes = new Uint8Array(len)
    for (let i = 0; i < len; i++) {
      bytes[i] = binaryString.charCodeAt(i)
    }
    return bytes.buffer
  }

  // 保存音频数据到本地（通过浏览器下载功能模拟）
  // saveAudioToLocal(): void {
  //   console.log('保存音频数据到本地', this.audioChunks);
  //   const wavBuffer = this.encodeWAV(this.audioChunks, this.sampleRate);
  //     // const combinedBuffer = this.combineBuffers(this.audioChunks); // 合并所有音频数据块为一个ArrayBuffer
  //   const audioBlob = new Blob([wavBuffer], { type: 'audio/wav' }); // 创建Blob对象，假设音频格式为WAV，可根据实际调整
  //   const url = URL.createObjectURL(audioBlob);
  //   const a = document.createElement('a');
  //   a.style.display = 'none';
  //   a.href = url;
  //   a.download = 'synthesized_audio.wav';
  //   document.body.appendChild(a);
  //   a.click();
  //   window.URL.revokeObjectURL(url);
  // }

  encodeWAV(samples: Int16Array, sampleRate: number): ArrayBuffer {
    const buffer = new ArrayBuffer(44 + samples.length * 2)
    const view = new DataView(buffer)

    function writeString(view: DataView, offset: number, string: string): void {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i))
      }
    }

    writeString(view, 0, 'RIFF')
    view.setUint32(4, 36 + samples.length * 2, true)
    writeString(view, 8, 'WAVE')
    writeString(view, 12, 'fmt ')
    view.setUint32(16, 16, true)
    view.setUint16(20, 1, true)
    view.setUint16(22, 1, true) // 单声道
    view.setUint32(24, sampleRate, true)
    view.setUint32(28, sampleRate * 2, true) // byte rate
    view.setUint16(32, 2, true) // block align
    view.setUint16(34, 16, true) // bits per sample
    writeString(view, 36, 'data')
    view.setUint32(40, samples.length * 2, true)

    let offset = 44
    for (let i = 0; i < samples.length; i++, offset += 2) {
      const s = Math.max(-1, Math.min(1, samples[i]))
      view.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7fff, true)
    }

    return view.buffer
    // return view;
  }

  // 合并多个ArrayBuffer为一个
  combineBuffers(buffers: ArrayBuffer[]): ArrayBuffer {
    let totalLength = 0
    buffers.forEach((buffer) => {
      totalLength += buffer.byteLength
    })
    const combinedBuffer = new ArrayBuffer(totalLength)
    const combinedView = new Uint8Array(combinedBuffer)
    let offset = 0
    buffers.forEach((buffer) => {
      combinedView.set(new Uint8Array(buffer), offset)
      offset += buffer.byteLength
    })
    return combinedBuffer
  }

  // 播放音频
  playPcm(arrayBuffer: ArrayBuffer): void {
    if (this.sourceNode) {
      this.sourceNode.stop(0); // 停止当前正在播放的音频
    }

    const sampleRate = 16000; 
    const numChannels = 1;
    const numSamples = arrayBuffer.byteLength / 2;

    const audioBuffer = this.audioContext!.createBuffer(numChannels, numSamples, sampleRate);
    const channelData = audioBuffer.getChannelData(0);

    const dataView = new DataView(arrayBuffer);
    for (let i = 0; i < numSamples; i++) {
      channelData[i] = dataView.getInt16(i * 2, true) / 32768;
    }

    this.sourceNode = this.audioContext!.createBufferSource();
    this.sourceNode.buffer = audioBuffer;
    this.sourceNode.connect(this.audioContext!.destination);
    this.sourceNode.start(0);

    // 设置播放结束后的回调
    this.sourceNode.onended = () => {
      this.playNextChunk();
    };
  }

  // 播放下一个音频数据块
  playNextChunk(): void {
    if (this.audioChunks.length > 0) {
      const audioDataRaw = this.audioChunks.shift()!;
      this.playPcm(audioDataRaw);
    } else if (this.sourceNode && !this.isEnd && !this.isStop && this.errorCallback) {
      this.errorCallback('ttsNoInternet')
    } else if (this.sourceNode && this.isEnd && this.finishCallback) {
      this.finishCallback()
    }
  }

  // 主动中止音频播放
  stopPlay(): void {
    if (this.sourceNode) {
      this.sourceNode.stop(0);
      this.sourceNode = null;
    }
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
    this.audioChunks = []; // 清空音频数据块
  }
}