'use client'
import type { FC } from 'react'
import React from 'react'
import { useTranslation } from 'react-i18next'
import ParamItem from '.'

type Props = {
  className?: string
  value: number
  onChange: (key: string, value: number) => void
  enable: boolean
  hasInputNumber?: boolean
  labelClassName?: string
  valueClassName?: string
}

const VALUE_LIMIT = {
  default: 2,
  step: 1,
  precision: 0,
  min: 1,
  max: 10,
}

const key = 'top_k'
const TopKItem: FC<Props> = ({
  className,
  value,
  enable,
  hasInputNumber,
  labelClassName,
  valueClassName,
  onChange,
}) => {
  const { t } = useTranslation()
  const handleParamChange = (key: string, value: number) => {
    let notOutRangeValue = parseFloat(value.toFixed(2))
    notOutRangeValue = Math.max(VALUE_LIMIT.min, notOutRangeValue)
    notOutRangeValue = Math.min(VALUE_LIMIT.max, notOutRangeValue)
    onChange(key, notOutRangeValue)
  }
  return (
    <ParamItem
      className={className}
      id={key}
      name={t(`dataset.paramConfig.${key}`)}
      tip={t(`dataset.paramConfig.${key}Tip`) as string}
      {...VALUE_LIMIT}
      value={value}
      hasInputNumber={hasInputNumber}
      enable={enable}
      onChange={handleParamChange}
      labelClassName={labelClassName}
      valueClassName={valueClassName}
    />
  )
}
export default React.memo(TopKItem)
