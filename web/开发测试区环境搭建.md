# 开发测试区环境搭建


## 环境准备

npm 20.+

npm install pnpm -g

## 依赖安装

项目目录下:

pnpm install


## 配置修改

next.config.js 中 defaultProxy 改成本地的的后端服务 127.0.0.1:5001; 以及一下代码：

```js

  async rewrites() {
    return {
      fallback: [
          ...(isGray ? grayProxy : defaultProxy),
          ...microProxys,
        ]
    }
  },

```



## 应用运行


pnpm run build

pnpm run start


## 访问

访问：

http://localhost:3000

登录账号：

<EMAIL>

Udblwfn@321