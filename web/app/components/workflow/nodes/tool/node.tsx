import { useTranslation } from 'react-i18next'
import type { FC } from 'react'
import React from 'react'
import style from '../styles/style.module.css'
import type { ToolNodeType } from './types'
import { useMiniConfig } from './use-config'
import type { NodeProps } from '@/app/components/workflow/types'
import ForkTip from '@/app/components/base/forkTip'

const Node: FC<NodeProps<ToolNodeType>> = ({
  id,
  data,
}) => {
  const { t } = useTranslation()
  // 预处理
  const result = useMiniConfig(id, data)
  const { tool_configurations } = data
  const toolConfigs = Object.keys(tool_configurations || {})

  if (!toolConfigs.length && !data.fork)
    return null

  return (
    <div className={style.nodeContent}>
      {data.fork && (
        <ForkTip
          className='forkTip mb-2'
          message={t('common.fork.singleToolTip')}
        >
          <div>
          </div>
        </ForkTip>
      )}
      {toolConfigs.length > 0 && (
        <div className='space-y-2'>
          {toolConfigs.map((key, index) => (
            <div key={index} className='flex items-center h-6 justify-between bg-gray-G7 rounded px-2 py-1 space-x-1 text-S1 font-normal text-gray-G1'>
              <div title={key} className='max-w-[100px] shrink-0 truncate text-S1 font-semibold text-gray-G2 uppercase'>
                {key}
              </div>
              <div title={tool_configurations[key]} className='grow w-0 shrink-0 truncate text-right text-S1 font-normal text-gray-G1'>
                {tool_configurations[key]}
              </div>
            </div>

          ))}

        </div>
      )}
    </div>
  )
}

export default React.memo(Node)
