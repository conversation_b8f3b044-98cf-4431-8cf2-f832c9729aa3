const translation = {
  operation: {
    abandon: '放弃',
    add: '添加',
    agree: '点赞',
    audioSourceUnavailable: '音源不可用',
    cancel: '取消',
    change: '更改',
    clear: '清空',
    configure: '配置',
    confirm: '确认',
    copy: '复制',
    copyImage: '复制图片',
    create: '创建',
    delete: '删除',
    disagree: '点踩',
    download: '下载',
    duplicate: '复制',
    detail: '详情',
    edit: '编辑',
    log: '日志',
    ok: '好的',
    openInNewTab: '在新标签页打开',
    params: '参数设置',
    preview: '预览',
    publish: '发布',
    pull: '下架',
    refresh: '重新开始',
    reUpload: '重新上传',
    reProcess: '重新处理',
    remove: '移除',
    rename: '重命名',
    reset: '重置',
    retry: '重试',
    run: '运行',
    save: '保存',
    search: '搜索',
    see: '查看',
    send: '发送',
    settings: '设置',
    submit: '提交',
    sync: '同步',
    unPublish: '取消发布',
    use: '使用',
    zoomIn: '放大',
    zoomOut: '缩小',
  },
  info: {
    all: '全部',
    file: '文件',
    result: '结果',
    row: '第{{row}}行',
    operation: '操作',
    fieldName: '字段名',
    fieldValue: '字段值',
    rowCol: '{{row}}行{{col}}列',
  },
  status: {
    active: '可用',
    inactive: '不可用',
    connected: '已绑定',
    disconnected: '未绑定',
    canBeAdded: '可添加',
    notCanBeAdded: '不可添加',
    authorized: '已授权',
    notAuthorized: '未授权',
    added: '已添加',
    uploading: '上传中',
    deleted: '已删除',
    enabled: '启用中',
    disabled: '已禁用',
    yes: '是',
    no: '否',
  },
  fork: {
    datasetTip: '原应用包含私有知识库文档（如下内容）无法被复制，您可手动添加其他文档',
    toolTip: '原应用包含私有工具（如下内容）无法被复制，您可手动添加其他工具',
    singleToolTip: '原应用包含私有工具无法被复制, 您可手动添加其他工具',
    tip: '您好，平台授权有效期至 {{date}}，为保证您的使用，请联系服务商进行重新授权',
    inviteTip: '提示：当前平台处于内测邀请阶段，如想获取邀请码，请联系官方人员。',
  },
  validate: {
    required: '请输入${label}',
    whitespace: '${label}不能为空字符',
    string: {
      min: '${label}长度应至少${min}个字符',
      max: '${label}长度应少于${max}个字符',
      range: '${label}长度应在${min}-${max}个字符之间',
    },
    pattern: '${label}不符合规范',
    fieldRequired: '{{field}} 为必填项',
    emptyError: '{{name}} 不能为空字符',
    rangeError: '{{name}} 必须在{{min}}~{{max}}个字符之间',
    urlError: 'url 应该以 http:// 或 https:// 开头',
  },
  placeholder: {
    input: '请输入{{label}}',
    select: '请选择',
    search: '搜索',
    startDay: '开始日期',
    endDay: '结束日期',
  },
  dateFormat: {
    dateTime: 'YYYY-MM-DD HH:mm:ss',
  },
  unit: {
    char: '个字符',
    characters: '字符',
    paragraphs: '段',
  },
  actionMsg: {
    updateSuccess: '更新成功',
    addedSuccessfully: '添加成功',
    actionSuccess: '操作成功',
    saveSuccessfully: '保存成功',
    modifiedSuccessfully: '修改成功',
    deleteSuccessfully: '删除成功',
    createSuccessfully: '创建成功',
    modifiedUnsuccessfully: '修改失败',
    copySuccessfully: '复制成功',
    volumeAloudTip: '声音过大，建议采取调小。',
    unPublishSuccess: '下架成功',
    enableSuccessfully: '启用成功',
    reprocessSuccessfully: '重新处理成功',
    disableSuccessfully: '禁用成功',
    success: '成功',
    saved: '已保存',
    create: '已创建',
    remove: '已移除',
    addnew: '新对话',
    accountChange: '您的账号信息已变更，请重新登录',
    coypSuccess: '复制成功',
    copyTodownload: '无法复制，已自动下载',
    generatedSuccessfully: '已重新生成',
    generatedUnsuccessfully: '生成失败',
  },
  voice: {
    language: {
      zhHans: '中文',
      zhHant: '繁体中文',
      enUS: '英语',
      deDE: '德语',
      frFR: '法语',
      esES: '西班牙语',
      itIT: '意大利语',
      thTH: '泰语',
      idID: '印尼语',
      jaJP: '日语',
      koKR: '韩语',
      ptBR: '葡萄牙语',
      ruRU: '俄语',
      ukUA: '乌克兰语',
      viVN: '越南语',
      plPL: '波兰语',
      roRO: '罗马尼亚语',
      hiIN: '印地语',
      trTR: '土耳其语',
      faIR: '波斯语',
    },
  },
  voiceInput: {
    speaking: '现在讲...',
    converting: '正在转换为文本...',
    notAllow: '麦克风未授权',
    voiceInput: '语音输入',
    voicePlay: '播放',
    voiceConversation: '语音通话',
    inputConverting: '语音输入中，',
    inputConvertingStop: '点击输入框可确认',
    cancelVoiceInput: '取消语音输入',
    connecting: '连接中...',
    inCall: '通话中…',
    thinking: '思考中…',
    hangup: '结束电话',
    hasImg: '回复内容包含图片，可后续查看',
    error: {
      permissionDenied: '设备需开启权限后可以使用',
      unknown: '异常建议重新尝试',
      asrNoInternet: '网络异常，建议重新尝试',
      asrError: '语音转文字异常，建议重新尝试',
      ttsNoInternet: '网络问题无法阅读，建议重新播放',
      ttsError: '建议重新播放',
      noAppId: '未获取到应用id，无法使用该功能',
    },
  },
  chat: {
    renameConversation: '重命名会话',
    conversationName: '会话名称',
    conversationNamePlaceholder: '请输入会话名称',
    conversationNameCanNotEmpty: '会话名称必填',
    citation: {
      title: '知识来源',
      linkToDataset: '跳转至知识库',
      characters: '字符：',
      hitCount: '命中次数：',
      vectorHash: '向量哈希：',
      hitScore: '命中得分：',
    },
    inputPlaceholder: '输入问题，发送Enter',
    thinking: '深度思考中...',
    thought: '已深度思考',
    tip: '内容由AI生成，无法确保真实准确，仅供参考',
    newChat: '新增对话',
    startChat: '开始对话',
    stopChat: '停止对话',
    resetChat: '重置对话',
    newChatLimit: '请先停止当前对话',
    market: {
      noDatasetTooltip: '此应用包含的私有文档及工具，无法被复制，需要您手动添加',
      datasetTooltip: '创建同款时，自定义工具栏和知识库板块会有内容新增',
      datasetTip: '创建同款时，知识库板块会有知识库新增',
      toolTip: '创建同款时，工具板块会有工具新增',
      createAppBtn: '创作同款',
      introLabel: '应用简介',
      usage: '使用量',
      createSuccessfully: '创建成功',
      createFailed: '创建失败',
    },
    checkFailText: '抱歉，由于回复的内容异常，暂不展示',
    voiceInput: '语音输入',
    voiceConversation: '语音对话',
    cancelVoiceInput: '取消语音输入',
    cancelVoiceConversation: '结束对话',
  },
  promptEditor: {
    placeholder: '',
    context: {
      item: {
        title: '上下文',
        desc: '插入上下文模板',
      },
      modal: {
        title: '有 {{num}} 个知识库在上下文中',
        add: '添加上下文',
        footer: '您可以在下面的“上下文”部分中管理上下文。',
      },
    },
    history: {
      item: {
        title: '会话历史',
        desc: '插入历史消息模板',
      },
      modal: {
        title: '示例',
        user: '你好',
        assistant: '你好！今天我能为您提供什么帮助？',
        edit: '编辑对话角色名称',
      },
    },
    variable: {
      item: {
        title: '变量 & 外部工具',
        desc: '插入变量和外部工具',
      },
      outputToolDisabledItem: {
        title: '变量',
        desc: '插入变量',
      },
      modal: {
        add: '添加新变量',
        addTool: '添加工具',
      },
    },
    query: {
      item: {
        title: '查询内容',
        desc: '插入用户查询模板',
      },
    },
  },
  imageUploader: {
    uploadFromComputer: '从本地上传',
    uploadFromComputerReadError: '图片读取失败，请重新选择。',
    uploadFromComputerUploadError: '图片上传失败，请重新上传。',
    uploadFromComputerLimit: '上传图片不能超过 {{size}} MB',
    pasteImageLink: '粘贴图片链接',
    pasteImageLinkInputPlaceholder: '将图像链接粘贴到此处',
    pasteImageLinkInvalid: '图片链接无效',
    imageUpload: '图片上传',
  },
  fileUploader: {
    uploadFromComputer: '从本地上传',
    pasteFileLink: '粘贴文件链接',
    pasteFileLinkInputPlaceholder: '输入文件链接',
    uploadFromComputerReadError: '文件读取失败，请重新选择。',
    uploadFromComputerUploadError: '文件上传失败，请重新上传。',
    uploadFromComputerLimit: '上传文件不能超过 {{size}}',
    pasteFileLinkInvalid: '文件链接无效',
    fileExtensionNotSupport: '文件类型不支持',
    img: '图片',
    link: '链接',
    file: '文件',
    button: '拖拽文件至此，或者',
    browse: '选择文件',
    tip: '支持 {{supportTypes}}',
    tip2: '每个文件不超过 {{size}}MB，最多同时上传 {{filesNumber}} 个文件',
    validation: {
      typeError: '文件类型不支持',
      size: '文件太大了，不能超过 {{size}}MB',
      count: '暂不支持多个文件',
      filesNumber: '批量上传限制 {{filesNumber}}。',
    },
  },
  db: {
    updateModelConfigFail: '更新模型配置数据失败',
    addModelConfigFail: '新增模型配置数据失败',
    createModelConfigFail: '创建模型配置数据库失败',
    deleteModelConfigFail: '删除模型配置数据失败',
    connectFail: '数据库连接异常',
  },
  uploader: {
    title: '上传文本文件',
    button: '拖拽文件至此，或者',
    browse: '选择文件',
    tip: '支持 {{supportTypes}}',
    tip2: '每个文件不超过 {{size}}MB，最多同时上传 {{filesNumber}} 个文件',
    validation: {
      typeError: '文件类型不支持',
      size: '文件太大了，不能超过 {{size}}MB',
      count: '暂不支持多个文件',
      filesNumber: '批量上传限制 {{filesNumber}}。',
    },
  },
  echarts: {
    echartsError: '图表渲染失败',
    jsonFormatError: 'JSON 格式错误，无法处理图表',
    jsonNotArray: 'JSON 格式错误，请输入图表数组',
  },
  component: {
    empty: {
      text: '暂无数据',
    },
    videoPlayer: {
      playError: '播放失败',
      playErrorDesc: '视频/音频文件格式与播放器不兼容',
      downloadError: '无法打开视频文件：{{url}}',
    },
    avatar: {
      sizeTip: '头像大小应不超过1MB!',
      typeTip: '头像仅支持jpg或png格式',
    },
    image: {
      downloadError: '无法打开图片文件：{{url}}',
    },
  },
  codeVerify: {
    getSms: '获取验证码',
    waitSms: '验证码已发送，有效期5分钟',
    remaingTimeText: '{{time}}秒后重发',
    clickPicture: '请依次点击【{{character}}',
    failed: '验证失败',
  },
  echarts: {
    echartsError: '图表渲染失败',
    jsonFormatError: 'JSON 格式错误，无法处理图表',
    jsonNotArray: 'JSON 格式错误，请输入图表数组',
  },
}

export default translation
