'use client'
import React, { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Divider } from 'antd'
import produce from 'immer'
import AddDatasetModal from '../add-dataset-modal'
import DatasetItem from '../dataset-item'
import SettingsModal from './setting-modal'
import ParamsConfig from './params-config'

import type { DataSet } from '@/models/datasets'
import type { DatasetConfigs } from '@/models/debug'

// 公共组件
import { Add } from '@/app/components/base/icons/src/vender/solid/general'
import FeatureCollapse from '@/app/components/base/features/feature-collapse'
import ForkTip from '@/app/components/base/forkTip'
import TextButton from '@/app/components/base/button/text-button'
import { Delete, Edit } from '@/app/components/base/icons/src/vender/line/general'

type AddDatasetPanelProps = {
  dataSets: DataSet[]
  config: DatasetConfigs
  scene: 'app' | 'workflow'
  onRemove: (id: string) => void
  onSelect: (dataSet: DataSet[]) => void
  onConfig: (dataSet: DatasetConfigs) => void
}
type AddDatasetPanelConetntProps = {
  dataSets: DataSet[]
  forkDatasets: DataSet[]
  config: DatasetConfigs
  scene: 'app' | 'workflow'
  border?: boolean
  onRemove: (id: string) => void
  onConfig: (dataSet: DatasetConfigs) => void
  onAdd: () => void
  onCloseFock: () => void
  onSetting: (dataSet: DataSet) => void
}

// 面板内容
export const AddDatasetPanelConetnt = React.memo(({
  dataSets,
  forkDatasets,
  config,
  scene,
  border = false,
  onRemove,
  onConfig,
  onAdd,
  onCloseFock,
  onSetting,
}: AddDatasetPanelConetntProps) => {
  const { t } = useTranslation()

  // 是否是工作流模式
  const isWorkflow = scene === 'workflow'
  // 是否为无效知识库
  const isFork = config.fork
  // 适配已删除的知识库信息
  const allDatasets = useMemo(() => {
    return config.datasets?.datasets.map((item) => {
      const findDataset = dataSets.find(dataset => dataset.id === item.id || (dataset.old_id === item.id))
      if (findDataset)
        return { ...findDataset, lack: false }
      else
        return { ...item, lack: true }
    }) || []
  }, [config.datasets?.datasets, dataSets])
  // 是否存在知识库数据
  const hasData = allDatasets.length > 0

  return (
    <>
      <FeatureCollapse
        title={t('dataset.title')}
        tooltip={t('dataset.notify.addTip')}
        headerRight={
          <>
            <div className='flex items-center gap-2'>
              {<ParamsConfig config={config} onChange={onConfig} disabled={!hasData} selectedDatasets={dataSets} />}
              <Divider type='vertical' className='!mx-0'></Divider>
              {<Add onClick={onAdd} className='text-[#5C6273] cursor-pointer'></Add>}
            </div>
          </>
        }
        useSwitch={false}
        inWorkflow={isWorkflow}
      >
        {forkDatasets.length > 0 && isFork && (
          <ForkTip
            className='forkTip mx-4 mb-2'
            message={t('common.fork.datasetTip')}
            onClose={onCloseFock}
          >
            <div>
              {forkDatasets.map(({ id, name }) => (
                <div key={id} className=''>
                  <div className=''>
                        · {name}
                  </div>
                </div>
              ))}
            </div>
          </ForkTip>
        )}
        {hasData && (
          <div className='flex flex-wrap justify-between'>
            {(allDatasets as DataSet[]).map(item => (
              <DatasetItem
                key={item.id}
                name={item.name}
                id={item.id}
                lack={item.lack}
                operations={
                  <>
                    { !item.lack
                    && <TextButton onClick={() => onSetting(item)} variant='text'>
                      <Edit className='w-4 h-4' />
                    </TextButton>
                    }
                    <TextButton variant='text' onClick={() => onRemove(item.id)}>
                      <Delete className='w-4 h-4' />
                    </TextButton>
                  </>
                }
                border={border}
              ></DatasetItem>
            ))}
          </div>
        )}
      </FeatureCollapse>
    </>
  )
})
AddDatasetPanelConetnt.displayName = 'AddDatasetPanelConetnt'

const AddDatasetPanel = ({
  dataSets,
  config,
  scene,
  onRemove,
  onSelect,
  onConfig,
}: AddDatasetPanelProps) => {
  // 是否显示选择知识库弹窗
  const [showSelectDataSet, setShowSelectDataSet] = useState(false)
  // 是否显示设置知识库弹窗
  const [showSettingDataSet, setShowSettingDataSet] = useState(false)
  // 选中的知识库
  const [selectDataset, setSelectDataset] = useState<DataSet>()
  // 知识库列表
  const [datasetList, setDatasetList] = useState<DataSet[]>([])
  // 无效知识库
  const [forkDatasets, setForkDatasets] = useState<DataSet[]>([])

  // 是否为无效知识库
  const isFork = config.fork

  // 编辑知识库列表
  const handleEditDataset = (dataSet: DataSet) => {
    const newInputs = produce(datasetList, (draft) => {
      const index = draft.findIndex(item => item.id === dataSet.id)
      draft[index] = dataSet
    })
    setDatasetList(newInputs)
    setShowSettingDataSet(false)
  }
  // 删除知识库列表
  const handleRemoveDataset = (id: string) => {
    const newInputs = produce(datasetList, (draft) => {
      const index = draft.findIndex(item => item.id === id)
      draft.splice(index, 1)
    })
    setDatasetList(newInputs)
    onRemove(id)
  }
  // 添加知识库
  const handleAddDataset = (value: DataSet[]) => {
    setDatasetList(value)
    onSelect(value)
  }

  useEffect(() => {
    if (isFork)
      setForkDatasets(dataSets)
    else
      setDatasetList(dataSets)
  }, [dataSets, isFork])

  return (
    <>
      <AddDatasetPanelConetnt
        dataSets={datasetList}
        forkDatasets={forkDatasets}
        config={config}
        scene={scene}
        onRemove={handleRemoveDataset}
        onConfig={onConfig}
        onCloseFock={() => onSelect([])}
        onAdd={() => setShowSelectDataSet(true)}
        onSetting={(value) => {
          setSelectDataset(value)
          setShowSettingDataSet(true)
        }}
      ></AddDatasetPanelConetnt>
      {/* 选择知识库弹窗 */}
      {
        showSelectDataSet && <AddDatasetModal
          isShow={true}
          value={datasetList}
          onClose={() => setShowSelectDataSet(false)}
          onSelect={handleAddDataset}
        ></AddDatasetModal>
      }
      {/* 编辑知识库弹窗 */}
      {
        showSettingDataSet && <SettingsModal
          onSave={handleEditDataset}
          onCancel={() => setShowSettingDataSet(false)}
          isShowSettingsModal={true}
          currentDataset={selectDataset!}
        ></SettingsModal>
      }
    </>
  )
}
export default React.memo(AddDatasetPanel)
