'use client'

import type { MenuItemGroupType, MenuItemType } from 'antd/es/menu/interface'
import { useSearchParams, useSelectedLayoutSegments } from 'next/navigation'
import type { Dispatch, FC, ReactNode, SetStateAction } from 'react'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { createContext, useContext } from 'use-context-selector'
import { useAppContext } from './app-context'
import { useSystemContext } from './system-context'
import { AlgorithmAuthActiveIcon, AlgorithmAuthIcon, AlgorithmManageActiveIcon, AlgorithmManageIcon, AppActiveIcon, AppIcon, AppSquareActiveIcon, AppSquareIcon, DataStatisticsActiveIcon, DataStatisticsIcon, DatasetActiveIcon, DatasetIcon, ModelCustomIcon, ModelDatasetsIcon, ModelReasonIcon, ModelsActiveIcon, ModelsIcon, ResourceActiveIcon, ResourceIcon, ToolActiveIcon, ToolIcon, VideosActiveIcon, VideosIcon } from '@/app/components/base/icons/src/vender/navbar'
import { generateMicroPath } from '@/utils/micro'

enum BackgroundType {
  primary = 'primary-bg',
  white = 'white-bg',
  secondary = 'secondary-bg',
}
type LayoutContextType = {
  // 是否展示菜单
  showMenu: boolean
  // 是否显示顶部菜单
  showTopMenu: boolean
  // 当前菜单
  currentMenu: string
  // 菜单
  menus: MenuItemGroupType[]
  // 顶部菜单
  topMenus: MenuItemType[]
  // 当前背景
  background: BackgroundType
  // 设置顶部菜单
  setTopMenus: Dispatch<SetStateAction<MenuItemType[]>>
  // 全局加载
  appLoading: boolean
  // 设置全局加载
  setAppLoading: Dispatch<SetStateAction<boolean>>
}
type LayoutContextProviderProps = {
  children: ReactNode
}

// 二级页背景路径
const SECONDARY_BG_PATH = ['datasets']
// 白底背景路径
const WHITE_BG_PATH = ['app']

// 应用实例前缀
const APP_KEYS = ['apps', 'app']
// 工具实例前缀
const TOOL_KEYS = ['tools']
// 知识库实例前缀
const DATASET_KEYS = ['datasets']
// 视频库实例缀
const VIDEOS_KEYS = ['videos']
// 应用市场实例前缀
const APP_SQUARE_KEYS = ['square']
// 数据统计实例前缀
const DATA_STATISTICS_KEYS = ['data-statistics']
// 模型库实录前缀
const CSGHUB_MODELS_KEYS = [generateMicroPath('modelDetails'), generateMicroPath('models')]
// 数据集实例前缀
const CSGHUB_DATASETS_KEYS = [generateMicroPath('datasetDetails'), generateMicroPath('datasets')]
// 在线标注实例前缀
const CSGHUB_DATASETS_LABEL_KEYS = [generateMicroPath('datasetLabel'), generateMicroPath('dataLabel'), generateMicroPath('dataManage'), generateMicroPath('newDataLabel')]
// 我的数据集实例前缀
const CSGHUB_MYDATASETS_KEYS = [generateMicroPath('myDatasets')]
// 我的模型实例前缀
const CSGHUB_MYMODELS_KEYS = [generateMicroPath('modeIfomation'), generateMicroPath('myModels')]
// 模型精调实例前缀
const CSGHUB_TASKS_KEYS = [generateMicroPath('taskDetail'), generateMicroPath('tasks')]
// 模型服务实例前缀
const CSGHUB_INFERENCE_KEYS = [generateMicroPath('inferencesDetails'), generateMicroPath('inferences')]
// 批量预测实例前缀
const CSGHUB_BATCH_KEYS = [generateMicroPath('predictionDetails'), generateMicroPath('prediction')]
// 算法授权实例前缀
const CSGHUB_ALGO_MANAGE_KEYS = [generateMicroPath('algoManageDetails'), generateMicroPath('algo-management')]

// 显示侧边栏实例前缀
const SHOW_NAVBAR_KEYS = ['apps', 'app-publish-review-management', 'tools', 'datasets', 'account-setting', 'square', 'train', 'data-statistics', 'videos']
// 不显示侧边栏地址
const NOT_SHOW_NAVBAR_KEYS = [
  generateMicroPath('newInferences'),
  generateMicroPath('newTask'),
  generateMicroPath('newPrediction'),
]

export const LayoutContext = createContext<LayoutContextType>({
  showMenu: true,
  showTopMenu: false,
  background: BackgroundType.primary,
  currentMenu: '',
  menus: [],
  topMenus: [],
  appLoading: false,
  setTopMenus: () => {},
  setAppLoading: () => {},
})

const LayoutContextProvider: FC<LayoutContextProviderProps> = ({ children }) => {
  const { t } = useTranslation()
  const selectedSegment = useSelectedLayoutSegments()
  const searchParams = useSearchParams()
  const { isPrivate } = useSystemContext()
  const { isMicroApp, isSuperUser, canViewVideoLib } = useAppContext()
  // 当前应用背景
  const [background, setBackground] = useState(BackgroundType.primary)
  // 是否显示菜单
  const [showMenu, setShowMenu] = useState(true)
  // 是否展示顶部菜单
  const [showTopMenu, setShowTopMenu] = useState(false)
  // 当前菜单
  const [currentMenu, setCurrentMenu] = useState('')
  // 菜单信息
  const [menus, setMenus] = useState<MenuItemGroupType[]>([])
  // 顶部菜单
  const [topMenus, setTopMenus] = useState<MenuItemType[]>([])
  // 全局加载
  const [appLoading, setAppLoading] = useState(false)

  useEffect(() => {
    // 菜单项配置
    if (APP_KEYS.includes(selectedSegment[0])) {
      setCurrentMenu('/apps')
    }
    else if (TOOL_KEYS.includes(selectedSegment[0])) {
      setCurrentMenu('/tools')
    }
    else if (DATASET_KEYS.includes(selectedSegment[0])) {
      setCurrentMenu('/datasets')
    }
    else if (VIDEOS_KEYS.includes(selectedSegment[0])) {
      setCurrentMenu('/videos')
    }
    else if (APP_SQUARE_KEYS.includes(selectedSegment[0])) {
      setCurrentMenu('/square')
    }
    else if (DATA_STATISTICS_KEYS.includes(selectedSegment[0])) {
      setCurrentMenu('/data-statistics')
    }
    else {
      if (isMicroApp) {
        if (CSGHUB_MODELS_KEYS.some(key => `/${selectedSegment[0]}?${searchParams}`.includes(key)))
          setCurrentMenu(generateMicroPath('models'))
        else if (CSGHUB_DATASETS_KEYS.some(key => `/${selectedSegment[0]}?${searchParams}`.includes(key)))
          setCurrentMenu(generateMicroPath('datasets'))
        else if (CSGHUB_DATASETS_LABEL_KEYS.some(key => `/${selectedSegment[0]}?${searchParams}`.includes(key)))
          setCurrentMenu(generateMicroPath('datasetLabel'))
        else if (CSGHUB_MYDATASETS_KEYS.some(key => `/${selectedSegment[0]}?${searchParams}`.includes(key)))
          setCurrentMenu(generateMicroPath('myDatasets'))
        else if (CSGHUB_MYMODELS_KEYS.some(key => `/${selectedSegment[0]}?${searchParams}`.includes(key)))
          setCurrentMenu(generateMicroPath('myModels'))
        else if (CSGHUB_TASKS_KEYS.some(key => `/${selectedSegment[0]}?${searchParams}`.includes(key)))
          setCurrentMenu(generateMicroPath('tasks'))
        else if (CSGHUB_INFERENCE_KEYS.some(key => `/${selectedSegment[0]}?${searchParams}`.includes(key)))
          setCurrentMenu(generateMicroPath('inferences'))
        else if (CSGHUB_BATCH_KEYS.some(key => `/${selectedSegment[0]}?${searchParams}`.includes(key)))
          setCurrentMenu(generateMicroPath('prediction'))
        else if (CSGHUB_ALGO_MANAGE_KEYS.some(key => `/${selectedSegment[0]}?${searchParams}`.includes(key)))
          setCurrentMenu(generateMicroPath('algo-management'))
        else
          setCurrentMenu(`/${selectedSegment[0]}?${searchParams}`)
      }
      else {
        setCurrentMenu(selectedSegment[0])
      }
    }
  }, [isMicroApp, searchParams, selectedSegment])
  useEffect(() => {
    // 菜单是否展示配置
    setShowMenu(!NOT_SHOW_NAVBAR_KEYS.some(path => currentMenu.includes(path)) && SHOW_NAVBAR_KEYS.includes(selectedSegment.join('/')))
    // 背景色配置
    if (selectedSegment.length > 1 && SECONDARY_BG_PATH.includes(selectedSegment[0]))
      setBackground(BackgroundType.primary)
    else if (selectedSegment.length > 1 && WHITE_BG_PATH.includes(selectedSegment[0]))
      setBackground(BackgroundType.white)
    else
      setBackground(BackgroundType.primary)
  }, [selectedSegment])
  useEffect(() => {
    // 菜单配置
    setMenus([{
      key: 'explore',
      label: t('menus.explore'),
      type: 'group',
      children: [
        {
          key: '/square',
          label: t('menus.appMarket') as string,
          icon: currentMenu === '/square' ? <AppSquareActiveIcon /> : <AppSquareIcon />,
        },
        ...(isMicroApp
          ? [{
            key: generateMicroPath('models'),
            label: t('menus.csghub.models') as string,
            icon: currentMenu === generateMicroPath('models') ? <ModelsActiveIcon /> : <ModelsIcon/>,
          }]
          : []),
      ],
    }, {
      key: 'my',
      label: t('menus.appDevelop'),
      type: 'group',
      children: [
        {
          key: '/apps',
          label: t('menus.apps'),
          icon: currentMenu === '/apps' ? <AppActiveIcon /> : <AppIcon />,
        }, {
          key: '/datasets',
          label: t('menus.datasets'),
          icon: currentMenu === '/datasets' ? <DatasetActiveIcon /> : <DatasetIcon />,
        },
        ...(canViewVideoLib
          ? [
            {
              key: '/videos',
              label: t('menus.videos'),
              icon: currentMenu === '/videos' ? <VideosActiveIcon /> : <VideosIcon />,
            },
          ]
          : [
          ]),
        {
          key: '/tools',
          label: t('menus.tools'),
          icon: currentMenu === '/tools' ? <ToolActiveIcon /> : <ToolIcon />,
        },
        /* {
          key: '/data-statistics',
          label: t('menus.dataStatistics'),
          icon: currentMenu === '/data-statistics' ? <DataStatisticsActiveIcon /> : <DataStatisticsIcon />,
        }, */
      ],
    },
    ...(isMicroApp
      ? [{
        key: 'modelDevelop',
        label: t('menus.modelDevelop'),
        type: 'group' as any,
        children: [
          {
            key: 'datasets',
            label: t('menus.csghub.datasets') as string,
            icon: <ModelDatasetsIcon />,
            children: [
              {
                key: generateMicroPath('datasets'),
                label: t('menus.csghub.publickDatasets') as string,
              },
              {
                key: generateMicroPath('myDatasets'),
                label: t('menus.csghub.myDatasets') as string,
              },
              {
                key: generateMicroPath('datasetLabel'),
                label: t('menus.csghub.datasetLabel') as string,
              },
            ],
          },
          {
            key: 'model-custom',
            label: t('menus.csghub.modelCustom'),
            icon: <ModelCustomIcon></ModelCustomIcon>,
            children: [
              {
                key: generateMicroPath('tasks'),
                label: t('menus.csghub.tasks') as string,
              },
              {
                key: generateMicroPath('myModels'),
                label: t('menus.csghub.myModels') as string,
              },
            ],
          },
          {
            key: 'model-reason',
            label: t('menus.csghub.modelReason'),
            icon: <ModelReasonIcon></ModelReasonIcon>,
            children: [
              {
                key: generateMicroPath('inferences'),
                label: t('menus.csghub.inferences') as string,
              },
              {
                key: generateMicroPath('prediction'),
                label: t('menus.csghub.batch') as string,
              },
            ],
          },
        ],
      },
      ...(isSuperUser
        ? [
          {
            key: 'systemTool',
            label: t('menus.systemTool'),
            type: 'group' as any,
            children: [
              ...(isPrivate
                ? []
                : [{
                  key: generateMicroPath('algo-management'),
                  label: t('menus.csghub.algorithmManage') as string,
                  icon: currentMenu === generateMicroPath('algo-management') ? <AlgorithmManageActiveIcon /> : <AlgorithmManageIcon/>,
                }]),
              {
                key: generateMicroPath('algo-accredit'),
                label: t('menus.csghub.algorithmAuth') as string,
                icon: currentMenu === generateMicroPath('algo-accredit') ? <AlgorithmAuthActiveIcon /> : <AlgorithmAuthIcon/>,
              },
              {
                key: generateMicroPath('resource'),
                label: t('menus.csghub.resource') as string,
                icon: currentMenu === generateMicroPath('resource') ? <ResourceActiveIcon /> : <ResourceIcon/>,
              },
            ],
          },
        ]
        : []),
      ]
      : []),
    ])
  }
  , [currentMenu, isSuperUser, isMicroApp, t])

  return (
    <LayoutContext.Provider
      value={{
        showMenu,
        showTopMenu,
        menus,
        topMenus,
        setTopMenus,
        currentMenu,
        background,
        appLoading,
        setAppLoading,
      }}
    >
      {children}
    </LayoutContext.Provider>
  )
}

export const useLayoutContext = () => useContext(LayoutContext)

export default LayoutContextProvider
