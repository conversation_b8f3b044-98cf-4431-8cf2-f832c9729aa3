'use client'
import type { FC } from 'react'
import React, { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import type { RadioChangeEvent } from 'antd'
import { Form, Input, Radio } from 'antd'
import Tooltip from '@/app/components/base/tooltip'
import type { Credential } from '@/app/components/tools/types'
import { AuthHeaderPrefix, AuthType } from '@/app/components/tools/types'

type Props = {
  credential: Credential
  onChange: (credential: Credential) => void
}

const GetCredential: FC<Props> = ({
  credential,
  onChange,
}) => {
  const { t } = useTranslation()
  const [form] = Form.useForm()
  const [tempCredential, setTempCredential] = React.useState<Credential>(credential)

  useEffect(() => {
    setTempCredential(credential)
  }, [credential])

  // 变更验证方法
  const changeAuthMethod = (e: RadioChangeEvent) => {
    const value = e.target.value
    let data: any = null
    if (value === AuthType.none)
      data = { ...tempCredential, auth_type: value as AuthType }
    if (value === AuthType.apiKey) {
      data = {
        ...tempCredential,
        auth_type: value as AuthType,
        api_key_header: tempCredential.api_key_header || 'Authorization',
        api_key_value: tempCredential.api_key_value || '',
        api_key_header_prefix: tempCredential.api_key_header_prefix || AuthHeaderPrefix.custom,
      }
    }
    setTempCredential(data)
    onChange(data)
  }
  // 变更验证头部
  const changeValue = (key: string, value: string) => {
    const data = { ...tempCredential, [key]: value as AuthHeaderPrefix }
    setTempCredential(data)
    onChange(data)
  }

  return (
    <Form form={form} layout='vertical'>
      <Form.Item label={t('tools.info.authMethod.type')}>
        <Radio.Group value={tempCredential.auth_type} onChange={changeAuthMethod}>
          <Radio value={AuthType.none}>
            {t('tools.info.authMethod.types.none')}
          </Radio>
          <Radio value={AuthType.apiKey}>{t('tools.info.authMethod.types.api_key')}</Radio>
        </Radio.Group>
      </Form.Item>
      {tempCredential.auth_type === AuthType.apiKey && (
        <>
          <Form.Item label={t('tools.info.authHeaderPrefix.title')}>
            <Radio.Group value={tempCredential.api_key_header_prefix} onChange={e => changeValue('api_key_header_prefix', e.target.value)}>
              <Radio value={AuthHeaderPrefix.basic}>{t('tools.info.authHeaderPrefix.types.basic')}</Radio>
              <Radio value={AuthHeaderPrefix.bearer}>{t('tools.info.authHeaderPrefix.types.bearer')}</Radio>
              <Radio value={AuthHeaderPrefix.custom}>{t('tools.info.authHeaderPrefix.types.custom')}</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item label={
            <>
              {t('tools.info.authMethod.key')}
              <Tooltip
                popupContent={
                  <div className='w-[261px] text-gray-500'>
                    {t('tools.info.authMethod.keyTooltip')}
                  </div>
                }
                triggerClassName='ml-0.5 w-4 h-4'
              />
            </>
          }>
            <Input
              value={tempCredential.api_key_header}
              onChange={e => changeValue('api_key_header', e.target.value)}
              placeholder={t('tools.info.authMethod.types.apiKeyPlaceholder')!}
            />
          </Form.Item>
          <Form.Item label={t('tools.info.authMethod.value')}>
            <Input
              value={tempCredential.api_key_value || ''}
              onChange={e => changeValue('api_key_value', e.target.value)}
              placeholder={t('tools.info.authMethod.types.apiValuePlaceholder')!}
            />
          </Form.Item>
        </>)}
    </Form>
  )
}
export default React.memo(GetCredential)
