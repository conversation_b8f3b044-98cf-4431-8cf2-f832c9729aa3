'use client'
import type { FC } from 'react'
import React, { useEffect, useMemo, useRef, useState } from 'react'
import useSWR from 'swr'
import {
  HandThumbDownIcon,
  HandThumbUpIcon,
} from '@heroicons/react/24/outline'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import { createContext, useContext } from 'use-context-selector'
import { useShallow } from 'zustand/react/shallow'
import { useTranslation } from 'react-i18next'
import type { TableColumnsType, TableProps } from 'antd'
import { Divider, Table } from 'antd'
import { debounce } from 'lodash-es'

import { datasetTypeList } from '../data-reflux/type'
import s from './style.module.css'
import VarPanel from './var-panel'
import { ChatSourceEnum } from './type'
import cn from '@/utils/classnames'
import type { ChatConversationGeneralDetail, ChatConversationsResponse, ChatMessage, ChatMessagesRequest } from '@/models/log'
import type { App } from '@/types/app'
import { fetchChatConversationDetail, fetchChatMessages, updateLogMessageFeedbacks } from '@/service/log'
import { addFileInfos, sortAgentSorts } from '@/app/components/tools/utils'
import { useStore as useAppStore } from '@/app/components/app/store'
import { useAppContext } from '@/context/app-context'
import useTimestamp from '@/hooks/use-timestamp'
import { fetchAppChatBgConfig } from '@/service/apps'
import { getCropperFontCss } from '@/app/components/app/app-bg-cropper/utils'

// 公共组件
import type { FeedbackFunc, FeedbackType, IChatItem } from '@/app/components/base/chat/chat/type'
import { ToastContext } from '@/app/components/base/toast'
import CopyBtn from '@/app/components/base/button/copy-button'
import Drawer from '@/app/components/base/drawer'
import Chat from '@/app/components/base/chat/chat'
import type { ScrollbarRef } from '@/app/components/base/scrollbar'
import Scrollbar from '@/app/components/base/scrollbar'
import MessageLogModal from '@/app/components/base/message-log-modal'
import Indicator from '@/app/components/base/indicator'
import { getProcessedFilesFromResponse } from '@/app/components/base/file-uploader/utils'
import type { AppChatBgConfig } from '@/app/components/base/features/types'
import CropperWrapper from '@/app/components/base/cropper/wrapper'
import { Close } from '@/app/components/base/icons/src/vender/line/general'
import Tooltip from '@/app/components/base/tooltip'

dayjs.extend(utc)
dayjs.extend(timezone)

type IConversationList = {
  logs?: ChatConversationsResponse
  appDetail: App
  loading: boolean
  onRefresh: () => void
  selectedRowKeys: React.Key[]
  onSelect: (selectedRowKeys: React.Key[], selectedRows: ChatConversationGeneralDetail[]) => void
}

const defaultValue = '-'

type IDrawerContext = {
  onClose: () => void
  appDetail?: App
}

const DrawerContext = createContext<IDrawerContext>({} as IDrawerContext)

/**
 * Icon component with numbers
 */
const HandThumbIconWithCount: FC<{ count: number; iconType: 'up' | 'down' }> = ({
  count,
  iconType,
}) => {
  const classname = iconType === 'up' ? 'text-primary-600 bg-primary-50' : 'text-red-600 bg-red-50'
  const Icon = iconType === 'up' ? HandThumbUpIcon : HandThumbDownIcon
  return (
    <div
      className={`inline-flex items-center w-fit rounded-md p-1 text-xs ${classname} mr-1 last:mr-0`}
    >
      <Icon className={'h-3 w-3 mr-0.5 rounded-md'} />
      {count > 0 ? count : null}
    </div>
  )
}

// Format interface data for easy display
const getFormattedChatList = (
  messages: ChatMessage[],
  conversationId: string,
  timezone: string,
  format: string,
) => {
  const newChatList: IChatItem[] = []
  messages.forEach((item: ChatMessage) => {
    const answerFiles = item.message_files?.filter((file: any) => file.belongs_to === 'assistant') || []
    const questionFiles = item.message_files?.filter((file: any) => file.belongs_to === 'user') || []
    newChatList.push({
      id: item.id,
      content: item.answer,
      agent_thoughts: addFileInfos(
        item.agent_thoughts ? sortAgentSorts(item.agent_thoughts) : item.agent_thoughts,
        item.message_files,
      ),
      feedback: item.feedbacks.find(item => item.from_source === 'user'), // user feedback
      adminFeedback: item.feedbacks.find(item => item.from_source === 'admin'), // admin feedback
      feedbackDisabled: false,
      isAnswer: true,
      message_files: getProcessedFilesFromResponse(answerFiles.map((item: any) => ({ ...item, related_id: item.id }))),
      log: [
        ...item.message,
        ...(item.message[item.message.length - 1]?.role !== 'assistant'
          ? [
            {
              role: 'assistant',
              text: item.answer,
              files: getProcessedFilesFromResponse(answerFiles.map((item: any) => ({ ...item, related_id: item.id }))),
            },
          ]
          : []),
      ],
      workflow_run_id: item.workflow_run_id,
      conversationId,
      input: {
        inputs: item.inputs,
        query: item.query,
      },
      more: {
        time: dayjs.unix(item.created_at).tz(timezone).format(format),
        tokens: item.answer_tokens + item.message_tokens,
        latency: item.provider_response_latency.toFixed(2),
      },
      citation: item.metadata?.retriever_resources,
      annotation: (() => {
        if (item.annotation_hit_history) {
          return {
            id: item.annotation_hit_history.annotation_id,
            authorName: item.annotation_hit_history.annotation_create_account?.name || '-',
            created_at: item.annotation_hit_history.created_at,
          }
        }

        if (item.annotation) {
          return {
            id: item.annotation.id,
            authorName: item.annotation.account.name,
            logAnnotation: item.annotation,
            created_at: 0,
          }
        }

        return undefined
      })(),
    })
    newChatList.push({
      id: `question-${item.id}`,
      content: item.inputs.query || item.inputs.default_input || item.query, // text generation: item.inputs.query; chat: item.query
      isAnswer: false,
      message_files: getProcessedFilesFromResponse(questionFiles.map((item: any) => ({ ...item, related_id: item.id }))),
    })
  })
  // 接口返回的聊天对话顺序反了，上面的提问与对话顺序也需要调整
  return newChatList.reverse()
}

type IDetailPanel = {
  detail: any
  chatBgConfig?: AppChatBgConfig
  onFeedback: FeedbackFunc
}

function DetailPanel({ detail, onFeedback, chatBgConfig }: IDetailPanel) {
  const { userProfile: { timezone } } = useAppContext()
  const { onClose, appDetail } = useContext(DrawerContext)
  const {
    currentLogItem,
    setCurrentLogItem,
    showMessageLogModal,
    setShowMessageLogModal,
    currentLogModalActiveTab,
  } = useAppStore(
    useShallow(state => ({
      currentLogItem: state.currentLogItem,
      setCurrentLogItem: state.setCurrentLogItem,
      showMessageLogModal: state.showMessageLogModal,
      setShowMessageLogModal: state.setShowMessageLogModal,
      currentLogModalActiveTab: state.currentLogModalActiveTab,
    })),
  )
  const { t } = useTranslation()
  const [items, setItems] = React.useState<IChatItem[]>([])
  // 是否还有更多聊天记录
  const [hasMore, setHasMore] = useState(true)
  const [varValues, setVarValues] = useState<Record<string, string>>({})
  const scrollRef = useRef<ScrollbarRef>(null)
  const isChatMode = appDetail?.mode !== 'completion'
  const message_files = (!isChatMode && detail.message.message_files && detail.message.message_files.length > 0)
    ? detail.message.message_files.map((item: any) => item.url)
    : []

  const [width, setWidth] = useState(0)
  const ref = useRef<HTMLDivElement>(null)
  // 第一风格字体颜色
  const firstColorCss = useMemo(() => {
    return chatBgConfig?.enabled ? ((chatBgConfig.fontColor === 'black' || chatBgConfig.fontColor === undefined) ? 'text-gray-G3' : 'text-gray-G4') : 'text-gray-G3'
  }, [chatBgConfig])
  // 第二风格字体颜色
  const secondColorCss = useMemo(() => {
    return chatBgConfig?.enabled ? ((chatBgConfig.fontColor === 'black' || chatBgConfig.fontColor === undefined) ? 'text-gray-G2' : 'text-gray-G5') : 'text-gray-G2'
  }, [chatBgConfig])
  // 变量列表
  const varList = (detail.model_config as any).user_input_form?.map((item: any) => {
    const itemContent = item[Object.keys(item)[0]]
    return {
      label: itemContent.variable,
      value: varValues[itemContent.variable] || detail.message?.inputs?.[itemContent.variable],
    }
  }) || []

  // 获取聊天详情
  const fetchData = async () => {
    try {
      if (!hasMore)
        return
      const params: ChatMessagesRequest = {
        conversation_id: detail.id,
        limit: 10,
      }
      if (items?.[0]?.id)
        params.first_id = items?.[0]?.id.replace('question-', '')

      const messageRes = await fetchChatMessages({
        url: `/apps/${appDetail?.id}/chat-messages`,
        params,
      })
      if (messageRes.data.length > 0) {
        const varValues = messageRes.data[0].inputs
        setVarValues(varValues)
      }
      const newItems = [...getFormattedChatList(messageRes.data, detail.id, timezone!, t('appLog.dateTimeFormat') as string), ...items]
      // 没有更多的时候，插入自我介绍？
      if (messageRes.has_more === false && detail?.model_config?.configs?.introduction) {
        newItems.unshift({
          id: 'introduction',
          isAnswer: true,
          isOpeningStatement: true,
          content: detail?.model_config?.configs?.introduction ?? 'hello',
          feedbackDisabled: true,
        })
      }
      setItems(newItems)
      setHasMore(messageRes.has_more)
    }
    catch (err) {
      console.error(err)
    }
  }
  // 工具函数————start
  const onScrollY = debounce(async () => {
    const container = scrollRef.current?.getContainer()
    if (container) {
      const { scrollTop, clientHeight, scrollHeight } = container
      if (scrollHeight - 100 <= scrollTop + clientHeight)
        await fetchData()
    }
  }, 200)
  const adjustModalWidth = () => {
    if (ref.current)
      setWidth(document.body.clientWidth - (ref.current?.clientWidth + 16) - 8)
  }
  // 工具函数————end

  useEffect(() => {
    adjustModalWidth()
  }, [])
  useEffect(() => {
    if (appDetail?.id && detail.id && appDetail?.mode !== 'completion')
      fetchData()
  }, [appDetail?.id, detail.id, appDetail?.mode])

  return (
    <>
      <CropperWrapper
        ref={ref}
        config={chatBgConfig?.enabled ? chatBgConfig.narrow : undefined}
        className={cn(s.appDebugWrap)}
        header={
          <>
            {/* Panel Header */}
            <div className='py-4 px-6 flex items-center justify-between'>
              {/* 左侧部分 */}
              <div className='flex items-center w-full text-S3 leading-H3'>
                <div className={firstColorCss}>{t('appLog.detail.conversationId')}：</div>
                <div title={detail.id} className={cn('truncate mr-1', secondColorCss)}>{detail.id}</div>
                <CopyBtn className={secondColorCss} value={detail.id} />
              </div>
              {/* 关闭按钮 */}
              <Close className={cn('w-4 h-4  cursor-pointer', secondColorCss)} onClick={onClose} />
            </div>
            <div className='px-6'>
              <Divider className='!my-0' type='horizontal'></Divider>
            </div>
            {/* Panel Body */}
            {(varList.length > 0 || (!isChatMode && message_files.length > 0)) && (
              <div className='px-6 pt-4 pb-2'>
                <VarPanel
                  varList={varList}
                  message_files={message_files}
                />
              </div>
            )}
          </>
        }
      >
        {/* 聊天信息 */}
        <Scrollbar
          onScrollY={onScrollY}
          ref={scrollRef}
        >
          <Chat
            className='!h-auto'
            answerIcon={appDetail?.icon_url || ''}
            config={{
              appId: appDetail?.id,
              text_to_speech: {
                enabled: true,
              },
              supportAnnotation: true,
              annotation_reply: {
                enabled: true,
              },
              supportFeedback: true,
            } as any}
            chatList={items}
            onFeedback={onFeedback}
            noChatInput
            showPromptLog
            hideProcessDetail
            chatContainerClassName='!h-auto mx-auto w-full max-w-[800px]'
            chatContainerInnerClassName={cn('px-6', getCropperFontCss(chatBgConfig))}
          />
        </Scrollbar>
      </CropperWrapper>
      {showMessageLogModal && (
        <MessageLogModal
          width={width}
          currentLogItem={currentLogItem}
          onCancel={() => {
            setCurrentLogItem()
            setShowMessageLogModal(false)
          }}
          defaultTab={currentLogModalActiveTab}
        />
      )}
    </>
  )
}

// 对话详情包裹
const ChatConversationDetailComp: FC<{ appId?: string; conversationId?: string }> = ({ appId, conversationId }) => {
  const { notify } = useContext(ToastContext)
  const { t } = useTranslation()

  // 通过应用id和对话id获取详情
  const detailParams = { url: `/apps/${appId}/chat-conversations/${conversationId}` }
  const { data: conversationDetail } = useSWR(() => (appId && conversationId) ? detailParams : null, fetchChatConversationDetail)
  // 获取背景图配置
  const { data: chatBgConfig } = useSWR(() => ({ appId: appId! }), fetchAppChatBgConfig, { revalidateOnFocus: false })

  // 更新feedback
  // const handleFeedback = async (mid: string, { rating }: FeedbackType): Promise<boolean> => {
  const handleFeedback = async (mid: string, bodyValue: FeedbackType): Promise<boolean> => {
    const body = {
      ...bodyValue,
      message_id: mid,
    }
    try {
      await updateLogMessageFeedbacks({
        url: `/apps/${appId}/feedbacks`,
        body,
      })
      notify({ type: 'success', message: t('common.actionMsg.modifiedSuccessfully') })
      return true
    }
    catch (err) {
      notify({ type: 'error', message: t('common.actionMsg.modifiedUnsuccessfully') })
      return false
    }
  }

  if (!conversationDetail)
    return null

  return <DetailPanel
    detail={conversationDetail}
    onFeedback={handleFeedback}
    chatBgConfig={chatBgConfig?.data as AppChatBgConfig}
  />
}
// 对话列表
const ConversationList: FC<IConversationList> = ({ logs, appDetail, loading, onRefresh, selectedRowKeys, onSelect }) => {
  const { t } = useTranslation()
  const { formatTime } = useTimestamp()

  // 是否显示日志详情抽屉
  const [showDrawer, setShowDrawer] = useState<boolean>(false) // Whether to display the chat details drawer
  // 当前日志
  const [currentConversation, setCurrentConversation] = useState<ChatConversationGeneralDetail>() // Currently selected conversation

  const { setCurrentLogItem, setShowAgentLogModal } = useAppStore(useShallow(state => ({
    setCurrentLogItem: state.setCurrentLogItem,
    setShowAgentLogModal: state.setShowAgentLogModal,
  })))

  // 关闭抽屉
  const onCloseDrawer = () => {
    onRefresh()
    setShowDrawer(false)
    setCurrentConversation(undefined)
  }
  // 数据来源, 工作流不包含本地数据源
  const chatSourceList = Object.keys(ChatSourceEnum).map(item => ({ label: t(`appLog.filter.source.${item}`), value: item }))
  // 表格列
  const columns: TableColumnsType<any> = [
    // 标题
    {
      title: t('appLog.table.header.summary'),
      key: 'summary',
      render: (_: any, log) => {
        return <>
          {!log.read_at
            ? <Indicator color='blue' textClassName='text-S3 leading-H3 text-gray-G1 truncate' title={log.name}>{log.name}</Indicator>
            : log.name
          }
        </>
      },
      ellipsis: true,
    },
    // 用户或账户
    {
      title: t('appLog.table.header.endUser'),
      key: 'endUser',
      render: (_: any, log) => {
        return log.from_end_user_session_id || log.from_account_name || defaultValue
      },
      ellipsis: true,
    },
    // 数据来源
    {
      title: t('appLog.table.header.source'),
      key: 'source',
      render: (_: any, log) => {
        return chatSourceList.find(item => item.value === log.source)?.label || '-'
      },
      ellipsis: true,
    },
    // 数据类型
    {
      title: t('appDataReflux.table.header.datasetType'),
      key: 'datasetType',
      render: (_: any, log) => {
        return datasetTypeList.find(item => item.value === log.dataset_type)?.label || '-'
      },
      ellipsis: true,
    },
    // 消息数
    {
      title: t('appLog.table.header.messageCount'),
      key: 'messageCount',
      dataIndex: 'message_count',
      ellipsis: true,
    },
    // 用户反馈
    {
      title: t('appLog.table.header.userRate'),
      key: 'userRate',
      render: (_: any, log) => {
        return (!log.user_feedback_stats.like && !log.user_feedback_stats.dislike)
          ? defaultValue
          : <>
            {!!log.user_feedback_stats.like && <HandThumbIconWithCount iconType='up' count={log.user_feedback_stats.like} />}
            {!!log.user_feedback_stats.dislike && <HandThumbIconWithCount iconType='down' count={log.user_feedback_stats.dislike} />}
          </>
      },
      width: 150,
      ellipsis: true,
    },
    // 反馈内容
    {
      title: t('appLog.table.header.reteContent'),
      key: 'userRate',
      render: (_: any, log) => {
        const tooltipContent = () => {
          return (
            <div className="flex flex-col">
              { log?.user_feedback_stats?.content || '-' }
            </div>
          )
        }
        return (
          <Tooltip popupContent={ tooltipContent() }>
            <div className='truncate'>
              { log?.user_feedback_stats?.content || '-' }
            </div>
          </Tooltip>
        )
      },
      ellipsis: true,
    },
    // 管理员反馈
    {
      title: t('appLog.table.header.adminRate'),
      key: 'adminRate',
      render: (_: any, log) => {
        return (!log.admin_feedback_stats.like && !log.admin_feedback_stats.dislike)
          ? defaultValue
          : <>
            {!!log.admin_feedback_stats.like && <HandThumbIconWithCount iconType='up' count={log.admin_feedback_stats.like} />}
            {!!log.admin_feedback_stats.dislike && <HandThumbIconWithCount iconType='down' count={log.admin_feedback_stats.dislike} />}
          </>
      },
      ellipsis: true,
    },
    // 更新时间
    {
      title: t('appLog.table.header.updatedTime'),
      key: 'updatedTime',
      render: (_: any, log) => {
        return formatTime(log.updated_at, t('appLog.dateTimeFormat') as string)
      },
      ellipsis: true,
    },
    // 创建时间
    {
      title: t('appLog.table.header.time'),
      key: 'time',
      render: (_: any, log) => {
        return formatTime(log.created_at, t('appLog.dateTimeFormat') as string)
      },
      ellipsis: true,
    },
  ]
  // 工作流应用不显示数据类型
  if (appDetail?.mode === 'advanced-chat') {
    const index = columns.findIndex(item => item.key === 'datasetType')
    columns.splice(index, 1)
  }

  const rowSelection: TableProps<any>['rowSelection'] = {
    preserveSelectedRowKeys: true,
    selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
      onSelect?.(selectedRowKeys, selectedRows)
    },
    //复选框禁用
    getCheckboxProps: (record: ChatConversationGeneralDetail) => {
      const label = datasetTypeList.find(item => item.value === record.dataset_type)?.label || '-';
      return { disabled: label === '-' };
    },
  }

  const [selectionType, setSelectionType] = useState<'checkbox' | 'radio'>('checkbox')

  return (
    <>
      <Table
        size='middle'
        loading={loading}
        columns={columns}
        rowSelection={{ type: selectionType, ...rowSelection }}
        pagination={false}
        scroll={{ y: 'calc(100vh - 285px)' }}
        rowKey='id'
        dataSource={logs?.data || []}
        className='border rounded border-gray-G5'
        rowClassName='cursor-pointer'
        onRow={(record) => {
          return {
            onClick: () => {
              setShowDrawer(true)
              setCurrentConversation(record)
            },
          }
        }}
      ></Table>
      <Drawer
        isOpen={showDrawer}
        onClose={onCloseDrawer}
        footer={null}
        mask={false}
        panelClassname='!m-0 !mt-[114px] !rounded-none w-[30%] !max-w-none !p-0 !min-w-[520px]'
      >
        <DrawerContext.Provider value={{
          onClose: onCloseDrawer,
          appDetail,
        }}>
          <ChatConversationDetailComp appId={appDetail.id} conversationId={currentConversation?.id} />
        </DrawerContext.Provider>
      </Drawer>
    </>
  )
}

export default ConversationList
