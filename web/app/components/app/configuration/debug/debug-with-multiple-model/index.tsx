import type { FC } from 'react'
import {
  memo,
  useCallback,
  useMemo,
} from 'react'
import { APP_CHAT_WITH_MULTIPLE_MODEL } from '../types'
import DebugItem from './debug-item'
import {
  DebugWithMultipleModelContextProvider,
  useDebugWithMultipleModelContext,
} from './context'
import type { DebugWithMultipleModelContextType } from './context'
import { useEventEmitterContextContext } from '@/context/event-emitter'
import { useDebugConfigurationContext } from '@/context/debug-configuration'
import { useFeatures } from '@/app/components/base/features/hooks'
import { useStore as useAppStore } from '@/app/components/app/store'
// 公共组件
import ChatInputArea from '@/app/components/base/chat/chat/chat-input-area'
import type { FileEntity } from '@/app/components/base/file-uploader/types'
import type { InputForm } from '@/app/components/base/chat/chat/type'
import Toast from '@/app/components/base/toast'

const DebugWithMultipleModel = () => {
  const {
    appId,
    mode,
    inputs,
    modelConfig,
  } = useDebugConfigurationContext()
  const { eventEmitter } = useEventEmitterContextContext()
  const {
    multipleModelConfigs,
    checkCanSend,
    handleNewConversation,
  } = useDebugWithMultipleModelContext()
  const setShowAppConfigureFeaturesModal = useAppStore(s => s.setShowAppConfigureFeaturesModal)
  const text2speech = useFeatures(s => s.features.text2speech)
  const { voice_input, voice_conversation } = text2speech || {}
  const file = useFeatures(s => s.features.file)

  const isChatMode = mode === 'chat' || mode === 'agent-chat'
  const twoLine = multipleModelConfigs.length === 2
  const threeLine = multipleModelConfigs.length === 3
  const fourLine = multipleModelConfigs.length === 4
  const inputsForm = modelConfig.configs.prompt_variables.filter(item => item.type !== 'api').map(item => ({ ...item, label: item.name, variable: item.key })) as InputForm[]

  // 调试窗口
  const size = useMemo(() => {
    let width = ''
    let height = ''
    if (twoLine) {
      width = 'calc(50% - 4px)'
      height = '100%'
    }
    if (threeLine) {
      width = 'calc(33.3% - 4px)'
      height = '100%'
    }
    if (fourLine) {
      width = 'calc(25% - 4px)'
      height = '100%'
    }

    return {
      width,
      height,
    }
  }, [twoLine, threeLine, fourLine])
  const position = useCallback((idx: number) => {
    let translateX = '0'
    const translateY = '0'

    if (twoLine && idx === 1)
      translateX = 'calc(100% + 8px)'
    if (threeLine && idx === 1)
      translateX = 'calc(100% + 8px)'
    if (threeLine && idx === 2)
      translateX = 'calc(200% + 16px)'
    if (fourLine && idx === 1)
      translateX = 'calc(100% + 8px)'
    if (fourLine && idx === 2)
      translateX = 'calc(200% + 16px)'
    if (fourLine && idx === 3)
      translateX = 'calc(300% + 24px)'

    return {
      translateX,
      translateY,
    }
  }, [twoLine, threeLine, fourLine])

  // 聊天信息发送
  const handleSend = useCallback((message: string, files?: FileEntity[]) => {
    if (checkCanSend && !checkCanSend())
      return

    eventEmitter?.emit({
      type: APP_CHAT_WITH_MULTIPLE_MODEL,
      payload: {
        message,
        files,
      },
    } as any)
  }, [eventEmitter, checkCanSend])

  return (
    <div className='flex flex-col h-full px-6'>
      <div
        className={`
          grow mb-3 relative overflow-auto
        `}
        style={{ height: isChatMode ? 'calc(100% - 60px)' : '100%' }}
      >
        {
          multipleModelConfigs.map((modelConfig, index) => (
            <DebugItem
              key={modelConfig.id}
              modelAndParameter={modelConfig}
              className={`
                absolute top-0 min-h-[200px]
                ${twoLine && index === 0 && 'mr-2'}
                ${threeLine && (index === 0 || index === 1) && 'mr-2'}
                ${fourLine && (index === 0 || index === 2 || index === 3) && 'mr-2'}
              `}
              style={{
                width: size.width,
                height: size.height,
                transform: `translateX(${position(index).translateX}) translateY(${position(index).translateY})`,
              }}
            />
          ))
        }
      </div>
      {isChatMode && (
        <div className='shrink-0 pb-0 px-6 pt-5'>
          <ChatInputArea
            appId={appId}
            showFeatureBar
            showFileUpload={false}
            onFeatureBarClick={setShowAppConfigureFeaturesModal}
            onSend={handleSend}
            visionConfig={file}
            inputs={inputs}
            inputsForm={inputsForm}
            showInputBottomTip
            handleNewConversation={handleNewConversation}
            voiceInput={voice_input}
            voiceConversation={voice_conversation}
            onVoiceCall={() => {
              Toast.notify({
                type: 'error',
                message: '多模型对比下，不支持语音通话功能',
              })
            }}
          />
        </div>
      )}
    </div>
  )
}

const DebugWithMultipleModelMemoed = memo(DebugWithMultipleModel)

const DebugWithMultipleModelWrapper: FC<DebugWithMultipleModelContextType> = ({
  bestModel,
  onMultipleModelConfigsChange,
  multipleModelConfigs,
  onDebugWithMultipleModelChange,
  checkCanSend,
  handleNewConversation,
}) => {
  return (
    <DebugWithMultipleModelContextProvider
      bestModel={bestModel}
      onMultipleModelConfigsChange={onMultipleModelConfigsChange}
      multipleModelConfigs={multipleModelConfigs}
      onDebugWithMultipleModelChange={onDebugWithMultipleModelChange}
      checkCanSend={checkCanSend}
      handleNewConversation={handleNewConversation}
    >
      <DebugWithMultipleModelMemoed />
    </DebugWithMultipleModelContextProvider>
  )
}

export default memo(DebugWithMultipleModelWrapper)
