'use client'
import React, { useMemo, useState } from 'react'

import { usePathname, useRouter } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import { Switch } from 'antd'
import SettingsModal from '../../settings'
import EmbeddedModal from '../../embedded'
import type { ConfigParams } from '../../settings'
import style from './styles/style.module.css'
import { asyncRunSafe } from '@/utils'
import cn from '@/utils/classnames'
import SecretKeyButton from '@/app/components/develop/secret-key/secret-key-button'
import type { AppDetailResponse } from '@/models/app'
import type { SiteConfig } from '@/types/share'
import type { AppSSO } from '@/types/app'
import { useAppContext } from '@/context/app-context'

// 公共组件
import ShareQRCode from '@/app/components/app/common/qrcode'
import Input from '@/app/components/base/input'
import Confirm from '@/app/components/base/confirm'
import CopyBtn from '@/app/components/base/button/copy-button'
import Button from '@/app/components/base/button'
import TextButton from '@/app/components/base/button/text-button'
import Tooltip from '@/app/components/base/tooltip'
import { Refresh } from '@/app/components/base/icons/src/vender/line/general'
import { File01 } from '@/app/components/base/icons/src/vender/line/files'

export type IAppActionProps = {
  className?: string
  appInfo: AppDetailResponse & Partial<AppSSO>
  siteInfo: SiteConfig
  actionType?: 'api' | 'webapp' | 'embed'
  hasPublish?: boolean
  customBgColor?: string
  onChangeStatus: (val: boolean) => Promise<void>
  onSaveSiteConfig?: (params: ConfigParams) => Promise<void>
  onGenerateCode?: () => Promise<void>
}

function AppAction({
  appInfo,
  siteInfo,
  actionType = 'webapp',
  hasPublish,
  customBgColor,
  onChangeStatus,
  onSaveSiteConfig,
  onGenerateCode,
  className,
}: IAppActionProps) {
  const router = useRouter()
  const pathname = usePathname()
  const { t } = useTranslation()

  const { canAdmin, canEditApp } = useAppContext()
  // 卡片类型
  const isApp = actionType === 'webapp'
  const isApi = actionType === 'api'
  const isEmbed = actionType === 'embed'
  // 卡片是否可启用
  const toggleDisabled = !hasPublish || (isApp ? !canEditApp : !canAdmin)
  // 是否启用
  const runningStatus = (isApi ? appInfo.api_enable_api : isApp ? appInfo.web_enable_site : appInfo.embed_enable_site) ?? false
  const { app_base_url, access_token } = siteInfo ?? {}
  const appMode = (appInfo.mode !== 'completion' && appInfo.mode !== 'workflow') ? 'chat' : appInfo.mode
  const appUrl = `${app_base_url}/${appMode}/${access_token}`
  const apiUrl = appInfo?.api_base_url

  // 是否显示设置弹窗
  const [showSettingsModal, setShowSettingsModal] = useState(false)
  // 是否显示嵌入弹窗
  const [showEmbedded, setShowEmbedded] = useState(false)
  // 是否显示确认删除
  const [showConfirmDelete, setShowConfirmDelete] = useState(false)

  // 操作按钮数组
  const OPERATIONS_MAP = useMemo(() => {
    const operationsMap = {
      webapp: [
        // 预览
        // { opName: t('appOverview.overview.appInfo.preview') },
        // 隐藏定制化按钮
        // { opName: t('appOverview.overview.appInfo.customize.entry'), opIcon: PaintBrushIcon },
      ] as { opName: string }[],
      api: [{ opName: t('appOverview.overview.apiInfo.doc'), opIcon: <File01 className='w-4 h-4 mr-1' /> }],
      app: [],
    } as Record<string, { opName: string; opIcon?: JSX.Element }[]>
    // if (appInfo.mode !== 'completion' && appInfo.mode !== 'workflow')
    //   operationsMap.webapp.push({ opName: t('appOverview.overview.appInfo.embedded.entry'), opIcon: EmbedIcon })

    // 设置
    // if (canEditApp)
    //   operationsMap.webapp.push({ opName: t('appOverview.overview.appInfo.settings.entry') })

    return operationsMap
  }, [canEditApp, appInfo, siteInfo, t])

  let bgColor = 'bg-primary-50 bg-opacity-40'
  if (actionType === 'api')
    bgColor = 'bg-purple-50'

  // 根据按钮名称生成点击事件
  const genClickFuncByName = (opName: string) => {
    switch (opName) {
      case t('appOverview.overview.appInfo.preview'):
        return () => {
          window.open(appUrl, '_blank')
        }
      case t('appOverview.overview.appInfo.settings.entry'):
        return () => {
          setShowSettingsModal(true)
        }
      case t('appOverview.overview.appInfo.embedded.entry'):
        return () => {
          setShowEmbedded(true)
        }
      default:
        // jump to page develop
        return () => {
          const pathSegments = pathname.split('/')
          pathSegments.pop()
          router.push(`${pathSegments.join('/')}/develop`)
        }
    }
  }
  // 生成编码
  const onGenCode = async () => {
    if (onGenerateCode)
      await asyncRunSafe(onGenerateCode())
  }
  // if(!hasPublish)
  //   return null
  if (actionType === 'embed') {
    return (
      <div className={cn(style.wrap, className)}>
        {/* 开启/关闭 */}
        <Switch size='small' value={runningStatus} onChange={onChangeStatus} disabled={toggleDisabled} />
        <Button onClick={() => setShowEmbedded(true)} disabled={!hasPublish} variant={'secondary-accent'}>{t('appOverview.overview.appInfo.embedded.entry')}</Button>
        {/* 嵌入弹窗 */}
        {showEmbedded && <EmbeddedModal
          siteInfo={siteInfo}
          isShow={showEmbedded}
          onClose={() => setShowEmbedded(false)}
          appBaseUrl={app_base_url}
          accessToken={access_token}
        />}
      </div>
    )
  }
  return (
    <div
      className={cn(style.wrap, className)}
    >
      {/* 开启/关闭 */}
      <Switch size='small' value={runningStatus} onChange={onChangeStatus} disabled={toggleDisabled} />
      {/* 网址 */}
      {access_token && <Input readOnly value={isApp ? appUrl : apiUrl} wrapperClassName='w-[388px]'></Input> }
      {/* 二维码 */}
      {access_token && isApp && <ShareQRCode app={appInfo} content={isApp ? appUrl : apiUrl} />}
      {/* 拷贝按钮 */}
      {access_token && <CopyBtn value={isApp ? appUrl : apiUrl}></CopyBtn> }
      {/* 重新生成 */}
      {access_token && isApp && canAdmin && (
        <Tooltip
          popupContent={t('appOverview.overview.appInfo.regenerate') || ''}
        >
          <TextButton
            variant={'text'}
            className="w-4 h-4 cursor-pointer rounded"
            onClick={() => setShowConfirmDelete(true)}
            disabled={!hasPublish}
          >
            <Refresh
              className={
                `w-full h-full ${style.generateLogo}`}
            ></Refresh>
          </TextButton>
        </Tooltip>
      )}
      {/* API 密钥 */}
      {!isApp && <SecretKeyButton variant={'secondary-accent'} appId={siteInfo.app_id} disabled={!hasPublish} />}
      {/* 预览/文档 */}
      {OPERATIONS_MAP[actionType].map((op) => {
        // 禁用状态
        const disabled
            = op.opName === t('appOverview.overview.appInfo.settings.entry')
              ? false
              : !hasPublish
        return (
          <Button
            variant={'secondary-accent'}
            key={op.opName}
            onClick={genClickFuncByName(op.opName)}
            disabled={disabled}
          >
            <Tooltip
              popupContent={
                t('appOverview.overview.appInfo.preUseReminder') ?? ''
              }
              disabled={!disabled}
            >
              <div className="flex flex-row items-center">
                {op.opIcon}
                <span className="text-[14px]">{op.opName}</span>
              </div>
            </Tooltip>
          </Button>
        )
      })}
      {/* 设置弹窗，嵌入弹窗 */}
      {isApp
        ? (
          <>
            {/* 设置弹窗 */}
            {showSettingsModal && <SettingsModal
              isChat={appMode === 'chat'}
              appInfo={appInfo}
              siteInfo={siteInfo}
              isShow={showSettingsModal}
              onClose={() => setShowSettingsModal(false)}
              onSave={onSaveSiteConfig}
            />}
          </>
        )
        : null}
      {/* 重新生成 确认弹框 */}
      {showConfirmDelete && (
        <Confirm
          type='warning'
          title={t('appOverview.overview.appInfo.regenerate')}
          content={t('appOverview.overview.appInfo.regenerateNotice')}
          isShow={showConfirmDelete}
          onConfirm={() => {
            onGenCode()
            setShowConfirmDelete(false)
          }}
          onCancel={() => setShowConfirmDelete(false)}
        />
      )}
    </div>
  )
}

export default AppAction
