'use client'
import type { FC } from 'react'
import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Divider } from 'antd'
import s from './styles/index.module.css'
import AddToolModal from '@/app/components/tools/add-tool-modal'
import type { AgentTool, ToolItem } from '@/types/tools'

import { type Collection, CollectionType } from '@/app/components/tools/types'
import SettingBuiltInTool from '@/app/components/app/configuration/feature-list/agent-tools/setting-built-in-tool'
import ToolItemCard from '@/app/components/tools/tool-item'

import Tooltip from '@/app/components/base/tooltip'
import { Delete, InfoCircle } from '@/app/components/base/icons/src/vender/line/general'
import Toast from '@/app/components/base/toast'
import FeatureCollapse from '@/app/components/base/features/feature-collapse'
import { MAX_TOOLS_NUM } from '@/config'
import { Add } from '@/app/components/base/icons/src/vender/solid/general'
import ForkTip from '@/app/components/base/forkTip'
import TextButton from '@/app/components/base/button/text-button'

const i18nPrefix = 'workflow.nodes.common.tool'

type ToolsProps = {
  tools: Array<AgentTool & { icon_url: any; collection?: Collection }>
  collections?: Collection[]
  scene: 'app' | 'workflow'
  onChangeTool: (tool: AgentToolWithMoreInfo, parameter: Record<string, any>) => void
  onSelectTool: (tools: Array<ToolItem>) => void
  onDelete: (index: number) => void
  className?: string
}
type AgentToolWithMoreInfo = AgentTool & { icon: any; collection?: Collection } | null

type AddToolPanelContentPorps = {
  tools: Array<AgentTool & { icon_url: any; collection?: Collection }>
  forkTools: Array<AgentTool & { icon_url: any; collection?: Collection }>
  scene: 'app' | 'workflow'
  border?: boolean
  extra?: (value: AgentTool & { icon_url: any; collection?: Collection }, index: number) => React.ReactNode
  onRemove: (id: number) => void
  onAdd: () => void
  onView: (item: AgentToolWithMoreInfo) => void
  onCloseFock: () => void
}

// 面板内容
export const AddToolPanelContent = React.memo(({
  tools,
  forkTools,
  scene,
  border = false,
  extra,
  onAdd,
  onView,
  onRemove,
  onCloseFock,
}: AddToolPanelContentPorps) => {
  const { t } = useTranslation()
  console.log('tools', tools)

  // 存在无效工具
  const hasFork = useMemo(() => {
    return (tools as AgentTool[]).some(item => item.fork)
  }, [tools])
  return (
    <FeatureCollapse
      title={t(`${i18nPrefix}.tool`)}
      headerRight={
        <>
          {tools.length < MAX_TOOLS_NUM
            ? <Add onClick={onAdd} className='ml-12 text-[#5C6273] cursor-pointer'></Add>
            : undefined
          }
        </>
      }
      useSwitch={false}
      inWorkflow={scene === 'workflow'}
    >
      {hasFork && (
        <ForkTip
          className='forkTip mx-4 mb-2'
          message={t('common.fork.datasetTip')}
          onClose={onCloseFock}
        >
          <div>
            {forkTools.map(({ provider_id, provider_name }) => (
              <div key={provider_id} className=''>
                <div className=''>
                        · {provider_name}
                </div>
              </div>
            ))}
          </div>
        </ForkTip>
      )}
      {tools.length > 0 && (
        <div className={s['tool-list']}>
          {tools.map((item, index) => (
            <ToolItemCard
              key={index}
              item={item}
              border={border}
              lack={item.isDeleted}
              operations={
                <div className='shrink-0 flex gap-2 items-center justify-end'>
                  <div className='hidden gap-2 items-center group-hover:flex'>
                    <Tooltip
                      popupContent={t('tools.setBuiltInTools.infoAndSetting')}
                      needsDelay
                    >
                      <TextButton variant='text' onClick={() => { onView(item as unknown as AgentToolWithMoreInfo) }}>
                        <InfoCircle className='w-4 h-4' />
                      </TextButton>
                    </Tooltip>
                    <TextButton variant='text' onClick={() => onRemove(index)}>
                      <Delete className={'w-4 h-4'} />
                    </TextButton>

                    { extra
                      ? <Divider type='vertical' className='!mx-0'></Divider>
                      : <></>
                    }
                  </div>
                  { extra
                    ? (<>
                      {extra(item, index)}
                    </>)
                    : <></> }
                </div>
              }
            ></ToolItemCard>
          ))}
        </div >
      )}
    </FeatureCollapse>
  )
})

AddToolPanelContent.displayName = 'AddToolPanelContent'

const ToolConfig: FC<ToolsProps> = ({
  tools,
  collections,
  scene,
  onChangeTool,
  onSelectTool,
  onDelete,
  className,
}) => {
  const { t } = useTranslation()
  // 是否显示选择工具列表
  const [isShowChooseTool, setIsShowChooseTool] = useState(false)
  // 当前选中的工具
  const [currentTool, setCurrentTool] = useState<AgentToolWithMoreInfo>(null)
  // 是否显示设置工具弹窗
  const [isShowSettingTool, setIsShowSettingTool] = useState(false)

  const forkTools = useMemo(() => {
    return (tools).filter((tool) => {
      return tool.fork
    })
  }, [tools])

  // 点击显示工具详情弹窗
  const handleShowToolSetting = useCallback((tool: AgentToolWithMoreInfo) => {
    if (tool?.isDeleted) {
      Toast.notify({
        type: 'error',
        message: t('tools.notify.toolRemoved'),
      })
      return
    }
    setCurrentTool(tool)
    setIsShowSettingTool(true)
  }, [t])
  // 变更工具设置
  const handleToolSettingChange = (value: Record<string, any>) => {
    onChangeTool(currentTool, value)
    setIsShowSettingTool(false)
  }
  // 工具删除
  const handleDeleteTools = (index: number) => {
    onDelete(index)
  }

  return (
    <>
      <AddToolPanelContent
        tools={tools}
        forkTools={forkTools}
        scene={scene}
        onAdd={() => { setIsShowChooseTool(true) }}
        onView={handleShowToolSetting}
        onRemove={handleDeleteTools}
        onCloseFock={() => { onSelectTool([]) }}
      />
      {/* 选择工具弹窗 */}
      {isShowChooseTool
        && <AddToolModal
          onHide={() => { setIsShowChooseTool(false) }}
          onChange={onSelectTool}
          tools={tools}
        />
      }
      {/* 配置工具弹窗 */}
      {
        isShowSettingTool && (
          <SettingBuiltInTool
            toolName={currentTool?.tool_name as string}
            setting={currentTool?.tool_parameters as any}
            collection={currentTool?.collection as Collection || {
              name: currentTool?.provider_name,
              id: currentTool?.provider_id,
              icon_url: currentTool?.tool_icon,
              type: currentTool?.provider_type,
            }}
            isBuiltIn={currentTool?.collection?.type === CollectionType.builtIn}
            isModel={currentTool?.collection?.type === CollectionType.model}
            onSave={handleToolSettingChange}
            onHide={() => setIsShowSettingTool(false)}
          />)
      }
    </>
  )
}

export default React.memo(ToolConfig)
