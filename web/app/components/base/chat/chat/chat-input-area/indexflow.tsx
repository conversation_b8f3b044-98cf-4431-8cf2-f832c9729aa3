/* eslint-disable multiline-ternary */
import { useCallback, useRef, useState,useEffect} from 'react'
import Textarea from 'rc-textarea'
import { useTranslation } from 'react-i18next'
import Recorder from 'js-audio-recorder'
import { MessageOutlined } from '@ant-design/icons'
import type { OnSend } from '../../types'
import type { Theme } from '../../embedded-chatbot/theme/theme-context'
import type { InputForm } from '../type'
import { useChatWithHistoryContext } from '../../chat-with-history/context'
import { useStore } from '@/app/components/workflow/store'
import { XCircle } from '../../../icons/src/vender/solid/general'
import VoiceInput from './components/voice-input'
import NewChat from './components/new-chat'
import { useCheckInputsForms, useTextAreaHeight } from './hooks'
import Operation from './operation'
import formRules from './formRules'
import style from './styles/style.module.css'
import ResearchReportInput from './ReportInputflow'
import useBreakpoints, { MediaType } from '@/hooks/use-breakpoints'
import cn from '@/utils/classnames'
import { FileListInChatInput, FileUploaderInChatInputMobile } from '@/app/components/base/file-uploader'
import { useFile } from '@/app/components/base/file-uploader/hooks'
import { FileContextProvider, useFileStore } from '@/app/components/base/file-uploader/store'
// import { Icon } from '@ant-design/icons';
// import VoiceInput from '@/app/components/base/voice-input'
import { useToastContext } from '@/app/components/base/toast'
// import FeatureBar from '@/app/components/base/features/new-feature-panel/feature-bar'
import type { FileUpload } from '@/app/components/base/features/types'
import { TransferMethod } from '@/types/app'
import { GetTemplateList } from '@/service/share'

type ChatInputAreaProps = {
  // 展示特性栏
  showFeatureBar?: boolean
  // 展示文件上床
  showFileUpload?: boolean
  // 特性栏disabled
  featureBarDisabled?: boolean
  // 特性栏点击
  onFeatureBarClick?: (state: boolean) => void
  // 文件上传配置
  visionConfig?: FileUpload
  // speechToTextConfig?: EnableType
  // 是否正在响应
  isResponding?: boolean

  //hq 判断是否以生成长文 
  isLongTxt?:boolean
  islongModel?:boolean
  isBook?:boolean
  inputs?: Record<string, any>
  inputsForm?: InputForm[]
  theme?: Theme | null
  // 显示输入框底部提示
  showInputBottomTip?: boolean
  // 发送函数
  onSend?: OnSend
  // 停止响应
  onStopResponding?: () => void
  // 开启新对话
  handleNewConversation?: () => void
  // 移动端显示底部上传卡片
  handleClickShowFiles?: () => void
}

const ChatInputArea = ({
  // showFeatureBar,
  // showFileUpload,
  // featureBarDisabled,
  // onFeatureBarClick,
  visionConfig,
  // speechToTextConfig = { enabled: true },
  isResponding = false,
  isLongTxt=false,
  islongModel=false,
  isBook=false,
  onSend,
  onStopResponding,
  inputs = {},
  inputsForm = [],
  theme,
  showInputBottomTip = true,
  handleNewConversation,
}: ChatInputAreaProps) => {
  const media = useBreakpoints()
  const isMobile = media === MediaType.mobile
  const { t } = useTranslation()
  const filesStore = useFileStore()
  const { notify } = useToastContext()
  const {
    wrapperRef,
    textareaRef,
    textValueRef,
    holdSpaceRef,
    handleTextareaResize,
    isMultipleLine,
  } = useTextAreaHeight()
  const longTxtForm = inputsForm.find(form => form.type === 'longTxt')

  const {
    handleDragFileEnter,
    handleDragFileLeave,
    handleDragFileOver,
    handleDropFile,
    handleClipboardPasteFile,
    isDragActive,
  } = useFile(visionConfig!)

  // 提问信息
  const [query, setQuery] = useState('')
  // 是否正在组合输入
  const isUseInputMethod = useRef(false)
  // 是否展示语音输入
  const [showVoiceInput, setShowVoiceInput] = useState(false)

  // 移动端文件卡片是否显示
  const [showFiles, setShowFiles] = useState(false)

  const { checkInputsForm } = useCheckInputsForms()
  const reportInputRef = useRef<{ getContent: () => string }>(null)

  const chatAllTypeResultflow = useStore(s => s.chatAllTypeResultflow)


  // 发送提问
  const handleSend = () => {
    if (onSend) {
      if (longTxtForm || isLongTxt) {
        const inputContent = reportInputRef.current?.getContent() || ''
        if ((!inputContent || !inputContent.trim()) && !isLongTxt) {
          notify({ type: 'warning', message: t('sampleTemplate.warning.FormRequired') })
          return
        }
        const { files, setFiles } = filesStore.getState()
        if (
          files.find(item => item.transferMethod === TransferMethod.local_file && !item.uploadedId)
        ) {
          notify({ type: 'info', message: t('appDebug.errorMessage.waitForFileUpload') })
          return
        }
        //hq 判断是否以生成长文,生成长文后发送指令
        if(isLongTxt){
          onSend(query,files,null)
          setQuery('')
          setFiles([])
          return;
        }
        if(inputContent.includes('[主题]')){
          notify({ type: 'warning', message:"请填写主题" });
          return;
        }else if(inputContent.includes('[输入数字]')){
          notify({ type: 'warning', message:"请输入字数数量" });
          return;
        }
        onSend(inputContent, files)
      }
      else {
        const { files, setFiles } = filesStore.getState()
        if (
          files.find(item => item.transferMethod === TransferMethod.local_file && !item.uploadedId)
        ) {
          notify({ type: 'info', message: t('appDebug.errorMessage.waitForFileUpload') })
          return
        }
        if ((!query || !query.trim()) && !files.length) {
          notify({ type: 'info', message: t('appAnnotation.errorMessage.queryRequired') })
          return
        }
        if (checkInputsForm(inputs, inputsForm)) {
          onSend(query, files)
          setQuery('')
          setFiles([])
        }
      }
    }
  }
  // enter按键按下处理
  const handleKeyUp = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.code === 'Enter') {
      e.preventDefault()
      // prevent send message when using input method enter
      if (!e.shiftKey && !isUseInputMethod.current)
        handleSend()
    }
  }
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // 是否正在组合输入
    isUseInputMethod.current = e.nativeEvent.isComposing
    if (e.code === 'Enter' && !e.shiftKey) {
      setQuery(query.replace(/\n$/, ''))
      e.preventDefault()
    }
  }

  const handleClickShowFiles = () => {
    setShowFiles(!showFiles)
  }

  const operation = (
    <Operation
      ref={holdSpaceRef}
      query={query}
      fileConfig={visionConfig}
      // speechToTextConfig={speechToTextConfig}
      isResponding={isResponding}
      onSend={handleSend}
      onStopResponding={onStopResponding}
      handleClickShowFiles={handleClickShowFiles}
    />
  )

  return longTxtForm ? (
    Object.keys(chatAllTypeResultflow.form || {}).length > 0 ? (
      <div className="flex  items-center justify-center w-full gap-2">
        {/* <div className='xq-no-scale messageDiv flex  items-center justify-center'><MessageOutlined /></div> */}
        <div className="flex items-center justify-end w-full gap-3">
          {handleNewConversation && <NewChat onClick={handleNewConversation} />}
          <div
            className={cn(
              style.chatInput,
              'rounded',
              !handleNewConversation && '!w-full',
              isDragActive
                    && 'border border-dashed border-components-option-card-option-selected-border',
            )}
          >
            <div className={cn('relative')}>
              <div className="flex flex-col items-start max-h-[150px] bg-white border border-gray-G5 rounded overflow-y-auto">
                {/* <FileListInChatInput fileConfig={visionConfig!} /> */}
                <div ref={wrapperRef} className="flex items-center justify-between w-full">
                  <div className="relative flex items-center w-full grow">
                    <ResearchReportInput ref={reportInputRef} />
                  </div>
                  <div
                    className={cn(
                      'absolute top-0 bottom-0 h-full flex items-center',
                      'right-3',
                    )}
                  >
                    {query
                      ? (
                        <div
                          className="flex items-center justify-center w-8 h-8 ml-2 cursor-pointer"
                          onClick={() => setQuery('')}
                        >
                          <XCircle className="w-4 h-4 text-gray-G5 hover:text-gray-G4" />
                        </div>
                      )
                      : null}

                    {!isMultipleLine && operation}
                    {showVoiceInput && (
                      <VoiceInput
                        onCancel={() => setShowVoiceInput(false)}
                        onConverted={text => setQuery(text)}
                      />
                    )}
                    {isMultipleLine && <div className="px-[9px]">{operation}</div>}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {visionConfig?.enabled && isMobile && showFiles && (
          <>
            <FileUploaderInChatInputMobile fileConfig={visionConfig!} />
          </>
        )}
        {/* {showInputBottomTip && (
          <div className="text-[12px] leading-H1 text-gray-G4 text-center mt-2 pb-3">
            {t('common.chat.tip')}
          </div>
        )} */}
        {/* {showFeatureBar && (
        <FeatureBar
          showFileUpload={showFileUpload}
          disabled={featureBarDisabled}
          onFeatureBarClick={onFeatureBarClick}
        />
      )} */}
      </div>
    ) : 
    (
      isLongTxt?
      <div>
       <div className='xq-flex-around xq-flex-vcenter'>
          
        </div> 
       <div className="flex  items-center justify-center w-full gap-2">
       {islongModel?<div className={`${style.blankDiv}`}></div>:null}
        {/* <div className='xq-no-scale messageDiv flex  items-center justify-center'><MessageOutlined /></div> */}
        <div className="w-full gap-3">
          {islongModel &&
            <div className='xq-nowrap xq-flex-vcenter w-full'>
              <div className='xq-nowrap xq-flex-vcenter inputbtn' onClick={() => setQuery(t('sampleTemplate.longText.fullfullrewritelong') as string)}>
                <svg  xmlns="http://www.w3.org/2000/svg" className="xqicon" viewBox="0 0 24 24" fill="currentColor"><path d="M15.1986 9.94447C14.7649 9.5337 14.4859 8.98613 14.4085 8.39384L14.0056 5.31138L11.275 6.79724C10.7503 7.08274 10.1433 7.17888 9.55608 7.06948L6.49998 6.50015L7.06931 9.55625C7.17871 10.1435 7.08257 10.7505 6.79707 11.2751L5.31121 14.0057L8.39367 14.4086C8.98596 14.4861 9.53353 14.7651 9.94431 15.1987L12.0821 17.4557L13.4178 14.6486C13.6745 14.1092 14.109 13.6747 14.6484 13.418L17.4555 12.0823L15.1986 9.94447ZM15.2238 15.5079L13.0111 20.1581C12.8687 20.4573 12.5107 20.5844 12.2115 20.442C12.1448 20.4103 12.0845 20.3665 12.0337 20.3129L8.49229 16.5741C8.39749 16.474 8.27113 16.4096 8.13445 16.3918L3.02816 15.7243C2.69958 15.6814 2.46804 15.3802 2.51099 15.0516C2.52056 14.9784 2.54359 14.9075 2.5789 14.8426L5.04031 10.3192C5.1062 10.1981 5.12839 10.058 5.10314 9.92253L4.16 4.85991C4.09931 4.53414 4.3142 4.22086 4.63997 4.16017C4.7126 4.14664 4.78711 4.14664 4.85974 4.16017L9.92237 5.10331C10.0579 5.12855 10.198 5.10637 10.319 5.04048L14.8424 2.57907C15.1335 2.42068 15.4979 2.52825 15.6562 2.81931C15.6916 2.88421 15.7146 2.95507 15.7241 3.02833L16.3916 8.13462C16.4095 8.2713 16.4739 8.39766 16.5739 8.49245L20.3127 12.0338C20.5533 12.2617 20.5636 12.6415 20.3357 12.8821C20.2849 12.9357 20.2246 12.9795 20.1579 13.0112L15.5078 15.224C15.3833 15.2832 15.283 15.3835 15.2238 15.5079ZM16.0206 17.435L17.4348 16.0208L21.6775 20.2634L20.2633 21.6776L16.0206 17.435Z"></path></svg>
                <div className='xqword'>{t('sampleTemplate.longText.fullfullrewrite')}</div>
              </div>
              <div className='xq-nowrap xq-flex-vcenter inputbtn' onClick={() => setQuery(t('sampleTemplate.longText.fullexpandContenlong') as string)}>
                <svg xmlns="http://www.w3.org/2000/svg" className="xqicon" viewBox="0 0 24 24" fill="currentColor"><path d="M21 6.75736L19 8.75736V4H10V9H5V20H19V17.2426L21 15.2426V21.0082C21 21.556 20.5551 22 20.0066 22H3.9934C3.44476 22 3 21.5501 3 20.9932V8L9.00319 2H19.9978C20.5513 2 21 2.45531 21 2.9918V6.75736ZM21.7782 8.80761L23.1924 10.2218L15.4142 18L13.9979 17.9979L14 16.5858L21.7782 8.80761Z"></path></svg>
                <div className='xqword'>{t('sampleTemplate.longText.fullexpandConten')}</div>
              </div>
              <div className='xq-nowrap xq-flex-vcenter inputbtn' onClick={() => setQuery(t('sampleTemplate.longText.fullstreamlininglong') as string)}>
                <svg xmlns="http://www.w3.org/2000/svg" className="xqicon" viewBox="0 0 24 24" fill="currentColor"><path d="M20 2C20.5523 2 21 2.44772 21 3V6.757L19 8.757V4H5V20H19V17.242L21 15.242V21C21 21.5523 20.5523 22 20 22H4C3.44772 22 3 21.5523 3 21V3C3 2.44772 3.44772 2 4 2H20ZM21.7782 8.80761L23.1924 10.2218L15.4142 18L13.9979 17.9979L14 16.5858L21.7782 8.80761ZM13 12V14H8V12H13ZM16 8V10H8V8H16Z"></path></svg>
                <div className='xqword'>{t('sampleTemplate.longText.fullstreamlining')}</div>
              </div>
            </div>
           }
          <div
            className={cn(
              style.chatInput,
              'rounded',
              !handleNewConversation && '!w-full',
              isDragActive
                    && 'border border-dashed border-components-option-card-option-selected-border',
            )}
          >
            <div className={cn('relative')}>
              <div className="flex flex-col items-start max-h-[150px] bg-white border border-gray-G5 rounded overflow-y-auto">
                <FileListInChatInput fileConfig={visionConfig!} />
                <div ref={wrapperRef} className="flex items-center justify-between w-full">
                  <div className="relative flex items-center w-full grow">
                    <Textarea
                      ref={textareaRef}
                      className={cn(
                        'block w-full h-[48px] px-4 pr-[118px] py-[12px] text-[14px] leading-H3 max-h-none text-gray-G1 outline-none appearance-none resize-none',
                      )}
                      placeholder={t('common.chat.inputPlaceholder') || ''}
                      autoSize={{ minRows: 1 }}
                      onResize={handleTextareaResize}
                      value={query}
                      onChange={(e) => {
                        setQuery(e.target.value)
                        handleTextareaResize()
                      }}
                      onKeyUp={handleKeyUp}
                      onKeyDown={handleKeyDown}
                      onPaste={handleClipboardPasteFile}
                      onDragEnter={handleDragFileEnter}
                      onDragLeave={handleDragFileLeave}
                      onDragOver={handleDragFileOver}
                      onDrop={handleDropFile}
                    />
                  </div>
                  <div
                    className={cn(
                      'absolute top-0 bottom-0 h-full flex items-center',
                      'right-3',
                    )}
                  >
                    {query
                      ? (
                        <div
                          className="flex items-center justify-center w-8 h-8 ml-2 cursor-pointer"
                          onClick={() => setQuery('')}
                        >
                          <XCircle className="w-4 h-4 text-gray-G5 hover:text-gray-G4" />
                        </div>
                      )
                      : null}

                    {!isMultipleLine && operation}
                    {showVoiceInput && (
                      <VoiceInput
                        onCancel={() => setShowVoiceInput(false)}
                        onConverted={text => setQuery(text)}
                      />
                    )}
                    {isMultipleLine && <div className="px-[9px]">{operation}</div>}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {visionConfig?.enabled && isMobile && showFiles && (
          <>
            <FileUploaderInChatInputMobile fileConfig={visionConfig!} />
          </>
        )}
        {islongModel?<div className={`${style.blankDivRight}`}></div>:null}
        {/* {showInputBottomTip && (
          <div className="text-[12px] leading-H1 text-gray-G4 text-center mt-2 pb-3">
            {t('common.chat.tip')}
          </div>
        )} */}
      </div>
      </div>
      :
      <div></div>
    )
  ) : (
    <div className="flex flex-col justify-center items-center w-full">
      <div className="flex justify-end items-center gap-3 w-full">
        {/* 新增对话 */}
        {handleNewConversation && <NewChat onClick={handleNewConversation} />}
        {/* 输入框 */}
        <div
          className={cn(
            style.chatInput,
            'rounded relative',
            !handleNewConversation && '!w-full',
            isDragActive
                && 'border border-dashed border-components-option-card-option-selected-border',
          )}
        >
          <div className="flex flex-col items-start max-h-[150px] bg-white rounded overflow-y-auto">
            <FileListInChatInput fileConfig={visionConfig!} />
            <div ref={wrapperRef} className="flex items-center justify-between w-full">
              <div className="flex items-center relative grow w-full">
                <Textarea
                  ref={textareaRef}
                  className={cn(
                    'block w-full h-[48px] px-4 pr-[118px] py-[12px] text-[14px] leading-H3 max-h-none text-gray-G1 outline-none appearance-none resize-none',
                  )}
                  placeholder={t('common.chat.inputPlaceholder') || ''}
                  autoSize={{ minRows: 1 }}
                  onResize={handleTextareaResize}
                  value={query}
                  onChange={(e) => {
                    setQuery(e.target.value)
                    handleTextareaResize()
                  }}
                  onKeyUp={handleKeyUp}
                  onKeyDown={handleKeyDown}
                  onPaste={handleClipboardPasteFile}
                  onDragEnter={handleDragFileEnter}
                  onDragLeave={handleDragFileLeave}
                  onDragOver={handleDragFileOver}
                  onDrop={handleDropFile}
                />
              </div>
              <div
                className={cn('absolute top-0 bottom-0 h-full flex items-center', 'right-3')}
              >
                {/* 删除按钮 */}
                {query
                  ? (
                    <div
                      className="flex justify-center items-center ml-2 w-8 h-8 cursor-pointer"
                      onClick={() => setQuery('')}
                    >
                      <XCircle className="w-4 h-4 text-gray-G5 hover:text-gray-G4" />
                    </div>
                  )
                  : null}
                {/* 发送/中止按钮 */}
                {!isMultipleLine && operation}
                {/* 音频输入 */}
                {/* {
                    showVoiceInput && (
                      <VoiceInput
                        onCancel={() => setShowVoiceInput(false)}
                        onConverted={text => setQuery(text)}
                      />
                    )
                  } */}
                {isMultipleLine && <div className="px-[9px]">{operation}</div>}
              </div>
            </div>
          </div>
        </div>
      </div>
      {visionConfig?.enabled && isMobile && showFiles && (
        <>
          <FileUploaderInChatInputMobile fileConfig={visionConfig!} />
        </>
      )}
      {/* 底部提示 */}
      {showInputBottomTip && (
        <div className="text-[12px] leading-H1 text-gray-G4 text-center mt-2 pb-3">
          {t('common.chat.tip')}
        </div>
      )}
      {/* {showFeatureBar && <FeatureBar showFileUpload={showFileUpload} disabled={featureBarDisabled} onFeatureBarClick={onFeatureBarClick} />} */}
    </div>
  )
}

const ChatInputAreaWrapper = (props: ChatInputAreaProps) => {
  return (
    <FileContextProvider>
      <ChatInputArea {...props} />
    </FileContextProvider>
  )
}

export default ChatInputAreaWrapper
