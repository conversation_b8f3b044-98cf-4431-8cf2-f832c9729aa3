'use client'

import { useTranslation } from 'react-i18next'
import But<PERSON> from '@/app/components/base/button'
import '@/app/styles/switchLongTxt.scss'
import { upload,uploadThumb } from '@/service/base'
import { CheckCard } from '@ant-design/pro-components'
import { PlusOutlined, EllipsisOutlined,ProfileOutlined, CarryOutOutlined,FileZipOutlined,FileProtectOutlined,HddOutlined,LayoutOutlined,GroupOutlined,LoadingOutlined} from '@ant-design/icons'
import { useChatWithHistoryContext } from '../../context'
import { use, useEffect, useRef, useState } from 'react'
import { Dropdown, Input, message, Tooltip, Upload,AutoComplete} from 'antd'
import type { GetProp, UploadFile, UploadProps } from 'antd'
import Field from '@/app/components/app/configuration/config-var/config-modal/field'
import { fetchGetTemplateList, GetTemplateList,getKeyword,getThumb} from '@/service/share'
import TemplateDrawer from './templateDrawer'
import { TEMPLATE_URL } from '@/config'
import style from '@/app/components/base/chat/chat-with-history/styles/style.module.css'
import { json } from 'stream/consumers'



interface TypeSelectorProps {
  selectedType: string
  selectedchildType:any
  selectedposType:any
  onTypeSelect: (type: string, item: any) => void
  onChildTypeSelect: (type: string, item: any) => void
}

type FileType = Parameters<GetProp<UploadProps, 'beforeUpload'>>[0]

const iconList = [
  ProfileOutlined,
  CarryOutOutlined,
  FileZipOutlined,
  FileProtectOutlined,
  HddOutlined,
  LayoutOutlined,
  GroupOutlined
];

const colors = [
  {
    bgcolor:"#F3E3F5",
    value:"#B34EBF"
  },
  {
    bgcolor:"#DFE9FC",
    value:"#3779EA"
  },
  {
    bgcolor:"#FBE2E7",
    value:"#E54D67"
  },
  {
    bgcolor:"#DDF3EE",
    value:"#2DB397"
  },
  {
    bgcolor:"#F3E3F5",
    value:"#B34EBF"
  },
  {
    bgcolor:"#DFE9FC",
    value:"#3779EA"
  },
  {
    bgcolor:"#FBE2E7",
    value:"#E54D67"
  },
  {
    bgcolor:"#DDF3EE",
    value:"#2DB397"
  }
];

const TemplatePage = ({ selectedType, selectedchildType, selectedposType,onTypeSelect,onChildTypeSelect}: TypeSelectorProps) => {
  const { t } = useTranslation()
  const [topicType, setopicType] = useState<string>('')
  const [ReportType, seReportType] = useState<string>('')
  const [fileList, setFileList] = useState<UploadFile[]>([])
  const [formValues, setFormValues] = useState<Record<string, string>>({})
  const [previewOpen, setPreviewOpen] = useState(false)
  const [previewImage, setPreviewImage] = useState('')
  const [open, setOpen] = useState(false)
  const [drawerData, setDrawerData] = useState<any>({})
  const UploadFiles = useRef<any>({})
  const { setChatAllTypeResult, chatAllTypeResult, appId } = useChatWithHistoryContext()
  const [templateList, setTemplateList] = useState<any[]>([])
  const [templateList1, setTemplateList1] = useState<any[]>([])
  const [templateList2, setTemplateList2] = useState<any[]>([])
  const selectedValue = useRef<any>({})
  const checkCardListRef = useRef<any>(null);
  const [curpos, seCurpos] = useState<any>(0);

  const autoCompleteRef = useRef<HTMLInputElement>(null); // 创建一个
  const [wordsList, setWordsList] = useState<any[]>([])
  const [wordskey, setWordsKey] = useState<any>('')
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isLoad, setIsLoad] = useState(false);//是否在搜索中


  const [templateList3, setTemplateList3] = useState<any[]>([])//自定义资料列表
  const [fileList2, setfileList2] = useState<UploadFile[]>([])

  const [thumbnail, setthumbnail] = useState<any>('');

  useEffect(() => {
    seCurpos(selectedposType);
  }, [selectedposType])

  useEffect(() => {
    handleGetTemplateList(selectedType)
  }, [selectedType])

  const handleGetTemplateList = async (e: any) => {
    const res = await GetTemplateList(
      {
        app_id: appId,
        level1: e,
        get_type: 'all'
      },
      false
    )
    if (res) {
      setTemplateList(res as any)
    }
  }

  useEffect(() => {
    if (topicType && ReportType) {
      if (chatAllTypeResult.files) {
        setChatAllTypeResult({
          files: {}
        })
      }
      if (chatAllTypeResult.Upload) {
        setChatAllTypeResult({
          Upload: {}
        })
      }
    }
  }, [ReportType])

  useEffect(() => {
    if (templateList2) {
      selectedValue.current = templateList2[0]
      setChatAllTypeResult({
        files: templateList2[0]
      })
    }
  }, [templateList2])

  useEffect(() => {
    if(setTemplateList3.length==0){
       setChatAllTypeResult({
        resource:[]
       })
    }else{
      setChatAllTypeResult({
        resource:templateList3
       })
    }
  }, [templateList3])

  const getBase64 = (file: FileType): Promise<string> =>
    new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = error => reject(error)
    })
  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as FileType)
    }

    setPreviewImage(file.url || (file.preview as string))
    setPreviewOpen(true)
  }

  const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    const validFileList = newFileList.filter(file => file.size !== undefined && file.size > 0);
    setFileList(validFileList);
  };

  const uploadButton = (
    <button style={{ border: 0, background: 'none' }} type="button">
      <PlusOutlined />
      <div style={{ marginTop: 10 }}>{t('sampleTemplate.longText.uploadmsg')}</div>
    </button>
  )

  const uploadButton2 = (
    <button style={{ border: 0, background: 'none' }} type="button">
      <PlusOutlined />
      <div className='upword'>{t('sampleTemplate.longText.uploadfile')}</div>
    </button>
  )

  const onProgress = (e: ProgressEvent, file: UploadFile) => {
    if (e.lengthComputable) {
      const percent = Math.floor((e.loaded / e.total) * 100)
      setFileList(prevFileList =>
        prevFileList.map(f => (f.uid === file.uid ? { ...f, percent } : f))
      )
    }
  }
  const onProgress2 = (e: ProgressEvent, file: UploadFile) => {}

  const customRequest = async (options: any) => {

    const file = options.file;
    if (file.size === 0) {
      message.error(t('sampleTemplate.longText.FileCannotBeEmpty')!);
      setFileList(prevFileList =>
        prevFileList.filter(f => f.uid !== options.file.uid)
      );
      return;
    }

    const formData = new FormData()
    formData.append('file', options.file)
    formData.append('source', 'web')
    upload(
      {
        xhr: new XMLHttpRequest(),
        data: formData,
        onprogress: (e: ProgressEvent) => onProgress(e, options.file)
      },
      true,
      ''
    )
      .then((res: { id: string; name: string; url: string }) => {
        if (res) {
          const files = {
            file_name: res.name,
            file_id: res.id,
            file_path: `${TEMPLATE_URL}files/${res.id}/file-preview-look`
          }
          UploadFiles.current = files
          setChatAllTypeResult({
           files: UploadFiles.current
          })
         selectedValue.current = UploadFiles.current;
        }
        setFileList(prevFileList =>
          prevFileList.map(f => (f.uid === options.file.uid ? { ...f, status: 'done' } : f))
        )
      })
      .catch(error => {
        //console.log(error, 'error')
        setFileList(prevFileList =>
          prevFileList.map(f => (f.uid === options.file.uid ? { ...f, status: 'error' } : f))
        )
      })
      setthumbnail("");
      uploadThumb(
        {
          xhr: new XMLHttpRequest(),
          data: formData,
          onprogress: (e: ProgressEvent) => onProgress(e, options.file)
        },
        true,
        ''
      )
        .then((res:any) => {
           if(res){
            let url = res.url;
            if(window.location.href.includes('localhost')){
              url="http://dify3.s9537.cn/"+res.url;
            }
            setthumbnail(url);
           }else{
            setthumbnail("");
           }
        })
        .catch(error => {
          //console.log(error, 'error')
          setthumbnail("");
        })

  }

  const customRequest2 = async (options: any) => {

    const file = options.file;
    if (file.size === 0) {
      message.error(t('sampleTemplate.longText.FileCannotBeEmpty')!);
      // setFileList(prevFileList =>
      //   prevFileList.filter(f => f.uid !== options.file.uid)
      // );
      return;
    }

    const formData = new FormData()
    formData.append('file', options.file)
    formData.append('source', 'web')
    upload(
      {
        xhr: new XMLHttpRequest(),
        data: formData,
        onprogress: (e: ProgressEvent) => onProgress2(e, options.file)
      },
      true,
      ''
    )
      .then((res: { id: string; name: string; url: string }) => {
        if (res) {
          let files = {
            file_name: res.name,
            file_id: res.id,
            file_path: `${TEMPLATE_URL}files/${res.id}/file-preview-look`
          }
          let list = [files,...templateList3];
          setTemplateList3(list);
        }
      })
      .catch(error => {
        //console.log(error, 'error')
        // setFileList(prevFileList =>
        //   prevFileList.map(f => (f.uid === options.file.uid ? { ...f, status: 'error' } : f))
        // )
      })
  }

  const handleRemove = (file: UploadFile) => {
    setChatAllTypeResult({
      Upload: {}
    })
  }

  const handlePayloadChange = (key: string, value: any) => {
    if(key=='Theme'){
      setWordsKey(value);
    }
    const newFormValues = {
      ...formValues,
      [key]: value
    }
    setFormValues(newFormValues)

    const updatedForm = {
      form: newFormValues
    }
    setChatAllTypeResult(updatedForm)
  }
  const searchKey=async()=>{
      if(!wordskey){
         message.warning(`${t('sampleTemplate.longText.warn4')}`);
         return;
      }
      if(isLoad){
        return;
      }
      setIsLoad(true);
      let data={
        conversation_id:"",
        inputs:{
          sample:JSON.stringify({
            request_type:t('sampleTemplate.longText.titleRecommend')
          })
        },
        is_delete_conversation:true,
        parent_message_id:null,
        query:wordskey
      }
      const res:any = await getKeyword(data);
      setIsLoad(false);
      if (res && res?.answer) {
        let glist = JSON.parse(res.answer).map((item:any)=>{
          return {
            value:item
          }
        })
        setWordsList(glist);
        //鼠标移动到输入框时，自动显示下拉列表
        setIsDropdownOpen(true);
        autoCompleteRef.current?.focus(); // 自动聚焦到输入框

      }
  }

  const customItemRender = (
    originNode: React.ReactElement,
    file: UploadFile,
    fileList: UploadFile[],
    actions: any
  ) => {
    return (
      <div className="custom-upload-item">
        <div className="file-actions">
            <span className='long-span-Tabs'>
              <CheckCard
                key={file.uid}
                avatar=""
                title=""
                className='docHas xq-border-box'
                value={UploadFiles.current}
                extra={
                  <>
                  <div  className="defaultpic">
                   <img src={thumbnail?thumbnail:defaultBj} alt="top"  />
                  </div>
                  <div className='docHasChild xq-border-box'>
                    <div className='xq-flex-cbetween'>
                        <img src={getAvatarForFileType(file.name)} className="docHasIco" alt="top"  />
                        <Dropdown
                          placement="top"
                          menu={{
                            onClick: e => {
                              if (e.key === '1') {
                                handleDrawer(UploadFiles.current)
                              } else if (e.key === '2') {
                                actions.remove(file)
                              }
                            },
                            items: [
                              {
                                label:t('sampleTemplate.longText.look'),
                                key: '1'
                              },
                              {
                                label:t('sampleTemplate.longText.deltxt'),
                                key: '2'
                              }
                            ]
                          }}
                        >
                          <EllipsisOutlined
                            style={{ fontSize: 22, color: 'rgba(0,0,0,0.5)' }}
                            onClick={e => e.stopPropagation()}
                          />
                        </Dropdown>
                      </div>
                      <p className="docName docMt">{file.name}</p>
                    </div>
                   </>
                }
              />
            </span>
        </div>
      </div>
    )
  }

  const fileTypeToAvatarMap: Record<string, string> = {
    '.docx': '/assets/image/word.png',
    '.doc': '/assets/image/word.png',
    '.pdf': '/assets/image/pdf.png',
    '.txt': '/assets/image/txt.png',
    '.xls': '/assets/image/excel.png',
    '.xlsx': '/assets/image/excel.png'
  }

  const defaultBj = '/assets/topremark.png'

  const getAvatarForFileType = (fileName: string): string => {
    const extension = fileName.slice(fileName.lastIndexOf('.')).toLowerCase()
    return fileTypeToAvatarMap[extension] || '/assets/image/article.png' // 默认图标
  }

  const handleDrawer = (item: any) => {
    setDrawerData(item)
    setOpen(true)
  }

  const onClose = () => {
    setOpen(false)
  }

  const clearFormValues = () => {
    setFormValues({})
    setFileList([])
    const updatedForm = {
      form: {},
      files: {}
    }
    setChatAllTypeResult(updatedForm)
  }
  const getStringFirst = (str:any) => {
      return str.substring(0, 1)
  }
  const getThumbPath=(item:any)=>{
    let path = defaultBj;
    if(item.file_pic){
      if(window.location.href.includes('localhost')){
        path="http://dify3.s9537.cn/"+item.file_pic;
      }else{
        path=item.file_pic
      }
    }
    return path;
  }
  const reshtml=(item:any)=>{
    return (
     <>
      <div  className="defaultpic">
       <img src={getThumbPath(item)} alt="top"  />
      </div>
      <div className='docHasChild xq-border-box'>
        <div className='xq-flex-cbetween'>
            <img src={getAvatarForFileType(item.file_name)} className="docHasIco" alt="top"  />
            <Dropdown
                placement="top"
                menu={{
                  onClick: () => {
                    handleDrawer(item)
                  },
                  items: [
                    {
                      label:t('sampleTemplate.longText.look'),
                      key: '1'
                    }
                  ]
                }}
              >
                <EllipsisOutlined
                  style={{ fontSize: 22, color: 'rgba(0,0,0,0.5)' }}
                  onClick={e => e.stopPropagation()}
                />
              </Dropdown>
          </div>
          <p className="docName docMt">{item.file_name}</p>
        </div>
       </>
    )
  }
  return (
    curpos!=0?<>
      <TemplateDrawer open={open} drawerData={drawerData} onClose={onClose}></TemplateDrawer>
      <div className="TopicType">
        {curpos==1?(
        <>
        <div>
         <div className={`${style.smallTel}`}><span>Step2</span>{t('sampleTemplate.FormDataEvent.SampleTemplate')}</div>
          {templateList.map((item: any, index: number) => (
            <Button
              key={index}
              variant="secondary-accent"
              size="small"
              className={`shrink-0 TopicButtons ${style.sBtn} ${topicType === item.text ? 'TopicActive' : ''}`}
              onClick={() => {
                seReportType('')
                setopicType(item.text)
                setTemplateList1(item.children)
                onChildTypeSelect(item.text, 1)
                const data = {
                  text: item.text
                }
                onTypeSelect('topic', data)
              }}
            >
              {iconList.map((IconComponent, iconIndex) => (
                  (index==iconIndex ? <IconComponent size={16} key={iconIndex} style={{ fontSize: 16, marginRight: 5 }} />:'' )
                ))}
              {item.text}
            </Button>
          ))}
        </div>
        <div className={`${style.smallTelBotDIV}`}>
          {topicType && (
            <>
              {/* <div>{t('sampleTemplate.FormDataEvent.TemplateType')}</div> */}
              <div className='xq-nowrap'>
                {templateList1.map((item: any, index: number) => (
                  <Button
                    key={index}
                    variant="secondary-accent"
                    size="small"
                    className={`shrink-0 TopicButtons ${style.sBtn2}  ${ReportType === item.text ? 'TopicActive' : ''}`}
                    onClick={() => {
                      clearFormValues()
                      seReportType(item.text)
                      setTemplateList2(item.children)
                      const data = {
                        text: item.text
                      }
                      onTypeSelect('Report', data)
                      onChildTypeSelect(item.text, 2)
                    }}
                  >
                    <div className={`${style.sBtn2c}`}  style={{'background':colors[index].bgcolor,'color':colors[index].value}}>
                    {getStringFirst(item.text)}
                    </div>
                    <div className={`${style.sBtn2txt}`}>
                      {item.text}
                    </div>
                  </Button>
                ))}
              </div>
            </>
          )}
        </div>
        </>
        ):
        <div className="CheckCardList">
          <CheckCard.Group
            value={selectedValue.current}
            onChange={(value: any) => {
              if (value?.originFileObj && value?.status == 'done') {
                selectedValue.current = UploadFiles.current
                setChatAllTypeResult({
                  files: UploadFiles.current
                })
              } else {
                selectedValue.current = value
                setChatAllTypeResult({
                  files: value
                })
              }
            }}
          >
            {ReportType ? (
              <>
                <div className={`${style.smallTel}`}><span>Step3</span>{t('sampleTemplate.longText.step3')}：</div>
                {(
                  t('sampleTemplate.FormDataEvent.ArrayFormList', { returnObjects: true }) as any[]
                ).map((item: any, index: number) => (
                  <Field
                    key={index}
                    className={`mt-3 ${item.required ? 'FieldBefore' : ''}`}
                    title={item.title}
                  >
                    {item.field!='Theme'?
                    <Input
                      key={index}
                      className="mt-1"
                      type={item.type}
                      value={formValues[item.field ? item.field : item.title]}
                      onChange={e => {
                        const value = e.target.value;
                        const numericValue = item.type === 'number' ? Math.max(0, Number(value)) : value;
                        handlePayloadChange(item.field ? item.field : item.title, String(numericValue));
                      }}
                      placeholder={item.placeholder}
                    />
                    :
                    <div className={`${style.completeDiv}`}>
                      <AutoComplete
                        ref={autoCompleteRef}
                        key={index}
                        className="mt-1 xq100"
                        value={formValues[item.field ? item.field : item.title]}
                        onSelect={(value) => {
                          setIsDropdownOpen(false); // 选中后隐藏下拉列表
                          const numericValue = item.type === 'number' ? Math.max(0, Number(value)) : value;
                          handlePayloadChange(item.field ? item.field : item.title, String(numericValue));
                          //setIsDropdownOpen(false);
                        }}
                        onChange={(value) => {
                          const numericValue = item.type === 'number' ? Math.max(0, Number(value)) : value;
                          handlePayloadChange(item.field ? item.field : item.title, String(numericValue));
                        }}
                        open={isDropdownOpen} // 控制下拉列表的显示与隐藏
                        onBlur={() => {
                           setIsDropdownOpen(false); // 当输入框失去焦点时，隐藏下拉列表
                        }}
                        onFocus={() => {
                          if(wordsList.length>0 && !isLoad){
                            setIsDropdownOpen(!isDropdownOpen); // 当输入框获得焦点时，显示下拉列表
                          }
                        }}
                        placeholder={item.placeholder}
                        // 假设这里有一个搜索建议列表，你可以根据实际情况修改
                        options={wordsList}
                      />
                      <div className={`xq-nowrap xq-flex-vcenter ${style.completePosw}`}>
                        <div className={`xq-nowrap xq-flex-vcenter ${style.completePos}`} onClick={()=>{searchKey()}}>
                          {isLoad?
                          <> <LoadingOutlined spin={true} style={{ fontSize: 14, color: 'rgba(0,0,0,0.5)' }} />loading..</>:
                          <>
                          <svg className={`${style.completesvg}`} xmlns="http://www.w3.org/2000/svg" width="12" height="13" viewBox="0 0 12 13" fill="none">
                          <path d="M10.2863 7.12932C10.2863 7.4631 10.0157 7.73368 9.68192 7.73368C9.34815 7.73368 9.07757 7.4631 9.07757 7.12932C9.07757 6.79555 9.34815 6.52497 9.68192 6.52497C10.0157 6.52497 10.2863 6.79555 10.2863 7.12932Z" fill="#3168F5"/>
                          <path d="M5.94083 1.0193C5.99275 0.878985 6.1912 0.878985 6.24312 1.01929L6.61036 2.01175C6.62669 2.05586 6.66147 2.09064 6.70558 2.10696L7.69803 2.4742C7.83834 2.52612 7.83834 2.72457 7.69803 2.77649L6.70558 3.14373C6.66147 3.16006 6.62669 3.19484 6.61036 3.23895L6.24312 4.2314C6.1912 4.37171 5.99275 4.37171 5.94083 4.2314L5.57359 3.23895C5.55727 3.19484 5.52249 3.16006 5.47838 3.14373L4.48593 2.77649C4.34562 2.72457 4.34562 2.52612 4.48593 2.4742L5.47838 2.10696C5.52249 2.09064 5.55727 2.05586 5.57359 2.01175L5.94083 1.0193Z" fill="#3168F5"/>
                          <path d="M3.06543 4.31548C3.11735 4.17517 3.3158 4.17517 3.36772 4.31548L3.62614 5.01385C3.64246 5.05796 3.67724 5.09274 3.72135 5.10906L4.41972 5.36748C4.56003 5.4194 4.56003 5.61785 4.41972 5.66977L3.72135 5.92819C3.67724 5.94451 3.64246 5.97929 3.62614 6.02341L3.36772 6.72178C3.3158 6.86209 3.11735 6.86209 3.06543 6.72178L2.80701 6.02341C2.79068 5.97929 2.7559 5.94451 2.71179 5.92819L2.01342 5.66977C1.87311 5.61785 1.87311 5.4194 2.01342 5.36748L2.71179 5.10906C2.7559 5.09274 2.79068 5.05796 2.80701 5.01385L3.06543 4.31548Z" fill="#3168F5"/>
                          <path d="M6.22631 5.17933C6.43984 4.80949 6.91276 4.68277 7.28261 4.8963C7.65245 5.10983 7.77917 5.58275 7.56564 5.9526L3.76974 12.5273C3.5562 12.8972 3.08328 13.0239 2.71344 12.8103C2.34359 12.5968 2.21687 12.1239 2.4304 11.754L6.22631 5.17933Z" fill="#3168F5"/>
                          <path opacity="0.01" d="M12 1L12 13L0 13L0 1L12 1Z" fill="#3168F5"/>
                          </svg>
                          优化标题
                           </>
                         }
                        </div>
                      </div>
                    </div>
                    }
                  </Field>
                ))}
                <div className="mt-4 flex flex-wrap" ref={checkCardListRef.current}>
                  {templateList2.map((item: any, index: number) => (
                    <>
                      <span className='long-span-Tabs'>
                        <CheckCard
                          key={index}
                          avatar=""
                          title=""
                          className='docHas xq-border-box'
                          value={item}
                          extra={
                            reshtml(item)
                          }
                        />
                      </span>
                    </>
                  ))}
                  <Upload
                    action="#"
                    accept=".pdf,.docx,.txt"
                    customRequest={customRequest}
                    listType="picture-card"
                    className="image-other xq-border-box"
                    maxCount={1}
                    fileList={fileList}
                    onPreview={handlePreview}
                    onChange={handleChange}
                    onRemove={handleRemove}
                    itemRender={customItemRender}
                  >
                    {fileList.length >= 1 ? null : uploadButton}
                  </Upload>
                </div>
                <Field
                    className={`mt-3`}
                    title={t('sampleTemplate.longText.localmaterials')}
                  >
                    <div className="mt-1 flex flex-wrap" ref={checkCardListRef.current}>
                    {templateList3.map((item: any, index: number) => (
                      <div className='docDiv xq-border-box'  key={index}>
                          <div className='xq-flex-cbetween'>
                          <img src={getAvatarForFileType(item.file_name)} className="docpic" alt="top"  />
                          <Dropdown
                              placement="topCenter"
                              menu={{
                                onClick: (e) => {
                                  if(e.key=='1'){
                                    handleDrawer(item)
                                  }else{
                                     //删除
                                     setTemplateList3(templateList3.filter((itemc:any,indexc:number)=>indexc!=index))
                                  }
                                },
                                items: [
                                  {
                                    label:t('sampleTemplate.longText.look'),
                                    key: '1'
                                  },
                                  {
                                    label:t('sampleTemplate.longText.deltxt'),
                                    key: '2'
                                  }
                                ]
                              }}
                            >
                              <EllipsisOutlined
                              style={{ fontSize: 22, color: 'rgba(0,0,0,0.5)' }}
                              onClick={e => e.stopPropagation()}
                            />
                          </Dropdown>
                          </div>
                          <p className="docName">{item.file_name}</p>
                          {/* <p className={`${style.fileSize}`}>{item.file_name},<span>{item.file_size}kb</span></p> */}
                      </div>
                  ))}
                  <Upload
                    action="#"
                    accept=".pdf,.docx,.txt,.HTML,.MarkDown,.JSON,.CSV,.Excel,.xlsx,.xls"
                    className="image-uploader xq-border-box"
                    customRequest={customRequest2}
                    listType="picture-card"
                    maxCount={1}
                    fileList={[]}
                    onPreview={handlePreview}
                  >
                    {fileList2.length >= 4 ? null : uploadButton2}
                  </Upload>
             </div>
                  </Field>
              </>
            ) : null}
          </CheckCard.Group>
        </div>
        }
        {/* <div>{JSON.stringify(chatAllTypeResult)}</div> */}
      </div>
    </>:null
  )
}

export default TemplatePage
