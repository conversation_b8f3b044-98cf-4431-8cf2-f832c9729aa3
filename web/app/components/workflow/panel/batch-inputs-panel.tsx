'use client'
import type { FC } from 'react'
import React, { useEffect, useMemo, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import {
  RiErrorWarningFill,
} from '@remixicon/react'
import { useNodes } from 'reactflow'
import CsvReader from '../../share/text-generation/run-batch/csv-reader'
import CsvDownload from '../../share/text-generation/run-batch/csv-download'
import ResDownload from '../../share/text-generation/run-batch/res-download'
import type { StartNodeType } from '../nodes/start/types'
import { BlockEnum, InputVarType } from '../types'
import { useStore as useAppStore } from '@/app/components/app/store'
import type { PromptConfig } from '@/models/debug'
import Res from '@/app/components/share/text-generation/result'
import type { InstalledApp } from '@/types/app'

// 公共组件
import { userInputsFormToPromptVariables } from '@/utils/model-config'
import { DEFAULT_VALUE_MAX_LEN } from '@/config'
import Toast from '@/app/components/base/toast'
import Loading from '@/app/components/base/loading'
import Scrollbar from '@/app/components/base/scrollbar'
import Button from '@/app/components/base/button'
import cn from '@/utils/classnames'
import { useFeatures } from '@/app/components/base/features/hooks'
import { FeatureEnum } from '@/app/components/base/features/types'

// 面板标签
enum PanelTab {
  input = 'INPUT',
  result = 'RESULT',
}

export type IRunBatchProps = {
  // 当前步骤
  tab: PanelTab
  // 发送事件
  onSend: () => void
}
const GROUP_SIZE = 5
enum TaskStatus {
  pending = 'pending',
  running = 'running',
  completed = 'completed',
  failed = 'failed',
}
type TaskParam = {
  inputs: Record<string, any>
}
type Task = {
  id: number
  status: TaskStatus
  params: TaskParam
}

const BatchInputsPanel: FC<IRunBatchProps> = ({ tab, onSend }) => {
  const { t } = useTranslation()
  const { notify } = Toast
  const { appDetail } = useAppStore()
  const nodes = useNodes<StartNodeType>()
  const startNode = nodes.find(node => node.data.type === BlockEnum.Start)
  const startVariables = startNode?.data.variables
  const visionConfig = useFeatures(s => s.features[FeatureEnum.file])?.image || { enabled: false }
  const textToSpeechConfig = useFeatures(s => s.features[FeatureEnum.text2speech])
  const moreLikeThisConfig = useFeatures(s => s.features[FeatureEnum.moreLikeThis])

  // 变量值
  const variables = useMemo(() => {
    const data = startVariables?.map(item => ({ [item.type]: item })) || []
    if (visionConfig?.enabled) {
      return userInputsFormToPromptVariables([
        ...data,
        {
          [InputVarType.files]: {
            type: InputVarType.files,
            variable: '__image',
            required: false,
            label: 'files',
          },
        },
      ] as any)
    }

    return userInputsFormToPromptVariables(data as any)
  }, [startVariables, visionConfig?.enabled])

  // csv文件数据
  const [csvData, setCsvData] = React.useState<string[][]>([])
  // 是否解析完成
  const [isParsed, setIsParsed] = React.useState(false)
  // 提示词配置
  const [promptConfig, setPromptConfig] = React.useState<PromptConfig | null>(null)
  const [controlSend, setControlSend] = React.useState(0)
  const [controlStopResponding, setControlStopResponding] = React.useState(0)
  // 批量任务
  const [allTaskList, doSetAllTaskList] = React.useState<Task[]>([])
  const allTaskListRef = useRef<Task[]>([])
  const setAllTaskList = (taskList: Task[]) => {
    doSetAllTaskList(taskList)
    allTaskListRef.current = taskList
  }
  const allSuccessTaskList = allTaskList.filter(task => task.status === TaskStatus.completed)
  const allFailedTaskList = allTaskList.filter(task => task.status === TaskStatus.failed)
  const allTasksFinished = allTaskList.every(task => task.status === TaskStatus.completed)
  const showTaskList = allTaskList.filter(task => task.status !== TaskStatus.pending)
  const allTasksRun = allTaskList.every(task => [TaskStatus.completed, TaskStatus.failed].includes(task.status))

  const getLatestTaskList = () => allTaskListRef.current
  // 批量任务结果
  const [batchCompletionRes, doSetBatchCompletionRes] = React.useState<Record<string, string>>({})
  const batchCompletionResRef = useRef<Record<string, string>>({})
  const setBatchCompletionRes = (res: Record<string, string>) => {
    doSetBatchCompletionRes(res)
    batchCompletionResRef.current = res
  }
  const getBatchCompletionRes = () => batchCompletionResRef.current
  // 当前组号
  const [currGroupNum, doSetCurrGroupNum] = React.useState(0)
  const currGroupNumRef = useRef(0)
  const setCurrGroupNum = (num: number) => {
    doSetCurrGroupNum(num)
    currGroupNumRef.current = num
  }
  const getCurrGroupNum = () => {
    return currGroupNumRef.current
  }
  // 重试事件
  const [controlRetry, setControlRetry] = React.useState(0)
  const handleRetryAllFailedTask = () => {
    setControlRetry(Date.now())
  }

  // 处理解析csv文件
  const handleParsed = (data: string[][]) => {
    setCsvData(data)
    setIsParsed(true)
  }
  // 检查批量输入
  const checkBatchInputs = (data: string[][]) => {
    // 如果没有数据
    if (!data || data.length === 0) {
      notify({ type: 'error', message: t('share.generation.errorMsg.empty') })
      return false
    }
    // 表格列名
    const headerData = data[0]
    let isMapVarName = true
    promptConfig?.prompt_variables.forEach((item, index) => {
      if (!isMapVarName)
        return

      if (item.name !== headerData[index])
        isMapVarName = false
    })

    if (!isMapVarName) {
      notify({ type: 'error', message: t('share.generation.errorMsg.fileStructNotMatch') })
      return false
    }

    let payloadData = data.slice(1)
    if (payloadData.length === 0) {
      notify({ type: 'error', message: t('share.generation.errorMsg.atLeastOne') })
      return false
    }

    // check middle empty line
    const allEmptyLineIndexes = payloadData.filter(item => item.every(i => i === '')).map(item => payloadData.indexOf(item))
    if (allEmptyLineIndexes.length > 0) {
      let hasMiddleEmptyLine = false
      let startIndex = allEmptyLineIndexes[0] - 1
      allEmptyLineIndexes.forEach((index) => {
        if (hasMiddleEmptyLine)
          return

        if (startIndex + 1 !== index) {
          hasMiddleEmptyLine = true
          return
        }
        startIndex++
      })

      if (hasMiddleEmptyLine) {
        notify({ type: 'error', message: t('share.generation.errorMsg.emptyLine', { rowIndex: startIndex + 2 }) })
        return false
      }
    }

    // check row format
    payloadData = payloadData.filter(item => !item.every(i => i === ''))
    // after remove empty rows in the end, checked again
    if (payloadData.length === 0) {
      notify({ type: 'error', message: t('share.generation.errorMsg.atLeastOne') })
      return false
    }
    let errorRowIndex = 0
    let requiredVarName = ''
    let moreThanMaxLengthVarName = ''
    let maxLength = 0
    payloadData.forEach((item, index) => {
      if (errorRowIndex !== 0)
        return

      promptConfig?.prompt_variables.forEach((varItem, varIndex) => {
        if (errorRowIndex !== 0)
          return
        if (varItem.type === 'string') {
          const maxLen = varItem.max_length || DEFAULT_VALUE_MAX_LEN
          if (item[varIndex].length > maxLen) {
            moreThanMaxLengthVarName = varItem.name
            maxLength = maxLen
            errorRowIndex = index + 1
            return
          }
        }
        if (!varItem.required)
          return

        if (item[varIndex].trim() === '') {
          requiredVarName = varItem.name
          errorRowIndex = index + 1
        }
      })
    })

    if (errorRowIndex !== 0) {
      if (requiredVarName)
        notify({ type: 'error', message: t('share.generation.errorMsg.invalidLine', { rowIndex: errorRowIndex + 1, varName: requiredVarName }) })

      if (moreThanMaxLengthVarName)
        notify({ type: 'error', message: t('share.generation.errorMsg.moreThanMaxLengthLine', { rowIndex: errorRowIndex + 1, varName: moreThanMaxLengthVarName, maxLength }) })

      return false
    }
    return true
  }
  // 发送
  const handleSend = () => {
    // 确认批量输入
    if (!checkBatchInputs(csvData))
      return
    // 所有批量任务是否结束
    if (!allTasksFinished) {
      notify({ type: 'info', message: t('appDebug.errorMessage.waitForBatchResponse') })
      return
    }

    onSend()
    const payloadData = csvData.filter(item => !item.every(i => i === '')).slice(1)
    const varLen = promptConfig?.prompt_variables.length || 0
    const allTaskList: Task[] = payloadData.map((item, i) => {
      const inputs: Record<string, string> = {}
      if (varLen > 0) {
        item.slice(0, varLen).forEach((input, index) => {
          inputs[promptConfig?.prompt_variables[index].key as string] = input
        })
      }
      return {
        id: i + 1,
        status: i < GROUP_SIZE ? TaskStatus.running : TaskStatus.pending,
        params: {
          inputs,
        },
      }
    })
    setAllTaskList(allTaskList)
    setControlSend(Date.now())
    setControlStopResponding(Date.now())
  }
  // 处理完成事件
  const handleCompleted = (completionRes: string, taskId?: number, isSuccess?: boolean) => {
    const allTaskListLatest = getLatestTaskList()
    const batchCompletionResLatest = getBatchCompletionRes()
    const pendingTaskList = allTaskListLatest.filter(task => task.status === TaskStatus.pending)
    const runTasksCount = 1 + allTaskListLatest.filter(task => [TaskStatus.completed, TaskStatus.failed].includes(task.status)).length
    const needToAddNextGroupTask = (getCurrGroupNum() !== runTasksCount) && pendingTaskList.length > 0 && (runTasksCount % GROUP_SIZE === 0 || (allTaskListLatest.length - runTasksCount < GROUP_SIZE))
    // avoid add many task at the same time
    if (needToAddNextGroupTask)
      setCurrGroupNum(runTasksCount)

    const nextPendingTaskIds = needToAddNextGroupTask ? pendingTaskList.slice(0, GROUP_SIZE).map(item => item.id) : []
    const newAllTaskList = allTaskListLatest.map((item) => {
      if (item.id === taskId) {
        return {
          ...item,
          status: isSuccess ? TaskStatus.completed : TaskStatus.failed,
        }
      }
      if (needToAddNextGroupTask && nextPendingTaskIds.includes(item.id)) {
        return {
          ...item,
          status: TaskStatus.running,
        }
      }
      return item
    })
    setAllTaskList(newAllTaskList)
    if (taskId) {
      setBatchCompletionRes({
        ...batchCompletionResLatest,
        [`${taskId}`]: completionRes,
      })
    }
  }

  const exportRes = allTaskList.map((task) => {
    const batchCompletionResLatest = getBatchCompletionRes()
    const res: Record<string, string> = {}
    const { inputs } = task.params
    promptConfig?.prompt_variables.forEach((v) => {
      res[v.name] = inputs[v.key]
    })
    let result = batchCompletionResLatest[task.id]
    // task might return multiple fields, should marshal object to string
    if (typeof batchCompletionResLatest[task.id] === 'object')
      result = JSON.stringify(result)

    res[t('share.generation.completionResult')] = result
    return res
  })

  useEffect(() => {
    (async () => {
      setPromptConfig({
        prompt_template: '', // placeholder for future
        prompt_variables: variables,
      } as PromptConfig)
    })()
  }, [variables])

  // 渲染结果
  const renderRes = (task?: Task) => (<Res
    key={task?.id}
    isWorkflow={true}
    isCallBatchAPI={true}
    isPC={true}
    isMobile={false}
    isInstalledApp={false}
    installedAppInfo={appDetail as unknown as InstalledApp}
    isError={task?.status === TaskStatus.failed}
    promptConfig={promptConfig}
    moreLikeThisEnabled={!!moreLikeThisConfig?.enabled}
    inputs={(task as Task).params.inputs}
    controlSend={controlSend}
    controlRetry={task?.status === TaskStatus.failed ? controlRetry : 0}
    controlStopResponding={controlStopResponding}
    onShowRes={() => {}}
    handleSaveMessage={() => {}}
    taskId={task?.id}
    onCompleted={handleCompleted}
    visionConfig={visionConfig as any}
    completionFiles={[]}
    isShowTextToSpeech={!!textToSpeechConfig?.enabled}
    siteInfo={appDetail?.site || null}
  />)

  if (!promptConfig)
    return <Loading></Loading>

  return (
    <>
      {/* 不是输入状态下隐藏 */}
      <Scrollbar className={cn(tab !== PanelTab.input && '!hidden', 'px-6')}>
        <CsvReader onParsed={handleParsed} />
        <CsvDownload vars={promptConfig?.prompt_variables} />
        <div className='flex justify-end'>
          <Button
            variant="primary"
            className='mt-5'
            onClick={handleSend}
            disabled={!isParsed || !allTasksRun}
          >
            <span>{t('common.operation.confirm')}</span>
          </Button>
        </div>
      </Scrollbar>
      {/* 不是结果状态下隐藏 */}
      <div className={cn(tab !== PanelTab.result && '!hidden', 'h-full flex flex-col')}>
        <Scrollbar className='grow px-6'>
          { showTaskList.map(task => renderRes(task)) }
        </Scrollbar>
        <div className='flex items-center justify-end px-6 pt-3 pb-6'>
          {allFailedTaskList.length > 0 && (
            <div className='flex items-center gap-1'>
              <RiErrorWarningFill className='w-4 h-4 text-[#D92D20]' />
              <div className='text-[#D92D20]'>{t('share.generation.batchFailed.info', { num: allFailedTaskList.length })}</div>
              <Button
                variant='primary'
                onClick={handleRetryAllFailedTask}
              >{t('share.generation.batchFailed.retry')}</Button>
            </div>
          )}
          {allSuccessTaskList.length > 0 && (
            <ResDownload
              isMobile={false}
              values={exportRes}
            />
          )}

        </div>
      </div>
    </>
  )
}
export default React.memo(BatchInputsPanel)
