import { memo } from 'react'
import { RiLock2Line } from '@remixicon/react'
import { useTranslation } from 'react-i18next'
import style from './styles/style.module.css'

import { useStore } from '@/app/components/workflow/store'
import type { EnvironmentVariable } from '@/app/components/workflow/types'
// 公共组件
import { Delete, Edit } from '@/app/components/base/icons/src/vender/line/general'
import { VariableIcon } from '@/app/components/base/icons/src/public/workflow'
import cn from '@/utils/classnames'

type EnvItemProps = {
  env: EnvironmentVariable
  onEdit: (env: EnvironmentVariable) => void
  onDelete: (env: EnvironmentVariable) => void
}

const EnvItem = ({
  env,
  onEdit,
  onDelete,
}: EnvItemProps) => {
  const { t } = useTranslation()
  const envSecrets = useStore(s => s.envSecrets)

  return (
    <div className={cn(style.variableItemWrap, 'group')}>
      <div className={cn(style.header)}>
        <div className={style.variableTitle}>
          <VariableIcon className='w-5 h-5 text-[#118FE9]' />
          <div className={style.normalTextG1}>{env.name}</div>
          <div className={style.normalTextG2}>{t(`workflow.env.type.${env.value_type}`)}</div>
          {env.value_type === 'secret' && <RiLock2Line className='w-3 h-3 text-gray-G2' />}
        </div>
        <div className={cn(style.actionWrap, 'hidden group-hover:flex')}>
          <Edit className={style.iconBtn} onClick={() => onEdit(env)}/>
          <Delete className={style.iconBtn} onClick={() => onDelete(env)}/>
        </div>
      </div>
      {/* <div className={style.desctext}>{env.description}</div> */}
      <div className={style.desctext}>{env.value_type === 'secret' ? envSecrets[env.id] : env.value}</div>
    </div>
  )
}

export default memo(EnvItem)
