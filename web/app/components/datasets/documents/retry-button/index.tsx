'use client'
import type { FC } from 'react'
import React, { useEffect, useMemo, useReducer, useState } from 'react'
import { useTranslation } from 'react-i18next'
import useSWR from 'swr'
import { Divider } from 'antd'
import s from './style.module.css'
import { getErrorDocs, retryErrorDocs } from '@/service/datasets'
import type { IndexingStatusResponse } from '@/models/datasets'
import TextButton from '@/app/components/base/button/text-button'
import { WarnFill } from '@/app/components/base/icons/src/public/common'

type Props = {
  datasetId: string
  onRefresh: () => void
}
type IIndexState = {
  value: string
}
type ActionType = 'retry' | 'success' | 'error'

type IAction = {
  type: ActionType
}

// 索引方式状态
const indexStateReducer = (state: IIndexState, action: IAction) => {
  const actionMap = {
    retry: 'retry',
    success: 'success',
    error: 'error',
  }

  return {
    ...state,
    value: actionMap[action.type] || state.value,
  }
}

// 导出错误文档 hook
export const useErrorDocs = (datasetId: string) => {
  const { data: errorDocs, mutate } = useSWR({ datasetId }, getErrorDocs)
  const [manualError, setManualError] = useState(false)

  const hasError = useMemo(() => {
    return (errorDocs?.total || 0) > 0 || manualError
  }, [errorDocs?.total, manualError])

  const updateErrorStatus = (hasError: boolean) => {
    setManualError(hasError)
    if (hasError) {
      // 触发重新获取错误文档
      mutate()
    }
  }

  return {
    errorDocs,
    hasError,
    updateErrorStatus,
  }
}

const RetryButton: FC<Props> = ({ datasetId, onRefresh }) => {
  const { t } = useTranslation()
  // 索引方式
  const [indexState, dispatch] = useReducer(indexStateReducer, { value: 'success' })
  // 使用 hook 获取错误文档
  const { errorDocs, hasError, updateErrorStatus } = useErrorDocs(datasetId)

  // 重新分段
  const onRetryErrorDocs = async () => {
    dispatch({ type: 'retry' })
    const document_ids = errorDocs?.data.map((doc: IndexingStatusResponse) => doc.id) || []
    const res = await retryErrorDocs({ datasetId, document_ids })
    if (res.result === 'success')
      dispatch({ type: 'success' })
    else dispatch({ type: 'error' })
    onRefresh()
  }

  useEffect(() => {
    if (errorDocs?.total === 0)
      dispatch({ type: 'success' })
    else dispatch({ type: 'error' })
  }, [errorDocs?.total])

  if (indexState.value === 'success' || !hasError)
    return null

  return (
    <div className={s['retry-btn']}>
      <WarnFill className='h-4 w-4' />
      <span className={s['warm-tip']}>
        {errorDocs?.total} {t('dataset.notify.docsFailedNotice')}
      </span>
      <Divider type='vertical' className='!mx-2' />
      <TextButton
        disabled={indexState.value === 'retry'}
        onClick={indexState.value === 'error' ? onRetryErrorDocs : undefined}
      >
        {t('common.operation.retry')}
      </TextButton>
    </div>
  )
}
export default RetryButton
