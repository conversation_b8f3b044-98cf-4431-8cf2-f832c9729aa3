'use client'
import React, { useEffect, useMemo, useState } from 'react'
import { Tabs } from 'antd'
import { useTabSearchParams } from '@/hooks/use-tab-searchparams'

import style from '@/app/components/app/styles/index.module.css'
import Scrollbar from '@/app/components/base/scrollbar'

type props = {
  options: Array<{key: string, label: string, tabChildren: React.ReactNode}>,
  defaultTab?: string,
}

const TabMainContainer = ({ options, defaultTab }: props) => {
  // 当前激活tab
  const [activeTab, setActiveTab] = useTabSearchParams({
    defaultTab: defaultTab || '',
  })
  const activeTabContent = useMemo(() => {
    return options.find(item => item.key === activeTab)?.tabChildren
  }, [options, activeTab])
  return (
    <div className={style.wrap}>
      <div className={style['left-part']}>
        <Tabs
          className='mb-3 px-8'
          items={options}
          activeKey={activeTab}
          onChange={value => setActiveTab(value)}
        ></Tabs>
        <Scrollbar className={style['left-content']}>
          {activeTabContent && React.cloneElement(
            activeTabContent as React.ReactElement,
            { setActiveTab }
          )}
        </Scrollbar>
      </div>
    </div>
  )
}
export default React.memo(TabMainContainer)
