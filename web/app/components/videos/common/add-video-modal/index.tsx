'use client'
import type { FC } from 'react'
import React, { useEffect, useState } from 'react'
import { useGetState } from 'ahooks'
import { useTranslation } from 'react-i18next'
import style from './styles/index.module.css'
import { fetchVideoList } from '@/service/videos'
import type { Video } from '@/types/videos'

// 公共组件
import cn from '@/utils/classnames'
import useTimestamp from '@/hooks/use-timestamp'
import Button from '@/app/components/base/button'
import CardList from '@/app/components/base/card-list'
import Card from '@/app/components/base/card'
import Modal from '@/app/components/base/modal'
import Avatar from '@/app/components/base/app-info/avatar'

export type ISelectVideoProps = {
  isShow: boolean
  onClose: () => void
  value: Video[]
  onSelect: (dataSet: Video[]) => void
}

const SelectVideo: FC<ISelectVideoProps> = ({
  isShow,
  onClose,
  value,
  onSelect,
}) => {
  const { t } = useTranslation()
  const { formatTime } = useTimestamp()

  const canSelectMulti = true

  // 选中的知识库信息
  const [selected, setSelected] = React.useState<Video[]>(value)
  // 页码
  const [page, setPage, getPage] = useGetState(1)
  // 是否还有未加载的
  const [hasMore, setHasMore] = useState(true)
  // 视频库是否正在加载
  const [loaded, setLoaded] = React.useState(false)
  // 视频库列表
  const [videos, setVideos] = React.useState<Video[]>([])

  // 获取视频库列表
  const getVideoList = async () => {
    if (hasMore) {
      setLoaded(true)
      const { data } = await fetchVideoList({ page })
      const { info, has_more } = data
      setHasMore(has_more)
      const newList = [...(videos || []), ...info]
      setVideos(newList)
      setPage(getPage() + 1)
      setLoaded(false)
    }
  }
  // 是否选中
  const isSelectd = (video: Video) => {
    return selected.some(i => i.id === video.id)
  }
  // 勾选视频库
  const toggleSelect = (video: Video) => {
    const isSelected = isSelectd(video)
    if (isSelected) {
      onSelect(selected.filter(item => item.id !== video.id))
      setSelected(selected.filter(item => item.id !== video.id))
    }
    else {
      if (canSelectMulti) {
        onSelect([...selected, video])
        setSelected([...selected, video])
      }
      else {
        setSelected([video])
        onSelect([video])
      }
    }
  }

  useEffect(() => {
    getVideoList()
  }, [])

  return (
    <Modal
      isShow={isShow}
      onClose={onClose}
      closable={true}
      scrollable={false}
      className='w-[720px]'
      title={t('videos.action.selectVideos')}
    >
      <CardList
        type='scroll'
        loading={loaded}
        layout='line'
        className='px-8'
        loadFunc={getVideoList}
      >
        {
          videos.map(item => (
            <Card
              key={item.id}
              replaceClassName={style.card}
              generateHead={() => <Avatar appMode={item.type} size={44}></Avatar>}
              title={() => (
                <>
                  <div className={style.cardInfoHeader}>
                    {/* 标题 */}
                    <div className={cn('max-w-[200px] title-16-26 overflow-hidden text-ellipsis whitespace-nowrap')}>{item.name}</div>
                    {/* 操作按钮 */}
                    {
                      isSelectd(item)
                        ? <div className='group'>
                          <Button
                            className='group-hover:hidden !border-none !w-[72px] !text-primary-P1 !bg-primary-P4 !shrink-0'
                            size={'small'}
                            variant='secondary'
                          >{t('common.status.added')}</Button>
                          <Button
                            className='group-hover:flex hidden !w-[72px] !shrink-0'
                            size={'small'}
                            variant={'warning'}
                            onClick={() => toggleSelect(item)}
                          >{t('common.operation.remove')}</Button>
                        </div>
                        : <Button
                          className='!w-[72px] !shrink-0'
                          size={'small'}
                          variant='secondary-accent'
                          onClick={() => toggleSelect(item)}
                        >{t('common.operation.add')}</Button>
                    }

                  </div>
                </>
              )}
              description={item.description || '-'}
            >
              <div className='text-[12px] leading-[18px] text-gray-G3 !visible'>
                {item.created_at ? formatTime(item.created_at, t('common.dateFormat.dateTime') as string) : '-'}
              </div>
            </Card>
          ))
        }
      </CardList>
    </Modal>
  )
}
export default React.memo(SelectVideo)
