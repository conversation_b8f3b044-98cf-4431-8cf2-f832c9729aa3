import type { FC } from 'react'
import { memo, useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import { Divider } from 'antd'
import VarReferencePicker from '../_base/components/variable/var-reference-picker'
import useConfig from './use-config'
import RetrievalConfig from './components/retrieval-config'
import AddKnowledge from './components/add-dataset'
import DatasetList from './components/dataset-list'
import type { KnowledgeRetrievalNodeType } from './types'
import type { DataSet } from '@/models/datasets'
import Field from '@/app/components/workflow/nodes/_base/components/field'
import OutputVars from '@/app/components/workflow/nodes/_base/components/variable/output-vars'
import { InputVarType, type NodePanelProps } from '@/app/components/workflow/types'
import BeforeRunForm from '@/app/components/workflow/nodes/_base/components/before-run-form'
import ResultPanel from '@/app/components/workflow/run/result-panel'
import ForkTip from '@/app/components/base/forkTip'

const i18nPrefix = 'workflow.nodes.knowledgeRetrieval'

const Panel: FC<NodePanelProps<KnowledgeRetrievalNodeType>> = ({
  id,
  data,
}) => {
  const { t } = useTranslation()

  const {
    readOnly,
    inputs,
    filterVar,
    selectedDatasets,
    forkDatasets,
    allDatasets,
    isShowSingleRun,
    runningStatus,
    query,
    setQuery,
    runResult,
    rerankModelOpen,
    setRerankModelOpen,
    handleRetrievalModeChange,
    handleCompletionParamsChange,
    handleModelChanged,
    handleMultipleRetrievalConfigChange,
    hideSingleRun,
    handleRun,
    handleStop,
    handleOnDatasetsChange,
    handleQueryVarChange,
  } = useConfig(id, data)

  const handleOpenFromPropsChange = useCallback((openFromProps: boolean) => {
    setRerankModelOpen(openFromProps)
  }, [setRerankModelOpen])

  return (
    <>
      <div className='flex flex-col gap-4'>
        {/* 输入变量 */}
        <Field title={t(`${i18nPrefix}.queryVariable`)}>
          <VarReferencePicker
            nodeId={id}
            readonly={readOnly}
            isShowNodeName
            value={inputs.query_variable_selector}
            onChange={handleQueryVarChange}
            filterVar={filterVar}
          />
        </Field>
        {/* 添加知识库 */}
        <Field
          title={t(`${i18nPrefix}.knowledge`)}
          operations={
            <div className='flex items-center space-x-1'>
              <RetrievalConfig
                payload={{
                  retrieval_mode: inputs.retrieval_mode,
                  multiple_retrieval_config: inputs.multiple_retrieval_config,
                  single_retrieval_config: inputs.single_retrieval_config,
                }}
                onRetrievalModeChange={handleRetrievalModeChange}
                onMultipleRetrievalConfigChange={handleMultipleRetrievalConfigChange}
                singleRetrievalModelConfig={inputs.single_retrieval_config?.model}
                onSingleRetrievalModelChange={handleModelChanged as any}
                onSingleRetrievalModelParamsChange={handleCompletionParamsChange}
                readonly={readOnly || !selectedDatasets.length}
                openFromProps={rerankModelOpen}
                onOpenFromPropsChange={handleOpenFromPropsChange}
                selectedDatasets={selectedDatasets}
              />
              {!readOnly && (<div className='w-px h-3 bg-gray-200'></div>)}
              {!readOnly && (
                <AddKnowledge
                  selected={selectedDatasets}
                  onChange={handleOnDatasetsChange}
                />
              )}
            </div>
          }
        >
          <>
            {forkDatasets.length > 0 && data.fork && (
              <ForkTip
                className='forkTip mx-4 mb-2'
                message={t('common.fork.datasetTip')}
                onClose={() => handleOnDatasetsChange([])}
              >
                <div>
                  {forkDatasets.map(({ id, name }) => (
                    <div key={id} className=''>
                      <div className=''>
                        · {name}
                      </div>
                    </div>
                  ))}
                </div>
              </ForkTip>
            )}
            <DatasetList
              list={allDatasets as DataSet[]}
              onChange={handleOnDatasetsChange}
              readonly={readOnly}
            />
          </>
        </Field>
      </div>
      <Divider />
      {/* 输出变量 */}
      <OutputVars
        vars={[
          {
            name: 'result',
            type: 'Array[Object]',
            description: t(`${i18nPrefix}.outputVars.output`),
            subItems: [
              {
                name: 'content',
                type: 'string',
                description: t(`${i18nPrefix}.outputVars.content`),
              },
              {
                name: 'title',
                type: 'string',
                description: t(`${i18nPrefix}.outputVars.title`),
              },
              {
                name: 'url',
                type: 'string',
                description: t(`${i18nPrefix}.outputVars.url`),
              },
              {
                name: 'icon',
                type: 'string',
                description: t(`${i18nPrefix}.outputVars.icon`),
              },
              {
                name: 'metadata',
                type: 'object',
                description: t(`${i18nPrefix}.outputVars.metadata`),
              },
            ],

          },
        ]}
      ></OutputVars>
      {isShowSingleRun && (
        <BeforeRunForm
          nodeName={inputs.title}
          onHide={hideSingleRun}
          forms={[
            {
              inputs: [{
                label: t(`${i18nPrefix}.queryVariable`)!,
                variable: 'query',
                type: InputVarType.paragraph,
                required: true,
              }],
              values: { query },
              onChange: keyValue => setQuery((keyValue as any).query),
            },
          ]}
          runningStatus={runningStatus}
          onRun={handleRun}
          onStop={handleStop}
          result={<ResultPanel {...runResult} showSteps={false} />}
        />
      )}
    </>
  )
}

export default memo(Panel)
