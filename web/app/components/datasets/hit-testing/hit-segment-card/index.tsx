'use client'

import type { FC } from 'react'
import { useTranslation } from 'react-i18next'
import { Divider } from 'antd'
import s from './style.module.css'
import type { SegmentDetailModel } from '@/models/datasets'

import DocumentTitle from '@/app/components/datasets/common/document-title'
import SegmentIndexTag from '@/app/components/datasets/common/segment-index'

import Card from '@/app/components/base/card'
import Tag from '@/app/components/base/tag'
import { Hit } from '@/app/components/base/icons/src/vender/line/others'
import Slider from '@/app/components/base/slider'
import { useProviderContext } from '@/context/provider-context'

type HitSegmentCardProps = {
  detail: SegmentDetailModel & { document?: { name: string } }
  refSource?: {
    title: string
    uri: string
  }
  score?: number
  contentExternal?: string
  answer?: string
  onClick: () => void
}

const HitSegmentCard: FC<HitSegmentCardProps> = ({ detail, refSource, onClick, score, answer, contentExternal }) => {
  const { t } = useTranslation()
  const { useXIYANRag } = useProviderContext()
  // 卡片内容
  const renderContent = () => {
    if (answer) {
      // eslint-disable-next-line react/display-name
      return () => (
        <>
          <div className='flex mb-2'>
            <div className='mr-2 text-[13px] font-semibold text-gray-400'>Q</div>
            <div className='text-[13px]'>{detail?.content}</div>
          </div>
          <div className='flex'>
            <div className='mr-2 text-[13px] font-semibold text-gray-400'>A</div>
            <div className='text-[13px]'>{answer}</div>
          </div>
        </>
      )
    }

    if (contentExternal)
      return contentExternal

    return detail?.content
  }
  return (
    <Card
      onClick={onClick}
      className={s['hit-segment-card']}
      title={() => (
        <div className='flex flex-col'>
          {/* 文件信息 */}
          <div className="flex items-center justify-between">
            <DocumentTitle
              name={detail?.document?.name || refSource?.title || ''}
              extension={(detail?.document?.name || refSource?.title || '').split('.').pop() || 'txt'}
              wrapperCls='w-full mr-10'
              iconCls="!h-4 !w-4 !bg-contain"
              textCls="!text-S3 !leading-H3 text-gray-G1 !font-semibold overflow-hidden whitespace-nowrap text-ellipsis"
            />
            <Tag className='shrink-0' color='blue'>
              <Hit className='h-4 w-4 mr-1'></Hit>
              {t('dataset.hit.hitSeg')}
            </Tag>
          </div>
          <Divider className='my-3' type='horizontal'></Divider>
          {/* 段落位置、分数 */}
          <div className='flex w-full items-center justify-between'>
            {!useXIYANRag ? <SegmentIndexTag positionId={detail.position}></SegmentIndexTag> : <span></span>}
            <div className='flex w-[220px] items-center'>
              <div className='text-S3 leading-H3 text-gray-G2 font-semibold shrink-0'>{t('dataset.hit.score')}</div>
              <Slider className='mx-2 w-full shrink' tooltip={{ open: false }} min={0} max={100} value={Number(score?.toFixed(2)) * 100}></Slider>
              <div className='text-S3 leading-H3 text-gray-G2 shrink-0'>{Number(score?.toFixed(2))}</div>
            </div>
          </div>
        </div>
      )}
      descriptionLine={5.8}
      description={renderContent()}
    >
    </Card>
  )
}

export default HitSegmentCard
