import type { FC } from 'react'
import React from 'react'
import type { LLMNodeType } from './types'
import { useMiniConfig } from './use-config'

// 模型选择公共能力
import {
  useTextGenerationCurrentProviderAndModelAndModelList,
} from '@/app/components/account-setting/model-provider-page/hooks'
import ModelSelector from '@/app/components/account-setting/model-provider-page/model-selector'
// 工作流公共能力
import type { NodeProps } from '@/app/components/workflow/types'
import s from '@/app/components/workflow/styles/index.module.css'

const Node: FC<NodeProps<LLMNodeType>> = ({
  id,
  data,
}) => {
  // 预处理
  const result = useMiniConfig(id, data)
  const { provider, name: modelId } = data.model || {}
  const {
    textGenerationModelList,
    mutateTextGenerationModelList,
    isTextGenerationModelListLoading,
  } = useTextGenerationCurrentProviderAndModelAndModelList()

  // 是否已经设置模型
  const hasSetModel = provider && modelId

  if (!hasSetModel)
    return null
  return (
    <div className={s['node-content-item']}>
      {hasSetModel && (
        <ModelSelector
          loading={isTextGenerationModelListLoading}
          defaultModel={{ provider, model: modelId }}
          modelList={textGenerationModelList}
          readonly
          onFetch={mutateTextGenerationModelList}
        />
      )}
    </div>
  )
}

export default React.memo(Node)
