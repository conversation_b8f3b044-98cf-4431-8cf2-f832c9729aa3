import React, { useEffect, useState } from 'react'
import { Card, Form, Input, List, Tooltip } from 'antd'
import s from './style.module.css'
type TableFormData = {
  [key: string]: string
}

type TableData = {
  headers: string[]
  values: string[]
  detail?: any
}

const { TextArea } = Input

type TableContentProps = {
  editable?: boolean
  detail?: any
  segInfo?: any
  onDataChange?: (data: TableData) => void
}

const TableContent: React.FC<TableContentProps> = ({
  editable = false,
  detail,
  segInfo,
  onDataChange,
}) => {
  const [form] = Form.useForm<TableFormData>()
  const [formFields, setFormFields] = useState<Array<{ name: string; label: string }>>([])
  const [lastSegInfoId, setLastSegInfoId] = useState<string>('')
  const [lastEditableState, setLastEditableState] = useState<boolean>(false)

  // 处理表单提交
  const handleFinish = (values: TableFormData) => {
    if (!onDataChange)
      return

    // 直接计算数据，避免使用 transformFormData
    const headers: string[] = []
    const tableValues: string[] = []
    formFields.forEach((field: { name: string; label: string }) => {
      tableValues.push(field.label)
      headers.push(values[field.name] || '')
    })
    onDataChange({ headers, values: tableValues, detail })
  }

  // 处理表单值变化
  const handleValuesChange = (_: any, allValues: TableFormData) => {
    if (!editable || !onDataChange)
      return

    // 直接计算数据，避免使用 transformFormData
    const headers: string[] = []
    const values: string[] = []
    formFields.forEach((field: { name: string; label: string }) => {
      values.push(field.label)
      headers.push(allValues[field.name] || '')
    })
    onDataChange({ headers, values, detail })
  }

  // 从 detail 数据生成表单字段
  useEffect(() => {
    console.log('TableContent useEffect - segInfo:', segInfo)
    const headers = segInfo?.headers || segInfo?.columns
    console.log('TableContent useEffect - headers:', headers)
    if (!headers)
      return

    const currentSegInfoId = segInfo?.id || ''
    // 创建一个唯一标识符，包含 id 和 headers 信息
    const currentIdentifier = `${currentSegInfoId}_${JSON.stringify(headers)}`

    // 检测是否是新增模式且从不可编辑变为可编辑（弹窗打开）
    const isNewAddAndJustOpened = editable && !lastEditableState && (!segInfo?.id || segInfo?.id === '')

    // 只有当segInfo发生变化或者是新增模式刚打开时才重新初始化
    console.log('TableContent - currentIdentifier:', currentIdentifier)
    console.log('TableContent - lastSegInfoId:', lastSegInfoId)
    console.log('TableContent - isNewAddAndJustOpened:', isNewAddAndJustOpened)
    console.log('TableContent - should update:', currentIdentifier !== lastSegInfoId || isNewAddAndJustOpened)

    if (currentIdentifier !== lastSegInfoId || isNewAddAndJustOpened) {
      console.log('TableContent - updating form fields')
      // 生成表单字段配置
      const fields = headers.map((title: string, index: number) => ({
        name: `column${index}`,
        label: title,
      }))
      console.log('TableContent - generated fields:', fields)
      setFormFields(fields)

      // 设置表单初始值
      // 如果是新增模式且segInfo没有id，则清空表单；否则使用segInfo的值
      const isNewAdd = !segInfo?.id || segInfo?.id === ''
      const initialValues = fields.reduce((acc: TableFormData, field: { name: string; label: string }) => ({
        ...acc,
        [field.name]: isNewAdd ? '' : (segInfo.values?.[fields.indexOf(field)] || ''),
      }), {})

      form.setFieldsValue(initialValues)
      setLastSegInfoId(currentIdentifier)
      setLastEditableState(editable)

      // 初始化完成后调用 onDataChange
      if (onDataChange) {
        const headers: string[] = []
        const values: string[] = []
        fields.forEach((field: { name: string; label: string }) => {
          values.push(field.label)
          headers.push(initialValues[field.name] || '')
        })
        onDataChange({ headers, values, detail })
      }
    }
  }, [segInfo?.id, segInfo?.headers, segInfo?.columns, segInfo?.values, lastSegInfoId, editable, lastEditableState])

  // 单独的 useEffect 来处理新增模式下的表单清空
  useEffect(() => {
    // 当 editable 变为 true 且是新增模式时，强制清空表单
    if (editable && (!segInfo?.id || segInfo?.id === '') && formFields.length > 0) {
      console.log('TableContent - 强制清空新增模式的表单')
      const emptyValues = formFields.reduce((acc: TableFormData, field: { name: string; label: string }) => ({
        ...acc,
        [field.name]: '',
      }), {})
      form.setFieldsValue(emptyValues)

      // 通知父组件数据变化
      if (onDataChange) {
        const headers: string[] = []
        const values: string[] = []
        formFields.forEach((field: { name: string; label: string }) => {
          values.push(field.label)
          headers.push('')
        })
        onDataChange({ headers, values, detail })
      }
    }
  }, [editable, segInfo?.id, formFields]) // 当 editable 或 formFields 变化时触发

  const renderReadOnlyContent = () => {
    return (
      <List
        className={s.listStyle}
        header={
          <div className={s.listHeader}>
            <span>字段名</span>
            <span>字段值</span>
          </div>
        }
        dataSource={formFields}
        renderItem={(field) => {
          const fieldValue = form.getFieldValue(field.name)
          const hasLongContent = (fieldValue?.match(/\n/g) || []).length > 2

          return (
            <List.Item className={s.listItem}>
              <div className={s.fieldLabel}>{field.label}</div>
              {hasLongContent
                ? (
                  <Tooltip title={fieldValue}>
                    <div className={s.fieldValue}>{fieldValue}</div>
                  </Tooltip>
                )
                : (
                  <div className={s.fieldValue}>{fieldValue}</div>
                )}
            </List.Item>
          )
        }}
      />
    )
  }

  console.log('TableContent render - formFields:', formFields)
  console.log('TableContent render - editable:', editable)

  return (
    <Card>
      {editable
        ? (
          <Form
            form={form}
            layout="horizontal"
            onFinish={handleFinish}
            disabled={!editable}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}
            style={{ minWidth: 480, maxWidth: 480 }}
            onValuesChange={handleValuesChange}
          >
            {formFields.length > 0
              ? formFields.map(field => (
                <Form.Item
                  key={field.name}
                  name={field.name}
                  label={field.label}
                >
                  <TextArea
                    placeholder={`请输入${field.label}`}
                    autoComplete="off"
                    autoSize={{ minRows: 1 }}
                  />
                </Form.Item>
              ))
              : <div>没有表单字段</div>}
          </Form>
        )
        : renderReadOnlyContent()}
    </Card>
  )
}

export default TableContent
