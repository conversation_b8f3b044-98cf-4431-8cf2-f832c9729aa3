'use client'
import type { FC } from 'react'
import React, { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useAsyncEffect, useDebounce, useGetState } from 'ahooks'
import type { TableColumnsType } from 'antd'
import { Form, Input, Table } from 'antd'
import produce from 'immer'
import type { Collection, Credential, CustomCollectionDetail, CustomParamSchema } from '../../types'
import { AuthHeaderPrefix, AuthType } from '../../types'
import { getCollectionDetail } from '../../utils'
import GetSchema from './get-schema'
import GetCredentials from './get-credentials'
import TestApi from './test-api'
import { createCustomCollection, parseParamsSchema, updateCustomCollection } from '@/service/tools'

/* 公共组件 */
import { useProviderContext } from '@/context/provider-context'
import Button from '@/app/components/base/button'
import Modal from '@/app/components/base/modal'
import Avatar from '@/app/components/base/avatar'
import TextButton from '@/app/components/base/button/text-button'
import Toast from '@/app/components/base/toast'
import { useFormDisabled } from '@/hooks/use-form'

type Props = {
  collection?: Collection
  onHide: () => void
  onAdd?: () => void
  onEdit?: () => void
}
// Add and Edit
const EditCustomCollectionModal: FC<Props> = ({
  collection,
  onHide,
  onAdd,
  onEdit,
}) => {
  const { t } = useTranslation()
  const { onPlanInfoChanged } = useProviderContext()
  const [form] = Form.useForm()
  const disabled = useFormDisabled(form)
  const isAdd = !collection
  const isEdit = !!collection

  // 可用工具列表
  const [paramsSchemas, setParamsSchemas] = useState<CustomParamSchema[]>([])
  // 当前工具
  const [currTool, setCurrTool] = useState<CustomParamSchema | null>(null)
  // 是否正在校验
  const isChecking = useRef(false)
  // 自定义工具集
  const [customCollection, setCustomCollection, getCustomCollection] = useGetState<CustomCollectionDetail>({
    provider: '',
    credentials: {
      auth_type: AuthType.none,
      api_key_header: 'Authorization',
      api_key_header_prefix: AuthHeaderPrefix.basic,
    },
    icon: {
      content: '/assets/avatar/tool.svg',
      background: '#FEF7C3',
    },
    icon_url: '/assets/avatar/tool.svg',
    schema_type: '',
    schema: '',
  } as any)
  // 是否显示测试api弹窗
  const [isShowTestApi, setIsShowTestApi] = useState(false)

  const schema = customCollection.schema
  const credential = customCollection.credentials
  const icon = customCollection.icon_url
  const debouncedSchema = useDebounce(schema, { wait: 500 })

  // 新增自定义工具
  const handleAdd = async (data: CustomCollectionDetail) => {
    await createCustomCollection(data)
    onPlanInfoChanged()
    onAdd && onAdd()
    Toast.notify({
      type: 'success',
      message: t('tools.notify.successCreateCustom'),
    })
  }
  // 编辑自定义工具
  const handleEdit = async (data: CustomCollectionDetail) => {
    await updateCustomCollection(data)
    onEdit && onEdit()
    Toast.notify({
      type: 'success',
      message: t('tools.notify.updateCustomSuccess'),
    })
  }
  // 保存
  const handleSave = () => {
    const { provider, custom_disclaimer, privacy_policy } = form.getFieldsValue()
    const postData = produce(customCollection, (draft) => {
      delete draft.tools

      if (draft.credentials.auth_type === AuthType.none) {
        delete draft.credentials.api_key_header
        delete draft.credentials.api_key_header_prefix
        delete draft.credentials.api_key_value
      }

      draft.provider = provider
      draft.custom_disclaimer = custom_disclaimer
      draft.privacy_policy = privacy_policy
    })

    if (isAdd)
      handleAdd?.(postData)
    if (isEdit) {
      handleEdit?.({
        ...postData,
        original_provider: customCollection.provider,
      })
    }
  }

  // 设置schema
  const setSchema = (schema: string) => {
    const newCollection = produce(customCollection, (draft) => {
      draft.schema = schema
    })
    setCustomCollection(newCollection)
  }
  // 设置鉴权
  const setCredential = (credential: Credential) => {
    const newCollection = produce(customCollection, (draft) => {
      draft.credentials = credential
    })
    setCustomCollection(newCollection)
  }
  // 设置头像
  const setAvatar = (avatar: string) => {
    const newCollection = produce(customCollection, (draft) => {
      draft.icon = {
        content: avatar,
        background: '#fff',
      }
    })
    setCustomCollection(newCollection)
  }
  // URI经过decode解码
  const getPath = (url: string) => {
    if (!url)
      return ''

    try {
      const path = decodeURI(new URL(url).pathname)
      return path || ''
    }
    catch (e) {
      return url
    }
  }

  useEffect(() => {
    if (!debouncedSchema) {
      const customCollection = getCustomCollection()
      const newCollection = produce(customCollection, (draft) => {
        draft.schema_type = ''
      })
      setCustomCollection(newCollection)
      setParamsSchemas([])
      return
    }
    (async () => {
      isChecking.current = true
      try {
        const { parameters_schema, schema_type } = await parseParamsSchema(debouncedSchema)
        const customCollection = getCustomCollection()
        const newCollection = produce(customCollection, (draft) => {
          draft.schema_type = schema_type
        })
        setCustomCollection(newCollection)
        setParamsSchemas(parameters_schema)
      }
      catch (e) {
        const customCollection = getCustomCollection()
        const newCollection = produce(customCollection, (draft) => {
          draft.schema_type = ''
        })
        setCustomCollection(newCollection)
        setParamsSchemas([])
      }
      isChecking.current = false
    })()
  }, [debouncedSchema, getCustomCollection, setCustomCollection])

  useAsyncEffect(async () => {
    if (collection) {
      const payload = await getCollectionDetail(collection) as unknown as CustomCollectionDetail
      form.setFieldsValue({
        provider: payload.provider,
        privacy_policy: payload.privacy_policy,
        custom_disclaimer: payload.custom_disclaimer,
      })
      setCustomCollection(payload)
      setParamsSchemas(payload?.tools as any || [])
    }
  }, [collection, form, setCustomCollection])

  // 表格列
  const columns: TableColumnsType<CustomParamSchema> = [
    {
      title: t('tools.info.availableTools.name'),
      key: 'operation_id',
      dataIndex: 'operation_id',
      ellipsis: true,
    },
    {
      title: t('tools.info.availableTools.description'),
      key: 'summary',
      dataIndex: 'summary',
      ellipsis: true,
    },
    {
      title: t('tools.info.availableTools.method'),
      key: 'method',
      dataIndex: 'method',
    },
    {
      title: t('tools.info.availableTools.path'),
      key: 'server_url',
      dataIndex: 'server_url',
      ellipsis: true,
      render: (url: string) => getPath(url),
    },
    {
      title: t('tools.info.availableTools.action'),
      key: 'action',
      width: 70,
      render: (_: any, record: CustomParamSchema) => (
        <TextButton
          size='small'
          onClick={() => {
            setCurrTool(record)
            setIsShowTestApi(true)
          }}
        >
          {t('tools.info.availableTools.test')}
        </TextButton>
      ),
    },
  ]

  return (
    <Modal
      isShow
      onClose={onHide}
      closable
      title={t(`tools.modal.${isAdd ? 'createCustomTool' : (isEdit ? 'editCustomTool' : 'toolDetail')}`)!}
      className='!w-[630px] !max-w-[630px]'
      footer={
        <>
          <Button variant='secondary-accent' className='!w-[92px]' onClick={onHide}>{t('common.operation.cancel')}</Button>
          <Button disabled={disabled || !schema} className='!w-[92px] ml-4' variant='primary' onClick={handleSave}>{t('common.operation.save')}</Button>
        </>
      }
    >
      <Form form={form} layout='vertical'>
        {/* 自定义工具名称 */}
        <Form.Item required label={t('tools.info.name')}>
          <div className='flex items-center justify-between gap-2'>
            <Avatar
              className='!rounded'
              showUpload
              size={36}
              avatar={icon}
              onChange={setAvatar}
            ></Avatar>
            <Form.Item
              name={'provider'}
              noStyle
              label={t('tools.info.name')}
              validateFirst
              rules={[
                {
                  required: true,
                  whitespace: true,
                  min: 2,
                  max: 20,
                },
              ]}
              validateTrigger='onBlur'
            >
              <Input placeholder={t('tools.placeholder.name')!}></Input>
            </Form.Item>
          </div>
        </Form.Item>
        {/* 自定义工具注册表 */}
        <Form.Item
          required
          label={t('tools.info.schema')}
        >
          <GetSchema className='absolute z-[999] right-0 top-[-40px]' onChange={setSchema} />
          <Input.TextArea
            rows={4}
            value={schema}
            onChange={e => setSchema(e.target.value)}
            placeholder={t('tools.placeholder.schema')!}
          ></Input.TextArea>
          <TextButton variant='primary' onClick={() => window.open('https://swagger.io/specification/', '_blank')}>{t('tools.action.viewSchemaSpec')}</TextButton>
        </Form.Item>
        {/* 可用工具 */}
        <Form.Item label={t('tools.info.availableTools.title')}>
          <Table
            size='small'
            rowKey='operation_id'
            columns={columns}
            dataSource={paramsSchemas}
            pagination={false}
            className='border-gray-G5 rounded border'
          ></Table>
        </Form.Item>
        {/* 鉴权方法 */}
        <GetCredentials
          credential={credential}
          onChange={setCredential}
        />
      </Form>
      {isShowTestApi && (
        <TestApi
          tool={currTool as CustomParamSchema}
          customCollection={customCollection}
          onHide={() => setIsShowTestApi(false)}
        />
      )}
    </Modal>

  )
}
export default React.memo(EditCustomCollectionModal)
