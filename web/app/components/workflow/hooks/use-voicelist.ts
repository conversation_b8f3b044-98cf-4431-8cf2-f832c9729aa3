import {
  useEffect,
  useState,
} from 'react'

import { getYjyTtsVoices } from '@/service/apps'
import type { NewLanguageType, NewVoiceConfigType } from '@/models/app'
export const useVoicelists = () => {
  // 语音开关配置
  const [languageList, setLanguageList] = useState<NewLanguageType[]>([])
  const [voicesConfigData, setVoicesConfigData] = useState<NewVoiceConfigType[]>([])
  const [languageDefaulVal, setLanguageDefaulValue] = useState<string>('')
  const [timbreDefaulValue, setTimbreDefaulValue] = useState<string>('')
  const getData = async () => {
    try {
      const data = await getYjyTtsVoices()
      if (!data) {
        // console.warn('未获取到语音输入开关数据')
        setLanguageDefaulValue('')
        setTimbreDefaulValue('')
        setLanguageList([])
        setVoicesConfigData([])
        return
      }
      const { voices_config, default_language = '', default_voice = '' } = data
      const languages = (voices_config || []).map(({ language_name = '', language_value = '' }) => ({
        label: language_name || '',
        value: language_value || '',
      }))
      const timbres = (voices_config || []).map(({ language_name = '', language_value = '', voices = [] }) => ({
        label: language_name,
        value: language_value,
        children: (voices || []).map(({ voice_name = '', voice_value = '' }) => ({
          label: voice_name,
          value: voice_value,
        })),
      }))
      setLanguageDefaulValue(default_language)
      setTimbreDefaulValue(default_voice)
      setLanguageList(languages || [])
      setVoicesConfigData(timbres || [])
    }
    catch (error) {
      console.error('Failed to fetch voice data:', error)
    }
  }
  useEffect(() => {
    getData()
  }, [])
  return {
    languageList, voicesConfigData, languageDefaulVal, timbreDefaulValue,
  }
}
