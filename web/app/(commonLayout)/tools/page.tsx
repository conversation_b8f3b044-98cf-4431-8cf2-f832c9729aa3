'use client'
import { useRouter } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import { useEffect, useMemo, useState } from 'react'
import { ConfigProvider, Tabs } from 'antd'
import { useContext } from 'use-context-selector'
import s from './styles/index.module.css'
import { deleteWorkflowTool, fetchCollectionList, removeCustomCollection } from '@/service/tools'
import { useTabSearchParams } from '@/hooks/use-tab-searchparams'
import { GRAY } from '@/themes/var-define'
import { useProviderContext } from '@/context/provider-context'
import { getLanguage } from '@/i18n/language'
import I18n from '@/context/i18n'
import { APP_NAME } from '@/config'

import { CollectionType } from '@/app/components/tools/types'
import type { Collection, WorkflowCollectionDetail, WorkflowToolProviderRequest } from '@/app/components/tools/types'
import WorkflowToolModal from '@/app/components/tools/workflow-tool'
import ConfigCredential from '@/app/components/tools/detail/build-in/config-credentials'
import ToolDetailModal from '@/app/components/tools/detail/tool-detail-modal'
import { getCollectionDetail, isCustomTool, isWorkflowTool } from '@/app/components/tools/utils'
/* 公共组件 */
import { useAppContext } from '@/context/app-context'
import { useLayoutContext } from '@/context/layout-context'
import cn from '@/utils/classnames'
import SearchInput from '@/app/components/base/input/search-input'
import CardList from '@/app/components/base/card-list'
import ProviderCard from '@/app/components/tools/detail/card'
import EditCustomToolModal from '@/app/components/tools/detail/custom-tool'
import Dropdown from '@/app/components/base/dropdown'
import Button from '@/app/components/base/button'
import Toast from '@/app/components/base/toast'
import { ArrowDown } from '@/app/components/base/icons/src/vender/line/arrows'
import Confirm from '@/app/components/base/confirm'

const Tools = () => {
  const { t } = useTranslation()
  const router = useRouter()
  const { canViewApp, canEditApp } = useAppContext()
  const { setAppLoading } = useLayoutContext()
  const { locale } = useContext(I18n)
  const language = getLanguage(locale)
  const { plan, onPlanInfoChanged } = useProviderContext()
  const isToolFull = plan.usage.buildTools >= plan.total.buildTools
  // 当前激活tab
  const [activeTab, setActiveTab] = useTabSearchParams({
    defaultTab: CollectionType.builtIn,
  })
  // 关键字
  const [keywords, setKeywords] = useState<string>('')
  // 工具集列表
  const [collectionList, setCollectionList] = useState<Collection[]>([])
  // 当前选中工具集
  const [currentCollection, setCurrentCollection] = useState<Collection>()

  // 是否显示工作流工具集弹窗
  const [showWorkflowCollectionModal, setShowWorkflowCollectionModal] = useState(false)
  // 是否显示自定义工具集弹窗
  const [showCustomCollectionModal, setShowCustomCollectionModal] = useState(false)
  // 是否显示工具集详情弹窗
  const [showCollectionDetailModal, setShowCollectionDetailModal] = useState(false)
  // 是否显示删除弹窗
  const [showConfirmDelete, setShowConfirmDelete] = useState(false)
  // 是否显示授权弹窗
  const [showAuthModal, setShowAuthModal] = useState(false)
  // 加载中
  const [loading, setLoading] = useState(true)

  // 搜索条件tab列表
  const tabList = [
    { key: CollectionType.builtIn, label: t('tools.type.builtIn') },
    { key: CollectionType.custom, label: t('tools.type.custom') },
    // { key: 'workflow', label: t('tools.type.workflow') },
  ]
  // 创建按钮下拉列表
  const createDropdown = [
    {
      text: t('tools.createDropdown.by') + t('tools.createDropdown.workflow'),
      value: 'workflow',
    },
    {
      text: t('tools.createDropdown.by') + t('tools.createDropdown.custom'),
      value: 'custom',
    },
  ]
  // 过滤后的工具集列表
  const filteredCollectionList = useMemo(() => {
    return collectionList.filter((collection) => {
      let filtered = true
      if (keywords)
        filtered = collection.name.toLowerCase().includes(keywords.toLowerCase()) || collection.label[language].toLowerCase().includes(keywords.toLowerCase())
      if (filtered && activeTab !== CollectionType.all) {
        if (activeTab === CollectionType.builtIn)
          filtered = collection.type === CollectionType.builtIn
        else
          filtered = collection.type !== CollectionType.builtIn
      }
      return filtered
    })
  }, [collectionList, keywords, language, activeTab])

  // 获取工具集列表
  const getCollectionList = async () => {
    setLoading(true)
    const list = await fetchCollectionList()
    setLoading(false)
    setCollectionList([...list])
  }
  // 创建工具
  const createCollection = (key: string) => {
    if (isToolFull) {
      Toast.notify({
        type: 'error',
        message: t('tools.notify.toolFull', { num: plan.total.buildTools }),
      })
    }
    else {
      setCurrentCollection(undefined)
      if (key === 'workflow')
        setShowWorkflowCollectionModal(true)
      if (key === 'custom')
        setShowCustomCollectionModal(true)
    }
  }
  // 完成变更自定义工具
  const handleCustomCollection = async () => {
    getCollectionList()
    setShowCustomCollectionModal(false)
  }
  // 完成变更工作流工具
  const handleWorkflowCollection = (data: WorkflowToolProviderRequest & Partial<{
    workflow_app_id: string
    workflow_tool_id: string
  }>) => {
    getCollectionList()
    setShowWorkflowCollectionModal(false)
    if (data.workflow_app_id)
      router.push(`/app/${data.workflow_app_id}/workflow`)
  }
  // 删除自定义工具
  const deleteCustomCollection = async () => {
    await removeCustomCollection(currentCollection!.name)
    getCollectionList()
    Toast.notify({
      type: 'success',
      message: t('tools.notify.successDeleteCustom'),
    })
    onPlanInfoChanged()
  }
  // 删除工作流工具
  const deleteWorkflowCollection = async () => {
    await deleteWorkflowTool(currentCollection!.id)
    getCollectionList()
    Toast.notify({
      type: 'success',
      message: t('tools.notify.successDeleteWorkflow'),
    })
    onPlanInfoChanged()
  }
  // 删除自定义工具
  const handleConfirmDelete = async () => {
    if (isCustomTool(currentCollection!))
      await deleteCustomCollection()
    if (isWorkflowTool(currentCollection!))
      await deleteWorkflowCollection()
    setShowConfirmDelete(false)
  }
  // 完成工具授权、移除
  const handleToolAuth = async () => {
    getCollectionList()
    setShowAuthModal(false)
  }

  // 监听卡片点击
  const onClickCard = async (collection: Collection) => {
    setCurrentCollection(collection)
    setShowCollectionDetailModal(true)
  }
  // 监听授权变化
  const onChangeAuth = async (collection: Collection) => {
    setCurrentCollection(collection)
    setShowAuthModal(true)
  }
  // 监听编辑工具
  const onEditTool = async (collection: Collection) => {
    setCurrentCollection(collection)
    if (isCustomTool(collection))
      setShowCustomCollectionModal(true)
    if (isWorkflowTool(collection)) {
      setAppLoading(true)
      try {
        const payload = await getCollectionDetail(collection)
        setAppLoading(false)
        window.open(`/app/${(payload as WorkflowCollectionDetail).workflow_app_id}/workflow`, '_blank', 'noreferrer')
      }
      catch (e) {
        setAppLoading(false)
      }
    }
  }
  // 监听删除工具
  const onDeleteTool = async (collection: Collection) => {
    setCurrentCollection(collection)
    setShowConfirmDelete(true)
  }

  useEffect(() => {
    getCollectionList()
  }, [])
  useEffect(() => {
    if (typeof window !== 'undefined')
      document.title = `${t('tools.title')} - ${APP_NAME}`
    if (!canViewApp)
      return router.replace('/square')
  }, [canViewApp, router, t])

  return (
    <>
      {/* 工具列表头部 */}
      <div className={s['tool-list-header']}>
        <span className={s['header-title']}>{t('tools.title')}</span>
        {canEditApp && <Dropdown
          items={createDropdown}
          placement='bottomRight'
          onSelect={({ value }) => createCollection(value)}
          renderTrigger={open => (
            <Button variant={'primary'}>
              { t('tools.action.createCollection') }
              <ArrowDown className={open ? 'rotate-180' : ''}></ArrowDown>
            </Button>
          )}
        >
        </Dropdown> }
      </div>
      {/* 工具筛选条件 */}
      <div className={s['tool-list-criteria']}>
        <Tabs activeKey={activeTab} items={tabList} onChange={value => setActiveTab(value as CollectionType)}></Tabs>
        <div className={s['search-criteria-right']}>
          <ConfigProvider
            theme={{
              components: {
                Input: {
                  colorBgContainer: 'transparent',
                  colorBorder: GRAY['G3-5'],
                },
              },
            }}
          >
            <SearchInput className='w-[220px]' onChange={setKeywords} />
          </ConfigProvider>
        </div>
      </div>
      {/* 工具列表 */}
      <CardList
        loading={loading}
        className={cn(s['card-list'])}
        layout='line'
        emptyText={t('tools.notify.noTool')}
      >
        {filteredCollectionList.map(collection => (
          <ProviderCard
            type={collection.type}
            onSelect={() => onClickCard(collection)}
            onAuth={() => onChangeAuth(collection)}
            onEdit={() => onEditTool(collection)}
            onDelete={() => onDeleteTool(collection)}
            key={collection.id}
            collection={collection}
          />
        ))}
      </CardList>

      {/* 工具详情弹窗 */}
      {showCollectionDetailModal && <ToolDetailModal
        collection={currentCollection!}
        onCancel={() => setShowCollectionDetailModal(false)}
      ></ToolDetailModal>}
      {/* 授权弹窗 */}
      {showAuthModal && <ConfigCredential
        collection={currentCollection!}
        onCancel={() => setShowAuthModal(false)}
        onSaved={handleToolAuth}
      />}
      {/* 删除工具弹窗 */}
      {showConfirmDelete && (
        <Confirm
          title={t('tools.notify.deleteToolConfirmTitle')}
          content={t('tools.notify.deleteToolConfirmContent')}
          isShow={showConfirmDelete}
          onConfirm={handleConfirmDelete}
          onCancel={() => setShowConfirmDelete(false)}
        />
      )}
      {/* 创建工作流弹窗 */}
      {showWorkflowCollectionModal && <WorkflowToolModal
        collection={currentCollection}
        onHide={() => setShowWorkflowCollectionModal(false)}
        onCreate={handleWorkflowCollection as any}
        onSave={handleWorkflowCollection}
      ></WorkflowToolModal>
      }
      {/* 创建自定义工具弹窗 */}
      {showCustomCollectionModal && <EditCustomToolModal
        collection={currentCollection}
        onHide={() => setShowCustomCollectionModal(false)}
        onAdd={handleCustomCollection}
        onEdit={handleCustomCollection}
      />
      }

    </>
  )
}
export default Tools
