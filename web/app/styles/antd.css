/* antd部分样式 */
.ant-popover-inner {
  padding: 0px !important;
} 
.ant-menu-item {
  display: flex !important;
  align-items: center !important;
}
.ant-menu-light .ant-menu-item-selected .ant-menu-title-content {
  font-weight: 600 !important;
}
.ant-menu-item-icon {
  color: #181818 !important;
}
.ant-menu-item-selected .ant-menu-item-icon {
  color: #3168F5 !important;
}
.worker-selector .ant-menu-submenu .ant-menu-submenu-title{
  height: 24px !important;
  line-height: 24px !important;
  background-color: transparent !important;
}

/* 下拉列表 */
.ant-select-dropdown {/*  */
z-index: 3000 !important;
padding: 4px 0 !important;
}
.ant-select-item-option-state {
display: none !important;
}
.ant-select-selection-item-content {
font-size: 12px !important;
color: #5C6273;
}
.ant-select-item-option-selected:not(.ant-select-item-option-disabled):hover {
background: #F1F7FF !important;
}
.ant-select-selection-item {
color: #181818 !important;
}
.select-multiple-style{
  .ant-select-selection-overflow-item{
    margin-right: 5px;
    /* height: 34px */
  }
}

.ant-select-open .ant-select-arrow {
@apply rotate-180;
}
.ant-select .ant-select-arrow {
color: var(--color-gray-G2);
}
/* 多选框 */
.ant-select-multiple .ant-select-selector {
padding: 6px 12px;;
}
.ant-select-multiple .ant-select-selection-placeholder {
left: 0;
}
.ant-select-multiple .ant-select-selection-overflow {
gap: 4px;
}
.no-border-select .ant-select-selector {
  border: none !important;
  padding: 0px !important;
}
/* 多选框 */
.ant-select-multiple .ant-select-selector {
  padding: 6px 12px;;
}
.ant-select-multiple .ant-select-selection-placeholder {
  left: 0;
}
.ant-select-multiple .ant-select-selection-overflow {
  gap: 4px;
}
/* 模型选择器 */
.ModelSelector.ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item-group-title, .ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-item-group-title {
padding: 0 16px;
line-height: 24px !important;
color: var(--color-gray-G1) !important;
font-size: 12px;
}
.ModelSelector.ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item {
padding: 4px 16px !important;
line-height: 20px !important;
margin-bottom: 8px;
}
.ModelSelector.ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item-group-list, .ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-item-group-list {
margin: 0;
}
.ModelSelector.ant-dropdown .ant-dropdown-menu > .ant-dropdown-menu-item-group > .ant-dropdown-menu-item-group-title {
font-size: 13px;
font-weight: 600;
margin-bottom: 8px;
}
.ModelSelector.ant-dropdown .ant-dropdown-menu > .ant-dropdown-menu-item-group .ant-dropdown-menu-item-group .ant-dropdown-menu-item-group > .ant-dropdown-menu-item-group-title {
color: var(--color-gray-G3) !important;
}
.ant-dropdown-menu-item-group .ant-dropdown-menu-item-group-list {
  margin: 0 !important;
}
.ant-dropdown-menu-item-group .ant-dropdown-menu-item-group-list .ant-dropdown-menu-item-group-title {
  color: var(--color-gray-G2) !important;
  font-size: 12px !important;
  line-height: 20px !important;
  margin-top: 8px;
}
.ant-dropdown-menu-item-group .ant-dropdown-menu-item-group-list .ant-dropdown-menu-item-group-title:first-child {
  margin-top: 0px !important;
}

/* steps步骤条 */
.ant-steps .ant-steps-item-wait .ant-steps-item-icon {
background: transparent;
border: 1.5px solid var(--color-gray-G5);
}
.ant-steps .ant-steps-item-title::after {
background-color: var(--color-gray-G5) !important;
position: relative !important;
width: 5vw !important;
max-width: 60px !important;
margin-left: 16px;
top: unset !important;
inset-inline-start: unset !important;
}
.ant-steps-item-container {
display: flex !important;
}
.ant-steps-item-finish .ant-steps-item-title::after,
.ant-steps-item-active .ant-steps-item-title::after {
background-color: var(--color-primary-P1) !important;
}
.ant-steps .ant-steps-item-process .ant-steps-item-icon {
background-color: var(--color-primary-P1);
border-color: var(--color-primary-P1);
margin-inline-end: 10px !important;
}
.ant-steps .ant-steps-item-finish .ant-steps-item-icon {
background-color: transparent;
border: 2px solid var(--color-primary-P1);
}
.ant-steps .ant-steps-item-wait .ant-steps-item-icon >.ant-steps-icon,
.ant-steps .ant-steps-item-wait>.ant-steps-item-container>.ant-steps-item-content>.ant-steps-item-title,
.ant-steps .ant-steps-item-process>.ant-steps-item-container>.ant-steps-item-content>.ant-steps-item-title
.ant-steps .ant-steps-item-finish>.ant-steps-item-container>.ant-steps-item-content>.ant-steps-item-title{
color: var(--color-gray-G1);
font-weight: 600;
font-size: 14px !important;
line-height: 24px !important;
}
.ant-steps .ant-steps-item-active .ant-steps-item-title{
color: var(--color-primary-P1) !important;
font-weight: 600 !important;
font-size: 14px !important;
}
.ant-steps-item {
flex: unset !important;
}
.ant-steps-item-title {
display: flex !important;
align-items: center !important;
padding-inline-end: 0 !important;
}


/* 输入框 */
.ant-form-item .ant-input {
box-shadow: 0 0 0px 1000px white inset;  /* 覆盖表单自动填充的蓝色背景色 */
}
.ant-input-prefix {
margin-inline-end: 4px !important;
color:#bec3cf;
}
.ant-input-suffix {
margin-inline-start: 4px !important;
}
.ant-input-affix-wrapper .ant-input-show-count-suffix {
color: var(--color-gray-G4);
}
.ant-input-number-input {
text-align: center !important;
}
.ant-input-number-handler-wrap + .ant-input-number-input-wrap .ant-input-number-input {
text-align: start !important;
}

/* textare 文本输入框 */
.ant-input-textarea-show-count .ant-input-data-count {
bottom: 8px !important;
right: 12px;
color: var(--color-gray-G4);
}

/* 菜单 */
.ant-menu-sub {
padding: 4px 0px 4px !important;
}
.ant-menu-item-group-list .ant-menu-item {
padding-inline: 16px 16px !important;
}

.ant-menu-item-group:not(:last-child) {
margin-bottom: 16px !important;
}
.ant-menu-item-group-title {
padding: 0px 16px 2px !important;
line-height: 20px !important;
}
.ant-menu-vertical {
border-inline-end: none !important;
}
.ant-menu-submenu-selected >.ant-menu-submenu-title {
color: #181818 !important;
}

/* tab组件 */
.ant-tabs-ink-bar {
height: 3px !important;
}
.ant-tabs-tab {
padding: 0px 0px 12px !important;
}
.ant-tabs-nav::before {
border-color: #BEC3CF !important;
opacity: 0.6;
}
.ant-tabs-ink-bar {
height: 3px;
}

.ant-tabs-tab-active .ant-tabs-tab-btn {
font-weight: 600 !important;
text-shadow: unset !important;
}
.ant-tabs-nav-more {
color: #5C6273 !important;
padding-top: 6px !important;
padding-bottom: 6px !important;
}
.ant-tabs-dropdown-menu {
border-radius: 4px !important;
}
.ant-tabs-dropdown-menu-item {
color: #181818 !important;
font-size: 12px !important;
height: 32px !important;
line-height: 32px !important;
padding: 0px 20px !important;
}
.ant-tabs-dropdown-menu-item:hover {
background: #F1F7FF !important;
}
.node-selector .ant-tabs-nav {
  padding: 0 16px !important;
}

/* tooltip组件 */
.ant-tooltip .ant-tooltip-arrow::after {
width: 8px !important;
height: 8px !important;
}
/* modal弹窗 */
.ant-modal {
border-radius: 4px;
background: linear-gradient(180deg, #EAF1FF 0%, #FFF 12.93%);
border: 0.5px solid #F0F1F5;
}
.ant-modal .ant-modal-content {
display: flex;
flex-direction: column;
padding: 0;
/* min-height: 60vh; */
max-height: calc(100vh - 120px);
background: unset !important;
box-shadow: unset;
background: linear-gradient(180deg, #EAF1FF 0%, #FFF 12.93%);
border: 0.5px solid #F0F1F5;
}
.ant-modal .ant-modal-header {
padding: 24px 32px 0px;
margin-bottom: 0;
flex-shrink: 0;
background: transparent;
}
.ant-modal .ant-modal-body {
padding: 12px 0px 0px;
height: 100%;
overflow-y: hidden;
display: flex;
flex-direction: column;
margin-bottom: 24px !important;
}
.ant-modal .ant-modal-footer {
flex-shrink: 0;
display: flex;
justify-content: flex-end;
align-items: center;
margin: 0px !important;
padding: 0px 32px 24px;
}
.ant-modal .ant-modal-close {
top: 22px;
right: 24px;
color: var(--color-gray-G2);
}
.ant-modal .ant-modal-close:hover {
background: transparent;
color: var(--color-gray-G1);
}
.form-modal .ant-modal-body {
margin-bottom: 0px !important;
}

/* 表单 */
.ant-form-item-explain-error {
font-size: 12px !important;
line-height: 16px !important;
}
.ant-form-item-label >label {
font-weight: 600 !important;
width: 100% !important;
}
.ant-form-item:not(.ant-form-item-horizontal) .ant-form-item-label>label {
display: flex !important;
}
.ant-form-item-label {
  display: flex;
  align-items: center;
}

/* alert警告提示 */
.ant-alert {
line-height: 24px;
border-width: 0;
}
.ant-alert-banner {
border-radius: 4px;
}
.ant-alert-with-description .ant-alert-message {
font-size: 14px;
margin-bottom: 4px;
}
.ant-alert-with-description .ant-alert-icon {
margin-top: 4px;
margin-right: 8px;
}
.ant-alert-with-description .ant-alert-close-icon {
margin-top: 5px;
font-size: 16px;
}
.ant-alert .ant-alert-close-icon {
font-size: 16px;
}
.ant-alert .ant-alert-icon {
font-size: 16px;
}

/* dropdown */
.ant-dropdown .ant-dropdown-menu {
max-height: 50vh;
padding-inline: 0px 0px !important;
overflow-y: auto;
}

.ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item {
padding: 6px 12px;
line-height: 24px;
}
.ant-dropdown-menu-item-selected {
background: transparent !important;
}
.ant-dropdown-menu-item-selected:hover,
.ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item:hover {
background: var(--color-primary-P4) !important;
}
.ant-dropdown-open-show.ant-dropdown-open {
display: flex;
}
/* 按钮 */
:where(.css-dev-only-do-not-override-yldb3q).ant-btn-link {
color: #3168F5 !important;
padding: 0px !important;
gap: 2px !important;
}
:where(.css-dev-only-do-not-override-yldb3q).ant-btn-link:hover {
color: #4086FF !important;
}

/* tag标签 */
.ant-tag {
height: 24px;
line-height: 20px;
padding: 1px 10px;
}
/* 小标签 默认样式 */
.ant-tag.tag-small {
border-radius: 2px;
border-color: var(--color-gray-G5);
height: 20px;
padding: 4px 8px;
font-size: 10px;
line-height: 12px;
}
/* 标签颜色 */
.ant-tag.ant-tag-blue {
color: var(--color-primary-P1) !important;
background: var(--color-primary-P4) !important;
border-color: var(--color-primary-P1);
}
/* 背景透明标签 */
.ant-tag.noBg {
background: transparent !important;
}

/* inputnumber数字输入框 */
.ant-input-number {
border-radius: 4px;
}
.ant-input-number .ant-input-number-handler:hover {
height: 40% !important;
}

/* slider 滑动输入 */
.ant-slider .ant-slider-handle::after {
outline: none !important;
background-color: var(--color-primary-P1) !important;
}
.ant-slider .ant-slider-handle:hover::after {
background-color: var(--color-primary-P2) !important;
}
.ant-slider-disabled .ant-slider-handle::after, .ant-slider-disabled .ant-slider-handle:hover::after{
background-color: var(--color-gray-G4) !important;
box-shadow: 0 0 0 2px #fff !important;
}
.ant-slider {
margin: 0;
}
/* 表格 */
.ant-table-wrapper .ant-table.ant-table-small .ant-table-thead>tr>th {
padding: 6px 12px !important;
line-height: 20px !important;
}
.ant-table-wrapper .ant-table.ant-table-small .ant-table-tbody>tr>td {
padding: 10px 12px !important;
line-height: 20px !important;
}
.ant-table-tbody tr:last-child td {
border-bottom: none !important;
}
.ant-table-thead tr th {
border-bottom: none !important;
}
.ant-table-wrapper {
overflow: hidden !important;
}
.ant-table-body {
scrollbar-width: auto;
scrollbar-color: auto;
}
.ant-table-scroll-horizontal .ant-table-tbody > tr .ant-table-cell:last-child > * {
padding-right: 4px !important;
}
.ant-table-scroll-horizontal .ant-table-body {
margin-right: 4px !important;
}
.ant-transparent-table .ant-table {
  background: transparent !important;
}

.forkTip {
@apply !p-3;
}

.forkTip.ant-alert-with-description .ant-alert-message, .forkTip.ant-alert-with-description .ant-alert-description {
@apply !text-[12px] !leading-H1 !text-gray-G2;
}

.forkTip.ant-alert-with-description .ant-alert-close-icon {
margin-top: 3px !important;
}

/* drawer 抽屉 */
.ant-drawer-content {
border-radius: 4px 0 0 4px;
}
.ant-drawer .ant-drawer-close {
top: 20px;
right: 24px;
color: var(--color-gray-G2);
}
.ant-drawer .ant-drawer-close:hover {
background: transparent;
color: var(--color-gray-G1);
}
.ant-drawer-header-title {
flex-direction: row-reverse;
}
.ant-drawer-header {
padding: 20px 32px 6px !important;
border-bottom: none !important;
}
.ant-drawer-body {
padding: 0px 32px 20px !important;
}
.ant-drawer-close {
margin: 0 !important;
width: auto !important;
}

/* 描述列表 */
.ant-descriptions-small.ant-descriptions .ant-descriptions-item-label, .ant-descriptions-small.ant-descriptions .ant-descriptions-item-content {
font-size: 12px;
line-height: 20px;
font-weight: 400;
}
.ant-descriptions-small.ant-descriptions .ant-descriptions-item-label {
color: var(--color-gray-G3);
}
.ant-descriptions-item-label, .ant-descriptions .ant-descriptions-item-content {
color: var(--color-gray-G1);
}

/* 日期选择框 */
.ant-picker-dropdown .ant-picker-cell {
color: var(--color-gray-G4);
}
.ant-picker-dropdown .ant-picker-cell-in-view {
color: var(--color-gray-G1);
}
.ant-picker-dropdown .ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner {
color: var(--color-primary-P1);
}
.ant-picker-dropdown .ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner::before {
border: none;
}
:where(.css-dev-only-do-not-override-15ffo7a).ant-picker .ant-picker-clear >* {
color: var(--color-gray-G5);
}
:where(.css-dev-only-do-not-override-15ffo7a).ant-picker .ant-picker-clear:hover >* {
  color: var(--color-gray-G4);
}
