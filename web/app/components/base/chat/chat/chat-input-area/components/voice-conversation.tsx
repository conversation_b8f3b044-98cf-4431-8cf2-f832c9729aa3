import { useTranslation } from 'react-i18next'
import React, { useMemo } from 'react'
import SoundWave from './sound-wave/sound-wave'
import Tooltip from '@/app/components/base/tooltip'
import { Hangup } from '@/app/components/base/icons/src/vender/solid/general'
import cn from '@/utils/classnames'

type VoiceConversationProps = {
  voiceStatus?: string
  onStop: () => void
}
const VoiceConversation = React.memo(({
  voiceStatus,
  onStop,
}: VoiceConversationProps) => {
  const { t } = useTranslation()

  const statusText = useMemo(() => {
    switch (voiceStatus) {
      case 'thinking':
        return t('common.voiceInput.thinking')
      case 'connecting':
        return t('common.voiceInput.connecting')
      default:
        return t('common.voiceInput.inCall')
    }
  }, [voiceStatus, t])

  return (
    <div className='flex items-center justify-between w-full'>
      <div className='flex items-center relative grow w-full cursor-pointer'>
        <div className='flex px-[14px] py-[12px] pr-[64px] text-S3 leading-H3'>
          <SoundWave className='mr-2'></SoundWave>
          <span className='text-gray-G2'>{statusText}</span>
        </div>
      </div>
      <div className={cn('absolute top-0 bottom-0 h-full flex items-center', 'right-3')}>
        <div className='flex justify-center items-center ml-2 cursor-pointer'>
          <Tooltip popupContent={t('common.voiceInput.hangup')}>
            <Hangup className='w-9 h-6 text-[#FF524c] hover:opacity-80' onClick={onStop}/>
          </Tooltip>
        </div>
      </div>
    </div>
  )
})

export default VoiceConversation
