import { type FC } from 'react'
import { Divider } from 'antd'
import { useTranslation } from 'react-i18next'
import type { ModelProvider } from '../declarations'
import {
  CustomConfigurationStatusEnum,
  EventEmitterType,
} from '../declarations'
import { useEventEmitterContextContext } from '@/context/event-emitter'
import { useAppContext } from '@/context/app-context'
// 公共组件
import Tooltip from '@/app/components/base/tooltip'
import Indicator from '@/app/components/base/indicator'
import TextButton from '@/app/components/base/button/text-button'
import { Delete, Settings01 } from '@/app/components/base/icons/src/vender/line/general'

type CredentialPanelProps = {
  provider: ModelProvider
}
const CredentialPanel: FC<CredentialPanelProps> = ({
  provider,
}) => {
  const { t } = useTranslation()
  const { canAdmin } = useAppContext()
  const { eventEmitter } = useEventEmitterContextContext()
  const customConfig = provider.custom_configuration
  const customConfiged = customConfig.status === CustomConfigurationStatusEnum.active

  // 设置模型供应商
  const setUpProvider = () => {
    eventEmitter?.emit({
      type: EventEmitterType.setUpProvider,
      provider,
    } as any)
  }
  // 删除模型供应商
  const deleteProvider = () => {
    eventEmitter?.emit({
      type: EventEmitterType.deleteProvider,
      provider,
    } as any)
  }

  return (
    <div className='relative px-2 py-1 rounded border border-gray-G5 inline-flex items-center'>
      {
        provider.provider_credential_schema && <>
          <div className='text-S1 leading-H1 text-gray-G2 mr-1'>API-KEY</div>
          <Indicator className='w-1.5 h-1.5' color={customConfiged ? 'green' : 'gray'} />
          {
            canAdmin && <>
              <Divider type='vertical' className='!mx-2'></Divider>
              <Tooltip
                popupContent={t('common.operation.settings')}
              >
                <TextButton onClick={setUpProvider} variant='hover'>
                  <Settings01 className='w-4 h-4' />
                </TextButton>
              </Tooltip>
              <div className='w-3'></div>
            </>
          }
        </>
      }
      {
        canAdmin && <Tooltip
          popupContent={t('common.operation.delete')}
        >
          <TextButton onClick={deleteProvider} variant='hover'>
            <Delete className='w-4 h-4 my-1'></Delete>
          </TextButton>
        </Tooltip>
      }
    </div>
  )
}

export default CredentialPanel
