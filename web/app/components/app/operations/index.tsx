import { useCallback, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Divider, Popover } from 'antd'
import { useRouter } from 'next/navigation'
import type { onEditType } from '../modal/add-or-edit'
import type { DuplicateAppModalProps } from '../modal/duplicate'
import type { EnvironmentVariable } from '../../workflow/types'
import PublishAppModal from '../modal/publish'
import s from './styles/operations.module.css'
import type { App } from '@/types/app'
import { copyApp, deleteApp, exportAppConfig, updateAppInfo } from '@/service/apps'
/* 公共钩子及工具 */
import { useAppContext } from '@/context/app-context'
import { useProviderContext } from '@/context/provider-context'
import { getRedirection } from '@/utils/app-redirection'
import cn from '@/utils/classnames'
import { publishAppspublic, unPublishAppspublic } from '@/service/market'
/* 公共组件部分 */
import { useStore as useAppStore } from '@/app/components/app/store'
import { useToastContext } from '@/app/components/base/toast'
import { useCardContext } from '@/app/components/base/card'
import EditAppModal from '@/app/components/app/modal/add-or-edit'
import DuplicateAppModal from '@/app/components/app/modal/duplicate'
import Confirm from '@/app/components/base/confirm'
import TextButton from '@/app/components/base/button/text-button'
import { More } from '@/app/components/base/icons/src/vender/solid/general'

type OperationsProps = {
  app: App
  onRefresh?: () => void
}
type OperationsContentProps = {
  app: App
  beforeAction?: () => void
  fallback?: () => void
  onDelete?: () => void
  itemWrapClass?: string
  itemTitleClass?: string
  layout?: 'col' | 'line'
}

export const OperationsContent = ({
  app,
  beforeAction,
  fallback,
  onDelete,
  itemWrapClass,
  itemTitleClass,
  layout = 'col',
}: OperationsContentProps) => {
  const { t } = useTranslation()
  const { notify } = useToastContext()
  const { canPublishApp } = useAppContext()
  const setAppDetail = useAppStore(state => state.setAppDetail)
  const { push } = useRouter()
  const { onPlanInfoChanged, plan } = useProviderContext()

  // 显示编辑modal
  const [showEditModal, setShowEditModal] = useState(false)
  // 显示复制modal
  const [showDuplicateModal, setShowDuplicateModal] = useState(false)
  // 显示确认窗口
  const [showConfirmDelete, setShowConfirmDelete] = useState(false)
  // 显示导入dsl弹窗
  const [showImportDSLModal, setShowImportDSLModal] = useState(false)
  // 显示发布弹窗
  const [showPublishModal, setShowPublishModal] = useState(false)
  const isAppsFull = plan.usage.buildApps >= plan.total.buildApps

  const baseFunc = (e: React.MouseEvent) => {
    e.stopPropagation()
    e.preventDefault()
    beforeAction && beforeAction()
  }
  // 确认删除动作
  const onConfirmDelete = useCallback(async () => {
    try {
      await deleteApp(app.id)
      notify({ type: 'success', message: t('app.notify.appDeleted') })
      if (fallback)
        fallback()
      if (onDelete)
        onDelete()
      onPlanInfoChanged()
    }
    catch (e: any) {
      notify({
        type: 'error',
        message: `${t('app.notify.appDeleteFailed')}${'message' in e ? `: ${e.message}` : ''}`,
      })
    }
    setShowConfirmDelete(false)
  }, [app.id, notify, t, fallback, onDelete, onPlanInfoChanged])
  // 编辑应用信息
  const onEdit: onEditType = useCallback(
    async ({ name, icon_type, icon, icon_background, description, use_icon_as_answer_icon }) => {
      try {
        const appInfo = await updateAppInfo({
          appID: app.id,
          name,
          icon_type,
          icon,
          icon_background,
          description,
          use_icon_as_answer_icon,
        })
        setShowEditModal(false)
        notify({
          type: 'success',
          message: t('app.notify.editDone'),
        })
        setAppDetail(appInfo)
        if (fallback)
          fallback()
      }
      catch (e) {
        notify({ type: 'error', message: t('app.notify.editFailed') })
      }
    },
    [app.id, notify, t, setAppDetail, fallback],
  )
  // 复制应用
  const onCopy: DuplicateAppModalProps['onConfirm'] = async ({
    name,
    icon_type,
    icon,
    icon_background,
  }) => {
    try {
      const newApp = await copyApp({
        appID: app.id,
        name,
        icon_type,
        icon,
        icon_background,
        mode: app.mode,
      })
      setShowDuplicateModal(false)
      notify({
        type: 'success',
        message: t('app.notify.appCreated'),
      })
      if (fallback)
        fallback()
      onPlanInfoChanged()
      getRedirection(newApp, push)
    }
    catch (e) {
      notify({ type: 'error', message: t('app.notify.appCreateFailed') })
    }
  }
  // 导出应用配置
  const onExport = async (include = false) => {
    try {
      const { data } = await exportAppConfig({
        appID: app.id,
        include,
      })
      const a = document.createElement('a')
      const file = new Blob([data], { type: 'application/yaml' })
      a.href = URL.createObjectURL(file)
      a.download = `${name}.yml`
      a.click()
    }
    catch (e) {
      notify({ type: 'error', message: t('app.notify.exportFailed') })
    }
  }
  // 导出确认
  const exportCheck = async () => {
    if (app.mode !== 'workflow' && app.mode !== 'advanced-chat') {
      onExport()
      return
    }
    try {
      const workflowDraft = await fetchWorkflowDraft(`/apps/${app.id}/workflows/draft`)
      const list = (workflowDraft.environment_variables || []).filter(
        env => env.value_type === 'secret',
      )
      if (list.length === 0) {
        onExport()
        return
      }
      setSecretEnvList(list)
    }
    catch (e) {
      notify({ type: 'error', message: t('app.notify.exportFailed') })
    }
  }
  // 发布确认
  const onPublish = async (params: any) => {
    await publishAppspublic(app.id, params)
      .then(() => {
        notify({
          type: 'success',
          message: t('app.notify.successPublishApp'),
        })
        setShowPublishModal(false)
        if (fallback)
          fallback()
      })
      .catch(() => {})
  }

  // 点击设置事件
  const onClickSettings = async (e: React.MouseEvent<HTMLButtonElement>) => {
    baseFunc(e)
    setShowEditModal(true)
  }
  // 点击复制福建
  const onClickDuplicate = async (e: React.MouseEvent<HTMLButtonElement>) => {
    baseFunc(e)
    if (isAppsFull) {
      notify({
        type: 'error',
        message: t('app.notify.appFull', { num: plan.total.buildApps }),
      })
    }
    else {
      setShowDuplicateModal(true)
    }
  }
  // 点击删除事件
  const onClickDelete = async (e: React.MouseEvent<HTMLDivElement>) => {
    baseFunc(e)
    setShowConfirmDelete(true)
  }
  // 点击发布事件
  const onClickPublish = async (e: React.MouseEvent<HTMLButtonElement>) => {
    baseFunc(e)
    setShowPublishModal(true)
  }
  // 点击取消发布
  const onClickUnPublish = async (e: React.MouseEvent<HTMLButtonElement>) => {
    baseFunc(e)
    try {
      await unPublishAppspublic(app.id, {
        failed_message: '',
      })
      notify({
        type: 'success',
        message: t('app.notify.unPublishSuccess'),
      })
      if (fallback)
        fallback()
    }
    catch {}
  }

  return (
    <>
      {layout === 'col' && (
        <div className='relative w-full py-1'>
          <button className={itemWrapClass || s['action-item']} onClick={onClickSettings}>
            <span className={itemTitleClass || s['action-name']}>{t('common.operation.edit')}</span>
          </button>
          {app.mode !== 'workflow' && (
            <button className={itemWrapClass || s['action-item']} onClick={onClickDuplicate}>
              <span className={itemTitleClass || s['action-name']}>
                {t('common.operation.copy')}
              </span>
            </button>
          )}
          {false && (
            <>
              <Divider className='!m-0'></Divider>
              <button className={itemWrapClass || s['action-item']} onClick={onClickExport}>
                <span className={itemTitleClass || s['action-name']}>{t('app.action.export')}</span>
              </button>
              {(app.mode === 'advanced-chat' || app.mode === 'workflow') && (
                <button className={itemWrapClass || s['action-item']} onClick={onClickImport}>
                  <span className={itemTitleClass || s['action-name']}>
                    {t('workflow.common.importDSL')}
                  </span>
                </button>
              )}
              <Divider className='!m-0'></Divider>
            </>
          )}

          <div
            className={cn('group', itemWrapClass || s['action-item'], s['delete-action-item'])}
            onClick={onClickDelete}
          >
            <span className={cn('group-hover:text-red-500', itemTitleClass || s['action-name'])}>
              {t('common.operation.delete')}
            </span>
          </div>
        </div>
      )}
      {layout === 'line' && (
        <div className='flex items-center gap-6'>
          {canPublishApp && !app.is_publish && (
            <TextButton size='small' onClick={onClickPublish}>
              {t('common.operation.publish')}
            </TextButton>
          )}
          {canPublishApp && app.is_publish && (
            <TextButton size='small' onClick={onClickUnPublish}>
              {t('common.operation.unPublish')}
            </TextButton>
          )}
          <TextButton size='small' onClick={onClickSettings}>
            {t('common.operation.edit')}
          </TextButton>
          <TextButton size='small' onClick={onClickDuplicate}>
            {t('common.operation.copy')}
          </TextButton>
          <TextButton size='small' onClick={onClickDelete}>
            {t('common.operation.delete')}
          </TextButton>
        </div>
      )}
      <div className='absolute' onClick={e => e.stopPropagation()}>
        {/* 编辑应用信息弹窗 */}
        {showEditModal && (
          <EditAppModal app={app} onEdit={onEdit} onClose={() => setShowEditModal(false)} />
        )}
        {/* 复制弹窗 */}
        {showDuplicateModal && (
          <DuplicateAppModal
            appName={app.name}
            icon={app.icon_url || ''}
            show={showDuplicateModal}
            onConfirm={onCopy}
            onHide={() => setShowDuplicateModal(false)}
          />
        )}
        {showConfirmDelete && (
          <Confirm
            title={t('app.modalTitle.deleteAppConfirmTitle')}
            content={t('app.notify.deleteAppConfirmContent')}
            isShow={showConfirmDelete}
            onConfirm={onConfirmDelete}
            onCancel={() => setShowConfirmDelete(false)}
          />
        )}
        {/* 发布应用弹窗 */}
        {showPublishModal && (
          <PublishAppModal
            app={app}
            onCancel={() => setShowPublishModal(false)}
            onSave={onPublish}
          ></PublishAppModal>
        )}
      </div>
    </>
  )
}

const Operations = ({ app, onRefresh }: OperationsProps) => {
  const { isActive, setIsActive } = useCardContext()
  // 弹窗是否打开
  const [open, setOpen] = useState(isActive)

  useEffect(() => {
    if (!isActive)
      setOpen(false)
  }, [isActive])

  return (
    <Popover
      open={open}
      content={
        <OperationsContent
          fallback={onRefresh}
          app={app}
          beforeAction={() => setIsActive(false)}
        ></OperationsContent>
      }
      arrow={false}
      trigger='click'
      placement='bottomRight'
      getPopupContainer={(node: HTMLElement) => node.parentElement!}
    >
      <More
        className={cn(s['more-icon'], open && s['more-icon-active'])}
        onClick={() => setOpen(true)}
      ></More>
    </Popover>
  )
}

export default Operations
