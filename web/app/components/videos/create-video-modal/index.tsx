'use client'

import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import cn from 'classnames'
import { useContext } from 'use-context-selector'

import { Form, Input } from 'antd'
import s from './styles/index.module.css'
import type { Video } from '@/types/videos'
import { MediaType } from '@/types/videos'
import { createVideo, updateVideo } from '@/service/videos'
// 公共组件
import { MAX_APP_DESC_LENGTH } from '@/config'
import VideoAvatar from '@/app/components/base/app-info/avatar'
import Button from '@/app/components/base/button'
import Modal from '@/app/components/base/modal'
import { useFormDisabled } from '@/hooks/use-form'
import { ToastContext } from '@/app/components/base/toast'

type CreateVideoModalProps = {
  video?: Video
  onSuccess?: (value: Video) => void
  onClose: () => void
}

const CreateVideoModal = ({ onSuccess, onClose, video }: CreateVideoModalProps) => {
  const { t } = useTranslation()
  const [form] = Form.useForm()
  const disabled = useFormDisabled(form)
  const { notify } = useContext(ToastContext)

  // 选中类型
  const [selectedType, setSelectedType] = useState<MediaType>(MediaType.VideoStream)
  // 是否为编辑模式
  const isEdit = useMemo(() => !!video, [video])
  // 弹窗title
  const modalTitle = useMemo(() => {
    return !isEdit ? t('videos.action.createVideos') : t('videos.action.editVideos')
  }, [isEdit, t])

  // 创建视频库
  const onCreateVideo = useCallback(async () => {
    const { name, description } = form.getFieldsValue()
    const params = {
      name,
      description,
      type: selectedType,
    }
    const res = await createVideo(params)
    notify({
      type: 'success',
      message: t('common.actionMsg.createSuccessfully'),
    })
    onSuccess?.(res.data)
  }, [form, notify, onSuccess, selectedType, t])
  // 编辑视频库
  const onEditVideo = useCallback(async () => {
    const { name, description } = form.getFieldsValue()
    const params = {
      name,
      description,
    }
    await updateVideo(video!.id, params).then((res) => {
      notify({
        type: 'success',
        message: t('common.actionMsg.updateSuccess'),
      })
      onSuccess?.(res.data)
    })
  }, [form, notify, onSuccess, t, video])

  useEffect(() => {
    if (isEdit && video) {
      form.setFieldsValue({
        name: video.name,
        description: video.description,
      })
      setSelectedType(video.type)
    }
  }, [form, isEdit, video])

  return (
    <Modal
      width={716}
      isShow
      title={modalTitle}
      footer={
        <>
          <Button variant='secondary-accent' className='mr-4' onClick={onClose}>{t('common.operation.cancel')}</Button>
          {isEdit
            ? <Button
              className='w-24'
              disabled={disabled}
              variant="primary"
              onClick={onEditVideo}
            >
              {t('common.operation.save')}
            </Button>
            : <Button
              className='w-24'
              disabled={disabled}
              variant="primary"
              onClick={onCreateVideo}
            >
              {t('common.operation.create')}
            </Button>
          }
        </>
      }
      closable
      onClose={onClose}
    >
      <Form layout='vertical' form={form}>
        {/* 应用类型 */}
        {!isEdit && <Form.Item required label={t('videos.info.createType')}>
          <div className='flex gap-3 flex-wrap'>
            {/* 自动编排 */}
            <div
              className={cn(
                s['video-mode-item'],
                selectedType === MediaType.VideoStream && s['video-active-mode-item'],
              )}
              onClick={() => {
                setSelectedType(MediaType.VideoStream)
              }}
            >
              <VideoAvatar
                appMode={MediaType.VideoStream}
                size={24}
                className={s['mode-icon']}
              />
              <div className={s['mode-title']}>{t('videos.type.videosBuket')}</div>
            </div>
            {/* 工作流-原聊天助手，改名为工作流 */}
            <div
              className={cn(
                s['video-mode-item'],
                selectedType === MediaType.Video && s['video-active-mode-item'],
              )}
              onClick={() => {
                setSelectedType(MediaType.Video)
              }}
            >
              <VideoAvatar
                appMode={MediaType.Video}
                size={24}
                className={s['mode-icon']}
              />
              <div className={s['mode-title']}>{t('videos.type.video')}</div>
            </div>
            {/* 工作流-基于范文模板创作 */}
            <div
              className={cn(
                s['video-mode-item'],
                selectedType === MediaType.Img && s['video-active-mode-item'],
              )}
              onClick={() => {
                setSelectedType(MediaType.Img)
              }}
            >
              <VideoAvatar
                appMode={MediaType.Img}
                size={24}
                className={s['mode-icon']}
              />
              <div className={s['mode-title']}>{t('videos.type.img')}</div>
            </div>
          </div>
        </Form.Item>}
        {/* 应用名称 */}
        <Form.Item
          name={'name'}
          validateFirst={true}
          rules={[
            {
              required: true,
              whitespace: true,
            },
            {
              pattern: /^[a-zA-Z0-9\u4E00-\u9FA5()-_.]{1,50}$/,
              message: t('videos.info.nameError')!,
            },
          ]}
          label={t('videos.info.name')}
          tooltip={t('videos.info.nameDescription')}
          validateTrigger='onBlur'
        >
          <Input placeholder={t('videos.placeholder.namePlaceholder')!}/>
        </Form.Item>
        {/* 应用描述 */}
        <Form.Item
          name={'description'}
          label={t('videos.info.desc')}
          validateFirst={true}
          rules={[
            { required: true, whitespace: true, max: MAX_APP_DESC_LENGTH },
          ]}
          validateTrigger='onBlur'
        >
          <Input.TextArea
            placeholder={t('videos.placeholder.descPlaceholder')!}
            maxLength={MAX_APP_DESC_LENGTH}
          ></Input.TextArea>
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default CreateVideoModal
