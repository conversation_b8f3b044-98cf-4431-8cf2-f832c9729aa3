'use client'
import type { FC } from 'react'
import React, { useEffect, useMemo } from 'react'
import useSWR from 'swr'
import { useRouter, useSelectedLayoutSegments } from 'next/navigation'
import { fetchVideoDetail } from '@/service/videos'

// 公共组件
import VideoDetailContext from '@/context/video-detail'
import { APP_NAME } from '@/config'
import Loading from '@/app/components/base/loading'
import TopNavbar from '@/app/components/layout/top-navbar'
import AppBasic from '@/app/components/base/app-info'
import { useAppContext } from '@/context/app-context'

export type IVideoDetailLayoutProps = {
  children: React.ReactNode
  params: { videoId: string }
}

const VideoDetailLayout: FC<IVideoDetailLayoutProps> = (props) => {
  const {
    children,
    params: { videoId },
  } = props
  const router = useRouter()
  const { canEditVideoLib } = useAppContext()
  const selectedSegment = useSelectedLayoutSegments()

  const { data: videoRes, error, mutate: mutateVideoRes } = useSWR({
    url: 'fetchVideoDetail',
    videoId,
  }, apiParams => fetchVideoDetail(apiParams.videoId))
  // 视频库信息
  const videoInfo = useMemo(() => videoRes?.data, [videoRes])
  // 回退方法
  const backFunc = () => {
    if (selectedSegment[selectedSegment.length - 1] === 'documents')
      router.push('/videos')
    else
      router.push(`/videos/${videoId}/documents`)
  }

  useEffect(() => {
    if (!canEditVideoLib)
      router.replace('/videos')
  }, [])
  useEffect(() => {
    if (videoInfo)
      document.title = `${videoInfo.name || 'Video'} - ${APP_NAME}`
  }, [videoInfo])

  if (!videoInfo || error)
    return <Loading />

  return (
    <div className='overflow-hidden flex flex-col h-full w-full'>
      <VideoDetailContext.Provider value={{
        video: videoInfo,
        mutateVideoRes: () => mutateVideoRes(),
      }}>
        {/* 顶栏 */}
        <TopNavbar
          navigation={[]}
          leftContent={
            <AppBasic
              name={videoInfo.name || '--'}
              type={videoInfo.description || '--'}
              mode={videoInfo.type}
            ></AppBasic>
          }
          backFunc={backFunc}
        />
        <div className='h-[calc(100%-60px)]'>
          {children}
        </div>
      </VideoDetailContext.Provider>
    </div>

  )
}
export default React.memo(VideoDetailLayout)
