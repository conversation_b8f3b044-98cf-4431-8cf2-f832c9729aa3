import { DatasetUsage, AppUsage, ToolUsage, UsageData, Datasets } from './type'
import { useEffect, useState } from 'react'
import { getDatasetUsage, getAppUsage, getToolUsage, getFreeResourcePoints } from '@/service/data-statistics'
const useDataStatistics = () => {
  const [resourcePoints, setResourcePoints] = useState<UsageData>({})
  const [datasetUsage, setDatasetUsage] = useState<Datasets>({
    knowledge_creation_limit: {},
    single_knowledge_file_upload_limit: {},
    total_knowledge_storage_space: {}
  })
  const [appUsage, setAppUsage] = useState<UsageData>({})
  const [toolUsage, setToolUsage] = useState<UsageData>({})

  useEffect(() => {
    // const testResourcePoints: UsageData = {
    //   total: 1000,
    //   consumed: 300,
    //   remaining: 700
    // }
    // const testDataset: Datasets = {
    //   knowledge_creation_limit: {
    //     total: 20,
    //     consumed: 5,
    //     remaining: 15
    //   },
    //   single_knowledge_file_upload_limit: {
    //     total: 10,
    //     consumed: 1,
    //     remaining: 9
    //   },
    //   total_knowledge_storage_space: {
    //     total: 1000,
    //     consumed: 500,
    //     remaining: 1500
    //   }
    // }
    // const testTool:UsageData = {
    //   total: 20,
    //   consumed: 6,
    //   remaining: 14
    // }
    // const testApp:UsageData = {
    //   total: 50,
    //   consumed: 24,
    //   remaining: 26
    // }
    getDatasetUsage().then(res => {
      setDatasetUsage(res.data?.datasets || {})
    })
    getAppUsage().then(res => {
      setAppUsage(res.data?.apps || {})
    })
    getToolUsage().then(res => {
      setToolUsage(res.data?.tools || {})
    })
    // getFreeResourcePoints().then(res => {
    //   const { total_free_points: total, consumed_points: consumed, available_points: remaining } = res.data
    //   setResourcePoints({
    //     total,
    //     consumed,
    //     remaining,
    //   })
    // })
    // setResourcePoints(testResourcePoints)
    // setDatasetUsage(testDataset)
    // setAppUsage(testApp)
    // setToolUsage(testTool)
  }, [])

  return {
    resourcePoints,
    datasetUsage,
    appUsage,
    toolUsage
  }
}

export default useDataStatistics