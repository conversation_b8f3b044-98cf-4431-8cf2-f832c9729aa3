'use client'

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useContext } from 'use-context-selector'
import { useMount } from 'ahooks'
import { ConfigProvider, Tabs } from 'antd'
import { useRouter } from 'next/navigation'
import useAppsQueryState from '../apps/hooks/useAppsQueryState'
import s from './styles/index.module.css'
import type { Video } from '@/types/videos'
import { fetchVideoList } from '@/service/videos'
import VideosCard from '@/app/components/videos/card'
import CreateVideoModal from '@/app/components/videos/create-video-modal'
import { VideoActionBtn } from '@/app/components/videos/common/video-action-btn'

// 公共能力
import { APP_NAME } from '@/config'
import { useProviderContext } from '@/context/provider-context'
import { useTabSearchParams } from '@/hooks/use-tab-searchparams'
import { GRAY } from '@/themes/var-define'
import SearchInput from '@/app/components/base/input/search-input'
import { ToastContext } from '@/app/components/base/toast'
import CardList from '@/app/components/base/card-list'

// 获取视频库卡片接口参数
const getKey = (
  pageIndex: number,
  tags: string[],
  keyword: string,
) => {
  const params = {
    url: 'datasets',
    params: {
      page: pageIndex,
      size: 30,
      tag_ids: [] as string[],
      keyword: '',
    },
  }
  if (tags.length)
    params.params.tag_ids = tags
  if (keyword)
    params.params.keyword = keyword
  return params
}

const Container = () => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  const { plan } = useProviderContext()
  const { query: { tagIDs = [], keywords = '' }, setQuery } = useAppsQueryState()
  const isVideoFull = plan.usage.buildVideos >= plan.total.buildVideos
  const router = useRouter()

  // 当前激活tab
  const [activeTab, setActiveTab] = useTabSearchParams({
    defaultTab: 'all',
  })
  // 是否显示创建视频库弹窗
  const [showCreateModal, setShowCreateModal] = useState(false)
  // 是否正在加载
  const [loading, setLoading] = useState(true)
  // 分页信息
  const [pagination, setPagination] = useState({
    page: 1,
    total: 0,
  })
  // 卡片列表
  const [cardList, setCardList] = useState<Array<Video>>([])
  // 是否还有未加载的部分
  const hasMore = useMemo(() => {
    return pagination.total > (pagination.page * 30)
  }, [pagination])
  // 页签列表
  const tabList = useMemo(() => {
    return [{
      key: 'all',
      label: `${t('videos.type.all')}`,
    }]
  }, [t])

  // 获取视频库卡片列表
  const fetchVideosCard = useCallback(async (page: number) => {
    const param = getKey(page, tagIDs, keywords)
    setLoading(true)
    await fetchVideoList(param.params).then((res) => {
      setCardList([...cardList, ...res.data.info])
      setPagination({
        page,
        total: res.data.total,
      })
    }).finally(() => {
      setLoading(false)
    })
  }, [cardList, keywords, tagIDs])
  // 重置视频库卡片列表
  const resetVideosCard = async (params?: { currentTag: string[]; currentKeyword: string }) => {
    const param = getKey(1, params ? params.currentTag : tagIDs, params ? params.currentKeyword : keywords)
    await fetchVideoList(param.params).then((res) => {
      setCardList([...res.data.info])
      setPagination({
        page: 1,
        total: res.data.total,
      })
    }).finally(() => {
      setLoading(false)
    })
  }
  // 获取下一页
  const nextVideosCard = async () => {
    if (hasMore) {
      setPagination({
        page: pagination.page + 1,
        total: pagination.total,
      })
      await fetchVideosCard(pagination.page + 1)
    }
  }
  // 变更关键字
  const changeKeyWord = (value: string) => {
    setQuery(prev => ({ ...prev, keywords: value }))
    resetVideosCard({ currentKeyword: value, currentTag: tagIDs })
  }
  // 创建视频库
  const createVideo = () => {
    if (isVideoFull)
      notify({ type: 'error', message: t('videos.notify.videoFull', { num: plan.total.buildVideos }) })
    else setShowCreateModal(true)
  }
  // 成功创建视频库
  const onSuccessCreate = (video: Video) => {
    setShowCreateModal(false)
    router.push(`/videos/${video.id}/documents`)
  }

  // 调整document信息
  useEffect(() => {
    if (typeof window !== 'undefined')
      document.title = `${t('videos.title')} - ${APP_NAME}`
  }, [t])
  useMount(() => {
    resetVideosCard()
  })

  return (
    <>
      {/* 头部信息 */}
      <div className={s['app-list-header']}>
        <div className={s['header-title']}>{t('videos.title')}</div>
        <VideoActionBtn onClick={createVideo} variant={'primary'}>{t('videos.action.createVideos')}</VideoActionBtn>
      </div>
      {/* 筛选条件 */}
      <div className={s['app-list-filter']}>
        <Tabs className={s['search-tab']} activeKey={activeTab} items={tabList} onChange={setActiveTab}></Tabs>
        <div className={s['search-criteria']}>
          <ConfigProvider
            theme={{
              components: {
                Input: {
                  colorBgContainer: 'transparent',
                  colorBorder: GRAY['G3-5'],
                },
              },
            }}
          >
            <SearchInput value={keywords} className='w-[220px]' onChange={changeKeyWord} />
          </ConfigProvider>
        </div>
      </div>
      {/* 卡片列表 */}
      <CardList
        type='scroll'
        loadFunc={nextVideosCard}
        loading={loading}
        layout='line'
        emptyText={t('videos.notify.noVideo')}
        className='!shrink h-full px-8 py-6 overflow-auto'
      >
        {cardList.map(video => (
          <VideosCard key={video.id} video={video} onSuccess={resetVideosCard} />),
        )}
      </CardList>
      {/* 创建视频库弹窗 */}
      {showCreateModal && <CreateVideoModal
        onClose= {() => { setShowCreateModal(false) }}
        onSuccess={onSuccessCreate}
      />}
    </>

  )
}

export default Container
