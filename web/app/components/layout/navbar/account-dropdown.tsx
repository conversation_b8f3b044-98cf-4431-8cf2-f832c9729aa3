'use client'
import { Config<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Popover } from 'antd'
import { useMemo } from 'react'
import type { MenuProps } from 'antd'
import { useRouter } from 'next/navigation'
import { t } from 'i18next'
import Link from 'next/link'
import { useContext } from 'use-context-selector'
import s from './styles/dropdown.module.css'
import WorkspaceSelector, { labelRender } from './workspce-selector'
import { logout } from '@/service/common'
// 公共能力
import I18n from '@/context/i18n'
import { LanguagesSupported, languages } from '@/i18n/language'
import cn from '@/utils/classnames'
import { useAppContext } from '@/context/app-context'
import { removeAuth } from '@/utils/user'
import Avatar from '@/app/components/base/avatar'
import { useSystemContext } from '@/context/system-context'

const AccountDropdown = () => {
  const router = useRouter()
  const { locale, setLocaleOnClient } = useContext(I18n)
  const { userProfile, canAccountSetting } = useAppContext()
  const { canI18n, isPrivate } = useSystemContext()

  // 账户信息下拉菜单
  const menuItemList = useMemo(() => ([
    ...((canAccountSetting)
      ? [{
        key: 'settings',
        label: <Link href="/account-setting">{t('common.operation.settings')}</Link>,
      }]
      : []),
    ...(canI18n
      ? [{
        key: 'language',
        label: t('account.info.language'),
        children: languages.map((item) => {
          return {
            key: item.value,
            label: labelRender({
              name: item.name,
              current: item.value === locale,
            } as any, true),
          }
        }),
      }]
      : []),
    {
      key: 'helpCenter',
      label: (<Link
        href={'https://k36drdpyul.feishu.cn/docx/B6htdYzRuovxiyxBIA0c2PlknPN'}
        target='_blank' rel='noopener noreferrer'
      >{t('account.action.helpCenter')}</Link>),
    },
    {
      key: 'logout',
      label: t('account.action.logout'),
    },
  ]), [canAccountSetting, canI18n, locale])

  /* 点击账户信息菜单 */
  const handleMenuClick: MenuProps['onClick'] = async (e) => {
    /* 退出登录 */
    if (e.key === 'logout') {
      const res = await logout({
        url: '/logout',
        params: {},
      })
      removeAuth()
      if (res.code === '3002')
        window.location.href = res.data
      else if (res.code === '********')
        window.location.href = res.msg
        // get(res.msg).then((res) => {
        //   console.log(res, '===res')
        //   // window.location.href = res.data
        // })
      else
        router.push('/apps')
    }
    else if (LanguagesSupported.includes(e.key) && e.key !== locale) {
      setLocaleOnClient(e.key)
    }
  }

  return (
    <div className={cn(s.accountDropdown)}>
      <div className={cn(s.accountDropdownHeader)}>
        <Avatar name={userProfile.name} className="sm:mr-1.5 mr-0" size={32} />
        <div className={cn(s.userInfo)}>
          <div className={cn(s.userInfoName)}>{userProfile.name}</div>
          { isPrivate && <div className={s.userInfoEmail}>{userProfile.email}</div> }
        </div>
      </div>
      {/* 私有化 */}
      {
        // userProfile.client_from === 'teleai-private' && <WorkspaceSelector></WorkspaceSelector>
        isPrivate && <WorkspaceSelector></WorkspaceSelector>
      }
      <ConfigProvider theme={{ components: { Menu: { itemMarginBlock: 0, itemHeight: 36 } } }}>
        <Menu items={menuItemList} onClick={handleMenuClick} selectable={false}></Menu>
      </ConfigProvider>
    </div>
  )
}

// 客户信息下拉框
const AccountDropdownPopover = () => {
  const { userProfile } = useAppContext()

  return (
    <Popover className="p-0" arrow={false} content={AccountDropdown}>
      <div className={cn(s.accountDropdownTrigger)}>
        <Avatar name={userProfile.name} className='mr-0 sm:mr-2' size={24} />
        <div className={cn(s.triggerName)} title={userProfile.name}>{userProfile.name}</div>
      </div>
    </Popover>
  )
}

export default AccountDropdownPopover
