'use client'
import { useCallback, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { debounce } from 'lodash-es'
import { useContext } from 'use-context-selector'
import { Checkbox, Form, Input } from 'antd'
import Button from '../../base/button'
import { statusEnum } from '../../app/app-publish-config/type'
import CheckboxCard from './checkboxCard'
import MarketConfigModal from './marketConfigModal'
import EmbeddingConfigModal from './embeddingConfigModal'
import { MAX_APP_DESC_LENGTH, MAX_APP_NAME_LENGTH } from '@/config'

import { useFeatures } from '@/app/components/base/features/hooks'
import type { App, AppSSO } from '@/types/app'
import Modal from '@/app/components/base/modal'
import Confirm from '@/app/components/base/confirm'
import { ToastContext } from '@/app/components/base/toast'
import type { ModelAndParameter } from '@/app/components/app/configuration/debug/types'

type AppPublishProps = {
  app: App & Partial<AppSSO>
  appPublishInfo: any
  isPublished?: boolean
  onPublish?: (showNotify: boolean, modelAndParameter?: ModelAndParameter, features?: any) => Promise<any> | any
  onCancel: () => void
  onSave: (params: any) => void
}

const AppPublish = ({
  app,
  appPublishInfo,
  isPublished,
  onPublish,
  onCancel,
  onSave,
}: AppPublishProps) => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  const [form] = Form.useForm()
  const values = Form.useWatch([], form)

  const features = useFeatures(s => s.features)
  // 应用类别列表
  const [appCategoriesList, setAppCategoriesList] = useState<Array<any>>([])
  // 是否可提交
  const [disabled, setDisabled] = useState(false)
  // 应用广场配置弹窗
  const [showMarketConfigModal, setShowMarketConfigModal] = useState(false)
  // 嵌入网站配置弹窗
  const [showEmbeddingConfigModal, setShowEmbeddingConfigModal] = useState(false)
  // 显示更新发布二次确认弹窗
  const [showReConfirmModal, setShowReConfirmModal] = useState(false)
  const [appPublishConfig, setPublishConfig] = useState<any>({})
  // 发布渠道选项
  const publishMethodOptions = [
    { label: t('app.publish.methodOption.market'), value: 'market', onConfigure: () => setShowMarketConfigModal(true) },
    { label: t('app.publish.methodOption.embed'), value: 'embed' },
    { label: t('app.publish.methodOption.web'), value: 'web' },
    { label: t('app.publish.methodOption.api'), value: 'api' },
  ]

  useEffect(() => {
    onPublish?.(false, undefined, features)
  }, [])

  // 表单初始化
  useEffect(() => {
    form.setFieldsValue({
      name: app.name,
      description: app.description,
    })
    let iconFileId = ''
    if (app.icon?.startsWith('/files')) {
      const regex = /\/files\/([^\/]+)\/file-preview/
      const match = app.icon.match(regex)
      if (match)
        iconFileId = match[1]
    }
    setPublishConfig({
      name: app.name,
      description: app.description,
      icon: iconFileId || app.icon,
      icon_type: app.icon_type,
      icon_background: app.icon_background,
    })
  }, [])

  // 配置弹窗保存回调
  const handleConfigureSave = (params: object) => {
    setPublishConfig({
      ...appPublishConfig,
      ...params,
    })
    setShowEmbeddingConfigModal(false)
    setShowMarketConfigModal(false)
  }
  useEffect(() => {
  }, [appPublishConfig])
  // 提交发布信息
  const handleSave = useCallback(() => {
    if (values?.publishMethod?.includes('market')) {
      if (!appPublishConfig.category_id || !appPublishConfig.acl) {
        notify({
          type: 'error',
          message: t('app.publish.tip.marketTip'),
        })
        return
      }
    }
    else if (values?.publishMethod?.includes('embedding')) {
      if (!appPublishConfig.embedding_method) {
        notify({
          type: 'error',
          message: t('app.publish.tip.embeddingTip'),
        })
        return
      }
    }

    if (onSave) {
      onSave({
        ...appPublishConfig,
        name: values.name,
        description: values.description,
        release: values?.publishMethod.join('_'),
      })
    }
  }, [values, appPublishConfig])

  useEffect(debounce(() => {
    form.validateFields({ validateOnly: true }).then(() => {
      setDisabled(false)
    },
    ).catch(() => {
      setDisabled(true)
    })
  }, 100), [form, values])

  return (
    <>
      <Modal
        isShow
        closable
        onClose={onCancel}
        title={t('app.modalTitle.publish')}
        footer={
          <>
            <Button onClick={onCancel} className="mr-4" variant={'secondary-accent'}>{ t('common.operation.cancel') }</Button>
            <Button
              disabled={disabled}
              onClick={() => {
                isPublished ? setShowReConfirmModal(true) : handleSave()
              }}
              variant={'primary'}
            >
              { t('common.operation.confirm') }
            </Button>
          </>
        }
      >
        <Form form={form} layout='vertical'>
          <Form.Item
            required
            label={t('app.info.appName')}
          >
            <div className='flex items-center gap-2'>
              <Form.Item
                name="name"
                label={t('app.info.appName')}
                rules={[
                  {
                    required: true,
                    whitespace: true,
                    max: MAX_APP_NAME_LENGTH,
                  },
                ]}
                validateTrigger='onBlur'
                noStyle
              >
                <Input placeholder={t('app.placeholder.appName') as string} maxLength={MAX_APP_NAME_LENGTH}></Input>
              </Form.Item>
            </div>
          </Form.Item>
          <Form.Item
            name="description"
            label={t('app.info.appDescription')}
            rules={[
              { required: true, whitespace: true, max: MAX_APP_DESC_LENGTH },
            ]}
          >
            <Input.TextArea
              placeholder={t('app.placeholder.appDescription') as string}
              maxLength={MAX_APP_DESC_LENGTH}
            ></Input.TextArea>
          </Form.Item>
          <Form.Item
            name='publishMethod'
            label={t('app.publish.method')}
            rules={[
              { required: true, type: 'array', message: t('app.publish.tip.publishMethodTip') || '', min: 1 },
            ]}
          >
            <Checkbox.Group className='grid grid-cols-2 gap-3' >
              {publishMethodOptions.map(option => (
                <CheckboxCard
                  key={option.value}
                  value={option.value}
                  onConfigure={option.onConfigure}
                  checked={values?.publishMethod?.includes(option.value)}
                  disabled={option.value === 'market' && appPublishInfo?.status === statusEnum.review}
                  disabledText={t('app.publish.status.review')!}
                >{option.label}</CheckboxCard>
              ))}
            </Checkbox.Group>
          </Form.Item>
          {/* <Form.Item
            name="category_id"
            label={t('app.info.appCategories')}
            validateFirst={true}
            rules={[{ required: true }]}
            validateTrigger='onChange'
          >
            <Select options={appCategoriesList}></Select>
          </Form.Item> */}
        </Form>
      </Modal>
      {showMarketConfigModal
        && <MarketConfigModal
          appPublishConfig={appPublishConfig}
          onCancel={() => setShowMarketConfigModal(false)}
          onSave={handleConfigureSave}
        />
      }
      {
        showEmbeddingConfigModal
          && <EmbeddingConfigModal
            appPublishConfig={appPublishConfig}
            onCancel={() => setShowEmbeddingConfigModal(false)}
            onSave={handleConfigureSave}
          />
      }
      {
        showReConfirmModal
        && <Confirm
          title={t('app.publish.reConfirm.title')}
          content={
            <div style={{ whiteSpace: 'pre-line' }}>
              {t('app.publish.reConfirm.content')}
            </div>
          }
          isShow={showReConfirmModal}
          onCancel={() => setShowReConfirmModal(false)}
          onConfirm={handleSave}
        ></Confirm>
      }
    </>
  )
}

export default AppPublish
