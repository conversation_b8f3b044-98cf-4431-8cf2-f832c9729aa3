import { useTranslation } from 'react-i18next'
import type { ModelAndParameter } from '../debug/types'
import Button from '@/app/components/base/button'
import { useFeaturesStore } from '@/app/components/base/features/hooks'
import type { Features } from '@/app/components/base/features/types'

const AppSaveBtn = ({ onSave }: {
  onSave: (showNotify: boolean, modelAndParameter?: ModelAndParameter, features?: Features) => void
}) => {
  const { t } = useTranslation()
  const featuresStore = useFeaturesStore()

  // 保存配置
  const handleSave = async () => {
    const {
      features,
    } = featuresStore!.getState()
    await onSave(true, undefined, features)
  }

  return (
    <Button variant={'secondary-accent'} onClick={handleSave}>{t('common.operation.save')}</Button>
  )
}

export default AppSaveBtn
