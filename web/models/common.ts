import type { I18nText } from '@/i18n/language'

export type CommonResponse<T = Record<string, any>> = {
  result: 'success' | 'fail'
  data: T
}
export type SsoCommonResponse = {
  code: string | number
  message?: string
  data?: any
  msg?: any
  status?: any
}
export type SsoVerifyResponse = {
  repCode: string
  repData: Record<string, any>
  repMsg: string
  success: boolean
  error: boolean
}
export type SetupStatusResponse = {
  step: 'finished' | 'not_started'
  setup_at?: Date
}
export type InitValidateStatusResponse = {
  status: 'finished' | 'not_started'
}
export type UserProfileResponse = {
  id: string
  name: string
  email?: string
  avatar: string
  is_parent: boolean
  is_vip: boolean
  client_from: string
  is_password_set: boolean
  interface_language?: string
  interface_theme?: string
  timezone?: string
  last_login_at?: string
  last_active_at?: string
  last_login_ip?: string
  created_at?: string
  super_user: boolean
  expiration_date?: string
  video_library_user: boolean
}
export type UserProfileOriginResponse = {
  json: () => Promise<UserProfileResponse>
  bodyUsed: boolean
  headers: any
}
export type LangGeniusVersionResponse = {
  current_env: string
}

export type Member = Pick<UserProfileResponse, 'id' | 'name' | 'email' | 'last_login_at' | 'last_active_at' | 'created_at'> & {
  avatar: string
  status: 'pending' | 'active' | 'banned' | 'closed'
  role: 'owner' | 'admin' | 'editor' | 'normal' | 'dataset_operator'
}

export enum ProviderName {
  OPENAI = 'openai',
  AZURE_OPENAI = 'azure_openai',
  ANTHROPIC = 'anthropic',
  Replicate = 'replicate',
  HuggingfaceHub = 'huggingface_hub',
  MiniMax = 'minimax',
  Spark = 'spark',
  Tongyi = 'tongyi',
  ChatGLM = 'chatglm',
}
export type ProviderAzureToken = {
  openai_api_base?: string
  openai_api_key?: string
}
export type ProviderAnthropicToken = {
  anthropic_api_key?: string
}

export type Provider = {
  [Name in ProviderName]: {
    provider_name: Name
  } & {
    provider_type: 'custom' | 'system'
    is_valid: boolean
    is_enabled: boolean
    last_used: string
    token?: string | ProviderAzureToken | ProviderAnthropicToken
  }
}[ProviderName]

export type IWorkspace = {
  id: string
  name: string
  plan: string
  status: string
  created_at: number
  current: boolean
}

export type ICurrentWorkspace = Omit<IWorkspace, 'current'> & {
  role: 'owner' | 'admin' | 'editor' | 'dataset_operator' | 'normal'
  providers: Provider[]
  in_trail: boolean
  trial_end_reason?: string
  custom_config?: {
    remove_webapp_brand?: boolean
    replace_webapp_logo?: string
  }
}

export type DataSourceNotionPage = {
  page_icon: null | {
    type: string | null
    url: string | null
    emoji: string | null
  }
  page_id: string
  page_name: string
  parent_id: string
  type: string
  is_bound: boolean
}

export type DataSourceNotionWorkspace = {
  workspace_name: string
  workspace_id: string
  workspace_icon: string | null
  total?: number
  pages: DataSourceNotionPage[]
}

export type DataSourceNotion = {
  id: string
  provider: string
  is_bound: boolean
  source_info: DataSourceNotionWorkspace
}

export enum DataSourceCategory {
  website = 'website',
}
export enum DataSourceProvider {
  fireCrawl = 'firecrawl',
  // jinaReader = 'jinareader',
}

export type FirecrawlConfig = {
  api_key: string
  base_url: string
}

export type DataSourceItem = {
  id: string
  category: DataSourceCategory
  provider: DataSourceProvider
  disabled: boolean
  created_at: number
  updated_at: number
}

export type DataSources = {
  sources: DataSourceItem[]
}

export type FileUploadConfigResponse = {
  file_size_limit: number
  batch_count_limit: number
  image_file_size_limit?: number | string
}

export type InvitationResult = {
  status: 'success'
  email: string
  url: string
} | {
  status: 'failed'
  email: string
  message: string
}

export type InvitationResponse = CommonResponse & {
  invitation_results: InvitationResult[]
}

export type ApiBasedExtension = {
  id?: string
  name?: string
  api_endpoint?: string
  api_key?: string
}

export type CodeBasedExtensionForm = {
  type: string
  label: I18nText
  variable: string
  required: boolean
  options: { label: I18nText; value: string }[]
  default: string
  placeholder: string
  max_length?: number
}

export type CodeBasedExtensionItem = {
  name: string
  label: any
  form_schema: CodeBasedExtensionForm[]
}
export type CodeBasedExtension = {
  module: string
  data: CodeBasedExtensionItem[]
}

export type ExternalDataTool = {
  type?: string
  label?: string
  icon?: string
  icon_background?: string
  variable?: string
  enabled?: boolean
  config?: {
    api_based_extension_id?: string
  } & Partial<Record<string, any>>
}

export type ModerateResponse = {
  flagged: boolean
  text: string
}

export type ModerationService = (
  url: string,
  body: {
    app_id: string
    text: string
  }
) => Promise<ModerateResponse>

// 根据code获取用户信息
export type QueryUserInfoByCodeType = {
  code: string
  accountId: string
}
// 查询团队空间
export type QueryTeamList = {
  team_name?: string
  page?: number
  size?: number
  search?: string
  data?: any
  isPage?: boolean
}
// 查询团队成员空间
export type QueryTeamMemberList = {
  team_id: string
  role?: string
  username?: string
  page?: number
  size?: number
  search?: string
  data?: any
  isPage?: boolean
  user_id?: string
}



/* 团队=相关接口类型--start */
// 查询团队
export type TeamDataItemsType = {
  id?: string
  team_name: string
  created_time: number
  created_by?: string
}
export type TeamDataType = {
  total?: number
  page?: number
  size?: number
  items?: TeamDataItemsType | QueryTeamMemberType
}
export type QueryTeamDataType = {
  code: string
  message: string
  data?: any | TeamDataType
  msg?: any
}
// 创建团队保存
export type QueryTeamCreate = QueryTeamDataType | {
  code: string
  message: string
  data?: any | {
    team_id?: string
    team_name?: string
  }
}
// 查看成员列表
export type QueryTeamMemberType = {
  user_id?: string
  username?: string
  role?: string
}

/* 团队=相关接口类型--end */
