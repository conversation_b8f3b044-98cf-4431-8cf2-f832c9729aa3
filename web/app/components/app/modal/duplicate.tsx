import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Form, Input } from 'antd'
import { getRandomAppHead } from '../utils'
import type { AppIconType } from '@/types/app'
import { MAX_APP_NAME_LENGTH } from '@/config'
// 公共组件
import { secureRandom } from '@/utils'
import cn from '@/utils/classnames'
import Modal from '@/app/components/base/modal'
import Button from '@/app/components/base/button'
import Avatar from '@/app/components/base/avatar'
import { useFormDisabled } from '@/hooks/use-form'

export type DuplicateAppModalProps = {
  appName: string
  icon: string
  show: boolean
  onConfirm: (info: {
    name: string
    icon_type: AppIconType
    icon: string
    icon_background?: string | null
  }) => Promise<void>
  onHide: () => void
}

const DuplicateAppModal = ({
  appName,
  icon,
  show = false,
  onConfirm,
  onHide,
}: DuplicateAppModalProps) => {
  const { t } = useTranslation()
  const [form] = Form.useForm()
  const disabled = useFormDisabled(form)

  // 应用图标
  const [appIcon, setAppIcon] = useState({
    type: 'image',
    url: icon || getRandomAppHead(),
    fileId: icon || secureRandom(),
  })

  // 表单提交动作
  const submit = () => {
    onConfirm({
      name: form.getFieldValue('name'),
      icon_type: 'image',
      icon: appIcon.url,
      icon_background: undefined,
    })
    onHide()
  }
  // 变更应用头像
  const changeAppHead = (value: string) => {
    setAppIcon({
      type: 'image',
      url: value,
      fileId: secureRandom(),
    })
  }

  return (
    <Modal
      isShow={show}
      title={t('app.modalTitle.duplicateTitle')}
      onClose={onHide}
      closable
      className={cn('!max-w-[480px] relative')}
      footer={
        <>
          <Button
            variant='secondary-accent'
            className='mr-4'
            onClick={onHide}
          >
            {t('common.operation.cancel')}
          </Button>
          <Button
            disabled={disabled}
            variant='primary'
            onClick={submit}
          >
            {t('common.operation.copy')}
          </Button>
        </>
      }
    >
      <Form layout='vertical' form={form} initialValues={{ name: appName }}>
        <Form.Item
          name={'name'}
          validateFirst={true}
          rules={[
            {
              required: true,
              whitespace: true,
              max: MAX_APP_NAME_LENGTH,
            },
          ]}
          label={t('app.info.appName')}
          validateTrigger={'onBlur'}
        >
          <Input placeholder={t('app.placeholder.appName') as string} maxLength={MAX_APP_NAME_LENGTH}/>
        </Form.Item>
        <Form.Item
          required
          label={t('app.info.appHead')}
        >
          <Avatar
            avatar={appIcon.url}
            size={80}
            showUpload
            className='!rounded'
            onChange={changeAppHead}
          ></Avatar>
        </Form.Item>
      </Form>
    </Modal>

  )
}

export default DuplicateAppModal
