import {
  useCallback,
  useRef,
  useState,
} from 'react'
import type {
  DebugWithSingleOrMultipleModelConfigs,
  ModelAndParameter,
} from './types'
import { ORCHESTRATE_CHANGED } from './types'
import type {
  ChatConfig,
  ChatItem,
} from '@/app/components/base/chat/types'
import {
  AgentStrategy,
} from '@/types/model'
import { promptVariablesToUserInputsForm } from '@/utils/model-config'
import { useDebugConfigurationContext } from '@/context/debug-configuration'
import { useEventEmitterContextContext } from '@/context/event-emitter'

export const useDebugWithSingleOrMultipleModel = (appId: string) => {
  const localeDebugWithSingleOrMultipleModelConfigs = localStorage.getItem('app-debug-with-single-or-multiple-models')

  const debugWithSingleOrMultipleModelConfigs = useRef<DebugWithSingleOrMultipleModelConfigs>({})

  if (localeDebugWithSingleOrMultipleModelConfigs) {
    try {
      debugWithSingleOrMultipleModelConfigs.current = JSON.parse(localeDebugWithSingleOrMultipleModelConfigs) || {}
    }
    catch (e) {
      console.error(e)
    }
  }

  // 最优模型
  const [bestModel, setBestModel] = useState(debugWithSingleOrMultipleModelConfigs.current[appId]?.bestModel || '')
  const [
    debugWithMultipleModel,
    setDebugWithMultipleModel,
  ] = useState(debugWithSingleOrMultipleModelConfigs.current[appId]?.multiple || false)

  const [
    multipleModelConfigs,
    setMultipleModelConfigs,
  ] = useState(debugWithSingleOrMultipleModelConfigs.current[appId]?.configs || [])

  // 修改多模型配置
  const handleMultipleModelConfigsChange = useCallback((
    multiple: boolean,
    modelConfigs: ModelAndParameter[],
    newBestModel: string,
  ) => {
    if (modelConfigs.length > 0) {
      // 开启时，bestModel设置为传入值，如果bestModel不在modelConfigs中，则设置为第一个;关闭时，只有两个模型，则开启另一个
      if (newBestModel)
        setBestModel(modelConfigs.some(item => item.id === newBestModel) ? newBestModel : modelConfigs[0].id)

      else
        setBestModel(modelConfigs.length === 2 ? modelConfigs.find(item => item.id !== bestModel)?.id || '' : '')
    }
    else {
      setBestModel('')
    }
    const value = {
      multiple,
      configs: modelConfigs,
      bestModel: newBestModel,
    }
    debugWithSingleOrMultipleModelConfigs.current[appId] = value
    localStorage.setItem('app-debug-with-single-or-multiple-models', JSON.stringify(debugWithSingleOrMultipleModelConfigs.current))
    setDebugWithMultipleModel(value.multiple)
    setMultipleModelConfigs(value.configs)
  }, [appId, bestModel])

  return {
    bestModel,
    debugWithMultipleModel,
    multipleModelConfigs,
    handleMultipleModelConfigsChange,
  }
}

export const useConfigFromDebugContext = () => {
  const {
    isAdvancedMode,
    modelConfig,
    appId,
    promptMode,
    chatPromptConfig,
    completionPromptConfig,
    dataSets,
    datasetConfigs,
    isFunctionCall,
  } = useDebugConfigurationContext()
  const postDatasets = dataSets.map(({ id }) => ({
    dataset: {
      enabled: true,
      id,
    },
  }))
  const contextVar = modelConfig.configs.prompt_variables.find(item => item.is_context_var)?.key
  const config: ChatConfig = {
    pre_prompt: !isAdvancedMode ? modelConfig.configs.prompt_template : '',
    prompt_type: promptMode,
    chat_prompt_config: isAdvancedMode ? chatPromptConfig : {} as any,
    completion_prompt_config: isAdvancedMode ? completionPromptConfig : {} as any,
    user_input_form: promptVariablesToUserInputsForm(modelConfig.configs.prompt_variables),
    dataset_query_variable: contextVar || '',
    more_like_this: {
      enabled: false,
    },
    agent_mode: {
      ...modelConfig.agentConfig,
      strategy: isFunctionCall ? AgentStrategy.functionCall : AgentStrategy.react,
      fork: false,
    },
    dataset_configs: {
      ...datasetConfigs,
      datasets: {
        datasets: [...postDatasets],
      } as any,
    },
    supportAnnotation: true,
    appId,
    supportCitationHitInfo: true,
  }

  return config
}

// 触发模型配置变更
export const useFormattingChangedDispatcher = () => {
  const { eventEmitter } = useEventEmitterContextContext()

  const dispatcher = useCallback(() => {
    eventEmitter?.emit({
      type: ORCHESTRATE_CHANGED,
    } as any)
  }, [eventEmitter])

  return dispatcher
}
// 订阅模型配置变更
export const useFormattingChangedSubscription = (chatList: ChatItem[]) => {
  const {
    formattingChanged,
    setFormattingChanged,
  } = useDebugConfigurationContext()
  const { eventEmitter } = useEventEmitterContextContext()
  eventEmitter?.useSubscription((v: any) => {
    if (v.type === ORCHESTRATE_CHANGED) {
      if (chatList.some(item => item.isAnswer && !item.isOpeningStatement) && !formattingChanged)
        setFormattingChanged(true)
    }
  })
}
