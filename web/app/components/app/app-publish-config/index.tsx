'use client'
import { use<PERSON><PERSON>back, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useContext, useContextSelector } from 'use-context-selector'
import { Table } from 'antd'
import type { TableColumnsType } from 'antd'
import { isBoolean } from 'lodash'
import { useAppPublishInit } from '../../app-publish/hooks/use-app-publish'
import Indicator from '../../base/indicator'
import Button from '../../base/button'
import type { statusEnum } from './type'
import { statusMap } from './type'
import AppAction from './app-action'
import { useStore as useAppStore } from '@/app/components/app/store'
import {
  fetchAppDetail,
  fetchAppSSO,
} from '@/service/apps'
import { ToastContext } from '@/app/components/base/toast'
import Confirm from '@/app/components/base/confirm'
import { unPublishAppspublic } from '@/service/market'
import EmbeddedModal from '@/app/components/app/embedded'
import SystemContext from '@/context/system-context'

type AppPublishConfigProps = {
  appId: string
}
const AppPublishConfig = ({
  appId,
}: AppPublishConfigProps) => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  const appDetail = useAppStore(state => state.appDetail)
  // const appPublishInfo = useAppStore(state => state.appPublishInfo)
  const setAppDetail = useAppStore(state => state.setAppDetail)
  const systemFeatures = useContextSelector(SystemContext, state => state.systemFeatures)
  // 是否显示嵌入弹窗
  const [showEmbedded, setShowEmbedded] = useState(false)
  // 是否显示确认下架弹窗
  const [showConfirmUnpublish, setShowConfirmUnpublish] = useState(false)
  const {
    getAppPublishInfoData,
    appPublishInfo,
  } = useAppPublishInit()
  // 更新应用信息
  const updateAppDetail = async () => {
    try {
      const res = await fetchAppDetail({ url: '/apps', id: appId })
      if (systemFeatures.enable_web_sso_switch_component) {
        const ssoRes = await fetchAppSSO({ appId })
        setAppDetail({ ...res, enable_sso: ssoRes.enabled })
      }
      else {
        setAppDetail({ ...res })
      }
    }
    catch (error) { console.error(error) }
  }
  const ClickUnpublish = useCallback((app: any) => {
    setShowConfirmUnpublish(true)
  }, [])
  // 下架
  const handleUnPublish = async () => {
    try {
      await unPublishAppspublic(appId, {
        failed_message: '',
      })
      notify({
        type: 'success',
        message: t('common.actionMsg.unPublishSuccess'),
      })
      setShowConfirmUnpublish(false)
      getAppPublishInfoData()
    }
    catch {}
  }
  if (!appDetail)
    return <></>
  const dataSource = useMemo(() => {
    return [
      {
        publishMethod: 'web',
        status: !!appDetail.web_site?.access_token,
        action: (
          <AppAction appDetail={appDetail} actionType={'webapp'} hasPublish={!!appDetail.web_site?.access_token} onUpdateAppDetail={updateAppDetail} />
        ),
      },
      {
        publishMethod: 'api',
        status: !!appDetail.api_site?.access_token,
        action: (
          <AppAction appDetail={appDetail} actionType={'api'} hasPublish={!!appDetail.api_site?.access_token} onUpdateAppDetail={updateAppDetail} />
        ),
      },
      {
        publishMethod: 'embed',
        status: !!appDetail.embed_site?.access_token,
        action: (
          <>
            <AppAction appDetail={appDetail} actionType={'embed'} hasPublish={!!appDetail.embed_site?.access_token} onUpdateAppDetail={updateAppDetail} />
          </>
        ),
      },
      {
        publishMethod: 'market',
        status: appPublishInfo?.status,
        action: (
          <Button variant={'secondary-accent'} onClick={ClickUnpublish} disabled={appPublishInfo?.status !== 'online'}>{t('common.operation.pull')}</Button>
        ),
      },
    ]
  }, [appDetail, appPublishInfo])

  // 表格列
  const columns: TableColumnsType<any> = [
    // 数据集英文名称
    {
      title: t('app.publish.method'),
      key: 'publishMethod',
      render: (_: any, data) => {
        return t(`app.publish.methodOption.${data.publishMethod}`)
      },
      width: 160,
      ellipsis: true,
    },
    // 状态
    {
      title: t('app.info.status'),
      key: 'status',
      render: (_: any, data) => {
        return (
          <Indicator
            color={statusMap[data.status as statusEnum]?.color || 'gray'}
            textClassName='text-S3 leading-H3'
          >
            {isBoolean(data.status)
              ? data.status ? t('app.publish.status.online') : t('app.publish.status.pending')
              : statusMap[data.status as statusEnum]?.text
                ? t(statusMap[data.status as statusEnum]?.text)
                : t('app.publish.status.pending')}
          </Indicator>
        )
      },
      width: 160,
      ellipsis: true,
    },
    // 操作
    {
      title: t('app.info.action'),
      render: (_: any, data) => (
        <>
          {data.action}
        </>
      ),
      key: 'operation',
      align: 'left',
      width: 800,
    },
  ]

  return (
    <>
      <Table
        size='large'
        columns={columns}
        pagination={false}
        scroll={{ y: 'calc(100vh - 285px)' }}
        rowKey='id'
        dataSource={dataSource || []}
        className='border-gray-G5 rounded border'
        rowClassName='cursor-pointer'
      ></Table>
      {/* 嵌入弹窗 */}
      {showEmbedded && <EmbeddedModal
        siteInfo={appDetail.embed_site}
        isShow={showEmbedded}
        onClose={() => setShowEmbedded(false)}
        appBaseUrl={appDetail.embed_site?.app_base_url}
        accessToken={appDetail.embed_site?.access_token}
      />}
      <Confirm
        title={t('app.action.unPublish')}
        content={t('app.publish.tip.unPublishTip')}
        isShow={showConfirmUnpublish}
        onCancel={() => setShowConfirmUnpublish(false)}
        onConfirm={handleUnPublish}
      ></Confirm>
    </>
  )
}

export default AppPublishConfig
