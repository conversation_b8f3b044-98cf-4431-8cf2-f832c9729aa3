'use client'
import { memo, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { RiEqualizer2Line } from '@remixicon/react'
import ConfigContent from './config-content'
import Modal from '@/app/components/base/modal'
import Button from '@/app/components/base/button'
import { RETRIEVE_TYPE } from '@/types/datasets'
import Toast from '@/app/components/base/toast'
import { useModelListAndDefaultModelAndCurrentProviderAndModel } from '@/app/components/account-setting/model-provider-page/hooks'
import { ModelTypeEnum } from '@/app/components/account-setting/model-provider-page/declarations'
import type { DataSet } from '@/models/datasets'
import type { DatasetConfigs } from '@/models/debug'
import {
  getMultipleRetrievalConfig,
  getSelectedDatasetsMode,
} from '@/app/components/workflow/nodes/knowledge-retrieval/utils'
import TextButton from '@/app/components/base/button/text-button'

type ParamsConfigProps = {
  disabled?: boolean
  selectedDatasets: DataSet[]
  config: DatasetConfigs
  onChange: (config: DatasetConfigs) => void
}
const ParamsConfig = ({
  disabled,
  selectedDatasets,
  onChange: setDatasetConfigs,
  config: datasetConfigs,
}: ParamsConfigProps) => {
  const { t } = useTranslation()
  const [tempDataSetConfigs, setTempDataSetConfigs] = useState(datasetConfigs)
  // 是否打开rerankSetting
  const [rerankSettingModalOpen, setRerankSettingModalOpen] = useState(false)

  useEffect(() => {
    const {
      allEconomic,
      allHighQuality,
      allHighQualityFullTextSearch,
      allHighQualityVectorSearch,
      allExternal,
    } = getSelectedDatasetsMode(selectedDatasets)

    if (allEconomic || allHighQuality || allHighQualityFullTextSearch || allHighQualityVectorSearch || (allExternal && selectedDatasets.length === 1))
      setRerankSettingModalOpen(false)
  }, [selectedDatasets])

  useEffect(() => {
    const {
      allEconomic,
      allInternal,
      allExternal,
    } = getSelectedDatasetsMode(selectedDatasets)
    const { datasets, retrieval_model, score_threshold_enabled, ...restConfigs } = datasetConfigs
    let rerankEnable = restConfigs.reranking_enable

    if (((allInternal && allEconomic) || allExternal) && !restConfigs.reranking_model?.reranking_provider_name && rerankEnable === undefined)
      rerankEnable = false

    setTempDataSetConfigs({
      ...getMultipleRetrievalConfig({
        top_k: restConfigs.top_k,
        score_threshold: restConfigs.score_threshold,
        reranking_model: restConfigs.reranking_model && {
          provider: restConfigs.reranking_model.reranking_provider_name,
          model: restConfigs.reranking_model.reranking_model_name,
        },
        reranking_mode: restConfigs.reranking_mode,
        weights: restConfigs.weights,
        reranking_enable: rerankEnable,
      }, selectedDatasets),
      reranking_model: restConfigs.reranking_model && {
        reranking_provider_name: restConfigs.reranking_model.reranking_provider_name,
        reranking_model_name: restConfigs.reranking_model.reranking_model_name,
      },
      retrieval_model,
      score_threshold_enabled,
      datasets,
    })
  }, [selectedDatasets, datasetConfigs])

  const {
    defaultModel: rerankDefaultModel,
    currentModel: isRerankDefaultModelValid,
  } = useModelListAndDefaultModelAndCurrentProviderAndModel(ModelTypeEnum.rerank)

  const isValid = () => {
    let errMsg = ''
    if (tempDataSetConfigs.retrieval_model === RETRIEVE_TYPE.multiWay) {
      if (!tempDataSetConfigs.reranking_model?.reranking_model_name && (rerankDefaultModel && !isRerankDefaultModelValid))
        errMsg = t('dataset.notify.rerankModelRequired')
    }
    if (errMsg) {
      Toast.notify({
        type: 'error',
        message: errMsg,
      })
    }
    return !errMsg
  }
  const handleSave = () => {
    if (!isValid())
      return
    const config = { ...tempDataSetConfigs }
    if (config.retrieval_model === RETRIEVE_TYPE.multiWay && !config.reranking_model) {
      config.reranking_model = {
        reranking_provider_name: rerankDefaultModel?.provider?.provider,
        reranking_model_name: rerankDefaultModel?.model,
      } as any
    }
    setDatasetConfigs(config)
    setRerankSettingModalOpen(false)
  }

  const handleSetTempDataSetConfigs = (newDatasetConfigs: DatasetConfigs) => {
    const { datasets, retrieval_model, score_threshold_enabled, ...restConfigs } = newDatasetConfigs

    const retrievalConfig = getMultipleRetrievalConfig({
      top_k: restConfigs.top_k,
      score_threshold: restConfigs.score_threshold,
      reranking_model: restConfigs.reranking_model && {
        provider: restConfigs.reranking_model.reranking_provider_name,
        model: restConfigs.reranking_model.reranking_model_name,
      },
      reranking_mode: restConfigs.reranking_mode,
      weights: restConfigs.weights,
      reranking_enable: restConfigs.reranking_enable,
    }, selectedDatasets)

    setTempDataSetConfigs({
      ...retrievalConfig,
      reranking_model: restConfigs.reranking_model && {
        reranking_provider_name: restConfigs.reranking_model.reranking_provider_name,
        reranking_model_name: restConfigs.reranking_model.reranking_model_name,
      },
      retrieval_model,
      score_threshold_enabled,
      datasets,
    })
  }

  return (
    <>
      <TextButton
        variant='hover'
        onClick={() => {
          setRerankSettingModalOpen(true)
        }}
        disabled={disabled}
      >
        <RiEqualizer2Line className='mr-1 w-3.5 h-3.5' />
        {t('dataset.action.retrievalSettings')}
      </TextButton>
      {
        rerankSettingModalOpen && (
          <Modal
            isShow={rerankSettingModalOpen}
            onClose={() => {
              setRerankSettingModalOpen(false)
            }}
            className='sm:min-w-[528px]'
            footer={
              <>
                <Button className='mr-4' onClick={() => {
                  setRerankSettingModalOpen(false)
                }}>{t('common.operation.cancel')}</Button>
                <Button variant='primary' onClick={handleSave} >{t('common.operation.save')}</Button>
              </>
            }
          >
            <ConfigContent
              datasetConfigs={tempDataSetConfigs}
              onChange={handleSetTempDataSetConfigs}
              selectedDatasets={selectedDatasets}
            />
          </Modal>
        )
      }
    </>
  )
}
export default memo(ParamsConfig)
