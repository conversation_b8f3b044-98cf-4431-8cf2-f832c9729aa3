'use client'

import { type FC, memo, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { RiEditLine } from '@remixicon/react'
import { Divider } from 'antd'
import { formatPosition } from '../segment-index'
import s from './style.module.css'
import TableContent from './table-content'
import type { SegmentDetailModel } from '@/models/datasets'

import cn from '@/utils/classnames'
import { formatNumber } from '@/utils/format'
import Modal from '@/app/components/base/modal'
import Button from '@/app/components/base/button'
import TagInput from '@/app/components/base/tag-input'
import { TypeSquare } from '@/app/components/base/icons/src/vender/line/editor'
import { Target04 } from '@/app/components/base/icons/src/vender/line/general'
import TextButton from '@/app/components/base/button/text-button'
import { useProviderContext } from '@/context/provider-context'

type ISegmentDetailProps = {
  canEdit?: boolean
  isShow?: boolean
  isAdd?: boolean
  segInfo?: Partial<SegmentDetailModel> & {
    id: string
    headers?: string[]
    values?: string[]
  }
  onUpdate?: (segmentId: string, q: string, a: string, k: string[], v: string[], h: string[]) => void
  onCancel: () => void
}

type TableData = {
  headers: string[]
  values: string[]
}

// 分段详情-单个分段组件
const SegmentDetailComponent: FC<ISegmentDetailProps> = ({
  segInfo,
  canEdit = true,
  onUpdate,
  onCancel,
  isShow = true,
  isAdd = false,
}) => {
  const { t } = useTranslation()
  // 是否是私有知识库
  const { useXIYANRag } = useProviderContext()
  // 是否正在编辑
  const [isEditing, setIsEditing] = useState(isAdd)
  // 问题内容
  const [question, setQuestion] = useState(segInfo?.content || '')
  // 回答文本
  const [answer, setAnswer] = useState(segInfo?.answer || '')
  // 关键字
  const [keywords, setKeywords] = useState<string[]>(segInfo?.keywords || [])
  // 是否正在索引
  const isIndexing = segInfo?.index_status === 'indexing'
  // 表格数据
  const [tableData, setTableData] = useState<TableData>({
    headers: [],
    values: [],
  })

  const resetState = () => {
    if (!isAdd)
      setIsEditing(false)

    // 如果是新增模式，清空所有数据；否则使用segInfo的数据
    if (isAdd) {
      setQuestion('')
      setAnswer('')
      setKeywords([])
      setTableData({
        headers: [],
        values: [],
      })
    }
    else {
      setQuestion(segInfo?.content || '')
      setAnswer(segInfo?.answer || '')
      setKeywords(segInfo?.keywords || [])
      setTableData({
        headers: segInfo?.headers || [],
        values: segInfo?.values || [],
      })
    }
  }

  const handleCancel = () => {
    if (isAdd)
      onCancel()
    else
      resetState()
  }

  const handleSave = async () => {
    if (onUpdate) {
      await onUpdate(
        segInfo?.id || '',
        question,
        answer,
        keywords,
        tableData.values,
        tableData.headers,
      )
    }
    if (!isAdd)
      setIsEditing(false)
    else
      onCancel()
  }

  const handleDataChange = (data: TableData) => {
    if (!isEditing)
      return
    setTableData(data)
  }

  useEffect(() => {
    if (!isAdd) {
      // 直接在这里重置状态，避免依赖 resetState 函数
      setIsEditing(false)
      setQuestion(segInfo?.content || '')
      setAnswer(segInfo?.answer || '')
      setKeywords(segInfo?.keywords || [])
      setTableData({
        headers: segInfo?.headers || [],
        values: segInfo?.values || [],
      })
    }
  }, [segInfo, isAdd])

  // 当弹窗显示状态改变时，如果是新增模式且弹窗显示，则重置状态
  useEffect(() => {
    if (isShow && isAdd) {
      // 直接在这里重置状态，避免依赖 resetState 函数
      setQuestion('')
      setAnswer('')
      setKeywords([])
      setTableData({
        headers: [],
        values: [],
      })
    }
  }, [isShow, isAdd])

  const renderContent = () => {
    return (
      <TableContent
        editable={isEditing}
        onDataChange={handleDataChange}
        segInfo={segInfo}
      />
    )
  }

  return (
    <Modal
      isShow={isShow}
      onClose={onCancel}
      closable
      className="!w-[800px] !min-h-[60vh]"
      title={useXIYANRag ? <span>&nbsp;</span> : `#${formatPosition(segInfo?.position || '')}`}
      wrapperClassName="flex !flex-row"
      headerExtra={
        !isEditing
        && !isIndexing
        && canEdit
        && (isAdd || segInfo?.enabled)
        && (
          <TextButton variant="text" onClick={() => setIsEditing(true)}>
            <RiEditLine className="w-4 h-4 text-gray-G1" />
          </TextButton>
        )
      }
    >
      {renderContent()}
      <Divider className="mx-4 h-full" type="vertical"></Divider>
      <div className={s.segInfoWrapper}>
        {/* 字符数 */}
        <div className={cn(s.segInfoItem, 'mb-2')}>
          <TypeSquare className={s.commonIcon} />
          <span>{formatNumber(segInfo?.word_count as number)}</span>
          <span>{t('common.unit.characters')}</span>
        </div>
        {/* 命中次数 */}
        <div className={cn(s.segInfoItem, 'mb-4')}>
          <Target04 className={s.commonIcon} />
          <span>{formatNumber(segInfo?.hit_count as number)} </span>
          <span>{t('datasetDocuments.segment.hitCount')}</span>
        </div>
        {/* 哈希码 */}
        {!useXIYANRag && (
          <>
            <div className={s.segInfoItemTitle}>
              {t('datasetDocuments.segment.vectorHash')}
            </div>
            <div className={s.hashValue}>{segInfo?.index_node_hash}</div>
            <Divider type="horizontal" className="!my-4"></Divider>
          </>
        )}
        {/* 标签 */}
        {!useXIYANRag && (
          <>
            <div className={s.segInfoItemTitle}>
              {t('datasetDocuments.segment.keywords')}
            </div>
            <div className={s.keywordWrapper}>
              <TagInput
                className="!min-w-[160px]"
                items={keywords}
                onChange={newKeywords => setKeywords(newKeywords)}
                disableAdd={!isEditing}
                canRemove={isEditing && keywords.length !== 1}
              />
            </div>
          </>
        )}
        {isEditing && (
          <div className="flex items-center justify-end">
            <Button
              size={'small'}
              className="!rounded !w-[72px] !mr-4"
              variant={'secondary-accent'}
              onClick={handleCancel}
            >
              {t('common.operation.cancel')}
            </Button>
            <Button
              size={'small'}
              variant="primary"
              className="!rounded !w-[72px]"
              onClick={handleSave}
            >
              {t('common.operation.save')}
            </Button>
          </div>
        )}
      </div>
    </Modal>
  )
}
export default memo(SegmentDetailComponent)
