import { useState } from 'react'
import { Checkbox } from 'antd'
import Select from '@/app/components/base/select/new-index'

type OptionType = {
  label: string
  value: any
}
type MultiplSelectProps = {
  defaultValue?: string[]
  placeholder: string
  options: Array<OptionType>
  disabledValue?: string[]
  maxCount?: number
  maxTagCount: number | 'responsive'
  className?: string
  size?: 'large' | 'middle' | 'small'
  showSearch: boolean
  onChange?: (value: string[], option: any) => void
}

const MultiplSelect = ({ defaultValue, placeholder, options, onChange, disabledValue, className, size, maxCount, maxTagCount, showSearch }: MultiplSelectProps) => {
  // 下拉弹窗是否展开
  const [value, setValue] = useState(defaultValue || [])
  // 变更下拉弹窗
  const changeSelect = (value: string[], option: any) => {
    setValue(value)
    if (onChange)
      onChange(value, option)
  }
  // 单项节点渲染
  const OptionRender = (item: { data: OptionType }) => {
    const select = value.includes(item.data.value)
    const disabled = disabledValue?.includes(item.data.value)
    return (
      <>
        <Checkbox className='mr-1.5' checked={select} disabled={disabled}></Checkbox>
        <span>{item.data.label}</span>
      </>
    )
  }

  return (
    <Select
      showSearch={false}
      placeholder={placeholder}
      mode='multiple'
      size={size}
      maxCount={maxCount}
      maxTagCount={maxTagCount}
      className={className}
      options={options}
      value={value}
      onChange={changeSelect}
      optionRender={OptionRender as any}
    ></Select>

  )
}

export default MultiplSelect
