import React from 'react'
import Overview from '@/app/components/app/overview'
import style from '@/app/components/app/styles/index.module.css'
import Scrollbar from '@/app/components/base/scrollbar'

export type IDevelopProps = {
  params: { appId: string }
}

const OverviewPage = async ({
  params: { appId },
}: IDevelopProps) => {
  return (
    <div className={style.wrap}>
      <div className={style['left-part']}>
        <Scrollbar className={style['left-content']}>
          <Overview appId={appId} />
        </Scrollbar>
      </div>
    </div>
  )
}

export default OverviewPage
