const translation = {
  operation: {
    publish: 'Publish',
    unPublish: 'Unpublish',
    create: 'Create',
    confirm: 'Confirm',
    cancel: 'Cancel',
    clear: 'Clear',
    save: 'Save',
    edit: 'Edit',
    add: 'Add',
    refresh: 'Refresh',
    reset: 'Reset',
    retry: 'Retry',
    search: 'Search',
    see: 'View',
    change: 'Change',
    remove: 'Remove',
    send: 'Send',
    copy: 'Copy',
    submit: 'Submit',
    abandon: 'Abandon',
    download: 'Download',
    delete: 'Delete',
    settings: 'Settings',
    ok: 'OK',
    log: 'Log',
    params: 'Parameter settings',
    duplicate: 'Duplicate',
    rename: 'Rename',
    audioSourceUnavailable: 'Audio source unavailable',
    copyImage: 'Copy image',
    zoomOut: 'Zoom out',
    zoomIn: 'Zoom in',
    openInNewTab: 'Open in new tab',
    reUpload: 'Re-upload',
    pull: 'Taken off the shelves',
    use: 'use',
    sync: 'synchronous',
    configure: 'disposition',
    detail: 'detail',
    reProcess: 'rehandle',
    run: 'run',
    reProcess: 'rehandle',
    detail: 'detail',
    agree: 'Like',
    disagree: 'Dislike',
    preview: 'preview',
  },
  placeholder: {
    input: 'Please enter',
    select: 'Please select',
    search: 'Search',
    endDay: 'End date',
    startDay: 'Start date',
  },
  voice: {
    language: {
      zhHans: 'Chinese',
      zhHant: 'Traditional Chinese',
      enUS: 'English',
      deDE: 'German',
      frFR: 'French',
      esES: 'Spanish',
      itIT: 'Italian',
      thTH: 'Thai',
      idID: 'Indonesian',
      jaJP: 'Japanese',
      koKR: 'Korean',
      ptBR: 'Portuguese',
      ruRU: 'Russian',
      ukUA: 'Ukrainian',
      viVN: 'Vietnamese',
      plPL: 'Polish',
      roRO: 'Romanian',
      hiIN: 'Hindi',
      trTR: 'Turkish',
      faIR: 'Persian',
    },
  },
  dateFormat: {
    dateTime: 'YYYY-MM-DD HH:mm:ss',
  },
  unit: {
    char: 'characters',
    characters: 'characters',
    paragraphs: 'paragraphs',
  },
  actionMsg: {
    addedSuccessfully: 'Added successfully',
    saveSuccessfully: 'Saved successfully',
    modifiedSuccessfully: 'Modified successfully',
    deleteSuccessfully: 'Deleted successfully',
    modifiedUnsuccessfully: 'Modification failed',
    copySuccessfully: 'Copied successfully',
    volumeAloudTip: 'Volume is too loud, it is recommended to lower it.',
    updateSuccess: 'The update was successful',
    unPublishSuccess: 'The removal was successful',
    create: 'Created',
    success: 'succeed',
    saved: 'Saved',
    actionSuccess: 'The operation was successful',
    remove: 'Removed',
    disableSuccessfully: 'The disabling was successful',
    enableSuccessfully: 'Enabled successfully',
    reprocessSuccessfully: 'Reprocessing successful',
    createSuccessfully: 'The creation is successful',
    accountChange: 'Your account information has been changed, please log in again.',
    coypSuccess: 'Copy successful',
    copyTodownload: 'Cannot copy, has been automatically downloaded.',
    generatedUnsuccessfully: 'The build failed',
    generatedSuccessfully: 'Regenerated',
  },
  voiceInput: {
    speaking: 'Now speaking...',
    converting: 'Converting to text...',
    notAllow: 'Microphone not authorized',
    error: {
      permissionDenied: 'The device must be enabled before it can be used',
      asrNoInternet: 'The network is abnormal, we recommend that you try again',
      ttsNoInternet: 'Network issues cannot be read, it is recommended to replay',
      noAppId: 'You cannot use this function if you do not obtain an app ID',
      asrError: 'The speech-to-text is abnormal, it is recommended to try again',
      ttsError: 'Replay is recommended',
      unknown: 'Exceptions suggest a retry',
    },
    cancelVoiceInput: 'Cancel voice input',
    inputConvertingStop: 'Click the input box to confirm',
    voiceInput: 'Voice input',
    hasImg: 'The reply contains an image, which can be viewed later',
    hangup: 'End the call',
    voiceConversation: 'Voice calls',
    thinking: 'Thinking...',
    connecting: 'Connecting...',
    voicePlay: 'Play',
    inputConverting: 'In voice input,',
    inCall: 'On the call...',
  },
  chat: {
    renameConversation: 'Rename Conversation',
    conversationName: 'Conversation Name',
    conversationNamePlaceholder: 'Please enter a conversation name',
    conversationNameCanNotEmpty: 'Conversation name is required',
    citation: {
      title: 'Knowledge Source',
      linkToDataset: 'Jump to Knowledge Base',
      characters: 'Characters:',
      hitCount: 'Hit Count:',
      vectorHash: 'Vector Hash:',
      hitScore: 'Hit Score:',
    },
    inputPlaceholder: 'Enter a question, press Enter to send',
    thinking: 'Thinking deeply...',
    thought: 'Deeply thought',
    tip: 'Content generated by AI, cannot be guaranteed to be true and accurate, for reference only',
    newChat: 'New Chat',
    stopChat: 'Stop Chat',
    newChatLimit: 'Please stop the current chat first',
    market: {
      noDatasetTooltip: 'This app contains private documents and tools that cannot be copied. You need to add them manually.',
      datasetTooltip: 'When creating a similar app, the custom toolbar and knowledge base section will have new content added.',
      datasetTip: 'When creating a similar app, the knowledge base section will have new knowledge bases added.',
      toolTip: 'When creating a similar app, the tool section will have new tools added.',
      createAppBtn: 'Create Similar App',
      introLabel: 'App Introduction',
      usage: 'usage',
      createSuccessfully: 'Create successfully',
      createFailed: 'Create failed',
    },
    voiceInput: 'Voice input',
    cancelVoiceConversation: 'End the conversation',
    voiceConversation: 'Voice conversations',
    cancelVoiceInput: 'Cancel voice input',
    resetChat: 'Reset the conversation',
    startChat: 'Open a conversation',
    checkFailText: 'Sorry, due to the abnormal content of the reply, it will not be displayed at this time',
  },
  promptEditor: {
    placeholder: '',
    context: {
      item: {
        title: 'Context',
        desc: 'Insert context template',
      },
      modal: {
        title: 'There are {{num}} knowledge bases in the context',
        add: 'Add Context',
        footer: 'You can manage the context in the "Context" section below.',
      },
    },
    history: {
      item: {
        title: 'Conversation History',
        desc: 'Insert history message template',
      },
      modal: {
        title: 'Example',
        user: 'Hello',
        assistant: 'Hello! How can I help you today?',
        edit: 'Edit conversation role name',
      },
    },
    variable: {
      item: {
        title: 'Variables & External Tools',
        desc: 'Insert variables and external tools',
      },
      outputToolDisabledItem: {
        title: 'Variables',
        desc: 'Insert variables',
      },
      modal: {
        add: 'Add New Variable',
        addTool: 'Add Tool',
      },
    },
    query: {
      item: {
        title: 'Query Content',
        desc: 'Insert user query template',
      },
    },
  },
  imageUploader: {
    uploadFromComputer: 'Upload from computer',
    uploadFromComputerReadError: 'Image read failed, please reselect.',
    uploadFromComputerUploadError: 'Image upload failed, please re-upload.',
    uploadFromComputerLimit: 'Uploaded image cannot exceed {{size}} MB',
    pasteImageLink: 'Paste image link',
    pasteImageLinkInputPlaceholder: 'Paste the image link here',
    pasteImageLinkInvalid: 'Invalid image link',
    imageUpload: 'Image Upload',
  },
  fileUploader: {
    uploadFromComputer: 'Upload from computer',
    pasteFileLink: 'Paste file link',
    pasteFileLinkInputPlaceholder: 'Enter file link',
    uploadFromComputerReadError: 'File read failed, please reselect.',
    uploadFromComputerUploadError: 'File upload failed, please re-upload.',
    uploadFromComputerLimit: 'Uploaded file cannot exceed {{size}}',
    pasteFileLinkInvalid: 'Invalid file link',
    fileExtensionNotSupport: 'File extension not supported',
    img: 'Image',
    link: 'Link',
    file: 'File',
    validation: {
      count: 'Multiple files are not supported',
      filesNumber: 'Bulk upload limit {{filesNumber}}.',
      typeError: 'File types are not supported',
      size: 'The file size is too large to exceed {{size}}MB',
    },
    button: 'Drag and drop the file here, or',
    browse: 'Select the file',
    tip2: 'Each file should be no more than {{size}}MB, and a maximum of {{filesNumber}} files can be uploaded at the same time',
    tip: 'Support {{supportTypes}}',
  },
  fork: {
    datasetTip: 'The original app contains private knowledge base documents (as follows) that cannot be copied. You can manually add other documents.',
    toolTip: 'The original app contains private tools (as follows) that cannot be copied. You can manually add other tools.',
    singleToolTip: 'The original app contains private tools that cannot be copied. You can manually add other tools.',
    tip: 'Hello, the platform authorization is valid until {{date}}, in order to ensure your use, please contact the service provider for re-authorization',
    inviteTip: 'Note: The platform is currently in the invitation stage of closed beta, if you want to get an invitation code, please contact the official staff.',
  },
  validate: {
    required: 'Please enter ${label}',
    whitespace: '${label} cannot be empty',
    string: {
      min: '${label} length should be at least ${min} characters',
      max: '${label} length should be less than ${max} characters',
      range: '${label} length should be between ${min} and ${max} characters',
    },
    pattern: '${label} does not meet the specifications',
    rangeError: '{{name}} must be between {{min}}~{{max}} characters',
    emptyError: '{{name}} cannot be a null character',
    fieldRequired: '{{field}} is required',
    urlError: 'The URL should start with http:// or https://',
  },
  db: {
    updateModelConfigFail: 'Failed to update model configuration data',
    addModelConfigFail: 'Failed to add model configuration data',
    createModelConfigFail: 'Failed to create model configuration database',
    deleteModelConfigFail: 'Failed to delete model configuration data',
    connectFail: 'Database connection exception',
  },
  status: {
    active: 'available',
    authorized: 'Authorized',
    disconnected: 'Unbound',
    uploading: 'Upaging',
    notCanBeAdded: 'Not addable',
    canBeAdded: 'Additive',
    notAuthorized: 'Unauthorized',
    inactive: 'not available',
    added: 'Added',
    connected: 'Bind',
    no: 'not',
    yes: 'be',
    disabled: 'Disabled',
    deleted: 'Deleted',
    enabled: 'Enabled',
  },
  component: {
    empty: {
      text: 'No data yet',
    },
    videoPlayer: {
      playError: 'Playback failed',
      playErrorDesc: 'The video/audio file format is not compatible with the player',
      downloadError: 'Unable to open video file: {{url}}',
    },
    avatar: {
      sizeTip: 'The size of the avatar should not exceed 1MB!',
      typeTip: 'Avatars only support jpg or png format',
    },
    image: {
      downloadError: 'Unable to open image file: {{url}}',
    },
  },
  codeVerify: {
    getSms: 'Get',
    remaingTimeText: '{{time}} seconds later',
    failed: 'Validation failed',
    clickPicture: 'Click {{character}}',
    waitSms: 'The verification code has been sent and is valid for 5 minutes',
  },
  uploader: {
    validation: {
      count: 'Multiple files are not supported',
      size: 'The file size is too large to exceed {{size}}MB',
      typeError: 'File types are not supported',
      filesNumber: 'Bulk upload limit {{filesNumber}}.',
    },
    tip2: 'Each file should be no more than {{size}}MB, and a maximum of {{filesNumber}} files can be uploaded at the same time',
    button: 'Drag and drop the file here, or',
    browse: 'Select the file',
    title: 'Upload a text file',
    tip: 'Support {{supportTypes}}',
  },
  echarts: {
    jsonFormatError: 'JSON is malformed and the chart cannot be processed',
    jsonNotArray: 'The JSON format is incorrect, please enter an array of charts',
    echartsError: 'Chart rendering failed',
  },
  info: {
    result: 'outcome',
    file: 'data',
    operation: 'operate',
    fieldValue: 'Field values',
    all: 'whole',
    fieldName: 'The name of the field',
    row: '{{row}} line',
    rowCol: '{{row}} row {{col}} column',
  },
  echarts: {
    jsonFormatError: 'JSON format error, unable to process the chart.',
    echartsError: 'Chart rendering failed',
    jsonNotArray: 'The JSON format is incorrect, please enter an array of charts',
  },
}

export default translation
