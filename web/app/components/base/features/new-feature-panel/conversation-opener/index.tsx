import React, { useCallback, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import produce from 'immer'
import { ReactSortable } from 'react-sortablejs'
import { Input } from 'antd'

import { useFeatures, useFeaturesStore } from '../../hooks'
import type { OnFeaturesChange } from '../../types'
import { FeatureEnum } from '../../types'
import style from '@/app/components/app/configuration/styles/style.module.scss'
// 基础组件
import { Delete, Draggable } from '@/app/components/base/icons/src/vender/line/general'
import FeatureCollapse from '@/app/components/base/features/feature-collapse'
import TextButton from '@/app/components/base/button/text-button'
import { Add } from '@/app/components/base/icons/src/vender/solid/general'

const labelStyle = 'text-[12px] leading-H1 text-gray-G1 mb-2'
const MAX_QUESTION_NUM = 5
const TextArea = Input.TextArea

type Props = {
  disabled?: boolean
  onChange?: OnFeaturesChange
}

const ConversationOpener = ({
  disabled,
  onChange,
}: Props) => {
  const { t } = useTranslation()
  const opening = useFeatures(s => s.features.opening)
  const inWorkflow = useFeatures(s => s.inWorkflow)
  const featuresStore = useFeaturesStore()
  const { features, setFeatures } = featuresStore!.getState()
  // 预设问题
  const [tempSuggestedQuestions, setTempSuggestedQuestions] = useState(opening?.suggested_questions || [])
  // 开场白
  const [tempValue, setTempValue] = useState(opening?.opening_statement || '')

  // 数据保存回调
  const handleSave = useCallback(() => {
    const newOpening = produce(opening, (draft) => {
      if (draft) {
        draft.opening_statement = tempValue
        draft.suggested_questions = tempSuggestedQuestions.filter(question => question !== '')
      }
    })
    const newFeatures = produce(features, (draft) => {
      draft.opening = newOpening
    })
    setFeatures(newFeatures)
    if (onChange)
      onChange(newFeatures)
  }, [opening, features, setFeatures, onChange, tempValue, tempSuggestedQuestions])

  // 删除预设问题
  const handleDelete = (index: number) => {
    const newFeatures = produce(features, (draft) => {
      draft.opening = {
        ...draft.opening,
        suggested_questions: draft.opening?.suggested_questions?.filter((_, i) => i !== index),
      }
    })
    setFeatures(newFeatures)
    if (onChange)
      onChange(newFeatures)
  }
  // 开启/关闭对话开场白
  const handleChange = useCallback((type: FeatureEnum, enabled: boolean) => {
    const {
      features,
      setFeatures,
    } = featuresStore!.getState()

    const newFeatures = produce(features, (draft) => {
      draft[type] = {
        ...draft[type],
        enabled,
      }
    })
    setFeatures(newFeatures)
    if (onChange)
      onChange(newFeatures)
  }, [featuresStore, onChange])
  // 填写新增问题内容后才能继续新增
  const handleAdd = () => {
    const emptyIndex = tempSuggestedQuestions.findIndex(question => question === '')
    if (emptyIndex > -1)
      return
    setTempSuggestedQuestions([...tempSuggestedQuestions, ''])
  }
  // 排序开场白问题
  const handleSort = useCallback((list: { id: number; name: string }[]) => {
    const newFeatures = produce(features, (draft) => {
      draft.opening = {
        ...draft.opening,
        suggested_questions: list.map(item => item.name),
      }
    })
    setFeatures(newFeatures)
    if (onChange)
      onChange(newFeatures)
  }, [features, onChange, setFeatures])
  // 变更预设问题
  const handleChangeQuestion = (index: number, value: string) => {
    const newFeatures = produce(features, (draft) => {
      draft.opening = {
        ...draft.opening,
        suggested_questions: tempSuggestedQuestions.map((item, i) => {
          if (index === i)
            return value
          return item
        }),
      }
    })
    setFeatures(newFeatures)
    if (onChange)
      onChange(newFeatures)
  }

  useEffect(() => {
    setTempValue(opening?.opening_statement || '')
  }, [opening?.opening_statement])
  useEffect(() => {
    setTempSuggestedQuestions(opening?.suggested_questions || [])
  }, [opening?.suggested_questions])

  // 开场白引导问题
  const renderQuestions = () => {
    return (
      <div>
        <div className='flex justify-between items-center'>
          <div className={labelStyle}>{t('appDebug.openingStatement.label2')}{tempSuggestedQuestions.length}/{MAX_QUESTION_NUM}</div>
          {tempSuggestedQuestions.length < MAX_QUESTION_NUM && (
            <TextButton variant='primary' onClick={handleAdd}><Add/></TextButton>
          )}
        </div>
        <div>
          <ReactSortable
            className="space-y-2"
            list={tempSuggestedQuestions.map((name, index) => {
              return {
                id: index,
                name,
              }
            })}
            setList={handleSort}
            handle='.handle'
            ghostClass={style.InputDragging}
            animation={150}
          >
            {tempSuggestedQuestions.map((question, index) => {
              return (
                <div className='flex gap-1 items-center' key={index}>
                  <TextButton className='handle w-6 h-6 !cursor-grab' variant='hover' onClick={() => handleDelete(index)}>
                    <Draggable className='w-4 h-4' />
                  </TextButton>
                  <Input
                    defaultValue={question || ''}
                    className={'hover:border-primary-P2 !bg-transparent'}
                    onChange={e => handleChangeQuestion(index, e.target.value)}
                    placeholder={t('appDebug.openingStatement.placeholder2') || ''}
                    suffix={(
                      <TextButton variant='text' onClick={() => handleDelete(index)}>
                        <Delete className='w-4 h-4' />
                      </TextButton>
                    )}
                  />
                </div>
              )
            })}</ReactSortable>
        </div>
      </div>
    )
  }

  if (!opening)
    return

  return (
    <FeatureCollapse
      inWorkflow={inWorkflow}
      title={t('appDebug.feature.conversationOpener.title')}
      tooltip={t('appDebug.feature.conversationOpener.description')}
      value={!!opening?.enabled}
      onChange={state => handleChange(FeatureEnum.opening, state)}
      disabled={disabled}
    >
      {!!opening?.enabled && (
        <>
          <div className='flex flex-col gap-[10px]'>
            <div>
              <div className={labelStyle}>{t('appDebug.openingStatement.label1')}</div>
              <TextArea
                value={tempValue}
                rows={3}
                onChange={e => setTempValue(e.target.value)}
                onBlur={() => handleSave()}
                className="w-full px-3 py-[10px] text-gray-G1 text-[14px] leading-H3 !bg-transparent"
                placeholder={t('appDebug.openingStatement.placeholder') as string}
              />
            </div>
            {renderQuestions()}
          </div>
          {/* {isShowConfirmAddVar && (
            <ConfirmAddVar
              varNameArr={notIncludeKeys}
              onConfirm={autoAddVar}
              onCancel={cancelAutoAddVar}
              onHide={hideConfirmAddVar}
            />
          )} */}
        </>
      )}
    </FeatureCollapse>
  )
}

export default ConversationOpener
