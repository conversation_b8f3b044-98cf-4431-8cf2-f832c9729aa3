'use client'
import React, { ReactNode } from 'react';
import s from '../styles/style.module.scss'
import { Table, Card } from 'antd';
import type { TableProps } from 'antd';

// 数据统计表格
interface StatisticsTableType<T> extends TableProps<T> {
  tableTitle?: ReactNode; // 表格标题
  titleExtra?: ReactNode; // 标题右侧额外内容
  tableExtra?: ReactNode; // 表格右上角额外内容             
  headerStyle?: React.CSSProperties; // 头部样式
  bodyStyle?: React.CSSProperties; // 表格区域样式
  showCard?: boolean; // 是否使用Card包裹                
}

const StatisticsTable = <T extends object>({
  tableTitle,
  titleExtra,
  tableExtra,
  headerStyle = {},
  bodyStyle = {},
  showCard = true,
  pagination=false,
  ...tableProps
}: StatisticsTableType<T>) => {
  // 表格头部render
  const renderHeader = () => (
    <div className={s['statistics-table-header']} style={headerStyle}>
      <div className={s['statistics-table-titleExtra']}>
        {typeof tableTitle === 'string' ? <div className={s['statistics-table-title']}>{tableTitle}</div> : tableTitle}
        {titleExtra && (
          <div className={s['statistics-table-extra']}>
            {titleExtra}
          </div>
        )}
      </div>
      
      {tableExtra && (
        <div>
          {tableExtra}
        </div>
      )}
    </div>
  );

  // 内容渲染
  const content = (
    <div className={s['statistics-table']} style={bodyStyle}>
      {renderHeader()}
      <Table<T> {...tableProps} pagination={pagination} />
    </div>
  );

  return showCard ? (
    <div className={s['statistics-table-card']}>
      {content}
    </div>
  ) : content;
};

export default StatisticsTable;
