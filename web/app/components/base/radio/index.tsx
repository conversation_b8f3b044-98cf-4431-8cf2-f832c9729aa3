import type React from 'react'
import type { IRadioProps } from './component/radio'
import RadioComps from './component/radio'
import Group from './component/group'
import Button from './component/button'

type CompoundedComponent = {
  Group: typeof Group
  Button: typeof Button
} & React.ForwardRefExoticComponent<IRadioProps & React.RefAttributes<HTMLElement>>

const Radio = RadioComps as CompoundedComponent
/**
 * Radio 组件出现一般是以一组的形式出现
 */
Radio.Group = Group
Radio.Button = Button

export default Radio
