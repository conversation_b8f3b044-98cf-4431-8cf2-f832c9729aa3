const translation = {
  title: '应用',
  appMarket: '应用广场',
  newApp: {
    basic: '自主规划',
    advanced: '工作流编排',
    longDoc: '范文模板',
  },
  appMenus: {
    overview: '监测',
    promptEng: '编排',
    apiAccess: '访问 API',
    logAndAnn: '回流',
    logs: '日志',
    workflowConfig: '工作流配置',
    publish: '发布',
  },
  // 应用类型
  types: {
    all: '全部',
    chatbot: '工作流',
    agent: '自主规划',
    workflow: '工作流',
    completion: '文本生成',
    emptyApp: '应用',
  },
  // 动作
  action: {
    publish: '发布',
    updatePublish: '更新发布',
    createApp: '创建应用',
    export: '导出 DSL',
    startByType: '创建{{type}}',
    experience: '立即体验',
    reUpload: '重新上传',
    configure: '配置',
    createTeam: '创建团队',
    personalSpace: '个人空间',
    person: '个人',
    team: '团队',
    downQRcode: '下载二维码',
    previewAndDebugger: '预览与调试',
    experienceApp: '体验应用',
    appDetail: '应用详情',
    publishApp: '上线应用',
    unPublish: '下线应用',
    reject: '拒绝申请',
    detail: '详情',
    publishMarket: '发布到应用广场',
  },
  // 检测
  tracing: {
    title: '追踪应用性能',
    description: '配置第三方 LLMOps 提供商并跟踪应用程序性能。',
    config: '配置',
    view: '查看',
    collapse: '折叠',
    expand: '展开',
    tracing: '追踪',
    disabled: '已禁用',
    disabledTip: '请先配置提供商',
    enabled: '启用中',
    tracingDescription: '捕获应用程序执行的完整上下文，包括 LLM 调用、上下文、提示、HTTP 请求等，发送到第三方跟踪平台。',
    configProviderTitle: {
      configured: '已配置',
      notConfigured: '配置提供商以启用追踪',
      moreProvider: '更多提供商',
    },
    langsmith: {
      title: 'LangSmith',
      description: '一个全方位的开发者平台，适用于 LLM 驱动应用程序生命周期的每个步骤。',
    },
    langfuse: {
      title: 'Langfuse',
      description: '跟踪、评估、提示管理和指标，以调试和改进您的 LLM 应用程序。',
    },
    inUse: '使用中',
    configProvider: {
      title: '配置 ',
      placeholder: '输入你的{{key}}',
      project: '项目',
      publicKey: '公钥',
      secretKey: '密钥',
      viewDocsLink: '查看 {{key}} 的文档',
      removeConfirmTitle: '删除 {{key}} 配置?',
      removeConfirmContent: '当前配置正在使用中，删除它将关闭追踪功能。',
    },
  },
  // 状态
  status: {
    published: '已发布',
    unPublish: '未发布',
    appUnknownError: '应用不可用',
  },
  // 输入提示
  placeholder: {
    defaultSelect: '请选择',
    defaultInput: '请输入',
    appName: '请输入应用名称',
    appDescription: '请输入一段应用的描述，此描述对智能体效果无影响。',
    chatbotDescription: '使用工作流进行智能体应用的编排，提供更多的自定义能力，可以处理复杂场景行为，适合有经验的用户。',
    agentDescription: '通过界面配置进行智能体应用的编排，快速便捷搭建应用，适合逻辑简单的场景。',
    templateDescription: '基于长文本创作，可以快速创建应用，完成长文本生成。',
    failMessagePlaceholder: '请输入原因',
    appCategoriesPlaceholder: '请选择场景标签',
  },
  // 权限
  appPermission: {
    copy: '公开配置',
    view: '私密配置',
  },
  // 弹窗标题
  modalTitle: {
    publish: '发布',
    publishMarket: '发布至应用广场',
    editAppTitle: '应用信息编辑',
    duplicateTitle: '复制应用',
    deleteAppConfirmTitle: '要删除应用吗？',
    publishDetail: '发布详情',
  },
  // 信息
  info: {
    acl: '应用能否支持创建同款',
    category: '类型',
    createAt: '创建时间',
    updateAt: '更新时间',
    publishAt: '发布时间',
    name: '应用名称',
    description: '应用描述',
    status: '状态',
    action: '操作',
    appName: '应用名称',
    appHead: '应用头像',
    appPermission: '应用权限',
    appCategories: '应用标签',
    appDescription: '应用描述',
    appCreateType: '应用创建方式',
    appMarket: '应用广场',
    publicConfig: '公开配置',
  },
  // 通知，公告
  notify: {
    appCreated: '应用已创建',
    appCreateFailed: '应用创建失败',
    successPublishApp: '应用发布成功',
    errorPublishApp: '应用发布失败',
    unPublishSuccess: '取消应用发布成功',
    unPublishError: '取消应用发布失败',
    editDone: '应用信息已更新',
    editFailed: '更新应用信息失败',
    appDeleted: '应用已删除',
    appDeleteFailed: '应用删除失败',
    exportFailed: '导出 DSL 失败',
    // 公告
    noApp: '没有可用的应用',
    nameNotEmpty: '名称不能为空',
    deleteAppConfirmContent: '删除后，如需再次使用，请重新创建',
    appFull: '最多可创建{{num}}个应用，请删除后重试。',
    multipleModelPublishTip: '当下处于多模型对比状态，不可发布，选择最优模型后方可发布',
    shareTip: '我在{{name}}搭建了一个智能体，欢迎扫码体验！',
  },
  // 排序
  sort: {
    hot: '最受欢迎',
    new: '最新发布',
  },
  // 裁剪器
  cropper: {
    title: '会话背景图',
    upload: '上传图片',
    uploadTip: '点击上传图片或将图片拖拽到此处',
    typeTip: '支持上传一张高度不小于640px的图，图片类型为png、jpg、jpeg',
    reUploadTip: '支持鼠标滚动缩放调整图片或拖拽调整位置',
    widePreview: '宽屏展示',
    narrowPreview: '窄屏展示',
    fontColor: '字体颜色',
    fontColorBlack: '深色',
    fontColorWhite: '浅色',
    typeErrorMessage: '文件类型需为png、jpg、jpeg',
    heightErrorMessage: '请上传一张高度不小于 640px 的背景图',
    sizeErrorMessage: '图片内存大小需小于 10mb',
  },
  mermaid: {
    handDrawn: '手绘',
    classic: '经典',
  },
  // 发布
  publish: {
    config: '发布配置',
    history: '发布历史',
    method: '发布渠道',
    messagePrefix: '原因：',
    methodOption: {
      embed: '嵌入网站',
      market: '应用广场',
      web: '前端页面',
      api: '后端API',
    },
    copy: {
      forkTag: '创建同款时导入',
      copyTool: '自定义工具同步情况',
      copyDataset: '私人知识库同步情况',
      tool: '工具同步',
      noTool: '工具不同步',
      dataset: '知识库同步',
      noDataset: '知识库不同步',
    },
    embed: {
      embeddingMethod: '方式选择',
      iframe: '居中嵌入',
      scripts: '右侧嵌入',
    },
    status: {
      pending: '待发布',
      review: '审核中',
      online: '已上架',
      offline: '已下架',
      failed: '未通过',
      on: '有',
      off: '无',
    },
    tip: {
      publishMethodTip: '至少需要选择一项发布渠道',
      marketTip: '发布到应用广场未进行配置',
      embeddingTip: '嵌入网站形式未选择',
      marketConfigTip: '当您支持用户创建同款且支持用户同步复制您搭建的自定义工具和私人知识库时，您上传的文件（包含切片）和搭建的工具会被用户使用。',
      unPublishTip: '选择应用下架后，已创建同款至应用板块的应用不受影响。仅下架已上传至应用广场的应用，是否下架?',
    },
    reConfirm: {
      title: '更新发布',
      content: '渠道首次发布以最新版本为准。\n应用广场更新发布会覆盖原版本且不可恢复，但导入到应用板块的应用仍会展示原版本。\n是否要更新发布?',
    },
  },
}

export default translation
