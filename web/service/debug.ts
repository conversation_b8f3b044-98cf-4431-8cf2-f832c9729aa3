import { get, post, ssePost } from './base'
import type {
  IOnCompleted,
  IOnData,
  IOnError,
  IOnFile,
  IOnIterationFinished,
  IOnIterationNext,
  IOnIterationStarted,
  IOnMessageEnd,
  IOnMessageReplace,
  IOnNodeFinished,
  IOnNodeStarted,
  IOnThought,
  IOnVoiceAnswerSafetyFence,
  IOnVoiceChannelBuild,
  IOnVoiceQuery,
  IOnVoiceQuerySafetyFence,
  IOnVoiceTimeout,
  IOnWorkflowFinished,
  IOnWorkflowStarted,
} from './base'

import type { ChatPromptConfig, CompletionPromptConfig } from '@/models/debug'
import type { ModelModeType } from '@/types/model'
import type { ModelParameterRule } from '@/app/components/account-setting/model-provider-page/declarations'
export type AutomaticRes = {
  prompt: string
  variables: string[]
  opening_statement: string
  error?: string
}

export const sendChatMessage = async (appId: string, body: Record<string, any>, { onData, onCompleted, onThought, onFile, onError, getAbortController, onMessageEnd, onMessageReplace }: {
  onData: IOnData
  onCompleted: IOnCompleted
  onFile: IOnFile
  onThought: IOnThought
  onMessageEnd: IOnMessageEnd
  onMessageReplace: IOnMessageReplace
  onError: IOnError
  getAbortController?: (abortController: AbortController) => void
}) => {
  return ssePost(`apps/${appId}/chat-messages`, {
    body: {
      ...body,
      response_mode: 'streaming',
    },
  }, { onData, onCompleted, onThought, onFile, onError, getAbortController, onMessageEnd, onMessageReplace })
}

export function getVoiceUrl(url: string, isPublicAPI: boolean, appId: string) {
  return isPublicAPI ? url : `apps/${appId}/${url.startsWith('/') ? url.slice(1) : url}`
}

export const sendVoiceChatMessage = async (
  {
    onVoiceChannelBuild,
    onVoiceQuery,
    onVoiceAnswer,
    getAbortController,
    onThought,
    onMessageEnd,
    onWorkflowStarted,
    onWorkflowFinished,
    onNodeStarted,
    onNodeFinished,
    onIterationStart,
    onIterationNext,
    onIterationFinish,
    onVoiceTimeout,
    onError,
    isPublicAPI,
    onVoiceAnswerSafetyFence,
    onVoiceQuerySafetyFence,
  }:
  {
    onVoiceChannelBuild: IOnVoiceChannelBuild
    onVoiceQuery: IOnVoiceQuery
    onVoiceAnswer: IOnData
    getAbortController?: (abortController: AbortController) => void
    onThought: IOnThought
    onMessageEnd: IOnMessageEnd
    onWorkflowStarted?: IOnWorkflowStarted
    onWorkflowFinished?: IOnWorkflowFinished
    onNodeStarted?: IOnNodeStarted
    onNodeFinished?: IOnNodeFinished
    onIterationStart?: IOnIterationStarted
    onIterationNext?: IOnIterationNext
    onIterationFinish?: IOnIterationFinished
    onVoiceTimeout: IOnVoiceTimeout
    onError?: IOnError
    isPublicAPI: boolean
    onVoiceAnswerSafetyFence?: IOnVoiceAnswerSafetyFence
    onVoiceQuerySafetyFence?: IOnVoiceQuerySafetyFence
  }, appId: string, body?: Record<string, any>) => {
  return ssePost(getVoiceUrl('voice-chat-messages', isPublicAPI, appId), {
    body: {
      ...body,
    },
  },
  {
    onVoiceChannelBuild,
    onVoiceQuery,
    onVoiceAnswer,
    onThought,
    onMessageEnd,
    getAbortController,
    onWorkflowStarted,
    onWorkflowFinished,
    onNodeStarted,
    onNodeFinished,
    onIterationStart,
    onIterationNext,
    onIterationFinish,
    onVoiceTimeout,
    onError,
    isPublicAPI,
    onVoiceQuerySafetyFence,
    onVoiceAnswerSafetyFence,
  })
}

export const stopChatMessageResponding = async (appId: string, taskId: string) => {
  return post(`apps/${appId}/chat-messages/${taskId}/stop`)
}

export const sendCompletionMessage = async (appId: string, body: Record<string, any>, { onData, onCompleted, onError, onMessageReplace }: {
  onData: IOnData
  onCompleted: IOnCompleted
  onError: IOnError
  onMessageReplace: IOnMessageReplace
}) => {
  return ssePost(`apps/${appId}/completion-messages`, {
    body: {
      ...body,
      response_mode: 'streaming',
    },
  }, { onData, onCompleted, onError, onMessageReplace })
}

export const fetchSuggestedQuestions = (appId: string, messageId: string, getAbortController?: any) => {
  return get(
    `apps/${appId}/chat-messages/${messageId}/suggested-questions`,
    {},
    {
      getAbortController,
    },
  )
}

export const fetchConversationMessages = (appId: string, conversation_id: string, getAbortController?: any) => {
  return get(`apps/${appId}/chat-messages`, {
    params: {
      conversation_id,
    },
  }, {
    getAbortController,
  })
}

// export const generationConversationName = (id: string) => {
//   return postPublic<AutomaticRes>(`conversations/${id}/name`, { body: { auto_generate: true } })
// }

export const generateRule = (body: Record<string, any>) => {
  return post<AutomaticRes>('/rule-generate', {
    body,
  })
}

export const fetchModelParams = (providerName: string, modelId: string) => {
  return get(`workspaces/current/model-providers/${providerName}/models/parameter-rules`, {
    params: {
      model: modelId,
    },
  }) as Promise<{ data: ModelParameterRule[] }>
}

export const fetchPromptTemplate = ({
  appMode,
  mode,
  modelName,
  hasSetDataSet,
}: { appMode: string; mode: ModelModeType; modelName: string; hasSetDataSet: boolean }) => {
  return get<Promise<{ chat_prompt_config: ChatPromptConfig; completion_prompt_config: CompletionPromptConfig; stop: [] }>>('/app/prompt-templates', {
    params: {
      app_mode: appMode,
      model_mode: mode,
      model_name: modelName,
      has_context: hasSetDataSet,
    },
  })
}

export const fetchTextGenerationMessage = ({
  appId,
  messageId,
}: { appId: string; messageId: string }) => {
  return get<Promise<any>>(`/apps/${appId}/messages/${messageId}`)
}
