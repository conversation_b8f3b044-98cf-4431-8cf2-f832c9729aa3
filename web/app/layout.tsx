import type { Viewport } from 'next'
import './styles/globals.css'
import './styles/markdown.scss'
import AntdInitor from './components/antd-initor'

import I18nServer from '@/app/components/i18n-server'
import BrowserInitor from '@/app/components/browser-initor'
import { getLocaleOnServer } from '@/i18n/server'
import { APP_DESCRIPTION, APP_KEY, APP_NAME } from '@/config'
import { SystemContextProvider } from '@/context/system-context'

export const metadata = {
  title: APP_NAME,
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  viewportFit: 'cover',
  userScalable: false,
}

const LocaleLayout = ({ children }: { children: React.ReactNode }) => {
  const locale = getLocaleOnServer()

  return (
    <html lang={locale ?? 'en'} className="h-full" data-theme="light">
      <head>
        <meta name="theme-color" content="#FFFFFF" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="description" content={APP_DESCRIPTION} />
        <meta name="Keywords" content={APP_KEY} />
      </head>
      <body
        className="h-full select-auto"
        data-api-prefix={process.env.NEXT_PUBLIC_API_PREFIX}
        data-public-api-prefix={process.env.NEXT_PUBLIC_PUBLIC_API_PREFIX}
        data-public-document-api-prefix={process.env.NEXT_PUBLIC_DOCUMENTATION_API_PREFIX}
        data-public-edition={process.env.NEXT_PUBLIC_EDITION}
        data-public-maintenance-notice={process.env.NEXT_PUBLIC_MAINTENANCE_NOTICE}
        data-public-site-about={process.env.NEXT_PUBLIC_SITE_ABOUT}
        data-public-app-name={process.env.NEXT_PUBLIC_APP_NAME}
        data-public-app-desc={process.env.NEXT_PUBLIC_APP_DESC}
        data-public-app-key={process.env.NEXT_PUBLIC_APP_KEY}
      >
        <BrowserInitor>
          <I18nServer>
            <SystemContextProvider>
              <AntdInitor>{children}</AntdInitor>
            </SystemContextProvider>
          </I18nServer>
        </BrowserInitor>
      </body>
    </html>
  )
}

export default LocaleLayout
