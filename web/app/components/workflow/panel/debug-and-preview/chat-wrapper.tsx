import {
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useImperative<PERSON><PERSON>le,
  useMemo,
  useState,
} from 'react'
import { useNodes } from 'reactflow'
import { t } from 'i18next'
import { BlockEnum } from '../../types'
import { useStore, useWorkflowStore } from '../../store'
import type { StartNodeType } from '../../nodes/start/types'
import { useWorkflowRun } from '../../hooks'
import UserInput from './user-input'
import type { ChatWrapperRefType } from './index'
import Chatflow from '@/app/components/base/chat/chat/indexflow'// 复制index的chat组件
import Chat from '@/app/components/base/chat/chat'
import { fetchSuggestedQuestions, stopChatMessageResponding } from '@/service/debug'
import { useStore as useAppStore } from '@/app/components/app/store'
// 公共能力
import ConversationVariableModal from '@/app/components/workflow/common/modal/conversation-variable'
import { getCropperFontCss } from '@/app/components/app/app-bg-cropper/utils'
import cn from '@/utils/classnames'
import { useFeatures } from '@/app/components/base/features/hooks'
import type { ChatItem, OnSend } from '@/app/components/base/chat/types'
import { getLastAnswer } from '@/app/components/base/chat/utils'
import { FeatureEnum } from '@/app/components/base/features/types'
import { useChat } from '@/app/components/base/chat/chat/hooks'

type ChatWrapperProps = {
  showConversationVariableModal: boolean
  onConversationModalHide: () => void
  showInputsFieldsPanel: boolean
  onHide: () => void
}

type ChatData = {
  write_type?: string
  write_theme?: string
  word_count?: string
  requirement?: string
  request_type?: string
}

const ChatWrapper = forwardRef<ChatWrapperRefType, ChatWrapperProps>(({
  showConversationVariableModal,
  onConversationModalHide,
  showInputsFieldsPanel,
  onHide,
}, ref) => {
  const nodes = useNodes<StartNodeType>()
  const appDetail = useAppStore(s => s.appDetail)
  const workflowStore = useWorkflowStore()
  const inputs = useStore(s => s.inputs)
  const chatAllTypeResultflow = useStore(s => s.chatAllTypeResultflow)
  const setChatAllTypeResultflow = useStore(s => s.setChatAllTypeResultflow)
  const setShowFeaturesPanel = useStore(s => s.setShowFeaturesPanel)
  const { handleRun } = useWorkflowRun()
  // 开始节点
  const startNode = nodes.find(node => node.data.type === BlockEnum.Start)
  // 开始节点的变量
  const startVariables = startNode?.data.variables
  // 应用特性
  const features = useFeatures(s => s.features)
  // 背景配置
  const bgConfig = useFeatures(s => s.features[FeatureEnum.chatBg])
  // 之前的对话列表
  const [appPrevChatList] = useState([])
  // 应用配置
  const config = useMemo(() => {
    return {
      opening_statement: features.opening?.enabled ? (features.opening?.opening_statement || '') : '',
      suggested_questions: features.opening?.enabled ? (features.opening?.suggested_questions || []) : [],
      suggested_questions_after_answer: features.suggested,
      text_to_speech: features.text2speech,
      speech_to_text: features.speech2text,
      retriever_resource: features.citation,
      sensitive_word_avoidance: features.moderation,
      file_upload: features.file,
      voiceInput: features.voiceInput,
      voiceConversation: features.voiceConversation,
    }
  }, [features])

  const {
    conversationId,
    chatList,
    chatListRef,
    suggestedQuestions,
    handleUpdateChatList,
    handleStop,
    handleSend,
    handleRestart,
    openVoiceCall,
    closeVoiceCall,
    isThinking,
    isConnecting,
    isVoiceConversation,
    isResponding,
  } = useChat(
    config as any,
    {
      inputs,
      inputsForm: (startVariables || []) as any,
    },
    appPrevChatList,
    taskId => stopChatMessageResponding(appDetail!.id, taskId),
    true,
    handleRun,
  )

  const [IsLongTxt, setIsLongTxt] = useState(false)
  const [IsLong, setIsLong] = useState(false)

  let chatData: ChatData = {}
  // 发送消息
  const doSend = useCallback<OnSend>(
    (query, files, last_answer) => {
      const data: any = {
        query,
        files,
        inputs: workflowStore.getState().inputs,
        conversation_id: conversationId,
        parent_message_id: last_answer?.id || getLastAnswer(chatListRef.current)?.id || null,
      }
      if (IsLong) {
        chatData = {
          write_type: chatAllTypeResultflow.type?.text || '',
          write_theme: chatAllTypeResultflow.form?.Theme || '',
          word_count: chatAllTypeResultflow.form?.Number || '',
          requirement: chatAllTypeResultflow.form,
        }
        if (chatAllTypeResultflow.type?.text === t('sampleTemplate.IntelligentAgent.CreationTypeTemplate') && chatAllTypeResultflow.files) {
          const sourceFiles = chatAllTypeResultflow.files
          const filesLong = {
            url: '',
            id: sourceFiles.id || sourceFiles.file_id,
            transferMethod: 'template_file',
            supportFileType: 'document',
            uploadedId: sourceFiles.id || sourceFiles.file_id,
            filename: sourceFiles.file_name,
          }
          data.inputs.template_file = filesLong
        }
        if (query === t('sampleTemplate.Generate.GenerateDocument'))
          chatData.request_type = t('sampleTemplate.Generate.GenerateDocument') as string

        data.inputs.sample = JSON.stringify(chatData)
      }
      setChatAllTypeResultflow('')
      handleSend(
        '',
        data,
        {
          onGetSuggestedQuestions: (messageId, getAbortController) =>
            fetchSuggestedQuestions(appDetail!.id, messageId, getAbortController),
        },
      )
    },
    [chatListRef, conversationId, handleSend, workflowStore, appDetail, IsLong, chatAllTypeResultflow],
  )
  // 语音通话
  const doVocieCall = useCallback(() => {
    openVoiceCall({})
  }, [openVoiceCall])
  // 重新生成回答
  const doRegenerate = useCallback(
    (chatItem: ChatItem) => {
      const index = chatList.findIndex(item => item.id === chatItem.id)
      if (index === -1)
        return

      const prevMessages = chatList.slice(0, index)
      const question = prevMessages.pop()
      const lastAnswer = getLastAnswer(prevMessages)

      if (!question)
        return

      handleUpdateChatList(prevMessages)
      doSend(question.content, question.message_files, lastAnswer)
    },
    [chatList, handleUpdateChatList, doSend],
  )

  useEffect(() => {
    startVariables?.forEach((item) => {
      if (item.type === 'longTxt')
        setIsLong(true)
    })
  }, [])
  useImperativeHandle(
    ref,
    () => {
      return {
        handleRestart,
      }
    },
    [handleRestart],
  )

  return (
    <>
      {IsLong
        ? <Chatflow
          answerIcon={appDetail?.icon_url || appDetail?.site?.icon_url || ''}
          config={{
            ...config,
            supportCitationHitInfo: true,
          } as any}
          appData={appDetail as any}
          chatList={chatList}
          isResponding={isResponding}
          chatContainerClassName='px-6'
          chatContainerInnerClassName={cn('mx-auto w-full max-w-[800px]', getCropperFontCss(bgConfig))}
          chatFooterClassName='bottom-0'
          chatFooterInnerClassName={'mx-auto w-full max-w-[800px]'}
          showFileUpload
          showFeatureBar
          onFeatureBarClick={setShowFeaturesPanel}
          onSend={doSend}
          inputs={inputs}
          inputsForm={(startVariables || []) as any}
          onRegenerate={doRegenerate}
          onStopResponding={handleStop}
          noChatInput={IsLong ? !!startVariables : false}
          islong={IsLongTxt}
          isBook={true}
          chatNode={(
            <>
              {showInputsFieldsPanel && <UserInput />}
            </>
          )}
          suggestedQuestions={suggestedQuestions}
          showPromptLog
          showInputBottomTip
        />
        : <Chat
          answerIcon={appDetail?.icon_url || appDetail?.site?.icon_url || ''}
          config={{
            ...config,
            supportCitationHitInfo: true,
          } as any}
          appData={appDetail as any}
          chatList={chatList}
          isResponding={isResponding}
          chatContainerClassName='px-6'
          chatContainerInnerClassName={cn('mx-auto w-full max-w-[800px]', getCropperFontCss(bgConfig))}
          chatFooterClassName='bottom-0'
          chatFooterInnerClassName={'mx-auto w-full max-w-[800px]'}
          showFileUpload
          showFeatureBar
          onFeatureBarClick={setShowFeaturesPanel}
          onSend={doSend}
          inputs={inputs}
          inputsForm={(startVariables || []) as any}
          onRegenerate={doRegenerate}
          onStopResponding={handleStop}
          noChatInput={IsLong ? !!startVariables : false}
          islong={IsLongTxt}
          chatNode={(
            <>
              {showInputsFieldsPanel && <UserInput />}
            </>
          )}
          suggestedQuestions={suggestedQuestions}
          showPromptLog
          showInputBottomTip
          isVoiceCall={isVoiceConversation}
          onVoiceCall={doVocieCall}
          onCancelVoiceCall={closeVoiceCall}
          isThinking={isThinking}
          isConnecting={isConnecting}
        />
      }

      {showConversationVariableModal && (
        <ConversationVariableModal
          conversationID={conversationId}
          onHide={onConversationModalHide}
        />
      )}
    </>
  )
},
)

ChatWrapper.displayName = 'ChatWrapper'

export default memo(ChatWrapper)
