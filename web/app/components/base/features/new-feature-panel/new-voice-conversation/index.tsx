import React, { useCallback, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import produce from 'immer'
import FeatureCollapse from '../../feature-collapse'
import { useFeatures, useFeaturesStore } from '@/app/components/base/features/hooks'
import type { OnFeaturesChange } from '@/app/components/base/features/types'
import { FeatureEnum } from '@/app/components/base/features/types'
import type { NewVoiceConfigType } from '@/models/app'
import LanguageParamConfig from '@/app/components/base/features/new-feature-panel/new-voice-input/param-config-content'

type Props = {
  disabled?: boolean
  onChange?: OnFeaturesChange
  applyType?: string
  voicesConfigData?: NewVoiceConfigType[]
}

const NewVoiceConversation = ({
  disabled,
  onChange,
  applyType,
  voicesConfigData,
}: Props) => {
  const { t } = useTranslation()
  const voiceConversation = useFeatures(s => s.features.text2speech?.voice_conversation)
  const inWorkflow = useFeatures(s => s.inWorkflow)

  const featuresStore = useFeaturesStore()

  // 语音输入参数配置
  const [voiceConversationLanguage, setVoiceConversationLanguage] = useState(voiceConversation?.language)
  const [voiceConversationTimbre, setVoiceConversationTimbre] = useState(voiceConversation?.timbre)

  // 开启/关闭对话语音
  const handleChange = useCallback((type: FeatureEnum, enabled: boolean) => {
    const {
      features,
      setFeatures,
    } = featuresStore!.getState()

    const newFeatures = produce(features, (draft) => {
      // draft[type] = {
      //   ...draft[type],
      //   enabled,
      // }
      if (type === FeatureEnum.text2speech && draft[type]?.voice_conversation) {
        draft[type].voice_conversation = {
          ...draft[type].voice_conversation, // 确保 voice_conversation 存在
          enabled,
        }
      }
      else {
        draft[type] = {
          ...draft[type],
          enabled,
        }
      }
    })
    // console.log(newFeatures, '======newFeatures开关==语音对话===')
    setFeatures(newFeatures)
    if (onChange)
      onChange(newFeatures)
  }, [featuresStore, onChange])
  // 数据保存回调
  const handleSave = useCallback((ignoreVariablesCheck?: boolean) => {
    const newVoiceConversation = produce(voiceConversation, (draft) => {
      if (draft) {
        draft.language = voiceConversationLanguage
        draft.timbre = voiceConversationTimbre
      }
    })
    // onSave(newOpening)
    const {
      features,
      setFeatures,
    } = featuresStore!.getState()
    const newFeatures = produce(features, (draft) => {
      draft.text2speech!.voice_conversation = newVoiceConversation
    })
    setFeatures(newFeatures)
    if (onChange)
      onChange(newFeatures)
  }, [voiceConversation, voiceConversationLanguage, voiceConversationTimbre])
  const onLanguageChange = (value: string) => {
    setVoiceConversationLanguage(value)
  }
  const onTimbreChange = (value: string) => {
    setVoiceConversationTimbre(value)
  }

  useEffect(() => {
    handleSave()
  }, [voiceConversationLanguage, voiceConversationTimbre])

  if (!voiceConversation)
    return
  // console.log('===语音对话===语音输入开关===', getLanguageList, getVoicesConfigData, timbreDefaulValue)
  // console.log(voiceConversation, '====语音对话===voiceConversation===')
  return (
    <FeatureCollapse
      inWorkflow={inWorkflow}
      title={t('appDebug.feature.newVoice.voiceConversation.title')}
      tooltip={applyType === 'selfPlanning' && t('appDebug.feature.voiceConversation.description')}
      rightTooltipDesc={ applyType === 'workflow' && t('appDebug.feature.voiceConversation.rightTooltipDesc')}
      value={!!voiceConversation?.enabled}
      onChange={state => handleChange(FeatureEnum.text2speech, state)}
      disabled={disabled}
    >
      {!!voiceConversation?.enabled && (
        <>
          <div className='flex flex-col gap-[10px]'>
            <div>
              {/* <div className={labelStyle}>{t('appDebug.openingStatement.label1')}</div> */}
              <LanguageParamConfig
                languageValue = { voiceConversation?.language || '' }
                timbreValue = { voiceConversation?.timbre || '' }
                onLanguageChange = { onLanguageChange }
                onTimbreChange = { onTimbreChange }
                voicesConfigData={voicesConfigData}
                disabled={disabled}
              />
            </div>
          </div>
        </>
      )}
    </FeatureCollapse>
  )
}

export default NewVoiceConversation
