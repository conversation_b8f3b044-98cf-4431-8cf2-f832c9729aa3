import { useTranslation } from 'react-i18next'
import { type FC, useEffect, useRef, useState } from 'react'
import React from 'react'
import type { VideoNodeType } from './types'
import useConfig from './use-config'
import style from './styles/index.module.css'
import type { Video } from '@/types/videos'
import { fetchVideoList } from '@/service/videos'

// 公共能力
import s from '@/app/components/workflow/nodes/styles/style.module.css'
import type { NodeProps } from '@/app/components/workflow/types'
import ForkTip from '@/app/components/base/forkTip'
import Avatar from '@/app/components/base/app-info/avatar'

const Node: FC<NodeProps<VideoNodeType>> = ({
  id,
  data,
}) => {
  const { t } = useTranslation()
  // 私有的视频库信息
  const { forkVideos, setForkVideos } = useConfig(id, data)
  // 选中的视频库信息
  const [selectedVideos, setSelectedVideos] = useState<Video[]>([])
  // 更新凭据
  const updateTime = useRef(0)

  // 更新视频库信息
  useEffect(() => {
    (async () => {
      updateTime.current = updateTime.current + 1
      const currUpdateTime = updateTime.current

      if (data.library_ids?.length > 0) {
        const { data: videosWithDetail } = await fetchVideoList({ page: 1, ids: data.library_ids })
        //  avoid old data overwrite new data
        if (currUpdateTime < updateTime.current)
          return
        if (data.fork) {
          setForkVideos(videosWithDetail.info)
          setSelectedVideos([])
        }
        else {
          setSelectedVideos(videosWithDetail.info)
        }
      }
      else {
        setSelectedVideos([])
      }
    })()
  }, [data.fork, data.library_ids, setForkVideos])
  if (!selectedVideos.length && !forkVideos.length)
    return null
  return (
    <div className={s.nodeContent}>
      {forkVideos.length > 0 && data.fork && (
        <ForkTip
          className='forkTip mb-2'
          message={t('common.fork.datasetTip')}
        >
          <div>
            {forkVideos.map(({ id, name }) => (
              <div key={id} className=''>
                <div className=''>
                  · {name}
                </div>
              </div>
            ))}
          </div>
        </ForkTip>
      )}
      {selectedVideos.length > 0 && (
        <div className='space-y-2'>
          {selectedVideos.map(({ id, name, type }) => (
            <div key={id} className={style['video-item']}>
              <Avatar className='mr-1' size={12} appMode={type}></Avatar>
              <div className={style['video-item-title']}>
                {name}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default React.memo(Node)
