.iconBtn {
  @apply w-4 h-4 text-gray-G3 hover:text-primary-P1 cursor-pointer;
}
.textBtn {
  @apply flex gap-1 items-center text-[14px] leading-H3 font-normal text-gray-G3 hover:text-primary-P1 cursor-pointer ;
}
.desctext {
  color: var(--color-gray-G2);
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 166.667% */
}
.normalTextG1 {
  @apply truncate;
  color: var(--color-gray-G2);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 166.667% */
}
.normalTextG2 {
  color: var(--color-gray-G2);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 166.667% */
}
.typeRadio {
  @apply text-gray-G1 border border-gray-G5 hover:border-primary-P1 hover:text-primary-P1 cursor-pointer;
  border-radius: 4px;
  display: flex;
  width: 120px;
  padding: 6px 12px;
  justify-content: center;
  align-items: center;
  gap: 4px;
}
.typeRadio.active {
  @apply border-primary-P1 text-primary-P1;
}
.variableItemWrap {
  @apply flex flex-col gap-0.5 border border-gray-G5 rounded mb-2 last-of-type:mb-0;
  padding: 8px 12px;
}
.header {
  @apply flex items-center justify-between h-[24px];
}
.variableTitle {
  @apply grow flex gap-1 items-center;
}
.actionWrap {
  @apply  gap-2 items-center;
}
.variableDesc {
  @apply flex;
}
.tipWrap {
  @apply p-2 rounded-[2px];
  background: var(--color-gray-G7);
}
.tip {
  @apply flex items-start gap-2;
}
.tipTag {
  @apply border !border-gray-G5;
  border-color: var(--color-gray-G5);
}

.object-value-table {
  @apply w-full border border-gray-G5 rounded overflow-hidden
}
.object-value-header {
  @apply flex items-center h-7 text-text-tertiary uppercase;
}
.object-value-cell, .object-value-border-cell {
  @apply flex items-center h-full pl-2;
}
.object-value-border-cell {
  @apply border-r border-gray-200;
}