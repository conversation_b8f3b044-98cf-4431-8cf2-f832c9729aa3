'use client'
import { useState } from 'react'
import useS<PERSON> from 'swr'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import relativeTime from 'dayjs/plugin/relativeTime'
import { useContext } from 'use-context-selector'
import { RiUserAddLine } from '@remixicon/react'
import { useTranslation } from 'react-i18next'
import cn from 'classnames'
import { Table, type TableColumnsType } from 'antd'
import InviteModal from './invite-modal'
import InvitedModal from './invited-modal'
import Operation from './operation'
import { fetchMembers } from '@/service/common'
import { LanguagesSupported } from '@/i18n/language'
import type { InvitationResult, Member } from '@/models/common'
import { Plan } from '@/types/public/billing'
import { NUM_INFINITE, useProviderContext } from '@/context/provider-context'
import { useAppContext } from '@/context/app-context'
import I18n from '@/context/i18n'

import Avatar from '@/app/components/base/avatar'
import LogoEmbedded<PERSON><PERSON><PERSON>eader from '@/app/components/base/logo/logo-embedded-chat-header'
import But<PERSON> from '@/app/components/base/button'
dayjs.extend(relativeTime)

const MembersPage = () => {
  const { t } = useTranslation()
  const RoleMap = {
    owner: t('account.role.owner'),
    admin: t('account.role.admin'),
    editor: t('account.role.editor'),
    dataset_operator: t('account.role.datasetOperator'),
    normal: t('account.role.normal'),
  }
  const { locale } = useContext(I18n)

  const { userProfile, currentWorkspace, canAdmin } = useAppContext()
  const { plan, enableBilling } = useProviderContext()
  const { data, mutate } = useSWR({ url: '/workspaces/current/members', params: {} }, fetchMembers)
  // 当前邀请弹窗是否可见
  const [inviteModalVisible, setInviteModalVisible] = useState(false)
  // 邀请结果
  const [invitationResults, setInvitationResults] = useState<InvitationResult[]>([])
  // 当前被邀请完成弹窗是否可见
  const [invitedModalVisible, setInvitedModalVisible] = useState(false)
  // 账号信息
  const accounts = data?.accounts || []
  // 管理者
  const owner = accounts.filter(account => account.role === 'owner')?.[0]?.email === userProfile.email
  const isNotUnlimitedMemberPlan = enableBilling && plan.type !== Plan.team && plan.type !== Plan.enterprise
  const isMemberFull = enableBilling && isNotUnlimitedMemberPlan && accounts.length >= plan.total.teamMembers

  // 表格列
  const columns: TableColumnsType<Member> = [
    {
      key: 'name',
      title: <div className='pl-3'>{t('account.info.name')}</div>,
      render: (_: any, record: Member) => {
        return <div className='flex items-center py-2 px-3'>
          <Avatar size={24} className='mr-2' name={record.name} />
          <div className=''>
            <div className='text-[13px] font-semibold text-gray-700 leading-[18px]'>
              {record.name}
              {record.status === 'pending' && <span className='ml-1 text-xs text-[#DC6803]'>{t('account.info.pending')}</span>}
              {userProfile.email === record.email && <span className='text-xs text-gray-500'>{t('account.info.you')}</span>}
            </div>
            <div className='text-xs text-gray-500 leading-[18px]'>{record.email}</div>
          </div>
        </div>
      },
    },
    {
      key: 'last_active_at',
      title: t('account.info.lastActive'),
      width: 104,
      render: (_, record) => (
        <div className='text-[13px] text-gray-700'>
          {dayjs(Number((record.last_active_at || record.created_at)) * 1000).locale(locale === 'zh-Hans' ? 'zh-cn' : 'en').fromNow()}
        </div>
      ),
    },
    {
      key: 'role',
      title: t('account.info.role'),
      width: 250,
      render: (_, record) => {
        return (owner && record.role !== 'owner')
          ? <Operation member={record} onOperate={mutate} />
          : <div className='text-[13px] text-gray-700'>{RoleMap[record.role] || RoleMap.normal}</div>
      },
    },
  ]

  return (
    <>
      <div className='flex flex-col'>
        <div className={cn('cardWrap flex items-center mb-4 p-3')}>
          <LogoEmbeddedChatHeader className='!w-10 !h-10' />
          <div className='grow mx-2'>
            <div className='text-sm font-semibold text-gray-900'>{currentWorkspace?.name}</div>
            {/* 有用成员计划，并且是否有限制 */}
            {enableBilling && (
              <div className='text-xs text-gray-500'>
                {isNotUnlimitedMemberPlan
                  ? (
                    <div className='flex space-x-1'>
                      <div>{t('account.plansCommon.member')}{locale !== LanguagesSupported[1] && accounts.length > 1 && 's'}</div>
                      <div className='text-gray-700'>{accounts.length}</div>
                      <div>/</div>
                      <div>{plan.total.teamMembers === NUM_INFINITE ? t('account.plansCommon.unlimited') : plan.total.teamMembers}</div>
                    </div>
                  )
                  : (
                    <div className='flex space-x-1'>
                      <div>{accounts.length}</div>
                      <div>{t('account.plansCommon.memberAfter')}{locale !== LanguagesSupported[1] && accounts.length > 1 && 's'}</div>
                    </div>
                  )}
              </div>
            )}

          </div>
          <Button
            variant='secondary'
            size='small'
            disabled={!canAdmin || isMemberFull}
            onClick={() => (canAdmin && !isMemberFull) && setInviteModalVisible(true)}
          >
            <RiUserAddLine className='w-[14px] h-[14px] mr-[5px]' />
            {t('account.action.invite')}
          </Button>
        </div>
        <Table
          pagination={false}
          columns={columns}
          dataSource={accounts}
          size='middle'
          scroll={{ y: 'calc(100vh - 264px)', x: '100%' }}
        ></Table>
      </div>
      {
        inviteModalVisible && (
          <InviteModal
            onCancel={() => setInviteModalVisible(false)}
            onSend={(invitationResults) => {
              // setInvitedModalVisible(true)
              setInvitationResults(invitationResults)
              mutate()
            }}
          />
        )
      }
      {
        invitedModalVisible && (
          <InvitedModal
            invitationResults={invitationResults}
            onCancel={() => setInvitedModalVisible(false)}
          />
        )
      }
    </>
  )
}

export default MembersPage
