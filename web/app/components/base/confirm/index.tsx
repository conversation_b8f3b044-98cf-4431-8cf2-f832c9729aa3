import React, { useEffect, useRef, useState } from 'react'
import { createPortal } from 'react-dom'
import { useTranslation } from 'react-i18next'
import Button from '../button'
import { ErrorFill, InfoFill, SuccessFill, WarnFill } from '../icons/src/public/common'
import { Close } from '@/app/components/base/icons/src/vender/line/general'

export type IConfirm = {
  className?: string
  isShow: boolean
  type?: 'info' | 'warning' | 'warning2' | 'success'
  title: string
  content?: React.ReactNode
  confirmText?: string | null
  onConfirm: () => void
  cancelText?: string
  onCancel: () => void
  isLoading?: boolean
  isDisabled?: boolean
  showConfirm?: boolean
  showCancel?: boolean
  maskClosable?: boolean
  showCancelAssistant?: boolean
  cancelAssistantTxt?: string
}

function Confirm({
  isShow,
  type = 'warning',
  title,
  content,
  confirmText,
  cancelText,
  onConfirm,
  onCancel,
  showConfirm = true,
  showCancel = true,
  isLoading = false,
  isDisabled = false,
  maskClosable = true,
  showCancelAssistant = false,
  cancelAssistantTxt,

}: IConfirm) {
  const { t } = useTranslation()
  const confirmTxt = confirmText || `${t('common.operation.confirm')}`
  const cancelTxt = cancelText || `${t('common.operation.cancel')}`
  // 弹窗dom索引
  const dialogRef = useRef<HTMLDivElement>(null)
  // 是否展示
  const [isVisible, setIsVisible] = useState(isShow)

  // 点击弹窗外事件
  const handleClickOutside = (event: MouseEvent) => {
    if (maskClosable && dialogRef.current && !dialogRef.current.contains(event.target as Node))
      onCancel()
  }

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape')
        onCancel()
    }
    document.addEventListener('keydown', handleKeyDown)
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [onCancel])
  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [maskClosable])

  useEffect(() => {
    if (isShow) {
      setIsVisible(true)
    }
    else {
      const timer = setTimeout(() => setIsVisible(false), 200)
      return () => clearTimeout(timer)
    }
  }, [isShow])

  if (!isVisible)
    return null

  return createPortal(
    <div className={'fixed inset-0 flex items-center justify-center z-[10000] bg-background-overlay'}
      onClick={(e) => {
        e.preventDefault()
        e.stopPropagation()
      }}>
      <div ref={dialogRef} className={'relative w-full max-w-[424px] overflow-hidden'}>
        <div className='flex flex-col max-w-full p-5 rounded common-bg'>
          {/* 标题部分 */}
          <div className='flex items-center mb-2'>
            { type === 'warning'
              ? <ErrorFill className="w-4 h-4 mr-2"></ErrorFill>
              : type === 'warning2' ? <WarnFill className="w-4 h-4 mr-2"></WarnFill>
              : type === 'success'
                ? <SuccessFill className="w-4 h-4 mr-2"></SuccessFill>
                : <InfoFill className="w-4 h-4 mr-2"></InfoFill> }
            <span className='text-gray-G1 font-semibold leading-S3 text-S4'>{title}</span>
          </div>
          {/* 内容部分 */}
          <div className='ml-6 text-S3 leading-H2 text-gray-G2'>{content}</div>
          {/* 关闭按钮 */}
          <Close className='absolute right-6 top-5 cursor-pointer z-20' onClick={onCancel}></Close>
          {/* 底部菜单 */}
          <div className='flex gap-4 mt-4 justify-end'>
            {showCancel && <Button onClick={onCancel} variant='secondary-accent' className='w-[92px]'>{cancelTxt}</Button>}
            {showConfirm && <Button variant={'primary'} className='w-[92px]' loading={isLoading} disabled={isDisabled} onClick={onConfirm}>{confirmTxt}</Button>}
            {showCancelAssistant && <Button onClick={onCancel} variant='secondary-accent' className='w-[92px]'>{cancelAssistantTxt}</Button>}
          </div>
        </div>
      </div>
    </div>, document.body,
  )
}

export default React.memo(Confirm)
