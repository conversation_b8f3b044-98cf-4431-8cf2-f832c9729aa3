'use client'
import React, { forwardRef, useEffect, useImperativeHandle, useMemo, useRef } from 'react'
import { Card } from 'antd'
import { useTranslation } from 'react-i18next'
import EnhancementParamSetting from './enhancement-param-setting'

import type { RetrievalConfig } from '@/types/datasets'

import style from '@/app/components/datasets/create/knowledge-enhancement-setting/styles/style.module.scss'

type Props = {
  value?: RetrievalConfig
  disabled?: boolean
  onChange: (value: RetrievalConfig) => void
  type?: 'create' | 'edit'
}

const KnowledgeEnhancementSetting = forwardRef(({
  value: passValue,
  disabled = false,
  onChange,
  type,
}: Props, ref) => {
  const { t } = useTranslation()
  const knowRef = useRef<{
    getConfig: () => RetrievalConfig
    resetConfig: () => void
  }>()

  // 数据预处理
  const value = useMemo(() => {
    if (passValue) {
      if (!passValue.weights) {
        return {
          ...passValue,
        }
      }
      return passValue
    }
  }, [passValue])

  // 重置索引方法
  const resetRetrievalMethod = (retrivalConfig: RetrievalConfig) => {
    knowRef.current?.resetConfig()
  }

  useEffect(() => {
    onChange(knowRef.current?.getConfig() as RetrievalConfig)
  }, [])

  useImperativeHandle(ref, () => ({
    resetConfig: resetRetrievalMethod,
  }))

  return (
    <div className='space-y-2'>
      <Card className={style.enhancementSetSty}>
        <EnhancementParamSetting
          // ref={hybridRef}
          ref={knowRef}
          // type={RETRIEVE_METHOD.hybrid}
          value={value}
          onChange={onChange}
          type={type}
        />
      </Card>
    </div>
  )
})

KnowledgeEnhancementSetting.displayName = 'KnowledgeEnhancementSetting'
export default React.memo(KnowledgeEnhancementSetting)
